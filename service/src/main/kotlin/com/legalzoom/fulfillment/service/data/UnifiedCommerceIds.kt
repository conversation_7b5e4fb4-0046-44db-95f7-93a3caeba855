package com.legalzoom.fulfillment.service.data

import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import java.util.UUID

data class UnifiedCommerceIds(
    val cp1OrderId: Int? = null,
    val cp2OrderItemId: String? = null,
) {
    private val containsCp1OrderId = cp1OrderId != null && cp1OrderId != NON_EXISTENT_CP1_ORDER_ID

    // Cam<PERSON> may deserialize the cp2OrderItemId as "" when it should be null, so treat "" as null.
    private val containsCp2OrderItemId = !cp2OrderItemId.isNullOrEmpty()

    init {

        check(containsCp1OrderId || containsCp2OrderItemId) {
            "Invalid UnifiedCommerceIds $this at least one id must exist but both are empty."
        }
        check(!(containsCp1OrderId && containsCp2OrderItemId)) {
            "Invalid UnifiedCommerceIds $this both ids exist and there should be only one."
        }
    }

    val system =
        if (containsCp2OrderItemId) {
            CommerceSystem.CP2
        } else {
            CommerceSystem.CP1
        }

    fun isForSystem(system: CommerceSystem) = this.system == system

    fun getCp2OrderItemIdAsUUID(): UUID =
        checkNotNull(this.cp2OrderItemId) {
            "Unable to get cp2 order item id for unified commerce ids $this"
        }.let { UUID.fromString(it) }

    companion object {
        // For backwards compatibility when a cp1 order id is non-nullable, 0 can be used as effectively null.
        const val NON_EXISTENT_CP1_ORDER_ID = 0
    }
}
