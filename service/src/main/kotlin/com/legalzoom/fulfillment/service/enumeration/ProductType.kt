package com.legalzoom.fulfillment.service.enumeration

import com.legalzoom.fulfillment.domain.Constants.APP_501C3_PROCESS
import com.legalzoom.fulfillment.domain.Constants.BIZ_FORMATION_PROCESS
import com.legalzoom.fulfillment.domain.Constants.EIN_OBTAINMENT_PROCESS
import com.legalzoom.fulfillment.domain.Constants.EP_BUNDLE_PROCESS
import com.legalzoom.fulfillment.domain.Constants.MANUAL_FILING_PROCESS

enum class ProductType(
    val processId: Int,
    val questionnaireId: Int,
    val productComponentIds: Set<Int> = setOf(),
    val productName: String,
    val workflow: String? = null,
    val rpaProductType: String? = null,
    /**
     * The unique name that can be used to identify a product in the CP2 commerce-platform service.
     */
    val cp2ProductInternalName: String? = null,
) {
    INC(1, 63, productName = "Incorporation", workflow = BIZ_FORMATION_PROCESS),
    LLC(
        processId = 2,
        questionnaireId = 27,
        productName = "LLC",
        workflow = BIZ_FORMATION_PROCESS,
        cp2ProductInternalName = "LLC Formation",
    ),
    DBA(18, 89, productName = "DBA", workflow = MANUAL_FILING_PROCESS),
    InitialReports(80, 308, productName = "Initial Reports", workflow = MANUAL_FILING_PROCESS),
    Amendment(25, 163, productName = "Amendment", workflow = MANUAL_FILING_PROCESS),
    AnnualReports(81, 309, productName = "Annual Reports", workflow = MANUAL_FILING_PROCESS),
    EIN(
        processId = 49,
        questionnaireId = 173,
        productName = "EIN Obtainment",
        workflow = EIN_OBTAINMENT_PROCESS,
        cp2ProductInternalName = "EIN",
    ),

    /**
     * In CP1 this is a shipping component on an order, but in CP2 it is a regular product like everything else.
     */
    FoundersKit(
        processId = -1,
        questionnaireId = -1,
        productName = "Founder's Kit",
        cp2ProductInternalName = "Founder's Kit",
    ),
    CertifiedCopies(82, 310, productName = "Certified Copies", workflow = MANUAL_FILING_PROCESS),
    CertificateofGoodStanding(29, 108, productName = "Certificate of Good Standing", workflow = MANUAL_FILING_PROCESS),
    Conversion(47, 106, productName = "Conversion", workflow = MANUAL_FILING_PROCESS),
    CorporateDissolution(26, 161, productName = "Corporate Dissolution", workflow = MANUAL_FILING_PROCESS),
    EntityNameReservation(107, 381, productName = "Entity Name Reservation"),
    SellersPermit(44, 118, productName = "Seller's Permit"),
    StateTaxID(45, 149, productName = "State Tax ID"),
    RegisteredAgentService(83, 307, productName = "Registered Agent Services", workflow = MANUAL_FILING_PROCESS),
    OperatingAgreement(
        76,
        303,
        setOf(132, 631, 632, 2995),
        productName = "Operating Agreements",
        workflow = MANUAL_FILING_PROCESS,
        cp2ProductInternalName = "Operating Agreement",
    ),
    ALTM(150, 672, productName = "Attorney-Led Trademark Registration"),
    NP(20, 98, productName = "Non-Profit", workflow = BIZ_FORMATION_PROCESS),
    LLP(24, 131, productName = "Limited Liability Partnership", workflow = MANUAL_FILING_PROCESS),
    LP(23, 130, productName = "Limited Partnership", workflow = MANUAL_FILING_PROCESS),
    BOIR(173, 698, productName = "Beneficial Ownership Information Report", workflow = MANUAL_FILING_PROCESS),
    ForeignQualification(27, 162, productName = "Foreign Qualification", workflow = MANUAL_FILING_PROCESS),
    TrademarkMonitoring(14, 80, productName = "Trademark Monitoring", workflow = MANUAL_FILING_PROCESS),
    TrademarkSearch(13, 77, productName = "Trademark Search", workflow = MANUAL_FILING_PROCESS),
    ProvisionalPatentSelfHelp(19, 21, productName = "Provisional Patent (Self-help)", workflow = MANUAL_FILING_PROCESS),
    ProvisionalPatentAttyAssisted(
        67,
        247,
        productName = "Provisional Patent (Atty-Assisted)",
        workflow = MANUAL_FILING_PROCESS,
    ),
    ByLawsAndResolutions(75, 304, productName = "Bylaws & Resolutions", workflow = MANUAL_FILING_PROCESS),
    PatentSearch(65, 244, productName = "Patent Search", workflow = MANUAL_FILING_PROCESS),
    PatentIllustrations(84, 314, productName = "Professional Patent Drawings", workflow = MANUAL_FILING_PROCESS),
    UtilityPatentStep1(70, 251, productName = "Utility Patent", workflow = MANUAL_FILING_PROCESS),
    UtilityPatentStep2(71, 253, productName = "Utility Patent Step II", workflow = MANUAL_FILING_PROCESS),
    DesignPatent(69, 250, productName = "Design Patent", workflow = MANUAL_FILING_PROCESS),
    LivingTrust(3, 637, productName = "Living Trust", workflow = EP_BUNDLE_PROCESS),
    LastWillAndTestament(6, 49, productName = "Last Will and Testament", workflow = EP_BUNDLE_PROCESS),
    PowerOfAttorney(11, 17, productName = "Power of Attorney", workflow = EP_BUNDLE_PROCESS),
    LivingWill(9, 54, productName = "Living Will", workflow = EP_BUNDLE_PROCESS),
    WillBundle(180, 703, productName = "Will Bundle", workflow = EP_BUNDLE_PROCESS),
    TrustBundle(181, 705, productName = "Trust Bundle", workflow = EP_BUNDLE_PROCESS),
    Copyright(8, 64, productName = "Copyright", workflow = MANUAL_FILING_PROCESS),
    RealEstateDeedTransfer(processId = 48, questionnaireId = 148, productName = "Real Estate Deed Transfer"),
    LivingTrustTransferDeed(processId = 146, questionnaireId = 666, productName = "Living Trust Transfer Deed"),
    App501c3(
        processId = 59,
        questionnaireId = 692,
        productName = "501c3 Application",
        workflow = APP_501C3_PROCESS,
        rpaProductType = "501c3",
    ),
    Reinstatement(processId = 192, questionnaireId = 698, productName = "Reinstatement", workflow = MANUAL_FILING_PROCESS),
    ;

    companion object {
        fun fromProcessId(key: Int) = enumValues<ProductType>().first { it.processId == key }

        fun fromProcessIdNullable(key: Int?) = enumValues<ProductType>().firstOrNull { it.processId == key }

        fun fromQuestionnaireId(key: Int) = enumValues<ProductType>().firstOrNull { it.questionnaireId == key }

        fun fromProductName(productName: String?) = enumValues<ProductType>().firstOrNull { it.productName == productName }

        fun fromName(entityTypeName: String?): ProductType? {
            if (entityTypeName.isNullOrEmpty()) {
                return null
            }

            return when (entityTypeName.lowercase()) {
                "limited liability company(llc)",
                "limited liability company (llc)",
                "limitedliabilitycompany",
                "limited liability company",
                -> LLC

                "incorporation", "corporation", "for profit corporation" -> INC

                "nonprofit", "non-profit", "non-profit corporation" -> NP

                "limited partnership", "limitedpartnership", "limited partnerships (lp)" -> LP

                "limited liability partnership(llp)",
                "limited liability partnership (llp)",
                "limitedliabilitypartnership",
                "limited liability partnership",
                -> LLP

                else -> {
                    return null
                }
            }
        }
    }
}
