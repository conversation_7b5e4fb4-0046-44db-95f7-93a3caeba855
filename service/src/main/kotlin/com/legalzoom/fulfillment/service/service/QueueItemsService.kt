package com.legalzoom.fulfillment.service.service

import com.legalzoom.api.model.rpa.ODataValueOfIEnumerableOfQueueItemDto
import com.legalzoom.api.rpa.QueueItemsApi
import com.legalzoom.fulfillment.service.service.helper.UipathFolderHelper
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class QueueItemsService(
    private val uipathFolderHelper: UipathFolderHelper,
    private val queueItemsApi: QueueItemsApi,
) {
    fun queueItemsGet(
        mandatoryPermissions: List<String>?,
        atLeastOnePermissions: List<String>?,
        expand: String?,
        filter: String?,
        select: String?,
        orderBy: String?,
        top: Int?,
        skip: Int?,
        count: Boolean?,
        xUipathOrganizationUnitId: Long?,
        rpaProcessId: String,
    ): Mono<ODataValueOfIEnumerableOfQueueItemDto> {
        val xUipathId = xUipathOrganizationUnitId ?: uipathFolderHelper.getUipathFolderPathId(rpaProcessId)!!
        return queueItemsApi.queueItemsGet(
            mandatoryPermissions,
            atLeastOnePermissions,
            expand,
            filter,
            select,
            orderBy,
            top,
            skip,
            count,
            xUipathId,
        )
    }

    fun queueItemsDeleteByIdWithHttpInfo(
        rpaQueueItemId: Long?,
        xUipathOrganizationUnitId: Long?,
        rpaProcessId: String,
    ): Mono<ResponseEntity<Void>> {
        val xUipathId = xUipathOrganizationUnitId ?: uipathFolderHelper.getUipathFolderPathId(rpaProcessId)!!
        return queueItemsApi.queueItemsDeleteByIdWithHttpInfo(
            rpaQueueItemId,
            xUipathId,
        )
    }

    fun queueItemsDeleteById(
        rpaQueueItemId: Long?,
        xUipathOrganizationUnitId: Long?,
        rpaProcessId: String,
    ): Mono<Void> {
        val xUipathId = xUipathOrganizationUnitId ?: uipathFolderHelper.getUipathFolderPathId(rpaProcessId)!!
        return queueItemsApi.queueItemsDeleteById(
            rpaQueueItemId,
            xUipathId,
        )
    }
}
