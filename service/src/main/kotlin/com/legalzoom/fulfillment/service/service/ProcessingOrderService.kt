package com.legalzoom.fulfillment.service.service

import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderRequest
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.fulfillment.common.exception.RetryableException
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.Constants.RETRY_PROCESSING_ORDER_API
import com.legalzoom.fulfillment.service.data.activityFeed.ActivityFeedVariables
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.ASSIGNED_NAME_CHECK
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.DOCUMENTS_PREPARED
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.DOCUMENTS_RECEIVED_FROM_SOS
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.NOT_YET_STARTED
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.READY_FOR_DOWNLOAD
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.SENT_TO_CUSTOMER
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.SENT_TO_SOS
import com.legalzoom.fulfillment.service.enumeration.ActivityFeedType.STATE_REJECTED_FILING
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.AWAITING_SOS
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.DOCUMENTS_AVAILABLE
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.DOCUMENTS_SHIPPED
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.FILING_ACCEPTED
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.FILING_IN_PROGRESS
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.FILING_PROBLEM
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.FILING_REJECTED
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.NOT_STARTED
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.QUESTIONNAIRE_IN_PROGRESS
import com.legalzoom.fulfillment.service.enumeration.WorkOrderStatusEnum.READY_FOR_FULFILLMENT
import com.legalzoom.fulfillment.service.service.OrderStatusDescription.Companion.getVariations
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedService
import com.legalzoom.fulfillment.service.service.orderStatusNotifications.OrderStatusNotificationsService
import io.github.resilience4j.retry.annotation.Retry
import io.opentelemetry.api.trace.SpanKind.CLIENT
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException

/**
* Enum for mapping fulfillment workflow states to COD processingStatusId (column pkProcessingStatus).
*
* Example query from source db:
*     SELECT pkProcessingStatus, sDescription
*     FROM [LZData_Replication].[dbo].[ProcessingStatus]
*     WHERE fkProcess = 2 AND bActive = 1
*     ORDER BY iIndex ASC
*/
enum class ProcessingOrderStatus(
    val processId: Int,
    val processingStatusId: Int,
    val description: OrderStatusDescription,
    val status: String? = null,
    val snsTopicName: String = "",
    val activityFeedType: ActivityFeedType? = null,
    val workOrderStatus: WorkOrderStatusEnum? = null,
    val isFulfillStatus: Boolean = false,
) {
    // LLC - Updated by NGF
    PreFilingValidationStarted(
        processId = 2,
        processingStatusId = 75,
        description = OrderStatusDescription.AssignedForNameCheck,
        activityFeedType = ASSIGNED_NAME_CHECK,
    ),
    PreliminaryNameValidationComplete(
        processId = 2,
        processingStatusId = 76,
        description = OrderStatusDescription.EntityNamePrelimAvailable2,
    ),
    PreliminaryNameValidationFailed(processId = 2, processingStatusId = 77, description = OrderStatusDescription.EntityNamesRejected),
    StateFilingComplete(
        processId = 2,
        processingStatusId = 80,
        description = OrderStatusDescription.SentToSOS,
        snsTopicName = "sentToState",
        activityFeedType = SENT_TO_SOS,
        workOrderStatus = AWAITING_SOS,
    ),
    DocumentsReceivedFromState(
        processId = 2,
        processingStatusId = 83,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
        activityFeedType = DOCUMENTS_RECEIVED_FROM_SOS,
        workOrderStatus = FILING_ACCEPTED,
    ),
    StateRejectedFiling(
        processId = 2,
        processingStatusId = 465,
        description = OrderStatusDescription.StateRejectedFiling,
        activityFeedType = STATE_REJECTED_FILING,
        workOrderStatus = FILING_REJECTED,
    ),
    ReadyForDownload(
        processId = 2,
        processingStatusId = 1456,
        description = OrderStatusDescription.ReadyForDownload,
        snsTopicName = "readyForDownload",
        activityFeedType = READY_FOR_DOWNLOAD,
        workOrderStatus = DOCUMENTS_AVAILABLE,
        isFulfillStatus = true,
    ),
    ShippingComplete(
        processId = 2,
        processingStatusId = 86,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        activityFeedType = SENT_TO_CUSTOMER,
        workOrderStatus = DOCUMENTS_SHIPPED,
        isFulfillStatus = true,
    ),
    DocumentsPrepared(
        processId = 2,
        processingStatusId = 82,
        description = OrderStatusDescription.DocumentsPrepared,
        activityFeedType = DOCUMENTS_PREPARED,
    ),
    AwaitingDocumentFromCustomer(
        processId = 2,
        processingStatusId = 1494,
        description = OrderStatusDescription.AwaitingDocumentFromCustomer,
    ),
    DocumentUnderReview(processId = 2, processingStatusId = 1495, description = OrderStatusDescription.DocumentUnderReview),

    // LLC - NOT updated by NGF
    NotStarted(processId = 2, processingStatusId = 81, description = OrderStatusDescription.NotYetStarted, workOrderStatus = NOT_STARTED),
    DocumentsPrinted(processId = 2, processingStatusId = 84, description = OrderStatusDescription.DocumentsPrinted),
    DocumentsReviewed(processId = 2, processingStatusId = 85, description = OrderStatusDescription.DocumentsReviewed),
    InternalProblem(processId = 2, processingStatusId = 87, description = OrderStatusDescription.InternalProblem),
    PostFilingProblem(processId = 2, processingStatusId = 127, description = OrderStatusDescription.PostFilingProblem),
    ReceivedCustomerSignature(processId = 2, processingStatusId = 463, description = OrderStatusDescription.SignatureReceivedFromCustomer),
    PreliminaryProblem(processId = 2, processingStatusId = 1260, description = OrderStatusDescription.PreFilingProblem),
    CheckRequested(processId = 2, processingStatusId = 1261, description = OrderStatusDescription.CheckRequested),
    AwaitingCustomerSignature(processId = 2, processingStatusId = 79, description = OrderStatusDescription.AwaitingSignatureFromCustomer),
    UnableToProcess(processId = 2, processingStatusId = 1357, description = OrderStatusDescription.UnableToProcess),
    AMReview(processId = 2, processingStatusId = 1412, description = OrderStatusDescription.AMReview),

    // INC - Updated by NGF
    IncPreFilingValidationStarted(
        processId = 1,
        processingStatusId = 1,
        description = OrderStatusDescription.AssignedForNameCheck,
        activityFeedType = ASSIGNED_NAME_CHECK,
    ),
    IncPreliminaryNameValidationComplete(
        processId = 1,
        processingStatusId = 2,
        description = OrderStatusDescription.EntityNamePrelimAvailable2,
    ),
    IncPreliminaryNameValidationFailed(processId = 1, processingStatusId = 3, description = OrderStatusDescription.EntityNamesRejected),
    IncStateFilingComplete(
        processId = 1,
        processingStatusId = 6,
        description = OrderStatusDescription.SentToSOS,
        snsTopicName = "sentToState",
        activityFeedType = SENT_TO_SOS,
    ),
    IncDocumentsReceivedFromState(
        processId = 1,
        processingStatusId = 70,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
        activityFeedType = DOCUMENTS_RECEIVED_FROM_SOS,
    ),
    IncStateRejectedFiling(
        processId = 1,
        processingStatusId = 464,
        description = OrderStatusDescription.StateRejectedFiling,
        activityFeedType = STATE_REJECTED_FILING,
    ),
    IncReadyForDownload(
        processId = 1,
        processingStatusId = 1474,
        description = OrderStatusDescription.ReadyForDownload,
        snsTopicName = "readyForDownload",
        activityFeedType = READY_FOR_DOWNLOAD,
        isFulfillStatus = true,
    ),
    IncShippingComplete(
        processId = 1,
        processingStatusId = 73,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        activityFeedType = SENT_TO_CUSTOMER,
        isFulfillStatus = true,
    ),
    IncDocumentsPrepared(
        processId = 1,
        processingStatusId = 69,
        description = OrderStatusDescription.DocumentsPrepared,
        activityFeedType = DOCUMENTS_PREPARED,
    ),
    IncAwaitingDocumentFromCustomer(
        processId = 1,
        processingStatusId = 1502,
        description = OrderStatusDescription.AwaitingDocumentFromCustomer,
    ),
    IncDocumentUnderReview(processId = 1, processingStatusId = 1503, description = OrderStatusDescription.DocumentUnderReview),

    // INC - NOT updated by NGF
    IncNotStarted(processId = 1, processingStatusId = 54, description = OrderStatusDescription.NotYetStarted),
    IncDocumentsPrinted(processId = 1, processingStatusId = 71, description = OrderStatusDescription.DocumentsPrinted),
    IncDocumentsReviewed(processId = 1, processingStatusId = 72, description = OrderStatusDescription.DocumentsReviewed),
    IncInternalProblem(processId = 1, processingStatusId = 74, description = OrderStatusDescription.InternalProblem),
    IncPostFilingProblem(processId = 1, processingStatusId = 126, description = OrderStatusDescription.PostFilingProblem),
    IncReceivedCustomerSignature(
        processId = 1,
        processingStatusId = 460,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    IncPreliminaryProblem(processId = 1, processingStatusId = 1268, description = OrderStatusDescription.PreFilingProblem),
    IncCheckRequested(processId = 1, processingStatusId = 1269, description = OrderStatusDescription.CheckRequested),
    IncAwaitingCustomerSignature(processId = 1, processingStatusId = 5, description = OrderStatusDescription.AwaitingSignatureFromCustomer),
    IncUnableToProcess(processId = 1, processingStatusId = 1353, description = OrderStatusDescription.UnableToProcess),
    IncAMReview(processId = 1, processingStatusId = 1411, description = OrderStatusDescription.AMReview),

    // NonProfit - Updated by NGF
    NonProfitValidationStarted(processId = 20, processingStatusId = 303, description = OrderStatusDescription.AssignedForNameCheck2),
    NonProfitPreliminaryNameValidationFailed(
        processId = 20,
        processingStatusId = 321,
        description = OrderStatusDescription.EntityNamesRejected,
    ),
    NonProfitPreliminaryNameValidationComplete(
        processId = 20,
        processingStatusId = 482,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    NonProfitStateFilingComplete(
        processId = 20,
        processingStatusId = 304,
        description = OrderStatusDescription.SentToSOS,
        activityFeedType = SENT_TO_SOS,
    ),
    NonProfitDocumentsReceivedFromState(
        processId = 20,
        processingStatusId = 307,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
        activityFeedType = DOCUMENTS_RECEIVED_FROM_SOS,
    ),
    NonProfitStateRejectedFiling(
        processId = 20,
        processingStatusId = 467,
        description = OrderStatusDescription.StateRejectedFiling,
        activityFeedType = STATE_REJECTED_FILING,
    ),
    NonProfitShippingComplete(
        processId = 20,
        processingStatusId = 311,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        activityFeedType = SENT_TO_CUSTOMER,
        isFulfillStatus = true,
    ),
    NonProfitDocumentsPrepared(
        processId = 20,
        processingStatusId = 1036,
        description = OrderStatusDescription.DocumentsPrepared,
        activityFeedType = DOCUMENTS_PREPARED,
    ),
    NonProfitReadyForDownload(
        processId = 20,
        processingStatusId = 1586,
        description = OrderStatusDescription.ReadyForDownload,
        activityFeedType = READY_FOR_DOWNLOAD,
        isFulfillStatus = true,
    ),

    // NonProfit - NOT updated by NGF
    NonProfitNotStarted(processId = 20, processingStatusId = 302, description = OrderStatusDescription.NotYetStarted),
    NonProfitAwaitingCustomerSignature(
        processId = 20,
        processingStatusId = 305,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    NonProfitReceivedCustomerSignature(
        processId = 20,
        processingStatusId = 306,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    NonProfitPreliminaryProblem(processId = 20, processingStatusId = 1258, description = OrderStatusDescription.PreFilingProblem),
    NonProfitPostFilingProblem(processId = 20, processingStatusId = 322, description = OrderStatusDescription.PostFilingProblem),
    NonProfitDocumentsPrinted(processId = 20, processingStatusId = 309, description = OrderStatusDescription.DocumentsPrinted),
    NonProfitDocumentsReviewed(processId = 20, processingStatusId = 310, description = OrderStatusDescription.DocumentsReviewed),
    NonProfitInternalProblem(processId = 20, processingStatusId = 323, description = OrderStatusDescription.InternalProblem),
    NonProfitCheckRequested(processId = 20, processingStatusId = 1259, description = OrderStatusDescription.CheckRequested),
    NonProfitUnableToProcess(processId = 20, processingStatusId = 1358, description = OrderStatusDescription.UnableToProcess),

    // EIN - Updated by NGF
    EinAwaitingDocumentFromCustomer(
        processId = 49,
        processingStatusId = 1518,
        description = OrderStatusDescription.AwaitingDocumentFromCustomer,
    ),
    EinAwaitingFormation(processId = 49, processingStatusId = 1515, description = OrderStatusDescription.AwaitingFormation),
    EinAwaitingIRS(processId = 49, processingStatusId = 1512, description = OrderStatusDescription.AwaitingIRS),
    EinDocumentsReceivedFromIRS(processId = 49, processingStatusId = 1517, description = OrderStatusDescription.DocumentsReceivedFromIRS),
    EinFilingComplete(
        processId = 49,
        processingStatusId = 637,
        description = OrderStatusDescription.SentToCustomer,
        activityFeedType = SENT_TO_CUSTOMER,
        isFulfillStatus = true,
    ),
    EinFilingInProgress(processId = 49, processingStatusId = 1516, description = OrderStatusDescription.FilingInProgress),
    EinPreliminaryProblem(processId = 49, processingStatusId = 1276, description = OrderStatusDescription.PreFilingProblem),

    // EIN - NOT updated by NGF
    EinFilingNotStarted(
        processId = 49,
        processingStatusId = 634,
        description = OrderStatusDescription.NotYetStarted,
        activityFeedType = NOT_YET_STARTED,
    ),
    EinValidationStarted(processId = 49, processingStatusId = 1381, description = OrderStatusDescription.AssignedForNameCheck2),
    EinFilingStarted(processId = 49, processingStatusId = 1045, description = OrderStatusDescription.DocumentsPrepared),
    EinValidationComplete(processId = 49, processingStatusId = 1044, description = OrderStatusDescription.EntityNamePrelimAvailable2),
    EinInternalProblem(processId = 49, processingStatusId = 638, description = OrderStatusDescription.InternalProblem),
    EinPostFilingProblem(processId = 49, processingStatusId = 639, description = OrderStatusDescription.PostFilingProblem),
    EinOnHold(processId = 49, processingStatusId = 640, description = OrderStatusDescription.OnHold),
    EinDocumentsPrinted(processId = 49, processingStatusId = 1046, description = OrderStatusDescription.DocumentsPrinted),
    EinDocumentsReviewed(processId = 49, processingStatusId = 1047, description = OrderStatusDescription.DocumentsReviewed),
    EinUnableToProcess(processId = 49, processingStatusId = 1350, description = OrderStatusDescription.UnableToProcess),

    // RA - Updated by NGF
    RaNotStarted(processId = 83, processingStatusId = 1025, description = OrderStatusDescription.NotYetStarted),
    RaFilingComplete(
        processId = 83,
        processingStatusId = 1028,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),

    // RA - NOT updated by NGF
    RaFilingStarted(processId = 83, processingStatusId = 1026, description = OrderStatusDescription.EntityNamePrelimAvailable),
    RaDocumentsReviewed(processId = 83, processingStatusId = 1027, description = OrderStatusDescription.DocumentsReviewed),
    RaStateFilingComplete(processId = 83, processingStatusId = 1507, description = OrderStatusDescription.SentToSOS),
    RaStateShippingComplete(
        processId = 83,
        processingStatusId = 1028,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    RaPreliminaryProblem(processId = 83, processingStatusId = 1508, description = OrderStatusDescription.PreFilingProblem),
    RaPostFilingProblem(processId = 83, processingStatusId = 1509, description = OrderStatusDescription.PostFilingProblem),
    RaFilingProblem(processId = 83, processingStatusId = 1029, description = OrderStatusDescription.ExternalProblem),

    // Initial Reports - Updated by NGX
    InitialReportsNotStarted(processId = 80, processingStatusId = 1021, description = OrderStatusDescription.NotYetStarted),
    InitialReportsValidationStarted(processId = 80, processingStatusId = 1385, description = OrderStatusDescription.AssignedForNameCheck2),
    InitialReportsPreliminaryNameValidationComplete(
        processId = 80,
        processingStatusId = 1022,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    InitialReportsStateFilingComplete(processId = 80, processingStatusId = 1328, description = OrderStatusDescription.SentToSOS),
    InitialReportsDocumentsReceivedFromState(
        processId = 80,
        processingStatusId = 1330,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    InitialReportsDocumentsPrepared(processId = 80, processingStatusId = 1331, description = OrderStatusDescription.DocumentsPrepared),
    InitialReportsShippingComplete(
        processId = 80,
        processingStatusId = 1023,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    InitialReportsStateRejectedFiling(processId = 80, processingStatusId = 1329, description = OrderStatusDescription.StateRejectedFiling),

    // Initial Reports - NOT Updated by NGX
    InitialReportsPostFilingProblem(processId = 80, processingStatusId = 1024, description = OrderStatusDescription.PostFilingProblem),
    InitialReportsPreFilingProblem(processId = 80, processingStatusId = 1267, description = OrderStatusDescription.PreFilingProblem),
    InitialReportsAwaitingCustomerSignature(
        processId = 80,
        processingStatusId = 1326,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    InitialReportsReceivedCustomerSignature(
        processId = 80,
        processingStatusId = 1327,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),

    // Annual Reports - Updated by NGX
    AnnualReportsValidationStarted(processId = 81, processingStatusId = 1374, description = OrderStatusDescription.AssignedForNameCheck),
    AnnualReportsPreliminaryNameValidationComplete(
        processId = 81,
        processingStatusId = 998,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    AnnualReportsStateFilingComplete(
        processId = 81,
        processingStatusId = 1001,
        description = OrderStatusDescription.SentToSOS,
    ),
    AnnualReportsDocumentsReceivedFromState(
        processId = 81,
        processingStatusId = 1003,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    AnnualReportsDocumentsPrepared(processId = 81, processingStatusId = 1004, description = OrderStatusDescription.DocumentsPrepared),
    AnnualReportsShippingComplete(
        processId = 81,
        processingStatusId = 1007,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),

    // Annual Reports - NOT updated by NGX
    AnnualReportsNotStarted(processId = 81, processingStatusId = 997, description = OrderStatusDescription.NotYetStarted),
    AnnualReportsAwaitingCustomerSignature(
        processId = 81,
        processingStatusId = 999,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    AnnualReportsReceivedCustomerSignature(
        processId = 81,
        processingStatusId = 1000,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    AnnualReportsStateRejectedFiling(processId = 81, processingStatusId = 1002, description = OrderStatusDescription.StateRejectedFiling),
    AnnualReportsDocumentsReviewed(processId = 81, processingStatusId = 1006, description = OrderStatusDescription.DocumentsReviewed),
    AnnualReportsDocumentsPrinted(processId = 81, processingStatusId = 1005, description = OrderStatusDescription.DocumentsPrinted),
    AnnualReportsPreliminaryProblem(processId = 81, processingStatusId = 1287, description = OrderStatusDescription.PreFilingProblem),
    AnnualReportsPostFilingProblem(processId = 81, processingStatusId = 1008, description = OrderStatusDescription.PostFilingProblem),
    AnnualReportsUnableToProcess(processId = 81, processingStatusId = 1343, description = OrderStatusDescription.UnableToProcess),
    AnnualReportsCheckRequested(processId = 81, processingStatusId = 1435, description = OrderStatusDescription.CheckRequested),
    AnnualReportsInternalProblem(processId = 81, processingStatusId = 1436, description = OrderStatusDescription.InternalProblem),

    // 501c3 - NOT updated by NGX
    App501c3Cancelled(processId = 59, processingStatusId = 830, description = OrderStatusDescription.NotYetStarted),
    App501c3NotYetStarted(processId = 59, processingStatusId = 834, description = OrderStatusDescription.NotYetStarted),
    App501c3OnHoldRequestByTheCustomer(
        processId = 59,
        processingStatusId = 835,
        description = OrderStatusDescription.App501c3OnHoldRequestByTheCustomer,
    ),
    App501cAssignedToProcessorOnHoldRequestByTheCustomer(
        processId = 59,
        processingStatusId = 836,
        description = OrderStatusDescription.App501cAssignedToProcessorOnHoldRequestByTheCustomer,
    ),
    App501c3AwaitingFiledNonProfitCorporation(
        processId = 59,
        processingStatusId = 837,
        description = OrderStatusDescription.App501c3AwaitingFiledNonProfitCorporation,
    ),
    App501c3AwaitingEIN(processId = 59, processingStatusId = 838, description = OrderStatusDescription.App501c3AwaitingEIN),
    App501c3RequestedInformationFromCustomer(
        processId = 59,
        processingStatusId = 839,
        description = OrderStatusDescription.App501c3RequestedInformationFromCustomer,
    ),
    App501c3InternalProblem(processId = 59, processingStatusId = 840, description = OrderStatusDescription.InternalProblem),
    App501c3SentToCustomer(
        processId = 59,
        processingStatusId = 841,
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    App501c3ReadyForDownload(
        processId = 59,
        processingStatusId = 1619,
        description = OrderStatusDescription.ReadyForDownload,
        isFulfillStatus = true,
    ),
    App501c3DocumentsReviewed(processId = 59, processingStatusId = 842, description = OrderStatusDescription.DocumentsReviewed),
    App501c3DocumentsPrepared(processId = 59, processingStatusId = 843, description = OrderStatusDescription.DocumentsPrepared),
    App501c3DocumentsPrinted(processId = 59, processingStatusId = 844, description = OrderStatusDescription.DocumentsPrinted),
    App501c31stFollowUpSent(processId = 59, processingStatusId = 914, description = OrderStatusDescription.App501c31stFollowUpSent),
    App501c32ndFollowUpSent(processId = 59, processingStatusId = 915, description = OrderStatusDescription.App501c32ndFollowUpSent),
    App501c3rdFollowUpSent(processId = 59, processingStatusId = 916, description = OrderStatusDescription.App501c3rdFollowUpSent),
    App501c3IncompleteApplication(
        processId = 59,
        processingStatusId = 917,
        description = OrderStatusDescription.App501c3IncompleteApplication,
    ),
    App501c3AmendmentNeeded(processId = 59, processingStatusId = 918, description = OrderStatusDescription.App501c3AmendmentNeeded),
    App501c3ReceivedResponses(processId = 59, processingStatusId = 919, description = OrderStatusDescription.App501c3ReceivedResponses),
    App501c3UnableToProcess(processId = 59, processingStatusId = 1341, description = OrderStatusDescription.UnableToProcess),
    App501c3AwaitingFormation(processId = 59, processingStatusId = 1551, description = OrderStatusDescription.AwaitingFormation),
    App501c3FilingInProgress(processId = 59, processingStatusId = 1552, description = OrderStatusDescription.FilingInProgress),
    App501c3PreFilingProblem(processId = 59, processingStatusId = 1553, description = OrderStatusDescription.PreFilingProblem),

    // Certificate Of Good Standing
    CertificateOfGoodStandingNotStarted(processId = 29, processingStatusId = 645, description = OrderStatusDescription.NotYetStarted),
    CertificateOfGoodStandingValidationStarted(
        processId = 29,
        processingStatusId = 1376,
        description = OrderStatusDescription.AssignedForNameCheck,
    ),
    CertificateOfGoodStandingPreliminaryProblem(
        processId = 29,
        processingStatusId = 1284,
        description = OrderStatusDescription.PreFilingProblem,
    ),
    CertificateOfGoodStandingPreliminaryNameValidationComplete(
        processId = 29,
        processingStatusId = 1030,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    CertificateOfGoodStandingAwaitingTaxClearance(
        processId = 29,
        processingStatusId = 1031,
        description = OrderStatusDescription.AwaitingTaxClearance,
    ),
    CertificateOfGoodStandingAssignedToBSF(processId = 29, processingStatusId = 647, description = OrderStatusDescription.AssignedToBSF),
    CertificateOfGoodStandingStateFilingComplete(processId = 29, processingStatusId = 648, description = OrderStatusDescription.SentToSOS),
    CertificateOfGoodStandingAwaitingCertificate(
        processId = 29,
        processingStatusId = 1406,
        description = OrderStatusDescription.AwaitingCertificateOfGoodStanding,
    ),
    CertificateOfGoodStandingStateRejectedFiling(
        processId = 29,
        processingStatusId = 649,
        description = OrderStatusDescription.StateRejectedFiling,
    ),
    CertificateOfGoodStandingPostFilingProblem(
        processId = 29,
        processingStatusId = 656,
        description = OrderStatusDescription.PostFilingProblem,
    ),
    CertificateOfGoodStandingDocumentsReceivedFromState(
        processId = 29,
        processingStatusId = 650,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    CertificateOfGoodStandingDocumentsPrepared(
        processId = 29,
        processingStatusId = 651,
        description = OrderStatusDescription.DocumentsPrepared,
    ),
    CertificateOfGoodStandingDocumentsPrinted(
        processId = 29,
        processingStatusId = 652,
        description = OrderStatusDescription.DocumentsPrinted,
    ),
    CertificateOfGoodStandingDocumentsReviewed(
        processId = 29,
        processingStatusId = 653,
        description = OrderStatusDescription.DocumentsReviewed,
    ),
    CertificateOfGoodStandingShippingComplete(
        processId = 29,
        processingStatusId = 654,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    CertificateOfGoodStandingInternalProblem(
        processId = 29,
        processingStatusId = 655,
        description = OrderStatusDescription.InternalProblem,
    ),
    CertificateOfGoodStandingCheckRequested(processId = 29, processingStatusId = 1285, description = OrderStatusDescription.CheckRequested),
    CertificateOfGoodStandingUnableToProcess(
        processId = 29,
        processingStatusId = 1345,
        description = OrderStatusDescription.UnableToProcess,
    ),

    // Beneficial Ownership Information Report
    BOIRNotYetStarted(
        processId = 173,
        processingStatusId = 1496,
        description = OrderStatusDescription.NotYetStarted,
        workOrderStatus = NOT_STARTED,
    ),
    BOIRQuestionnaireInProgress(
        processId = 173,
        processingStatusId = 1497,
        description = OrderStatusDescription.QuestionnaireInProgress,
        workOrderStatus = QUESTIONNAIRE_IN_PROGRESS,
    ),
    BOIRDocumentsPrepared(
        processId = 173,
        processingStatusId = 1498,
        description = OrderStatusDescription.DocumentsPrepared,
        workOrderStatus = FILING_IN_PROGRESS,
    ),
    BOIRPreliminaryProblem(
        processId = 173,
        processingStatusId = 1499,
        description = OrderStatusDescription.PreFilingProblem,
        workOrderStatus = FILING_PROBLEM,
    ),
    BOIRReadyForFulfillment(
        processId = 173,
        processingStatusId = 1501,
        description = OrderStatusDescription.ReadyForFulfillment,
        workOrderStatus = READY_FOR_FULFILLMENT,
    ),
    BOIRReadyForDownload(
        processId = 173,
        processingStatusId = 1500,
        description = OrderStatusDescription.ReadyForDownload,
        workOrderStatus = WorkOrderStatusEnum.READY_FOR_DOWNLOAD,
        isFulfillStatus = true,
    ),

    // Trademark Monitoring
    TrademarkMonitoringNotStarted(processId = 14, processingStatusId = 113, description = OrderStatusDescription.NotYetStarted),
    TrademarkMonitoringMonthly(processId = 14, processingStatusId = 115, description = OrderStatusDescription.Monthly),
    TrademarkMonitoringSubscriptionEnded(processId = 14, processingStatusId = 117, description = OrderStatusDescription.SubscriptionEnded),
    TrademarkMonitoringProblem(processId = 14, processingStatusId = 118, description = OrderStatusDescription.Problem),
    TrademarkMonitoringCanceled(processId = 14, processingStatusId = 833, description = OrderStatusDescription.Canceled),

    // Trademark Search
    TrademarkSearchNotStarted(processId = 13, processingStatusId = 104, description = OrderStatusDescription.NotYetStarted),
    TrademarkSearchCompleted(processId = 13, processingStatusId = 100, description = OrderStatusDescription.SearchCompleted),
    TrademarkSearchBound(processId = 13, processingStatusId = 101, description = OrderStatusDescription.SearchBound),
    TrademarkSearchMailed(
        processId = 13,
        processingStatusId = 102,
        description = OrderStatusDescription.SearchMailed,
        snsTopicName = "iPTrademarkSearchComplete",
    ),
    TrademarkSearchProblem(processId = 13, processingStatusId = 103, description = OrderStatusDescription.Problem),
    TrademarkSearchCanceled(processId = 13, processingStatusId = 513, description = OrderStatusDescription.Canceled),
    TrademarkSearchAdminHold(processId = 13, processingStatusId = 792, description = OrderStatusDescription.AdminHold),
    TrademarkSearchWaitingForLogo(processId = 13, processingStatusId = 831, description = OrderStatusDescription.WaitingForLogo),
    TrademarkSearchUnableToProcess(processId = 13, processingStatusId = 926, description = OrderStatusDescription.SearchUnableToProcess),
    TrademarkSearchCompSearchGAndSProblem(
        processId = 13,
        processingStatusId = 954,
        description = OrderStatusDescription.CompSearchGAndSProblem,
    ),
    TrademarkSearchCompSearchAvailable(processId = 13, processingStatusId = 1416, description = OrderStatusDescription.CompSearchAvailable),
    TrademarkSearchOrderReview(processId = 13, processingStatusId = 1457, description = OrderStatusDescription.OrderReview),
    TrademarkSearchCompSearchRequested(processId = 13, processingStatusId = 1459, description = OrderStatusDescription.CompSearchRequested),

    // Certified Copies
    CertifiedCopiesNotStarted(processId = 82, processingStatusId = 1009, description = OrderStatusDescription.NotYetStarted),
    CertifiedCopiesValidationStarted(processId = 82, processingStatusId = 1377, description = OrderStatusDescription.AssignedForNameCheck),
    CertifiedCopiesPreliminaryNameValidationComplete(
        processId = 82,
        processingStatusId = 1010,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    CertifiedCopiesStateFilingComplete(processId = 82, processingStatusId = 1011, description = OrderStatusDescription.SentToSOS),
    CertifiedCopiesStateRejectedFiling(processId = 82, processingStatusId = 1012, description = OrderStatusDescription.StateRejectedFiling),
    CertifiedCopiesDocumentsReceivedFromState(
        processId = 82,
        processingStatusId = 1013,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    CertifiedCopiesDocumentsPrepared(processId = 82, processingStatusId = 1014, description = OrderStatusDescription.DocumentsPrepared),
    CertifiedCopiesDocumentsPrinted(processId = 82, processingStatusId = 1015, description = OrderStatusDescription.DocumentsPrinted),
    CertifiedCopiesDocumentsReviewed(processId = 82, processingStatusId = 1016, description = OrderStatusDescription.DocumentsReviewed),
    CertifiedCopiesShippingComplete(
        processId = 82,
        processingStatusId = 1017,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    CertifiedCopiesExternalProblem(processId = 82, processingStatusId = 1018, description = OrderStatusDescription.ExternalProblem),
    CertifiedCopiesPreliminaryProblem(processId = 82, processingStatusId = 1282, description = OrderStatusDescription.PreFilingProblem),
    CertifiedCopiesCheckRequested(processId = 82, processingStatusId = 1283, description = OrderStatusDescription.CheckRequested),
    CertifiedCopiesUnableToProcess(processId = 82, processingStatusId = 1346, description = OrderStatusDescription.UnableToProcess),
    CertifiedCopiesAwaitingCertifiedCopy(
        processId = 82,
        processingStatusId = 1408,
        description = OrderStatusDescription.AwaitingCertifiedCopy,
    ),

    // Conversion
    ConversionNotStarted(processId = 47, processingStatusId = 614, description = OrderStatusDescription.NotYetStarted),
    ConversionValidationStarted(processId = 47, processingStatusId = 1378, description = OrderStatusDescription.AssignedForNameCheck2),
    ConversionPreliminaryNameValidationComplete(
        processId = 47,
        processingStatusId = 616,
        description = OrderStatusDescription.EntityNamePrelimAvailable2,
    ),
    ConversionPreliminaryNameValidationFailed(
        processId = 47,
        processingStatusId = 617,
        description = OrderStatusDescription.EntityNamesRejected,
    ),
    ConversionAwaitingCustomerSignature(
        processId = 47,
        processingStatusId = 618,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    ConversionReceivedCustomerSignature(
        processId = 47,
        processingStatusId = 619,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    ConversionStateFilingComplete(processId = 47, processingStatusId = 620, description = OrderStatusDescription.SentToSOS),
    ConversionStateRejectedFiling(processId = 47, processingStatusId = 621, description = OrderStatusDescription.StateRejectedFiling),
    ConversionDocumentsReceivedFromState(
        processId = 47,
        processingStatusId = 622,
        description = OrderStatusDescription.DocumentsReceivedFromSOS2,
    ),
    ConversionDocumentsPrepared(processId = 47, processingStatusId = 623, description = OrderStatusDescription.DocumentsPrepared),
    ConversionDocumentsPrinted(processId = 47, processingStatusId = 626, description = OrderStatusDescription.DocumentsPrinted),
    ConversionDocumentsReviewed(processId = 47, processingStatusId = 6627, description = OrderStatusDescription.DocumentsReviewed),
    ConversionShippingComplete(
        processId = 47,
        processingStatusId = 628,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    ConversionInternalProblem(processId = 47, processingStatusId = 629, description = OrderStatusDescription.InternalProblem),
    ConversionPostFilingProblem(processId = 47, processingStatusId = 630, description = OrderStatusDescription.PostFilingProblem),
    ConversionAwaitingCertificate(
        processId = 47,
        processingStatusId = 1038,
        description = OrderStatusDescription.AwaitingCertificateOfGoodStanding2,
    ),
    ConversionPreliminaryProblem(processId = 47, processingStatusId = 1294, description = OrderStatusDescription.PreFilingProblem),
    ConversionCheckRequested(processId = 47, processingStatusId = 1295, description = OrderStatusDescription.CheckRequested),
    ConversionUnableToProcess(processId = 47, processingStatusId = 1347, description = OrderStatusDescription.UnableToProcess),

    // Corporate Dissolution
    CorporateDissolutionNotStarted(processId = 26, processingStatusId = 589, description = OrderStatusDescription.NotYetStarted),
    CorporateDissolutionAwaitingSignatureFromCustomer(
        processId = 26,
        processingStatusId = 596,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    CorporateDissolutionSignatureReceivedFromCustomer(
        processId = 26,
        processingStatusId = 597,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    CorporateDissolutionStateFilingComplete(processId = 26, processingStatusId = 598, description = OrderStatusDescription.SentToSOS),
    CorporateDissolutionStateRejectedFiling(
        processId = 26,
        processingStatusId = 599,
        description = OrderStatusDescription.StateRejectedFiling,
    ),
    CorporateDissolutionDocumentsReceivedFromState(
        processId = 26,
        processingStatusId = 600,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    CorporateDissolutionDocumentsPrepared(processId = 26, processingStatusId = 601, description = OrderStatusDescription.DocumentsPrepared),
    CorporateDissolutionDocumentsPrinted(processId = 26, processingStatusId = 602, description = OrderStatusDescription.DocumentsPrinted),
    CorporateDissolutionDocumentsReviewed(processId = 26, processingStatusId = 603, description = OrderStatusDescription.DocumentsReviewed),
    CorporateDissolutionShippingComplete(
        processId = 26,
        processingStatusId = 604,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    CorporateDissolutionInternalProblem(processId = 26, processingStatusId = 605, description = OrderStatusDescription.InternalProblem),
    CorporateDissolutionPostFilingProblem(processId = 26, processingStatusId = 606, description = OrderStatusDescription.PostFilingProblem),
    CorporateDissolutionPreliminaryNameValidationComplete(
        processId = 26,
        processingStatusId = 1039,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    CorporateDissolutionEntityNamesRejected(
        processId = 26,
        processingStatusId = 1040,
        description = OrderStatusDescription.EntityNamesRejected,
    ),
    CorporateDissolutionAwaitingTaxClearance(
        processId = 26,
        processingStatusId = 1041,
        description = OrderStatusDescription.AwaitingTaxClearance,
    ),
    CorporateDissolutionOrderExpedited(processId = 26, processingStatusId = 1057, description = OrderStatusDescription.OrderExpedited),
    CorporateDissolutionAssignedForFolderCreation(
        processId = 26,
        processingStatusId = 1058,
        description = OrderStatusDescription.AssignedForFolderCreation,
    ),
    CorporateDissolutionPreFilingProblem(processId = 26, processingStatusId = 1280, description = OrderStatusDescription.PreFilingProblem),
    CorporateDissolutionCheckRequested(processId = 26, processingStatusId = 1281, description = OrderStatusDescription.CheckRequested),
    CorporateDissolutionUnableToProcess(processId = 26, processingStatusId = 1348, description = OrderStatusDescription.UnableToProcess),
    CorporateDissolutionAwaitingCertificateOfGoodStanding(
        processId = 26,
        processingStatusId = 1410,
        description = OrderStatusDescription.AwaitingCertificateOfGoodStanding,
    ),

    // Foreign Qualification
    ForeignQualificationNotYetStarted(processId = 27, processingStatusId = 419, description = OrderStatusDescription.NotYetStarted),
    ForeignQualificationValidationStarted(
        processId = 27,
        processingStatusId = 420,
        description = OrderStatusDescription.AssignedForNameCheck2,
    ),
    ForeignQualificationPreliminaryNameValidationComplete(
        processId = 27,
        processingStatusId = 429,
        description = OrderStatusDescription.EntityNamePrelimAvailable2,
    ),
    ForeignQualificationAwaitingSignatureFromCustomer(
        processId = 27,
        processingStatusId = 421,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    ForeignQualificationAwaitingCertificateOfGoodStanding(
        processId = 27,
        processingStatusId = 422,
        description = OrderStatusDescription.AwaitingCertificateOfGoodStanding,
    ),
    ForeignQualificationStateFilingComplete(processId = 27, processingStatusId = 423, description = OrderStatusDescription.SentToSOS),
    ForeignQualificationStateRejectedFiling(
        processId = 27,
        processingStatusId = 424,
        description = OrderStatusDescription.StateRejectedFiling,
    ),
    ForeignQualificationDocumentsReceivedFromState(
        processId = 27,
        processingStatusId = 425,
        description = OrderStatusDescription.DocumentsReceivedFromSOS2,
    ),
    ForeignQualificationDocumentsPrepared(processId = 27, processingStatusId = 426, description = OrderStatusDescription.DocumentsPrepared),
    ForeignQualificationDocumentsPrinted(processId = 27, processingStatusId = 427, description = OrderStatusDescription.DocumentsPrinted),
    ForeignQualificationDocumentsReviewed(processId = 27, processingStatusId = 428, description = OrderStatusDescription.DocumentsReviewed),
    ForeignQualificationSignatureReceivedFromCustomer(
        processId = 27,
        processingStatusId = 430,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    ForeignQualificationEntityNameRejected(
        processId = 27,
        processingStatusId = 457,
        description = OrderStatusDescription.EntityNamesRejected,
    ),
    ForeignQualificationShippingComplete(
        processId = 27,
        processingStatusId = 609,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    ForeignQualificationInternalProblem(processId = 27, processingStatusId = 610, description = OrderStatusDescription.InternalProblem),
    ForeignQualificationPostFilingProblem(processId = 27, processingStatusId = 611, description = OrderStatusDescription.PostFilingProblem),
    ForeignQualificationPreFilingProblem(processId = 27, processingStatusId = 1272, description = OrderStatusDescription.PreFilingProblem),
    ForeignQualificationCheckRequested(processId = 27, processingStatusId = 1273, description = OrderStatusDescription.CheckRequested),
    ForeignQualificationUnableToProcess(processId = 27, processingStatusId = 1352, description = OrderStatusDescription.UnableToProcess),
    ForeignQualificationAwaitingCertifiedCopy(
        processId = 27,
        processingStatusId = 1405,
        description = OrderStatusDescription.AwaitingCertifiedCopy,
    ),

    // Amendments
    AmendmentNoStatus(processId = 25, processingStatusId = 0, description = OrderStatusDescription.NoStatus),
    AmendmentNotYetStarted(processId = 25, processingStatusId = 558, description = OrderStatusDescription.NotYetStarted),
    AmendmentValidationStarted(processId = 25, processingStatusId = 570, description = OrderStatusDescription.AssignedForNameCheck2),
    AmendmentPreliminaryNameValidationComplete(
        processId = 25,
        processingStatusId = 573,
        description = OrderStatusDescription.EntityNamePrelimAvailable2,
    ),
    AmendmentEntityNameRejected(processId = 25, processingStatusId = 574, description = OrderStatusDescription.EntityNamesRejected),
    AmendmentAwaitingSignatureFromCustomer(
        processId = 25,
        processingStatusId = 575,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    AmendmentSignatureReceivedFromCustomer(
        processId = 25,
        processingStatusId = 576,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    AmendmentStateFilingComplete(processId = 25, processingStatusId = 577, description = OrderStatusDescription.SentToSOS),
    AmendmentStateRejectedFiling(processId = 25, processingStatusId = 578, description = OrderStatusDescription.StateRejectedFiling),
    AmendmentDocumentsReceivedFromState(
        processId = 25,
        processingStatusId = 579,
        description = OrderStatusDescription.DocumentsReceivedFromSOS2,
    ),
    AmendmentDocumentsPrepared(processId = 25, processingStatusId = 580, description = OrderStatusDescription.DocumentsPrepared),
    AmendmentDocumentsPrinted(processId = 25, processingStatusId = 581, description = OrderStatusDescription.DocumentsPrinted),
    AmendmentDocumentsReviewed(processId = 25, processingStatusId = 582, description = OrderStatusDescription.DocumentsReviewed),
    AmendmentShippingComplete(
        processId = 25,
        processingStatusId = 584,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    AmendmentInternalProblem(processId = 25, processingStatusId = 585, description = OrderStatusDescription.InternalProblem),
    AmendmentPostFilingProblem(processId = 25, processingStatusId = 586, description = OrderStatusDescription.PostFilingProblem),
    AmendmentPreFilingProblem(processId = 25, processingStatusId = 1288, description = OrderStatusDescription.PreFilingProblem),
    AmendmentCheckRequested(processId = 25, processingStatusId = 1289, description = OrderStatusDescription.CheckRequested),
    AmendmentUnableToProcess(processId = 25, processingStatusId = 1342, description = OrderStatusDescription.UnableToProcess),

    // Limited Liability Partnership
    LimitedLiabilityPartnershipNotYetStarted(processId = 24, processingStatusId = 362, description = OrderStatusDescription.NotYetStarted),
    LimitedLiabilityPartnershipValidationStarted(
        processId = 24,
        processingStatusId = 363,
        description = OrderStatusDescription.AssignedForNameCheck2,
    ),
    LimitedLiabilityPartnershipPreliminaryNameValidationComplete(
        processId = 24,
        processingStatusId = 364,
        description = OrderStatusDescription.EntityNamePrelimAvailable3,
    ),
    LimitedLiabilityPartnershipAwaitingSignature(
        processId = 24,
        processingStatusId = 365,
        description = OrderStatusDescription.AwaitingSignature,
    ),
    LimitedLiabilityPartnershipStateFilingComplete(
        processId = 24,
        processingStatusId = 366,
        description = OrderStatusDescription.SentToSOS2,
    ),
    LimitedLiabilityPartnershipStateRejectedFiling(
        processId = 24,
        processingStatusId = 1510,
        description = OrderStatusDescription.StateRejectedFiling,
    ),
    LimitedLiabilityPartnershipDocumentsReceivedFromState(
        processId = 24,
        processingStatusId = 367,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    LimitedLiabilityPartnershipDocumentsPrepared(
        processId = 24,
        processingStatusId = 368,
        description = OrderStatusDescription.DocumentsPrepared,
    ),
    LimitedLiabilityPartnershipDocumentsPrinted(
        processId = 24,
        processingStatusId = 369,
        description = OrderStatusDescription.DocumentsPrinted,
    ),
    LimitedLiabilityPartnershipDocumentsReviewed(
        processId = 24,
        processingStatusId = 370,
        description = OrderStatusDescription.DocumentsReviewed,
    ),
    LimitedLiabilityPartnershipShippingComplete(
        processId = 24,
        processingStatusId = 371,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    LimitedLiabilityPartnershipPostFilingProblem(
        processId = 24,
        processingStatusId = 372,
        description = OrderStatusDescription.PostFilingProblem,
    ),
    LimitedLiabilityPartnershipInternalProblem(
        processId = 24,
        processingStatusId = 373,
        description = OrderStatusDescription.InternalProblem,
    ),
    LimitedLiabilityPartnershipSignatureReceivedFromCustomer(
        processId = 24,
        processingStatusId = 1019,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    LimitedLiabilityPartnershipPreFilingProblem(
        processId = 24,
        processingStatusId = 1264,
        description = OrderStatusDescription.PreFilingProblem,
    ),
    LimitedLiabilityPartnershipCheckRequested(
        processId = 24,
        processingStatusId = 1265,
        description = OrderStatusDescription.CheckRequested,
    ),
    LimitedLiabilityPartnershipUnableToProcess(
        processId = 24,
        processingStatusId = 1355,
        description = OrderStatusDescription.UnableToProcess,
    ),

    // Limited Partnership
    LimitedPartnershipNotYetStarted(processId = 23, processingStatusId = 376, description = OrderStatusDescription.NotYetStarted),
    LimitedPartnershipValidationStarted(
        processId = 23,
        processingStatusId = 377,
        description = OrderStatusDescription.AssignedForNameCheck2,
    ),
    LimitedPartnershipPreliminaryNameValidationComplete(
        processId = 23,
        processingStatusId = 378,
        description = OrderStatusDescription.EntityNamePrelimAvailable3,
    ),
    LimitedPartnershipAwaitingSignature(processId = 23, processingStatusId = 379, description = OrderStatusDescription.AwaitingSignature),
    LimitedPartnershipStateFilingComplete(processId = 23, processingStatusId = 380, description = OrderStatusDescription.SentToSOS2),
    LimitedPartnershipStateRejectedFiling(
        processId = 23,
        processingStatusId = 1511,
        description = OrderStatusDescription.StateRejectedFiling,
    ),
    LimitedPartnershipDocumentsReceivedFromState(
        processId = 23,
        processingStatusId = 381,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    LimitedPartnershipDocumentsPrepared(processId = 23, processingStatusId = 382, description = OrderStatusDescription.DocumentsPrepared),
    LimitedPartnershipDocumentsPrinted(processId = 23, processingStatusId = 383, description = OrderStatusDescription.DocumentsPrinted),
    LimitedPartnershipDocumentsReviewed(processId = 23, processingStatusId = 384, description = OrderStatusDescription.DocumentsReviewed),
    LimitedPartnershipShippingComplete(
        processId = 23,
        processingStatusId = 385,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    LimitedPartnershipPostFilingProblem(processId = 23, processingStatusId = 386, description = OrderStatusDescription.PostFilingProblem),
    LimitedPartnershipInternalProblem(processId = 23, processingStatusId = 387, description = OrderStatusDescription.InternalProblem),
    LimitedPartnershipSignatureReceivedFromCustomer(
        processId = 23,
        processingStatusId = 1020,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    LimitedPartnershipPreFilingProblem(processId = 23, processingStatusId = 1262, description = OrderStatusDescription.PreFilingProblem),
    LimitedPartnershipCheckRequested(processId = 23, processingStatusId = 1263, description = OrderStatusDescription.CheckRequested),
    LimitedPartnershipUnableToProcess(processId = 23, processingStatusId = 1356, description = OrderStatusDescription.UnableToProcess),

    // Provisional Patent Self Help
    ProvisionalPatentNotYetStarted(processId = 19, processingStatusId = 138, description = OrderStatusDescription.NotYetStarted),
    ProvisionalPatentWaitingForDrawings(
        processId = 19,
        processingStatusId = 139,
        description = OrderStatusDescription.WaitingForDrawings,
        snsTopicName = "provisionalPatentWaitingForDrawings",
    ),
    ProvisionalPatentDrawingsSubmitted(
        processId = 19,
        processingStatusId = 140,
        description = OrderStatusDescription.DrawingsReceivedFromCustomer,
    ),
    ProvisionalPatentApplicationFiled(
        processId = 19,
        processingStatusId = 142,
        description = OrderStatusDescription.ApplicationFiled,
        snsTopicName = "provisionalPatentApplicationFiled",
    ),
    ProvisionalPatentProblem(processId = 19, processingStatusId = 145, description = OrderStatusDescription.Problem),
    ProvisionalPatentHold(processId = 19, processingStatusId = 146, description = OrderStatusDescription.Hold),
    ProvisionalPatentDrawingsNotSubmitted(
        processId = 19,
        processingStatusId = 335,
        description = OrderStatusDescription.DrawingsNotSubmitted,
    ),
    ProvisionalPatentCancelled(processId = 19, processingStatusId = 348, description = OrderStatusDescription.Cancelled),
    ProvisionalPatentPreppedForFiling(processId = 19, processingStatusId = 540, description = OrderStatusDescription.PreppedForFiling),
    ProvisionalPatentSearchInProcess(processId = 19, processingStatusId = 759, description = OrderStatusDescription.SearchInProcess),
    ProvisionalPatentSearchComplete(processId = 19, processingStatusId = 761, description = OrderStatusDescription.SearchComplete),
    ProvisionalPatentSearchSentToCustomer(
        processId = 19,
        processingStatusId = 762,
        description = OrderStatusDescription.SearchSentToCustomer,
    ),
    ProvisionalPatentSearchApproved(processId = 19, processingStatusId = 763, description = OrderStatusDescription.SearchApproved),
    ProvisionalPatentDraftingIllustrations(
        processId = 19,
        processingStatusId = 764,
        description = OrderStatusDescription.DraftingIllustrations,
    ),
    ProvisionalPatentIllustrationsSentToCustomer(
        processId = 19,
        processingStatusId = 766,
        description = OrderStatusDescription.IllustrationsSentToCustomer,
    ),
    ProvisionalPatentRevisingIllustrations(
        processId = 19,
        processingStatusId = 767,
        description = OrderStatusDescription.RevisingIllustrations,
    ),
    ProvisionalPatentRevisedIllustrationsSentToCustomer(
        processId = 19,
        processingStatusId = 769,
        description = OrderStatusDescription.RevisedIllustrationsSentToCustomer,
    ),
    ProvisionalPatentIllustrationsApproved(
        processId = 19,
        processingStatusId = 772,
        description = OrderStatusDescription.IllustrationsApproved,
    ),
    ProvisionalPatentApplicationSentToCustomer(
        processId = 19,
        processingStatusId = 773,
        description = OrderStatusDescription.ApplicationSentToCustomer,
        snsTopicName = "provisionalPatentDIYSearchApplicationSentToCustomer",
    ),
    ProvisionalPatentRevisedApplicationSentToCustomer(
        processId = 19,
        processingStatusId = 774,
        description = OrderStatusDescription.RevisedApplicationSentToCustomer,
    ),
    ProvisionalPatentApplicationApproved(
        processId = 19,
        processingStatusId = 775,
        description = OrderStatusDescription.ApplicationApproved,
    ),
    ProvisionalPatentOrderShipped(
        processId = 19,
        processingStatusId = 786,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),

    // DBA
    DBANotStarted(processId = 18, processingStatusId = 149, description = OrderStatusDescription.NotYetStarted),
    DBAAwaitingCustomerSignature(
        processId = 18,
        processingStatusId = 151,
        description = OrderStatusDescription.AwaitingSignatureFromCustomer,
    ),
    DBAStateFilingComplete(processId = 18, processingStatusId = 152, description = OrderStatusDescription.SentToSOS),
    DBADocumentsReceivedFromState(processId = 18, processingStatusId = 153, description = OrderStatusDescription.DocumentsReceivedFromSOS2),
    DBAShippingComplete(
        processId = 18,
        processingStatusId = 154,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    DBAInternalProblem(processId = 18, processingStatusId = 155, description = OrderStatusDescription.InternalProblem),
    DBAPostFilingProblem(processId = 18, processingStatusId = 156, description = OrderStatusDescription.PostFilingProblem),
    DBAPreliminaryNameValidationComplete(
        processId = 18,
        processingStatusId = 165,
        description = OrderStatusDescription.EntityNamePrelimAvailable2,
    ),
    DBADocumentsPrepared(processId = 18, processingStatusId = 316, description = OrderStatusDescription.DocumentsPrepared),
    DBAReceivedCustomerSignature(
        processId = 18,
        processingStatusId = 450,
        description = OrderStatusDescription.SignatureReceivedFromCustomer,
    ),
    DBAStateRejectedFiling(processId = 18, processingStatusId = 466, description = OrderStatusDescription.StateRejectedFiling),
    DBADocumentsPrinted(processId = 18, processingStatusId = 569, description = OrderStatusDescription.DocumentsPrinted),
    DBADocumentsReviewed(processId = 18, processingStatusId = 1037, description = OrderStatusDescription.DocumentsReviewed),
    DBAPreliminaryProblem(processId = 18, processingStatusId = 1278, description = OrderStatusDescription.PreFilingProblem),
    DBACheckRequested(processId = 18, processingStatusId = 1279, description = OrderStatusDescription.CheckRequested),
    DBAPreliminaryNameValidationFailed(processId = 18, processingStatusId = 1296, description = OrderStatusDescription.EntityNamesRejected),
    DBAUnableToProcess(processId = 18, processingStatusId = 1349, description = OrderStatusDescription.UnableToProcess),
    DBAValidationStarted(processId = 18, processingStatusId = 1380, description = OrderStatusDescription.AssignedForNameCheck2),
    DBAReadyForDownload(
        processId = 18,
        processingStatusId = 1493,
        description = OrderStatusDescription.ReadyForDownload,
        isFulfillStatus = true,
    ),

    // Patent Search
    PatentSearchNotStarted(processId = 65, processingStatusId = 857, description = OrderStatusDescription.NotYetStarted),
    PatentSearchInProcess(processId = 65, processingStatusId = 858, description = OrderStatusDescription.SearchInProcess),
    PatentSearchOrderShipped(
        processId = 65,
        processingStatusId = 859,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        snsTopicName = "provisionalPatentStandaloneSearchSentToCustomer",
    ),
    PatentSearchComplete(processId = 65, processingStatusId = 860, description = OrderStatusDescription.SearchComplete),
    PatentSearchOnHold(processId = 65, processingStatusId = 927, description = OrderStatusDescription.Hold),
    PatentSearchCancelled(processId = 65, processingStatusId = 928, description = OrderStatusDescription.Canceled),

    // Operating Agreements
    OperatingAgreementsNotYetStarted(processId = 76, processingStatusId = 961, description = OrderStatusDescription.NotYetStarted),
    OperatingAgreementsValidationStarted(
        processId = 76,
        processingStatusId = 1387,
        description = OrderStatusDescription.AssignedForNameCheck2,
    ),
    OperatingAgreementsPreliminaryNameValidationComplete(
        processId = 76,
        processingStatusId = 962,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    OperatingAgreementsDocumentsPrepared(processId = 76, processingStatusId = 963, description = OrderStatusDescription.DocumentsPrepared),
    OperatingAgreementsDocumentsPrinted(processId = 76, processingStatusId = 964, description = OrderStatusDescription.DocumentsPrinted),
    OperatingAgreementsDocumentsReviewed(processId = 76, processingStatusId = 965, description = OrderStatusDescription.DocumentsReviewed),
    OperatingAgreementsShippingComplete(
        processId = 76,
        processingStatusId = 966,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    OperatingAgreementsInternalProblem(processId = 76, processingStatusId = 969, description = OrderStatusDescription.InternalProblem),
    OperatingAgreementsOnHold(processId = 76, processingStatusId = 968, description = OrderStatusDescription.OnHold),
    OperatingAgreementsPostFilingProblem(processId = 76, processingStatusId = 967, description = OrderStatusDescription.PostFilingProblem),
    OperatingAgreementsPreFilingProblem(processId = 76, processingStatusId = 1291, description = OrderStatusDescription.PreFilingProblem),
    OperatingAgreementsUnableToProcess(processId = 76, processingStatusId = 1359, description = OrderStatusDescription.UnableToProcess),

    // By Laws And Resolutions
    ByLawsAndResolutionsNotYetStarted(processId = 75, processingStatusId = 970, description = OrderStatusDescription.NotYetStarted),
    ByLawsAndResolutionsValidationStarted(
        processId = 75,
        processingStatusId = 1375,
        description = OrderStatusDescription.AssignedForNameCheck2,
    ),
    ByLawsAndResolutionsPreliminaryNameValidationComplete(
        processId = 75,
        processingStatusId = 971,
        description = OrderStatusDescription.EntityNamePrelimAvailable,
    ),
    ByLawsAndResolutionsDocumentsPrepared(processId = 75, processingStatusId = 972, description = OrderStatusDescription.DocumentsPrepared),
    ByLawsAndResolutionsDocumentsPrinted(processId = 75, processingStatusId = 973, description = OrderStatusDescription.DocumentsPrinted),
    ByLawsAndResolutionsDocumentsReviewed(processId = 75, processingStatusId = 974, description = OrderStatusDescription.DocumentsReviewed),
    ByLawsAndResolutionsShippingComplete(
        processId = 75,
        processingStatusId = 975,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
        isFulfillStatus = true,
    ),
    ByLawsAndResolutionsInternalProblem(processId = 75, processingStatusId = 978, description = OrderStatusDescription.InternalProblem),
    ByLawsAndResolutionsOnHold(processId = 75, processingStatusId = 977, description = OrderStatusDescription.OnHold),
    ByLawsAndResolutionsPostFilingProblem(processId = 75, processingStatusId = 976, description = OrderStatusDescription.PostFilingProblem),
    ByLawsAndResolutionsPreFilingProblem(processId = 75, processingStatusId = 1286, description = OrderStatusDescription.PreFilingProblem),
    ByLawsAndResolutionsUnableToProcess(processId = 75, processingStatusId = 1344, description = OrderStatusDescription.UnableToProcess),

    // Patent Illustrations
    PatentIllustrationsNotStarted(processId = 84, processingStatusId = 1073, description = OrderStatusDescription.NotYetStarted),
    PatentIllustrationsWaitingForMaterials(
        processId = 84,
        processingStatusId = 1074,
        description = OrderStatusDescription.WaitingForCustomerMaterials,
    ),
    PatentIllustrationsMaterialsReceived(
        processId = 84,
        processingStatusId = 1075,
        description = OrderStatusDescription.CustomerMaterialReceived,
    ),
    PatentIllustrationsInProcess(processId = 84, processingStatusId = 1076, description = OrderStatusDescription.DraftingIllustrations),
    PatentIllustrationsSent(
        processId = 84,
        processingStatusId = 1078,
        status = "ShippingComplete",
        description = OrderStatusDescription.IllustrationsSentToCustomer,
    ),
    PatentIllustrationsRevision(processId = 84, processingStatusId = 1079, description = OrderStatusDescription.RevisingIllustrations),
    PatentIllustrationsRevisionSentToLawFirm(
        processId = 84,
        processingStatusId = 1080,
        description = OrderStatusDescription.RevisedIllustrationsSentToLawFirm,
    ),
    PatentIllustrationsRevisionSent(
        processId = 84,
        processingStatusId = 1081,
        description = OrderStatusDescription.RevisedIllustrationsSentToCustomer,
    ),
    PatentIllustrationsComplete(processId = 84, processingStatusId = 1082, description = OrderStatusDescription.OrderCompleted),
    PatentIllustrationsOnHold(processId = 84, processingStatusId = 1083, description = OrderStatusDescription.AdminHold),
    PatentIllustrationsCancelled(processId = 84, processingStatusId = 1084, description = OrderStatusDescription.Canceled),

    // Utility Patent Step 1
    UtilityPatent1NotYetStarted(processId = 70, processingStatusId = 942, description = OrderStatusDescription.NotYetStarted),
    UtilityPatent1ProcessingPatentSearch(processId = 70, processingStatusId = 943, description = OrderStatusDescription.SearchInProcess),
    UtilityPatent1PatentSearchComplete(processId = 70, processingStatusId = 944, description = OrderStatusDescription.SearchComplete),
    UtilityPatent1PatentSearchSentToCustomer(
        processId = 70,
        processingStatusId = 945,
        description = OrderStatusDescription.PatentSearchSentToCustomer,
        snsTopicName = "utilityPatentSearchSentCustomer",
    ),
    UtilityPatent1PatentSearchApproved(
        processId = 70,
        processingStatusId = 946,
        description = OrderStatusDescription.PatentSearchApproved,
        snsTopicName = "utilityPatentSearchApproved",
    ),
    UtilityPatent1AttorneyConsultationComplete(
        processId = 70,
        processingStatusId = 947,
        description = OrderStatusDescription.AttorneyConsultationComplete,
    ),
    UtilityPatent1Hold(processId = 70, processingStatusId = 949, description = OrderStatusDescription.Hold),
    UtilityPatent1Cancelled(processId = 70, processingStatusId = 950, description = OrderStatusDescription.Cancelled),
    UtilityPatent1WaitingForMaterials(processId = 70, processingStatusId = 1306, description = OrderStatusDescription.WaitingForMaterials),
    UtilityPatent1MaterialsReceivedFromCustomer(
        processId = 70,
        processingStatusId = 1307,
        description = OrderStatusDescription.MaterialsReceivedFromCustomer,
    ),
    UtilityPatent1InitialConsultationInProgress(
        processId = 70,
        processingStatusId = 1308,
        description = OrderStatusDescription.InitialConsultationInProgress,
    ),
    UtilityPatent1InitialConsultationCompleted(
        processId = 70,
        processingStatusId = 1309,
        description = OrderStatusDescription.InitialConsultationComplete,
    ),
    UtilityPatent1DraftingTechnicalDrwaings(
        processId = 70,
        processingStatusId = 1310,
        description = OrderStatusDescription.DraftingTechnicalDrawings,
    ),
    UtilityPatent1TechnicalDrawingsSentToCustomer(
        processId = 70,
        processingStatusId = 1311,
        description = OrderStatusDescription.TechnicalDrawingsSentToCustomer,
        snsTopicName = "utilityPatentTechDrawingsSentToCustomer",
    ),
    UtilityPatent1RevisingTechnicalDrawings(
        processId = 70,
        processingStatusId = 1312,
        description = OrderStatusDescription.RevisingTechnicalDrawings,
    ),
    UtilityPatent1RevisedTechnicalDrawingsSentToCustomer(
        processId = 70,
        processingStatusId = 1313,
        description = OrderStatusDescription.RevisedTechnicalDrawingsSentToCustomer,
        snsTopicName = "utilityPatentRevTechDrawingsSentToCustomer",
    ),
    UtilityPatent1TechnicalDrawingsApproved(
        processId = 70,
        processingStatusId = 1314,
        description = OrderStatusDescription.TechnicalDrawingsApproved,
    ),
    UtilityPatent1OrderCompleted(
        processId = 70,
        processingStatusId = 1316,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
    ),

    // Utility Patent Step 2
    UtilityPatent2NotYetStarted(processId = 71, processingStatusId = 1062, description = OrderStatusDescription.NotYetStarted),
    UtilityPatent2InitialLawFirmReview(
        processId = 71,
        processingStatusId = 1063,
        description = OrderStatusDescription.InitialLawFirmReview,
    ),
    UtilityPatent2RevisingTechnicalDrawings(
        processId = 71,
        processingStatusId = 1067,
        description = OrderStatusDescription.RevisingTechnicalDrawings,
    ),
    UtilityPatent2RevisedTechnicalDrawingsSentToCustomer(
        processId = 71,
        processingStatusId = 1068,
        description = OrderStatusDescription.RevisedTechnicalDrawingsSentToCustomer,
    ),
    UtilityPatent2Hold(processId = 71, processingStatusId = 1070, description = OrderStatusDescription.Hold),
    UtilityPatent2Cancelled(processId = 71, processingStatusId = 1071, description = OrderStatusDescription.Cancelled),
    UtilityPatent2SendingMaterialsToLawFirm(
        processId = 71,
        processingStatusId = 1317,
        description = OrderStatusDescription.SendingMaterialsToLawFirm,
    ),
    UtilityPatent2LawFirmInProcess(processId = 71, processingStatusId = 1318, description = OrderStatusDescription.LawFirmInProcess),
    UtilityPatent2ApplicationFiled(processId = 71, processingStatusId = 1319, description = OrderStatusDescription.ApplicationFiled),

    // Provisional Patent Atty Assisted
    ProvisionalPatentAANotYetStarted(processId = 67, processingStatusId = 888, description = OrderStatusDescription.NotYetStarted),
    ProvisionalPatentAAIllustrationsRequested(
        processId = 67,
        processingStatusId = 890,
        description = OrderStatusDescription.IllustrationsRequested,
    ),
    ProvisionalPatentAATechnicalDrawingsSentToCustomer(
        processId = 67,
        processingStatusId = 891,
        description = OrderStatusDescription.TechnicalDrawingsSentToCustomer,
        snsTopicName = "provisionalPatentAATechDrawingsSentToCustomer",
    ),
    ProvisionalPatentAARevisingTechnicalDrawings(
        processId = 67,
        processingStatusId = 892,
        description = OrderStatusDescription.RevisingTechnicalDrawings,
    ),
    ProvisionalPatentAAWaitingForDrawings(
        processId = 67,
        processingStatusId = 894,
        description = OrderStatusDescription.WaitingForDrawings,
    ),
    ProvisionalPatentAAPatentSearchComplete(processId = 67, processingStatusId = 895, description = OrderStatusDescription.SearchComplete),
    ProvisionalPatentAASendingMaterialsToLawFirm(
        processId = 67,
        processingStatusId = 896,
        description = OrderStatusDescription.SendingMaterialsToLawFirm,
    ),
    ProvisionalPatentAAHold(processId = 67, processingStatusId = 897, description = OrderStatusDescription.Hold),
    ProvisionalPatentAASearchInProcess(processId = 67, processingStatusId = 931, description = OrderStatusDescription.SearchInProcess),
    ProvisionalPatentAASearchSentToCustomer(
        processId = 67,
        processingStatusId = 932,
        description = OrderStatusDescription.SearchSentToCustomer,
        snsTopicName = "provisionalPatentAASearchSentToCustomer",
    ),
    ProvisionalPatentAASearchApproved(
        processId = 67,
        processingStatusId = 933,
        description = OrderStatusDescription.SearchApproved,
        snsTopicName = "provisionalPatentAASearchApproved",
    ),
    ProvisionalPatentAADraftingTechnicalDrawings(
        processId = 67,
        processingStatusId = 934,
        description = OrderStatusDescription.DraftingTechnicalDrawings,
    ),
    ProvisionalPatentAARevisedTechnicalDrawingsSentToCustomer(
        processId = 67,
        processingStatusId = 935,
        description = OrderStatusDescription.RevisedTechnicalDrawingsSentToCustomer,
        snsTopicName = "provisionalPatentAARevTechDrawingsSentToCustomer",
    ),
    ProvisionalPatentAATechnicalDrawingsApproved(
        processId = 67,
        processingStatusId = 936,
        description = OrderStatusDescription.TechnicalDrawingsApproved,
    ),
    ProvisionalPatentAACancelled(processId = 67, processingStatusId = 937, description = OrderStatusDescription.Cancelled),
    ProvisionalPatentAADrawingsSubmitted(
        processId = 67,
        processingStatusId = 1235,
        description = OrderStatusDescription.DrawingsReceivedFromCustomer,
    ),
    ProvisionalPatentAADrawingsNotSubmitted(
        processId = 67,
        processingStatusId = 1302,
        description = OrderStatusDescription.NoDrawingsToSubmit,
    ),
    ProvisionalPatentAAConsultationInProgress(
        processId = 67,
        processingStatusId = 1303,
        description = OrderStatusDescription.ConsultationInProgress,
    ),
    ProvisionalPatentAALawFirmInProcess(processId = 67, processingStatusId = 1304, description = OrderStatusDescription.LawFirmInProcess),
    ProvisionalPatentAAApplicationFiled(
        processId = 67,
        processingStatusId = 1305,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
    ),

    // Design Patent
    DesignPatentNotYetStarted(processId = 69, processingStatusId = 905, description = OrderStatusDescription.NotYetStarted),
    DesignPatentWaitingForMaterials(processId = 69, processingStatusId = 1389, description = OrderStatusDescription.WaitingForMaterials),
    DesignPatentMaterialsReceived(
        processId = 69,
        processingStatusId = 1390,
        description = OrderStatusDescription.MaterialsReceivedFromTheCustomer,
    ),
    DesignPatentConsultationInProcess(
        processId = 69,
        processingStatusId = 1392,
        description = OrderStatusDescription.ConsultationInProcess,
    ),
    DesignPatentConsultationCompleted(
        processId = 69,
        processingStatusId = 1393,
        description = OrderStatusDescription.ConsultationCompleted,
    ),
    DesignPatentProcessingPatentSearch(processId = 69, processingStatusId = 1394, description = OrderStatusDescription.SearchInProcess),
    DesignPatentSearchSentToCustomer(
        processId = 69,
        processingStatusId = 1395,
        description = OrderStatusDescription.PatentSearchSentToCustomer,
    ),
    DesignPatentSearchConsultationInProcess(
        processId = 69,
        processingStatusId = 1396,
        description = OrderStatusDescription.PatentSearchConsultationInProcess,
    ),
    DesignPatentSearchConsultationCompleted(
        processId = 69,
        processingStatusId = 1397,
        description = OrderStatusDescription.PatentSearchConsultationCompleted,
    ),
    DesignPatentDraftingTechnicalDrawings(
        processId = 69,
        processingStatusId = 1398,
        description = OrderStatusDescription.DraftingTechnicalDrawings,
    ),
    DesignPatentTechnicalDrawingsSentToCustomer(
        processId = 69,
        processingStatusId = 1399,
        description = OrderStatusDescription.TechnicalDrawingsSentToCustomer,
        snsTopicName = "designPatentTechDrawingsSentToCustomer",
    ),
    DesignPatentRevisingTechnicalDrawings(
        processId = 69,
        processingStatusId = 1400,
        description = OrderStatusDescription.RevisingTechnicalDrawings,
    ),
    DesignPatentRevisedTechnicalDrawingsSentToCustomer(
        processId = 69,
        processingStatusId = 1401,
        description = OrderStatusDescription.RevisedTechnicalDrawingsSentToCustomer,
        snsTopicName = "designPatentRevTechDrawingsSentToCustomer",
    ),
    DesignPatentTechnicalDrawingsApproved(
        processId = 69,
        processingStatusId = 1402,
        description = OrderStatusDescription.TechnicalDrawingsApproved2,
    ),
    DesignPatentLawFirmInProcess(processId = 69, processingStatusId = 1403, description = OrderStatusDescription.LawFirmInProcess),
    DesignPatentApplicationFiled(processId = 69, processingStatusId = 1404, description = OrderStatusDescription.ApplicationFiled),
    DesignPatentHold(processId = 69, processingStatusId = 912, description = OrderStatusDescription.Hold),
    DesignPatentCancelled(processId = 69, processingStatusId = 930, description = OrderStatusDescription.Cancelled),

    // LivingTrust
    LivingTrustNotYetStarted(processId = 3, processingStatusId = 56, description = OrderStatusDescription.NotYetStarted),
    LivingTrustStarted(processId = 3, processingStatusId = 13, description = OrderStatusDescription.Started),
    LivingTrustHold(processId = 3, processingStatusId = 106, description = OrderStatusDescription.Hold),
    LivingTrustProblem(processId = 3, processingStatusId = 14, description = OrderStatusDescription.Problem),
    LivingTrustReviewFinished(processId = 3, processingStatusId = 556, description = OrderStatusDescription.ReviewFinished),
    LivingTrustReadyForDownload(processId = 3, processingStatusId = 1448, description = OrderStatusDescription.ReadyForDownload),
    LivingTrustReadyToPrint(processId = 3, processingStatusId = 1340, description = OrderStatusDescription.ReadyToPrint),
    LivingTrustSentToShipping(processId = 3, processingStatusId = 543, description = OrderStatusDescription.SentToShipping),
    LivingTrustOrderPrintedAndShipped(
        processId = 3,
        processingStatusId = 15,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
    ),

    LastWillAndTestamentNotYetStarted(processId = 6, processingStatusId = 59, description = OrderStatusDescription.NotYetStarted),
    LastWillAndTestamentStarted(processId = 6, processingStatusId = 22, description = OrderStatusDescription.Started),
    LastWillAndTestamentHold(processId = 6, processingStatusId = 108, description = OrderStatusDescription.Hold),
    LastWillAndTestamentProblem(processId = 6, processingStatusId = 23, description = OrderStatusDescription.Problem),
    LastWillAndTestamentReviewFinished(processId = 6, processingStatusId = 1334, description = OrderStatusDescription.ReviewFinished),
    LastWillAndTestamentReadyForDownload(processId = 6, processingStatusId = 1444, description = OrderStatusDescription.ReadyForDownload),
    LastWillAndTestamentReadyToPrint(processId = 6, processingStatusId = 731, description = OrderStatusDescription.ReadyToPrint),
    LastWillAndTestamentSentToShipping(processId = 6, processingStatusId = 542, description = OrderStatusDescription.SentToShipping),
    LastWillAndTestamentOrderPrintedAndShipped(
        processId = 6,
        processingStatusId = 24,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
    ),

    LivingWillNotYetStarted(processId = 9, processingStatusId = 62, description = OrderStatusDescription.NotYetStarted),
    LivingWillStarted(processId = 9, processingStatusId = 31, description = OrderStatusDescription.Started),
    LivingWillHold(processId = 9, processingStatusId = 109, description = OrderStatusDescription.Hold),
    LivingWillProblem(processId = 9, processingStatusId = 32, description = OrderStatusDescription.Problem),
    LivingWillReviewFinished(processId = 9, processingStatusId = 1336, description = OrderStatusDescription.ReviewFinished),
    LivingWillReadyForDownload(processId = 9, processingStatusId = 1445, description = OrderStatusDescription.ReadyForDownload),
    LivingWillReadyToPrint(processId = 9, processingStatusId = 659, description = OrderStatusDescription.ReadyToPrint),
    LivingWillSentToShipping(processId = 9, processingStatusId = 544, description = OrderStatusDescription.SentToShipping),
    LivingWillOrderPrintedAndShipped(
        processId = 9,
        processingStatusId = 33,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
    ),

    PowerOfAttorneyNotYetStarted(processId = 11, processingStatusId = 52, description = OrderStatusDescription.NotYetStarted),
    PowerOfAttorneyStarted(processId = 11, processingStatusId = 34, description = OrderStatusDescription.Started),
    PowerOfAttorneyHold(processId = 11, processingStatusId = 110, description = OrderStatusDescription.Hold),
    PowerOfAttorneyProblem(processId = 11, processingStatusId = 35, description = OrderStatusDescription.Problem),
    PowerOfAttorneyReviewFinished(processId = 11, processingStatusId = 1335, description = OrderStatusDescription.ReviewFinished),
    PowerOfAttorneyReadyForDownload(processId = 11, processingStatusId = 1446, description = OrderStatusDescription.ReadyForDownload),
    PowerOfAttorneyReadyToPrint(processId = 11, processingStatusId = 732, description = OrderStatusDescription.ReadyToPrint),
    PowerOfAttorneySentToShipping(processId = 11, processingStatusId = 545, description = OrderStatusDescription.SentToShipping),
    PowerOfAttorneyOrderPrintedAndShipped(
        processId = 11,
        processingStatusId = 36,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
    ),

    // Copyright
    CopyrightDepositReceived(processId = 8, processingStatusId = 28, description = OrderStatusDescription.DepositReceived),
    CopyrightNoDepositReceived(processId = 8, processingStatusId = 29, description = OrderStatusDescription.NoDepositReceived),
    CopyrightSentToCROffice(
        processId = 8,
        processingStatusId = 30,
        description = OrderStatusDescription.SentToCopyRightOffice,
        snsTopicName = "orderCopyrightSentToCROffice",
    ),
    CopyrightPrintAndSend(
        processId = 8,
        processingStatusId = 50,
        status = "ShippingComplete",
        description = OrderStatusDescription.SentToCustomer,
        isFulfillStatus = true,
    ),
    CopyrightNotStarted(processId = 8, processingStatusId = 61, description = OrderStatusDescription.NotYetStarted),
    CopyrightProblem(processId = 8, processingStatusId = 93, description = OrderStatusDescription.Problem),
    CopyrightHold(processId = 8, processingStatusId = 120, description = OrderStatusDescription.Hold),
    CopyrightCancelled(processId = 8, processingStatusId = 334, description = OrderStatusDescription.Cancelled),
    CopyrightWaitingForAdditionalMaterials(
        processId = 8,
        processingStatusId = 920,
        description = OrderStatusDescription.WaitingForAdditionalMaterials,
    ),
    CopyrightApplicationProcessed(processId = 8, processingStatusId = 1032, description = OrderStatusDescription.ApplicationProcessed),
    CopyrightApplicationReviewedAndSubmitted(
        processId = 8,
        processingStatusId = 1033,
        description = OrderStatusDescription.ApplicationReadyToFile,
    ),
    CopyrightApplicationSaved(processId = 8, processingStatusId = 1034, description = OrderStatusDescription.ApplicationSubmitted),

    // EP 2.0 - Will bundle
    WillBundleNotYetStarted(processId = 180, processingStatusId = 1621, description = OrderStatusDescription.NotYetStarted),
    WillBundleStarted(processId = 180, processingStatusId = 1622, description = OrderStatusDescription.Started),
    WillBundleReadyForDownload(processId = 180, processingStatusId = 1623, description = OrderStatusDescription.ReadyForDownload),
    WillBundleSentToShipping(processId = 180, processingStatusId = 1624, description = OrderStatusDescription.SentToShipping),
    WillBundleReadyToPrint(processId = 180, processingStatusId = 1625, description = OrderStatusDescription.ReadyToPrint),
    WillBundleOrderPrintedAndShipped(
        processId = 180,
        processingStatusId = 1626,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
    ),
    WillBundleOrderRevisionRequired(processId = 180, processingStatusId = 1869, description = OrderStatusDescription.RevisionRequired),

    // EP 2.0 - Trust bundle
    TrustBundleNotYetStarted(processId = 181, processingStatusId = 1659, description = OrderStatusDescription.NotYetStarted),
    TrustBundleStarted(processId = 181, processingStatusId = 1660, description = OrderStatusDescription.Started),
    TrustBundleReadyForDownload(processId = 181, processingStatusId = 1661, description = OrderStatusDescription.ReadyForDownload),
    TrustBundleSentToShipping(processId = 181, processingStatusId = 1662, description = OrderStatusDescription.SentToShipping),
    TrustBundleReadyToPrint(processId = 181, processingStatusId = 1663, description = OrderStatusDescription.ReadyToPrint),
    TrustBundleOrderPrintedAndShipped(
        processId = 181,
        processingStatusId = 1664,
        description = OrderStatusDescription.SentToCustomer,
        status = "ShippingComplete",
    ),
    TrustBundleOrderRevisionRequired(processId = 181, processingStatusId = 2005, description = OrderStatusDescription.RevisionRequired),

    // Reinstatement
    ReinstatementNotYetStarted(processId = 192, processingStatusId = 1935, description = OrderStatusDescription.NotYetStarted),
    ReinstatementAwaitingRequiredFilingCompletion(
        processId = 192,
        processingStatusId = 1968,
        description = OrderStatusDescription.AwaitingRequiredFilingCompletion,
    ),
    ReinstatementSentToSoS(processId = 192, processingStatusId = 1969, description = OrderStatusDescription.SentToSOS),
    ReinstatementDocumentsReceivedFromSoS(
        processId = 192,
        processingStatusId = 1970,
        description = OrderStatusDescription.DocumentsReceivedFromSOS,
    ),
    ReinstatementSentToCustomer(processId = 192, processingStatusId = 1971, description = OrderStatusDescription.SentToCustomer),
    ReinstatementPreFilingProblem(processId = 192, processingStatusId = 1972, description = OrderStatusDescription.PreFilingProblem),
    ;

    companion object {
        private val enumValues = enumValues<ProcessingOrderStatus>()

        private val nameMap = enumValues.associateBy { it.name }

        fun fromName(name: String) = nameMap.getValue(name)

        fun fromNameOrNull(name: String) = nameMap.getOrDefault(name, null)

        private val processingStatusIdProcessIdMap =
            enumValues.map { it.processId to it.processingStatusId }.toSet()
                .associateBy({ it }, { enumValues.first { enum -> enum.processId == it.first && enum.processingStatusId == it.second } })

        fun fromProcessingStatusId(
            processingStatusId: Int,
            processId: Int,
        ): ProcessingOrderStatus {
            return processingStatusIdProcessIdMap.getValue(processId to processingStatusId)
        }

        private val processIdAndStatusMap =
            enumValues.filter { x -> x.status != null }.map { it.processId to it.status }.toSet()
                .associateBy({ it }, { enumValues.first { enum -> enum.processId == it.first && enum.status == it.second } })

        fun fromProcessIdAndStatus(
            processId: Int,
            status: String,
        ): ProcessingOrderStatus? {
            return processIdAndStatusMap.getOrDefault(processId to status, null)
        }

        private val processIdAndDescriptionMap =
            enumValues.map {
                it.processId to it.description
            }.toSet()
                .associateBy({ it }, { enumValues.first { enum -> enum.processId == it.first && enum.description == it.second } })

        fun fromProcessIdAndDescription(
            processId: Int,
            description: OrderStatusDescription?,
        ): ProcessingOrderStatus? {
            if (description == null) {
                return null
            }

            return processIdAndDescriptionMap.getOrDefault(processId to description, null)
                ?: getVariations(description).firstNotNullOfOrNull {
                    processIdAndDescriptionMap.getOrDefault(processId to it, null)
                }
        }

        fun fromProcessIdAndDescription(
            processId: Int,
            description: String?,
        ): ProcessingOrderStatus? {
            return fromProcessIdAndDescription(
                processId,
                OrderStatusDescription.fromValue(description) ?: OrderStatusDescription.fromName(description),
            )
        }

        fun fromProcessIdAndDescriptionNotNull(
            processId: Int,
            description: OrderStatusDescription,
        ): ProcessingOrderStatus {
            return checkNotNull(this.fromProcessIdAndDescription(processId, description)) {
                "Unable to determine processing order status from process id $processId and description $description"
            }
        }
    }
}

enum class OrderStatusDescription(val value: String) {
    AMReview("AM Review"),
    AssignedForNameCheck("Assigned For Name Check"),
    AssignedForNameCheck2("Assigned for Name Check"),
    AwaitingDocumentFromCustomer("Awaiting Document From Customer"),
    AwaitingFormation("Awaiting Formation"),
    AwaitingIRS("Awaiting IRS"),
    AwaitingSignatureFromCustomer("Awaiting Signature From Customer"),
    AwaitingSignature("Awaiting signature"),
    AwaitingRequiredFilingCompletion("Awaiting Required Filing Completion"),
    CheckRequested("Check Requested"),
    Canceled("Canceled"),
    DocumentUnderReview("Document Under Review"),
    DocumentsPrepared("Documents Prepared"),
    DocumentsPrinted("Documents Printed"),
    DocumentsReceivedFromIRS("Document Received from IRS"),
    DocumentsReceivedFromSOS("Documents Received From SOS"),
    DocumentsReceivedFromSOS2("Documents Received from SOS"),
    DocumentsReviewed("Documents Reviewed"),
    EntityNamePrelimAvailable("Entity Name Prelim Available"),
    EntityNamePrelimAvailable2("Entity Name Prelim. Available"),
    EntityNamePrelimAvailable3("Entity Name Preliminary Available"),
    EntityNamesRejected("Entity Names Rejected"),
    ExternalProblem("External Problem"),
    FilingInProgress("Filing in Progress"),
    InternalProblem("Internal Problem"),
    Monthly("Monthly"),
    NotYetStarted("Not Yet Started"),
    NoStatus("No Status"),
    OnHold("On Hold"),
    PostFilingProblem("Post-Filing Problem (Information Required)"),
    PreFilingProblem("Pre-Filing Problem (Information Required)"),
    Problem("Problem"),
    ReadyForDownload("Ready for Download"),
    RevisionRequired("Revision Required"),
    SentToSOS("Sent To SOS"),
    SentToSOS2("Sent out to SOS"),
    SentToCustomer("Sent to Customer"),
    SignatureReceivedFromCustomer("Signature Received From Customer"),
    StateRejectedFiling("State Rejected Filing"),
    SubscriptionEnded("Subscription Ended"),
    UnableToProcess("Unable to Process"),
    AwaitingTaxClearance("Awaiting Tax Clearance"),
    AssignedToBSF("Assigned to BSF"),
    AwaitingCertificateOfGoodStanding("Awaiting Certificate of Good Standing"),
    AwaitingCertificateOfGoodStanding2("Awaiting Cert. of Good Standing"),
    QuestionnaireInProgress("Questionnaire In Progress"),
    ReadyForFulfillment("Ready for Fulfillment"),
    AwaitingCertifiedCopy("Awaiting Certified Copy"),
    OrderExpedited("Order Expedited"),
    AssignedForFolderCreation("Assigned for Folder Creation"),
    SearchCompleted("Search Completed"),
    SearchBound("Search Bound"),
    SearchMailed("Comp Search Delivered"),
    WaitingForLogo("Waiting for Logo"),
    SearchUnableToProcess("Search Unable to Process"),
    CompSearchGAndSProblem("Comp Search G&S Problem"),
    CompSearchAvailable("Comp Search Available"),
    OrderReview("Order Review"),
    CompSearchRequested("Comp Search Requested"),

    // Patent Flow
    WaitingForDrawings("Waiting for Drawings"),
    DrawingsReceivedFromCustomer("Drawings Received from Customer"),
    PatentSearchSentToCustomer("Patent Search Sent to Customer"),
    PatentSearchApproved("Patent Search Approved"),
    AttorneyConsultationComplete("Attorney Consultation Complete"),
    Cancelled("Cancelled"),
    WaitingForMaterials("Waiting for Materials"),
    MaterialsReceivedFromCustomer("Materials Received from Customer"),
    InitialConsultationInProgress("Initial Consultation in Progress"),
    InitialConsultationComplete("Initial Consultation Completed"),
    DraftingTechnicalDrawings("Drafting Technical Drawings"),
    TechnicalDrawingsSentToCustomer("Technical Drawings Sent to Customer"),
    RevisingTechnicalDrawings("Revising Technical Drawings"),
    RevisedTechnicalDrawingsSentToCustomer("Revised Technical Drawings Sent to Customer"),
    TechnicalDrawingsApproved("Techncial Drawings Approved"),
    InitialLawFirmReview("Initial Law Firm Review"),
    SendingMaterialsToLawFirm("Sending Materials to Law Firm"),
    LawFirmInProcess("Law Firm in Process"),
    ApplicationFiled("Application Filed"),
    Hold("Hold"),
    DrawingsNotSubmitted("Drawings Not Submitted"),
    PreppedForFiling("Prepped for Filing"),
    SearchSentToCustomer("Patent Search Sent to Customer"),
    SearchApproved("Patent Search Approved"),
    DraftingIllustrations("Drafting Illustrations"),
    IllustrationsSentToCustomer("Illustrations Sent to Customer"),
    RevisingIllustrations("Revising Illustrations"),
    RevisedIllustrationsSentToCustomer("Revised Illustrations Sent to Customer"),
    IllustrationsApproved("Illustrations Approved"),
    ApplicationSentToCustomer("Application Sent to Customer"),
    RevisedApplicationSentToCustomer("Revised Application Sent to Customer"),
    ApplicationApproved("Application Approved"),
    FinalPackageShipped("Final Package Shipped"),
    SearchInProcess("Processing Patent Search"),
    SearchComplete("Patent Search Complete"),
    WaitingForCustomerMaterials("Waiting for Customer Materials"),
    CustomerMaterialReceived("Customer Materials Received"),
    RevisedIllustrationsSentToLawFirm("Revised Illustrations Sent to Law Firm"),
    OrderCompleted("Order Completed"),
    AdminHold("Admin Hold"),
    NoDrawingsToSubmit("No Drawings to Submit"),
    IllustrationsRequested("Illustrations Requested"),
    ConsultationInProgress("Consultation in Progress"),
    MaterialsReceivedFromTheCustomer("Materials Received From the Customer"),
    ConsultationInProcess("Consultation In Process"),
    ConsultationCompleted("Consultation Completed"),
    PatentSearchConsultationInProcess("Patent Search Consultation in Process"),
    PatentSearchConsultationCompleted("Patent Search Consultation Completed"),
    TechnicalDrawingsApproved2("Technical Drawings Approved"),

    // EP
    Started("Started"),
    ReviewFinished("Review Finished"),
    ReadyToPrint("Ready to Print"),
    SentToShipping("Sent to Shipping"),
    OrderPrintedAndShipped("Order Printed & Shipped"),

    // Copyright
    DepositReceived("Deposit Received"),
    NoDepositReceived("No Deposit Received"),
    SentToCopyRightOffice("Sent To Copyright Office"),
    PrintAndSend("Print And Send"),
    WaitingForAdditionalMaterials("Waiting for Additional Materials"),
    ApplicationProcessed("Application Processed"),
    ApplicationReadyToFile("Application reviewed and submitted"),
    ApplicationSubmitted("Application Saved"),

    // 501c3
    App501c3OnHoldRequestByTheCustomer("On Hold Request by the Customer"),
    App501cAssignedToProcessorOnHoldRequestByTheCustomer("Assigned to Processor On Hold Request by the Customer"),
    App501c3AwaitingFiledNonProfitCorporation("Awaiting Filed Non Profit Corporation"),
    App501c3AwaitingEIN("Awaiting EIN"),
    App501c3RequestedInformationFromCustomer("Requested Information from Customer"),
    App501c31stFollowUpSent("1st Follow Up Sent"),
    App501c32ndFollowUpSent("2nd Follow Up Sent"),
    App501c3rdFollowUpSent("3rd Follow Up Sent"),
    App501c3IncompleteApplication("Incomplete Application"),
    App501c3AmendmentNeeded("Amendment Needed"),
    App501c3ReceivedResponses("Received Responses"),
    ;

    companion object {
        private val valueMap = enumValues<OrderStatusDescription>().associateBy({ it.value }, { it })
        private val nameMap = enumValues<OrderStatusDescription>().associateBy({ it.name }, { it })

        private val variations =
            mapOf(
                AssignedForNameCheck to listOf(AssignedForNameCheck2),
                AssignedForNameCheck2 to listOf(AssignedForNameCheck),
                EntityNamePrelimAvailable to listOf(EntityNamePrelimAvailable2, EntityNamePrelimAvailable3),
                EntityNamePrelimAvailable2 to listOf(EntityNamePrelimAvailable, EntityNamePrelimAvailable3),
                EntityNamePrelimAvailable3 to listOf(EntityNamePrelimAvailable, EntityNamePrelimAvailable2),
                DocumentsReceivedFromSOS to listOf(DocumentsReceivedFromSOS2),
                DocumentsReceivedFromSOS2 to listOf(DocumentsReceivedFromSOS),
                AwaitingCertificateOfGoodStanding to listOf(AwaitingCertificateOfGoodStanding2),
                AwaitingCertificateOfGoodStanding2 to listOf(AwaitingCertificateOfGoodStanding),
                SentToSOS to listOf(SentToSOS2),
                SentToSOS2 to listOf(SentToSOS),
            )

        fun fromValue(value: String?): OrderStatusDescription? = value?.let { valueMap.getOrDefault(it, null) }

        fun fromName(name: String?): OrderStatusDescription? = name?.let { nameMap.getOrDefault(it, null) }

        fun getVariations(description: OrderStatusDescription): List<OrderStatusDescription> {
            return variations.getOrDefault(description, emptyList())
        }
    }
}

@Service
@Retry(name = RETRY_PROCESSING_ORDER_API)
class ProcessingOrderService(
    private val processingOrdersApi: ProcessingOrdersApi,
    private val activityFeedService: ActivityFeedService,
    private val orderStatusNotificationsService: OrderStatusNotificationsService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan(kind = CLIENT)
    fun updateProcessingOrderStatus(
        processingOrderId: Int,
        customerId: String,
        status: ProcessingOrderStatus,
        activityFeedVariables: ActivityFeedVariables? = null,
    ) {
        try {
            var statusChanged = false
            if (activityFeedVariables != null) { // Check the current status
                try {
                    val currentOrderStatus = getProcessingOrderStatusId(processingOrderId, customerId)
                    when {
                        currentOrderStatus.processingStatusId != status.processingStatusId -> statusChanged = true
                    }
                } catch (ise: IllegalStateException) {
                    logger.warn("Unable to get current order status for activity feed. Publishing new status to feed.")
                    statusChanged = true
                }
            }

            logger.event(
                "Updating order status to $status",
                mapOf(
                    "processingOrderId" to processingOrderId,
                ),
            )
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                status.processingStatusId,
                "1",
                customerId,
                true,
                PutProcessingOrderRequest()
                    .comment("Updated processing status to $status")
                    .updatedBy("NGF"),
            ).blockSingle()

            when {
                statusChanged ->
                    activityFeedVariables?.let {
                        activityFeedService.sendEvent(status, it)
                    }
                else -> logger.info("Skipping publishing activity feed.  Previous status is the same as updated.")
            }

            if (statusChanged) {
                try {
                    orderStatusNotificationsService.processOrderStatusChange(status, activityFeedVariables!!)
                } catch (ex: Exception) {
                    logger.error(
                        "ProcessOrderStatusChange failed for status {}, activityFeedVariables {}",
                        status,
                        activityFeedVariables,
                        ex,
                    )
                }
            }

            logger.info(
                "API call to processingOrdersApi to update order status completed.  processId:${status.processId} " +
                    "processingStatusId:${status.processingStatusId} name:${status.name} description:${status.description.value}",
            )
        } catch (wcre: WebClientResponseException) {
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        }
    }

    @WithSpan(kind = CLIENT)
    fun updateProcessingOrderStatus(
        processingOrderId: Int,
        status: ProcessingOrderStatus,
        activityFeedVariables: ActivityFeedVariables? = null,
    ) {
        updateProcessingOrderStatus(processingOrderId, customerId = "", status, activityFeedVariables)
    }

    @WithSpan(kind = CLIENT)
    fun getProcessingOrderStatusId(
        processingOrderId: Int,
        customerId: String,
    ): ProcessingOrderStatus {
        val processingOrder = getProcessingOrder(processingOrderId, customerId)
        if (processingOrder.processingStatusId == null || processingOrder.processId == null) {
            logger.warn(
                "Unable to retrieve processing order for processingOrderId: $processingOrderId and " +
                    "customerId: $customerId. processingStatusId and/or processId are null. " +
                    "ProcessingOrder: $processingOrder",
            )
            throw IllegalStateException("Empty processing order. Unable to map processing status.")
        }
        return ProcessingOrderStatus.fromProcessingStatusId(processingOrder.processingStatusId!!, processingOrder.processId!!)
    }

    @WithSpan(kind = CLIENT)
    fun getProcessingOrder(
        processingOrderId: Int,
        customerId: String,
    ): ProcessingOrderDto =
        try {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                "1",
                customerId,
                true,
            ).blockSingle().processingOrder!!
        } catch (wcre: WebClientResponseException) {
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        }

    @WithSpan(kind = CLIENT)
    fun getProcessingOrder(processingOrderId: Int): ProcessingOrderDto =
        try {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                "1.0",
                null,
                null,
            ).blockSingle().processingOrder!!
        } catch (wcre: WebClientResponseException) {
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        }
}
