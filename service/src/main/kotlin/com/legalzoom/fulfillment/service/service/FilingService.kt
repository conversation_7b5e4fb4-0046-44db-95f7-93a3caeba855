package com.legalzoom.fulfillment.service.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.convertValue
import com.legalzoom.api.expertscollabplatform.WorkItemsApi
import com.legalzoom.api.model.answer.SaveAnswerBankRequest
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerDto
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.expertscollabplatform.MinimalWorkItemDTO
import com.legalzoom.api.order.OrdersOrderItemsApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.answersapi.model.States
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.Constants.UUID_REGEX
import com.legalzoom.fulfillment.service.data.AddressComponents
import com.legalzoom.fulfillment.service.data.Order
import com.legalzoom.fulfillment.service.data.ProcessingOrderResponse
import com.legalzoom.fulfillment.service.data.UnifiedCommerceIds
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.rpa.RPADocument
import com.legalzoom.fulfillment.service.data.rpa.RPAValidationResult
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import com.legalzoom.fulfillment.service.enumeration.DocumentSortDirection.Descending
import com.legalzoom.fulfillment.service.enumeration.DocumentSortOption.CreatedDate
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.helper.ProcessingOrderHelper
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.businessdata.BusinessDataMapperLookupService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataPayload
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerPayload
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.util.UriComponentsBuilder
import java.util.Optional

@Service
class FilingService(
    private val rpaSchemaService: RpaSchemaService,
    private val documentService: DocumentService,
    private val ordersApiService: OrdersApiService,
    private val rPAAddressSeparatedService: RPAAddressSeparatedService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val processingOrderHelper: ProcessingOrderHelper,
    private val objectMapper: ObjectMapper,
    private val processingOrdersApi: ProcessingOrdersApi,
    private val ordersOrderItemsApi: OrdersOrderItemsApi,
    private val businessDataMapperLookupService: BusinessDataMapperLookupService,
    private val orderService: OrderService,
    private val workItemsApi: WorkItemsApi,
    private val unifiedCommerceService: UnifiedCommerceService,
) {
    @Value("\${rpa-document.download-document.partial-url}")
    lateinit var partialUrl: String
    private val logger = LoggerFactory.getLogger(javaClass)

    fun findByOrderId(
        orderId: Long,
        orderVersionNumber: Long,
        subRevision: Long,
    ): Order? {
        return Order()
    }

    fun getFilingData(
        processingOrderId: Int,
        processId: Int,
        customerId: String,
        product: String,
        jurisdiction: String,
        orderId: Int,
        workOrderId: String? = null,
    ): ProcessingOrderResponse? {
        val rpaDocuments: MutableList<RPADocument> = mutableListOf()
        val iteratorDocuments = callFindDocBy(processingOrderId.toLong())
        val jsonDocumentArray = objectMapper.createArrayNode()
        val order: Order?
        val applyValidation: Boolean?
        val commerceIds = unifiedCommerceService.getCommerceIdsByProcessingOrderId(processingOrderId)
        if (iteratorDocuments != null) {
            while (iteratorDocuments.hasNext()) {
                val documentContent: ObjectNode = objectMapper.createObjectNode()
                val document = iteratorDocuments.next()
                val matchResult = document.documentPath?.let { UUID_REGEX.find(it) }
                documentContent.put("documentId", document.documentId)
                documentContent.put("uuid", matchResult?.value)
                rpaDocuments.add(buildRPADocument(document))

                jsonDocumentArray.add(documentContent)
            }
        }

        if (ProductType.BOIR.processId == processId && workOrderId != null) {
            val workOrderAnswers =
                questionnaireAnswerService.getFilingDataForRPAByWorkOrder(
                    processingOrderId,
                    orderId,
                    customerId,
                    workOrderId,
                    jsonDocumentArray,
                )
            order = objectMapper.treeToValue(workOrderAnswers?.get("workOrder"), Order::class.java)
            applyValidation = false
        } else {
            val answers = getAnswers(processId, orderId, processingOrderId, customerId, jurisdiction, commerceIds)
            order = objectMapper.convertValue<Order>(answers)
            applyValidation = (answers.answerSource == AnswerSource.AnswerBank)
        }

        val validationResult: RPAValidationResult? =
            if (applyValidation) {
                order?.let { rpaSchemaService.validateJSON(product, jurisdiction, it) }
            } else {
                RPAValidationResult(
                    passed = true,
                    errors = emptyList(),
                )
            }

        if (order != null) {
            order.isCancelled = unifiedCommerceService.isCanceled(commerceIds, processingOrderId, customerId)
        }

        if (rPAAddressSeparatedService.isAgentAddressSeparated(jurisdiction)) {
            order?.company?.agent?.address?.components =
                rPAAddressSeparatedService.findAddressSeparated(order?.company?.agent?.address!!) as AddressComponents?
        }

        if (ProductType.ALTM.processId == processId) {
            order?.trademark?.attorneyDocketNumber = genAttorneyDocketNumber(processingOrderId)
        }

        return ProcessingOrderResponse(validationResult, order, rpaDocuments)
    }

    private fun getAnswers(
        processId: Int,
        orderId: Int?,
        processingOrderId: Int,
        customerId: String,
        jurisdiction: String,
        commerceIds: UnifiedCommerceIds,
    ): FilingDataPayload {
        var processIdToFetch = processId
        var processingOrderIdToFetch = processingOrderId

        // If order is CP1, look for parent order item
        if (commerceIds.isForSystem(CommerceSystem.CP1)) {
            orderService.getParentPackageOrderItem(processingOrderId, orderId).apply {
                processIdToFetch = this.processingOrder?.processId!!
                processingOrderIdToFetch = this.processingOrder?.processingOrderId!!
            }
        }
        return questionnaireAnswerService.getFilingDataForRPA(
            processingOrderIdToFetch,
            processIdToFetch,
            customerId,
            jurisdiction,
        )
    }

    fun getFilingData(
        processingOrderId: Int?,
        processId: Int?,
        customerId: String?,
    ): FilingDataPayload {
        return questionnaireAnswerService.getFilingData(processingOrderId, processId, customerId)
    }

    fun getBusinessData(processingOrderId: Int): Map<String, Any?> {
        try {
            val processingOrder = processingOrderHelper.obtainProcessingOrder(processingOrderId)
            val customerId: String = processingOrder!!.customerId!!.toString()
            val processId: Int = processingOrder.processId!!

            // Maps business data from answers
            val mapper = businessDataMapperLookupService.getService(processId)
            val answers = getFilingData(processingOrderId, processId, customerId)

            val businessData = mapper.mapBusinessData(processingOrderId, customerId, processId, answers)

            return businessData
        } catch (ex: Exception) {
            logger.error(ex.message, ex)
            throw ex
        }
    }

    fun previewFilingData(processingOrderId: Int): ProcessingOrderResponse? {
        val orderDetails =
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                "1",
                "",
                true,
            ).blockSingle().processingOrder!!

        val processId = orderDetails.processId

        val customerId = orderDetails.customerId

        val product = ProductType.fromProcessId(processId!!).name

        val stateId = orderDetails.stateId

        val jurisdiction = States.fromId(stateId!!)!!.abbreviation

        val orderId =
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                null,
                null,
                null,
            ).blockSingle().orderId!!

        return getFilingData(
            processingOrderId,
            processId,
            customerId.toString(),
            product,
            jurisdiction,
            orderId,
        )
    }

    fun saveSSOrcoUpdatedFields(saveAnswerBankRequest: SaveAnswerBankRequest): SaveAnswerPayload? {
        logger.event(
            "FilingService.saveSSOrcoUpdatedFields.start",
            mapOf("processingOrderId" to saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId),
        )
        val processingOrder =
            try {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                    saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId,
                    "1",
                    "",
                    true,
                ).blockSingle().processingOrder
            } catch (ex: Exception) {
                val processingOrderId = saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId
                addWarnLog(
                    "ssOrcoUpdatedFields",
                    "Looking for processing order $processingOrderId failed",
                    "processingOrderId" to processingOrderId,
                    "message" to ex.message.toString(),
                )
                null
            }
        saveAnswerBankRequest.questionnaireFieldGroupAnswers.let {
            it.questionireId = it.questionireId ?: processingOrder?.questionnaireId
            it.isMajorRevision = it.isMajorRevision
        }
        val processId: Int? = processingOrder?.processId
        val customerId = processingOrder?.customerId

        return if (customerId != null) {
            val response =
                questionnaireAnswerService.saveSSOrcoUpdatedFields(
                    customerId.toString(),
                    saveAnswerBankRequest,
                    processId,
                )
            logger.event(
                "FilingService.saveSSOrcoUpdatedFields.end",
                mapOf(
                    "processingOrderId" to saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId,
                    "processId" to processId,
                    "customerId" to customerId,
                ),
            )
            response
        } else {
            addWarnLog(
                "ssOrcoUpdatedFields",
                "failed",
                "processingOrderId" to saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId.toString(),
                "message" to "customerId can't be null",
            )
            SaveAnswerComposite(
                SaveQuestionnaireAnswerResponse().apply {
                    saveAnswerResponse = SaveQuestionnaireAnswerDto().numberofRecordsAffected(0)
                },
            )
        }
    }

    private fun checkIsCancelled(orderId: Int): Boolean {
        val orderResponse =
            ordersApiService.getOrders(
                orderId,
                null,
                null,
                null,
                null,
                null,
            )

        return orderResponse.order?.isCancelled == true
    }

    private fun callFindDocBy(processingOrderId: Long): Iterator<Document>? {
        var result: Iterator<Document>? = null
        try {
            result =
                documentService.findDocumentsBy(
                    processingOrderId = processingOrderId,
                    customerId = null,
                    customerDocumentType = null,
                    sortOption = CreatedDate,
                    sortDirection = Descending,
                )
                    .iterator()
        } catch (e: Exception) {
            logger.error(e.stackTraceToString())
        }
        return result
    }

    private fun genAttorneyDocketNumber(processingOrderId: Int): String {
        var attorneyDocketNumber = ""
        try {
            var prefixAttorneyDocketNumber = "K"
            if (checkPresentInECP(processingOrderId, "ALTM")) {
                prefixAttorneyDocketNumber = "E"
            }
            attorneyDocketNumber = prefixAttorneyDocketNumber + processingOrderId
        } catch (e: Exception) {
            logger.error(
                "The existence of documents in the Expert Collaboration Platform for processingOrderId $processingOrderId " +
                    "could not be verified",
                e,
            )
        }
        return attorneyDocketNumber
    }

    /**
     * Check if the order is present in Experts Collaboration Platform
     */
    private fun checkPresentInECP(
        processingOrderId: Int,
        tenantName: String,
    ): Boolean {
        val minimalWorkItemDTOList: List<MinimalWorkItemDTO> =
            workItemsApi.findAllWorkItems(
                tenantName,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                listOf(processingOrderId.toString()),
                null,
                null,
                null,
                null,
                null,
            ).blockSingle().content
        return minimalWorkItemDTOList.isNotEmpty()
    }

    /**
     * it builds partial url to download document associated to payload
     */
    private fun buildRPADocument(document: Document): RPADocument {
        val docId: String = document.documentId
        val storagePlatformDocumentVersion: Optional<String> =
            Optional.ofNullable(document.versionHistory.storagePlatformDocumentVersion)
        val map = mapOf("docId" to docId)
        val uriString =
            UriComponentsBuilder.newInstance()
                .path(partialUrl) // using this UriComponentBuilder as it's needed to include Optional queryParameters
                .uriVariables(map)
                .queryParamIfPresent("storagePlatformDocumentVersion", storagePlatformDocumentVersion)
                .build().toUriString()
        logger.info(uriString)
        return RPADocument(
            document.documentName,
            document.documentType,
            uriString,
            document.documentId,
            document.documentPath,
            document.versionHistory.storagePlatformDocumentStatus,
        )
    }

    private fun addWarnLog(
        action: String,
        eventName: String,
        vararg params: Pair<String, Any>,
    ) {
        val paramMap = mutableMapOf<String, Any>("action" to action)
        params.forEach { paramMap[it.first] = it.second }
        logger.warnEvent(
            "filingService.$action.$eventName",
            paramMap,
        )
    }
}
