package com.legalzoom.fulfillment.service.service.questionnaire

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.model.answer.Company
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.SaveAnswerBankRequest
import com.legalzoom.api.order.OrdersApi
import com.legalzoom.api.order.OrdersOrderItemsApi
import com.legalzoom.fulfillment.answers.service.ModelOneFeatureToggleService
import com.legalzoom.fulfillment.answersapi.Constants
import com.legalzoom.fulfillment.answersapi.Constants.INC_Q2_FLOW_NAME
import com.legalzoom.fulfillment.answersapi.Constants.LLC_Q2_FLOW_NAME
import com.legalzoom.fulfillment.answersapi.TransformerEnabledFlow
import com.legalzoom.fulfillment.answersapi.TransformerName
import com.legalzoom.fulfillment.answersapi.contract.AnswersContract
import com.legalzoom.fulfillment.answersapi.exception.AnswerNotValidatedException
import com.legalzoom.fulfillment.answersapi.exception.NotFoundException
import com.legalzoom.fulfillment.answersapi.model.AnswerIdType
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.answersapi.model.AnswersResponse
import com.legalzoom.fulfillment.answersapi.model.States
import com.legalzoom.fulfillment.answersapi.model.dtos.v2.AnswerDto
import com.legalzoom.fulfillment.answersapi.model.dtos.v2.AnswerReferenceDto
import com.legalzoom.fulfillment.answersapi.model.toAnswerDto
import com.legalzoom.fulfillment.common.exception.RetryableException
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.Constants.RETRY_ANSWERS_API
import com.legalzoom.fulfillment.service.service.M1MapperHelperService
import com.legalzoom.fulfillment.service.service.M1MappersLookupService
import com.legalzoom.fulfillment.service.service.businessInfo.BusinessInfoService
import com.legalzoom.fulfillment.service.service.helper.ProcessingOrderHelper
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.BoirDataMapperService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataComposite
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataModelOne
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataPayload
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.GetAnswersPayload
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerPayload
import io.github.resilience4j.retry.annotation.Retry
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.util.UUID

@Service
class QuestionnaireAnswerService(
    private val objectMapper: ObjectMapper,
    @Qualifier("postgres")
    private val answersService: AnswersContract.ServiceV2,
    private val answerApi: AnswerApi,
    private val processingOrderHelper: ProcessingOrderHelper,
    private val businessInfoService: BusinessInfoService,
    private val m1MappersLookupService: M1MappersLookupService,
    private val featureFlagService: ModelOneFeatureToggleService,
    private val mapperHelper: M1MapperHelperService,
    private val boirDataMapperService: BoirDataMapperService,
    private val answerStoreService: AnswersContract.AnswersStoreService,
    private val ordersApi: OrdersApi,
    private val ordersOrderItemsApi: OrdersOrderItemsApi,
    private val customerApi: CustomerApi,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    fun getFilingDataForRPA(
        processingOrderId: Int,
        processId: Int,
        customerId: String,
        jurisdiction: String,
    ): FilingDataPayload {
        // RPA bots use this method to obtain answers for filing (v1/filings/answers-data endpoint)
        val forceAnswersBank = featureFlagService.forceAnswerBankOnAnswersData(null, jurisdiction, processId)

        logger.info(
            "Retrieving filingData for processingOrderId $processingOrderId. forceAnswersBank: $forceAnswersBank",
        )

        val filingData: FilingDataPayload =
            if (forceAnswersBank) {
                try {
                    FilingDataComposite(
                        processId,
                        answerApi.answersProcessingOrdersProcessingOrderIdProcessesProcessIdFilingDataGet(
                            processingOrderId,
                            processId,
                            Constants.QA_HEADER_LZ_API_VERSION,
                            customerId,
                            Constants.QA_HEADER_LZ_AUTHORIZE,
                        ).blockSingle(),
                    )
                } catch (wcre: WebClientResponseException) {
                    if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
                }
            } else {
                getFilingData(processingOrderId, processId, customerId)
            }

        return filingData
    }

    @Retry(name = RETRY_ANSWERS_API)
    fun getFilingData(
        processingOrderId: Int?,
        processId: Int?,
        customerId: String?,
        transformerName: String? = null,
    ): FilingDataPayload {
        var jurisdiction: String?

        val answersModelOne: FilingDataPayload? =
            try {
                val answers =
                    answersService.searchAnswer(
                        listOf(processingOrderId.toString()),
                        listOf(AnswerIdType.ProcessingOrderId),
                    )

                val answersQ2 =
                    answers.filter { answer ->
                        val flowReference = answer.references.first { reference -> (reference.idType == AnswerIdType.Flow) }
                        TransformerEnabledFlow.isEnabledByFlowAndProcessId(flowReference.referenceId, processId)
                    }

                if (answersQ2.isNotEmpty()) {
                    val recentAnswers = answersQ2.first()

                    val payload = objectMapper.readTree(recentAnswers.payload)
                    jurisdiction = States.fromName(payload.get("entityState")?.asText())?.abbreviation
                        ?: States.fromName(payload.get("state")?.asText())?.abbreviation

                    if (featureFlagService.isTransformersEnabled(customerId, jurisdiction, processId)) {
                        if (recentAnswers.validated == false || recentAnswers.validated == null) {
                            val message =
                                "Answer with processingOrderId $processingOrderId " +
                                    "and flow \"${
                                        recentAnswers.references.firstOrNull{ it.idType == AnswerIdType.Flow }?.referenceId
                                    }\" is not validated. Transformation can't be done."
                            throw AnswerNotValidatedException(message)
                        }

                        val mappedPayload: JsonNode =
                            transformFilingData(
                                recentAnswers.id!!,
                                transformerName ?: TransformerName.fromProcessId(processId)?.transformerName,
                            )

                        // updating answer payload with the partial updates
                        val businessInfoDto = businessInfoService.getBusinessInfo(UUID.fromString(recentAnswers.id))
                        val updatesMapper = m1MappersLookupService.getMapperForPartialUpdates(processId)

                        updatesMapper?.let {
                            businessInfoDto?.let {
                                val mappedBusinessInfo =
                                    updatesMapper.mapBusinessInfoForFilingData(objectMapper.readTree(businessInfoDto.payload))
                                if (!mappedBusinessInfo.get("company").isEmpty) {
                                    mapperHelper.addBusinessInfoFields(
                                        mappedPayload.get("company"),
                                        mappedBusinessInfo.get("company"),
                                    )
                                }
                            }
                        }

                        if (!mappedPayload.isEmpty && mappedPayload.isObject) {
                            FilingDataComposite(
                                FilingDataModelOne.Builder()
                                    .ordersApi(ordersApi)
                                    .ordersOrderItemsApi(ordersOrderItemsApi)
                                    .customerApi(customerApi)
                                    .company(objectMapper.convertValue<Company>(mappedPayload.get("company")))
                                    .schemaName(
                                        objectMapper.readTree(recentAnswers.payload).get("metadata")?.get("flow")
                                            ?.asText()
                                            ?: "",
                                    )
                                    .build(processingOrderId!!, customerId),
                            )
                        } else {
                            null
                        }
                    } else {
                        null
                    }
                } else {
                    null
                }
            } catch (ex: Exception) {
                addWarnLog(
                    "filingData.ModelOne",
                    "failed",
                    mutableMapOf(
                        "processingOrderId" to processingOrderId.toString(),
                        "customerId" to customerId.toString(),
                        "processId" to processId.toString(),
                        "message" to ex.message.toString(),
                    ),
                )
                null
            }

        val answersBank: FilingDataPayload =
            try {
                FilingDataComposite(
                    processId!!,
                    answerApi.answersProcessingOrdersProcessingOrderIdProcessesProcessIdFilingDataGet(
                        processingOrderId!!,
                        processId,
                        Constants.QA_HEADER_LZ_API_VERSION,
                        customerId,
                        Constants.QA_HEADER_LZ_AUTHORIZE,
                    ).blockSingle(),
                )
            } catch (wcre: WebClientResponseException) {
                if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
            }

        jurisdiction = answersModelOne?.jurisdiction ?: answersBank.jurisdiction
        val modelOneEnable = featureFlagService.isModelOneEnabled(null, jurisdiction, processId)

        val filingData =
            if (modelOneEnable) {
                answersModelOne ?: answersBank
            } else {
                answersBank
            }

        logger.info(
            "Retrieving filingData for processingOrderId $processingOrderId. Source: ${filingData.answerSource}",
        )

        return filingData
    }

    @Retry(name = RETRY_ANSWERS_API)
    fun getFilingDataForRPAByWorkOrder(
        processingOrderId: Int?,
        orderId: Int?,
        customerId: String?,
        workOrderId: String,
        jsonDocumentArray: JsonNode?,
    ): JsonNode? {
        var answers = mutableListOf<AnswerDto>()
        // TODO for now we are relying only on v1 answers due to the impossibility to enable v2
        // answersService.searchAnswer(listOf(workOrderId), listOf(AnswerIdType.WorkOrder))
//        if (answers.isEmpty()) {
        val v1Answers =
            answerStoreService.getAnswers(
                AnswerIdType.WorkOrder,
                workOrderId,
                parentIdType = null,
                parentId = null,
                version = null,
                onlyLatest = true,
            )
        var v1Map = mutableMapOf<String, AnswersResponse>()
        if (!v1Answers.isNullOrEmpty()) {
            for (v1Answer in v1Answers) {
                var key = v1Answer.metaData?.key
                if (key != null) {
                    if (key.endsWith("null")) {
                        v1Map[key] = v1Answer
                    } else {
                        var modifiedKey =
                            key.replace("_boir_boir", "_boir_null").replace("_idUpload_boir", "_idUpload_null")
                        if (!v1Map.containsKey(modifiedKey)) {
                            v1Map[modifiedKey] = v1Answer
                        }
                    }
                }
            }
        }
        answers = v1Map.values.map { it.toAnswerDto(getReferences(it.metaData?.key)) }.toMutableList()

        return if (answers.isNotEmpty() && processingOrderId != null) {
            return boirDataMapperService.mapBoirData(
                processingOrderId,
                orderId,
                customerId,
                workOrderId,
                answers,
                jsonDocumentArray,
            )
        } else {
            null
        }
    }

    private fun getReferences(key: String?): List<AnswerReferenceDto> {
        val split = key?.split("_")
        val id: String?
        val idType: AnswerIdType?
        var parentId: String? = null
        var parentIdType: AnswerIdType? = null
        val flow: String
        val ids = mutableListOf<String>()
        val idTypes = mutableListOf<AnswerIdType>()
        if (split?.count() == 4) {
            id = split[1]
            idType = AnswerIdType.fromName(split[0])!!
            flow = split[2]
        } else if (split?.count() == 6) {
            id = split[3]
            idType = AnswerIdType.fromName(split[2])!!
            parentId = split[1]
            parentIdType = AnswerIdType.fromName(split[0])!!
            flow = split[4]
        } else {
            // Key does not contain neither 4 nor 6 parts, we can't parse it
            throw Exception("Cannot correctly parse key: $key.")
        }

        ids.add(id)
        idTypes.add(idType)
        ids.add(flow)
        idTypes.add(AnswerIdType.Flow)
        if (parentId != null && parentIdType != null) {
            ids.add(parentId)
            idTypes.add(parentIdType)
        }
        val response = mutableListOf<AnswerReferenceDto>()
        for (i in 0 until ids.size) {
            response.add(AnswerReferenceDto(idTypes[i], ids[i]))
        }
        return response
    }

    @Retry(name = RETRY_ANSWERS_API)
    fun getAnswersByUserOrderId(
        processingOrderId: Int,
        customerId: String?,
        answerSource: AnswerSource = AnswerSource.FieldAnswer,
    ): GetAnswersPayload {
        val processId: Int = processingOrderHelper.obtainProcessId(processingOrderId, customerId)
        val jurisdiction: String?

        val answersModelOne: GetAnswersPayload? =
            try {
                val answers =
                    answersService.searchAnswer(
                        listOf(processingOrderId.toString(), INC_Q2_FLOW_NAME),
                        listOf(AnswerIdType.ProcessingOrderId, AnswerIdType.Flow),
                    )

                val mapper = m1MappersLookupService.getMapperForAnswersByUserOrderId(processId)

                if (answers.isNotEmpty() && processingOrderId != null && mapper != null) {
                    val recentAnswers = answers.first()
                    var answerPayload: JsonNode = objectMapper.readTree(recentAnswers.payload)

                    val businessInfoDto = businessInfoService.getBusinessInfo(UUID.fromString(recentAnswers.id))
                    businessInfoDto?.let {
                        answerPayload =
                            mapperHelper.addBusinessInfoFields(
                                objectMapper.readTree(recentAnswers.payload),
                                objectMapper.readTree(businessInfoDto.payload),
                            )
                    }

                    GetAnswersComposite(
                        mapper.mapAnswer(processingOrderId, objectMapper.writeValueAsString(answerPayload)),
                    )
                } else {
                    null
                }
            } catch (ex: Exception) {
                logger.error(ex.message, ex)
                null
            }

        val answersBank =
            try {
                GetAnswersComposite(
                    processId,
                    answerApi.answersUserOrderIdSourceGet(
                        processingOrderId!!,
                        if (answerSource == AnswerSource.FieldAnswer) {
                            com.legalzoom.api.model.answer.AnswerSource.NUMBER_1
                        } else {
                            com.legalzoom.api.model.answer.AnswerSource.NUMBER_0
                        },
                        null,
                        null,
                        null,
                        "1.0",
                        customerId,
                        null,
                    ).blockSingle(),
                )
            } catch (wcre: WebClientResponseException) {
                if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
            }

        jurisdiction = answersModelOne?.jurisdiction ?: answersBank.jurisdiction
        return if (featureFlagService.isModelOneEnabled(null, jurisdiction, processId)) {
            answersModelOne ?: answersBank
        } else {
            answersBank
        }
    }

    @Retry(name = RETRY_ANSWERS_API)
    fun getAnswerVersionForUserOrderId(
        processingOrderId: Int,
        customerId: String?,
        answerSource: AnswerSource = AnswerSource.FieldAnswer,
    ): GetQuestionnaireAnswerResponse {
        val processId: Int = processingOrderHelper.obtainProcessId(processingOrderId, customerId)
        val jurisdiction: String?

        val answersBank =
            try {
                answerApi.answersUserOrderIdSourceGet(
                    processingOrderId!!,
                    if (answerSource == AnswerSource.FieldAnswer) {
                        com.legalzoom.api.model.answer.AnswerSource.NUMBER_1
                    } else {
                        com.legalzoom.api.model.answer.AnswerSource.NUMBER_0
                    },
                    null,
                    null,
                    null,
                    "1.0",
                    customerId,
                    null,
                ).blockSingle()
            } catch (wcre: WebClientResponseException) {
                if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
            }

        return answersBank
    }

    @Retry(name = RETRY_ANSWERS_API)
    fun putPartialUpdate(
        customerId: String,
        saveAnswerBankRequest: SaveAnswerBankRequest,
    ): SaveAnswerPayload? {
        try {
            // save updates to business_table if answers exist in model one
            val processingOrderId = saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId
            val processId: Int = processingOrderHelper.obtainProcessId(processingOrderId, customerId)

            val q2Flows = setOf(INC_Q2_FLOW_NAME, LLC_Q2_FLOW_NAME)

            val answers = answersService.searchAnswer(listOf(processingOrderId.toString()), listOf(AnswerIdType.ProcessingOrderId))
            val q2Answers =
                answers.filter { answer ->
                    q2Flows.contains(answer.references.firstOrNull { reference -> reference.idType == AnswerIdType.Flow }?.referenceId)
                }

            val mapper = m1MappersLookupService.getMapperForPartialUpdates(processId)
            if (q2Answers.isNotEmpty() && mapper != null) {
                val recentAnswers = q2Answers.first()
                val businessInfoNode = objectMapper.createObjectNode()
                saveAnswerBankRequest.questionnaireFieldGroupAnswers.fieldAnswers.forEach {
                    mapper.mapBusinessInfo(businessInfoNode, it)
                }
                businessInfoService.saveBusinessInfo(
                    UUID.fromString(recentAnswers.id),
                    objectMapper.writeValueAsString(businessInfoNode),
                )
            } else {
                logger.warn("Couldn't find answers with this processing order id: $processingOrderId")
            }
        } catch (wcre: WebClientResponseException) {
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        } catch (ex: Exception) {
            logger.error("Error saving partial update on BusinessInfo table: ${ex.message}", ex)
        }

        val answerBankSaved: SaveAnswerPayload =
            try {
                SaveAnswerComposite(
                    answerApi.answersAnswerBankPartialUpdatePut(
                        "1.0",
                        customerId,
                        false,
                        saveAnswerBankRequest,
                    ).blockSingle(),
                )
            } catch (wcre: WebClientResponseException) {
                if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
            }
        return answerBankSaved
    }

    @Retry(name = RETRY_ANSWERS_API)
    fun saveSSOrcoUpdatedFields(
        customerId: String,
        saveAnswerBankRequest: SaveAnswerBankRequest,
        processId: Int? = null,
    ): SaveAnswerPayload? {
        val processingOrderId = saveAnswerBankRequest.questionnaireFieldGroupAnswers.processingOrderId

        try {
            addLog(
                "saveSSOrcoUpdatedFields",
                "updating.ModelOne.start",
                mutableMapOf(
                    "processingOrderId" to processingOrderId,
                    "customerId" to customerId,
                ),
            )

            val answers = answersService.searchAnswer(listOf(processingOrderId.toString()), listOf(AnswerIdType.ProcessingOrderId))
            val mapper =
                m1MappersLookupService.getMapperForPartialUpdates(
                    processId ?: processingOrderHelper.obtainProcessId(processingOrderId, customerId),
                )

            val q2FlowsInModelOne = setOf(INC_Q2_FLOW_NAME, LLC_Q2_FLOW_NAME)

            if (answers.isEmpty()) throw NotFoundException("No answers were found for processingOrderId: $processingOrderId")

            if (mapper != null) {
                val updatedFieldsNode = objectMapper.createObjectNode()
                saveAnswerBankRequest.questionnaireFieldGroupAnswers.fieldAnswers.forEach {
                    mapper.mapBusinessInfo(updatedFieldsNode, it)
                }

                answers.firstOrNull { answer ->
                    q2FlowsInModelOne.contains(
                        answer.references.firstOrNull {
                                reference ->
                            reference.idType == AnswerIdType.Flow
                        }?.referenceId,
                    )
                }?.let {
                    val newPayload = mapperHelper.addBusinessInfoFields(objectMapper.readTree(it.payload), updatedFieldsNode)
                    answersService.saveAnswer(
                        newPayload.toString(),
                        it.references.map { reference -> reference.referenceId },
                        it.references.map { reference -> reference.idType },
                        false,
                    )

                    addLog(
                        "saveSSOrcoUpdatedFields",
                        "updating.ModelOne.succeeded",
                        mutableMapOf(
                            "processingOrderId" to processingOrderId,
                            "customerId" to customerId,
                        ),
                    )
                } ?: let {
                    val flows = answers.map { answer -> answer.references.filter { reference -> reference.idType == AnswerIdType.Flow } }
                    addLog(
                        "saveSSOrcoUpdatedFields",
                        "updating.ModelOne.end",
                        mutableMapOf(
                            "processingOrderId" to processingOrderId,
                            "customerId" to customerId,
                            "message" to "Flow/s $flows not enabled to be updated in model one",
                        ),
                    )
                }
            }
        } catch (ex: NotFoundException) {
            addWarnLog(
                "saveSSOrcoUpdatedFields",
                "updating.ModelOne.failed",
                mutableMapOf(
                    "processingOrderId" to processingOrderId,
                    "customerId" to customerId,
                    "message" to ex.message.toString(),
                ),
            )
        }

        val answerBankSaved: SaveAnswerPayload =
            try {
                addLog(
                    "saveSSOrcoUpdatedFields",
                    "updating.AnswerBank.start",
                    mutableMapOf(
                        "processingOrderId" to processingOrderId,
                        "customerId" to customerId,
                    ),
                )

                SaveAnswerComposite(
                    answerApi.answersAnswerBankPartialUpdatePut(
                        "1.0",
                        customerId,
                        false,
                        saveAnswerBankRequest,
                    ).blockSingle(),
                )
            } catch (wcre: WebClientResponseException) {
                if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
            }
        addLog(
            "saveSSOrcoUpdatedFields",
            "updating.AnswerBank.succeeded",
            mutableMapOf(
                "processingOrderId" to processingOrderId,
                "customerId" to customerId,
            ),
        )
        return answerBankSaved
    }

    private fun transformFilingData(
        answerId: String,
        transformerName: String?,
    ): JsonNode {
        var answersMapped: String = ""

        if (!transformerName.isNullOrEmpty()) {
            answersMapped = answersService.getAnswerTransformed(UUID.fromString(answerId), transformerName = transformerName)
        }
        return objectMapper.readTree(answersMapped).first()
    }

    private fun addLog(
        action: String,
        eventName: String,
        params: MutableMap<String, Any>,
    ) {
        params["action"] = action
        logger.event(
            "questionnaireAnswerService.$action.$eventName",
            params,
        )
    }

    private fun addWarnLog(
        action: String,
        eventName: String,
        params: MutableMap<String, Any>,
    ) {
        params["action"] = action
        logger.warnEvent(
            "questionnaireAnswerService.$action.$eventName",
            params,
        )
    }
}
