package com.legalzoom.fulfillment.service.mappers

import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.fulfillment.service.service.questionnaire.AnswerFieldType

fun GetQuestionnaireAnswerResponse.getFieldAnswerValueOrNull(fieldName: String): String? {
    return this.questionnaireFieldGroupAnswers?.fieldAnswers?.firstOrNull {
        it.fieldName == fieldName
    }?.fieldValue
}

fun GetQuestionnaireAnswerResponse.getFieldAnswerValueOrNull(answerFieldType: AnswerFieldType): String? {
    return this.getFieldAnswerValueOrNull(answerFieldType.fieldName)
}

fun GetQuestionnaireAnswerResponse.getFieldAnswerValue(answerFieldType: AnswerFieldType): String {
    return checkNotNull(this.getFieldAnswerValueOrNull(answerFieldType.fieldName)) {
        "Unable to get value for field type $answerFieldType and field name ${answerFieldType.fieldName}"
    }
}

fun GetQuestionnaireAnswerResponse.getGroupAnswerValue(fieldName: String): String? {
    return this.questionnaireFieldGroupAnswers?.groupAnswers?.firstOrNull {
        it.fieldName == fieldName
    }?.fieldValue
}
