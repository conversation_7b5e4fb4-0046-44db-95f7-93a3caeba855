package com.legalzoom.fulfillment.service.enumeration

import com.legalzoom.fulfillment.domain.enumeration.DocumentEventType
import com.legalzoom.fulfillment.domain.enumeration.DocumentEventType.DOCGEN_UPLOAD
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.AnnualReports_FinalLetter
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_ApplicationForEIN
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_ArticlesPreFiled
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_BusinessRegistrationApplication
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_CertificateOfPublication
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_CompanyAgreement
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_CompanyAgreementWord
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_CoverLetter
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_FinancialAccountAuthorizationLetter
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_FinancialAccountAuthorizationLetterWord
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_NewspaperListing
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_OperatingAgreement
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_OperatingAgreementWord
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_RegisteredAgentAcceptance
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_SSCNotice
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_SoSWelcomeLetter
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_WelcomePacket
import com.legalzoom.fulfillment.service.enumeration.ProductType.AnnualReports
import com.legalzoom.fulfillment.service.enumeration.ProductType.INC
import com.legalzoom.fulfillment.service.enumeration.ProductType.InitialReports
import com.legalzoom.fulfillment.service.enumeration.ProductType.LLC
import com.legalzoom.fulfillment.service.enumeration.ProductType.NP

/**
 * File that captures Regexes used in the Filenet system for identifying the document types of documents uploaded via fulfillment
 * This is sourced from this Filenet table
 * select * from FN_SHARED.dbo.FileImportPattern
 * @property pattern - regex pattern
 * @property customerDocumentType
 * @property patternID - id of the (TemplateID) regex pattern in the Filenet DB table FN_SHARED.dbo.FileImportPattern
 * @property documentEventType - which event type where we are expecting this Regex to be used to identify a document type
 */
enum class FileImportPattern(
    val pattern: Regex,
    val customerDocumentType: CustomerDocumentType,
    val patternID: Int,
    val documentEventType: DocumentEventType,
    val processId: Int = LLC.processId,
) {
    ARFinalLetter(
        regex(""".*Final_Letter.PDF$"""),
        AnnualReports_FinalLetter,
        221,
        DOCGEN_UPLOAD,
        AnnualReports.processId,
    ),

    FinancialAccountAuthorizationLetter(
        regex(""".*FINANCIAL_ACCOUNT_AUTHORIZATION_LETTER.PDF$"""),
        LLC_FinancialAccountAuthorizationLetter,
        1148,
        DOCGEN_UPLOAD,
    ),
    FinancialAccountAuthorizationLetterWord(
        regex(""".*FINANCIAL_ACCOUNT_AUTHORIZATION_LETTER.DOC$"""),
        LLC_FinancialAccountAuthorizationLetterWord,
        1149,
        DOCGEN_UPLOAD,
    ),
    SoSWelcomeLetter(
        regex(""".*SOS_WELCOME_LETTER.PDF$"""),
        LLC_SoSWelcomeLetter,
        1192,
        DOCGEN_UPLOAD,
    ),
    WelcomePacket(
        regex(""".*WELCOME_PACKET.PDF$"""),
        LLC_WelcomePacket,
        1143,
        DOCGEN_UPLOAD,
    ),
    ApplicationForEIN(
        regex(""".*APPLICATION_FOR_EIN.PDF$"""),
        LLC_ApplicationForEIN,
        1150,
        DOCGEN_UPLOAD,
    ),
    OperatingAgreement(
        regex(""".*OPERATING_AGREEMENT.PDF$"""),
        LLC_OperatingAgreement,
        1151,
        DOCGEN_UPLOAD,
    ),
    OperatingAgreementWord(
        regex(""".*OPERATING_AGREEMENT.DOC$"""),
        LLC_OperatingAgreementWord,
        1147,
        DOCGEN_UPLOAD,
    ),
    StatementOfInformationForm(
        regex(""".*STATEMENT_OF_INFORMATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            LLC.productName,
            "Statement of Information Form",
        ),
        1584,
        DOCGEN_UPLOAD,
    ),
    CompanyAgreement(
        regex(""".*COMPANY_AGREEMENT.PDF$"""),
        LLC_CompanyAgreement,
        1998,
        DOCGEN_UPLOAD,
    ),
    CompanyAgreementWord(
        regex(""".*COMPANY_AGREEMENT.DOC$"""),
        LLC_CompanyAgreementWord,
        1999,
        DOCGEN_UPLOAD,
    ),
    RegisteredAgentAcceptance(
        regex(""".*REGISTERED_AGENT_ACCEPTANCE.PDF$"""),
        LLC_RegisteredAgentAcceptance,
        1202,
        DOCGEN_UPLOAD,
    ),
    RAAcceptance(
        regex(""".*RA_ACCEPTANCE.PDF$"""),
        LLC_RegisteredAgentAcceptance,
        1200,
        DOCGEN_UPLOAD,
    ),
    PreFilingArticles(
        regex(""".*PREFILING_ARTICLES.PDF$"""),
        LLC_ArticlesPreFiled,
        1194,
        DOCGEN_UPLOAD,
    ),
    CertificateOfPublication(
        regex(""".*CERTIFICATE_OF_PUBLICATION.PDF$"""),
        LLC_CertificateOfPublication,
        1201,
        DOCGEN_UPLOAD,
    ),
    CoverLetter(
        regex(""".*Cover_Letter.PDF$"""),
        LLC_CoverLetter,
        1438,
        DOCGEN_UPLOAD,
    ),
    NewspaperListing(
        regex(""".*NEWSPAPER_LISTING.PDF$"""),
        LLC_NewspaperListing,
        1203,
        DOCGEN_UPLOAD,
    ),
    BusinessRegistrationApplication(
        regex(""".*BUSINESS_REGISTRATION_APPLICATION.PDF$"""),
        LLC_BusinessRegistrationApplication,
        1158,
        DOCGEN_UPLOAD,
    ),
    SSCNotice(
        regex(""".*SCC_Notice_to_Virginia_LLCs.pdf$"""),
        LLC_SSCNotice,
        1159,
        DOCGEN_UPLOAD,
    ),
    RegisterOfDeedsAttachment(
        regex(""".*Register_of_Deeds_Attachment.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            LLC.productName,
            "Register of Deeds Attachment",
        ),
        0,
        DOCGEN_UPLOAD,
        LLC.processId,
    ),
    AnnualReportsPreFilingArticles(
        regex(""".*PREFILING_ARTICLES.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(AnnualReports.productName, "Articles (pre-filed)"),
        0,
        DOCGEN_UPLOAD,
        AnnualReports.processId,
    ),
    IncorporationPreFilingArticles(
        regex(""".*PREFILING_ARTICLES.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "ArticlesPreFiled",
        ),
        92,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCertifiedCopyCoverLetter(
        regex(""".*COVER.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Certified Copy Cover Letter",
        ),
        0,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationWelcomePacket(
        regex(""".*WELCOME_PACKET.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Welcome Packet",
        ),
        1464,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationWelcomePacketWord(
        regex(""".*WELCOME_PACKET.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Welcome Packet - Word",
        ),
        1465,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationArticlesOfIncorporation(
        regex(""".*ARTICLES_OF_INCORPORATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Articles of Incorporation",
        ),
        1466,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCertificateOfIncorporation(
        regex(""".*CERTIFICATE_OF_INCORPORATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Certificate of Incorporation",
        ),
        1467,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationFillingAcknowledgement(
        regex(""".*FILING_ACKNOWLEDGEMENT.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Filing Acknowledgement",
        ),
        1468,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationApplicationForEIN(
        regex(""".*APPLICATION_FOR_EIN.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Application For EIN",
        ),
        1469,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationEINConfirmationLetter(
        regex(""".*EIN_Confirmation_Letter.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(INC.productName, "EIN Confirmation Letter"),
        1470,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationActionByWrittenConsent(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_INCORPORATOR.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(INC.productName, "Action By Written Consent of the Sole Incorporator"),
        1471,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationActionByWrittenConsentWord(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_INCORPORATOR.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Action By Written Consent of the Sole Incorporator - Word",
        ),
        1472,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationActionByUnanimousWrittenConsentBoardOfDirectors(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_DIRECTORS.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Action By Written Consent Directors",
        ),
        1473,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationActionByUnanimousWrittenConsentBoardOfDirectorsWord(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_DIRECTORS.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Action By Written Consent Directors - Word",
        ),
        1474,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationByLaws(
        regex(""".*BYLAWS.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Bylaws and Resolutions - PDF",
        ),
        1475,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationByLawsWord(
        regex(""".*BYLAWS.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(INC.productName, "Bylaws and Resolutions - Word"),
        1476,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCertificateOfSecretary(
        regex(""".*CERTIFICATE_OF_SECRETARY.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Certificate of Secretary",
        ),
        1477,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCertificateOfSecretaryWord(
        regex(""".*CERTIFICATE_OF_SECRETARY.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Certificate of Secretary - Word",
        ),
        1478,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCertificationLetter(
        regex(""".*CERTIFICATION_LETTER.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Certification Letter",
        ),
        1479,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationElectionSmallBusinessCorporation(
        regex(""".*ELECTION_SMALL_BUSINESS_CORPORATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Election Small Business Corporation",
        ),
        1480,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationSCORP2553(
        regex(""".*Election_Small_Business_Corporation_New_York.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Election Small Business Corporation New York",
        ),
        443,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCertificateOfPublication(
        regex(""".*CERTIFICATE_OF_PUBLICATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Certificate of Publication",
        ),
        1481,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationRegisteredAgentAcceptance(
        regex(""".*REGISTERED_AGENT_ACCEPTANCE.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "RegisteredAgentAcceptance",
        ),
        1482,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationStatementOfInformationForm(
        regex(""".*STATEMENT_OF_INFORMATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Statement of Information Form",
        ),
        1583,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationCaliforniaFranchiseTaxForm(
        regex(""".*CALIFORNIA_FRANCHISE_TAX_FORM.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "FTB 3522 Franchise Tax Form",
        ),
        1484,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationSOSWelcomeLetter(
        regex(""".*SOS_WELCOME_LETTER.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "SOS Welcome Letter",
        ),
        1485,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationBusinessRegistrationApplication(
        regex(""".*BUSINESS_REGISTRATION_APPLICATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Business Registration Application",
        ),
        1490,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationArticlesAttachment(
        regex(""".*ARTICLES_ATTACHMENT.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Articles Attachment",
        ),
        1492,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationPreferredStockAttachment(
        regex(""".*Preferred_Stock_Attachment.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Preferred Stock Attachment",
        ),
        1585,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    IncorporationRegisterOfDeedsAttachment(
        regex(""".*Register_of_Deeds_Attachment.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            INC.productName,
            "Register of Deeds Attachment",
        ),
        0,
        DOCGEN_UPLOAD,
        INC.processId,
    ),
    InitialReportsFinalLetter(
        regex(""".*Final_Letter.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            InitialReports.productName,
            "Final Letter",
        ),
        1234,
        DOCGEN_UPLOAD,
        InitialReports.processId,
    ),
    StandAloneOperatingAgreement(
        regex(""".*OPERATING_AGREEMENT.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.OperatingAgreement.productName,
            "Operating Agreement",
        ),
        324,
        DOCGEN_UPLOAD,
        ProductType.OperatingAgreement.processId,
    ),
    StandAloneOperatingAgreementWord(
        regex(""".*Operating_Agreement.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.OperatingAgreement.productName,
            "Operating Agreement - Word",
        ),
        1358,
        DOCGEN_UPLOAD,
        ProductType.OperatingAgreement.processId,
    ),
    StandAloneCompanyAgreement(
        regex(""".*COMPANY_AGREEMENT.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.OperatingAgreement.productName,
            "Company Agreement",
        ),
        1359,
        DOCGEN_UPLOAD,
        ProductType.OperatingAgreement.processId,
    ),
    StandAloneCompanyAgreementWord(
        regex(""".*COMPANY_AGREEMENT.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.OperatingAgreement.productName,
            "Company Agreement - Word",
        ),
        1360,
        DOCGEN_UPLOAD,
        ProductType.OperatingAgreement.processId,
    ),
    StandAloneOperatingAgreementFinalLetter(
        regex(""".*Final_Letter.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.OperatingAgreement.productName,
            "FinalLetter",
        ),
        325,
        DOCGEN_UPLOAD,
        ProductType.OperatingAgreement.processId,
    ),
    StandAloneByLawsAndResolutions(
        regex(""".*BYLAWS.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Bylaws",
        ),
        563,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsWord(
        regex(""".*BYLAWS.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Bylaws and Resolutions - Word",
        ),
        565,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsFinalLetter(
        regex(""".*Final_Letter.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "FinalLetter",
        ),
        325,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsActionByWrittenConsent(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_INCORPORATOR.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Action By Written Consent of the Sole Incorporator",
        ),
        1601,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsActionByWrittenConsentWord(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_INCORPORATOR.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Action By Written Consent of the Sole Incorporator - Word",
        ),
        1600,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsActionByUnanimousWrittenConsentBoardOfDirectors(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_DIRECTORS.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Action By Written Consent Directors",
        ),
        1599,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsActionByUnanimousWrittenConsentBoardOfDirectorsWord(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_DIRECTORS.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Action By Written Consent Directors - Word",
        ),
        1598,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsCertificateOfSecretary(
        regex(""".*CERTIFICATE_OF_SECRETARY.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Certificate of Secretary",
        ),
        1597,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    StandAloneByLawsAndResolutionsCertificateOfSecretaryWord(
        regex(""".*CERTIFICATE_OF_SECRETARY.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.ByLawsAndResolutions.productName,
            "Certificate of Secretary - Word",
        ),
        1596,
        DOCGEN_UPLOAD,
        ProductType.ByLawsAndResolutions.processId,
    ),
    DeedDoc(
        regex(""".*DeedDoc.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Deed",
        ),
        657,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTVaultInfo(
        regex(""".*Vaulting_Information.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Vaulting Information",
        ),
        1430,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTStatementOfIntermentG1Word(
        regex(""".*Statement_of_Interment_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Statement of Interment Doc - G1",
        ),
        1429,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTStatementOfIntermentG1Pdf(
        regex(""".*Statement_of_Interment_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Statement of Interment - G1",
        ),
        1428,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTSelfProvingAffidavitG1Word(
        regex(""".*Self_Proving_Affidavit_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Self Proving Affidavit Doc - G1",
        ),
        1427,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTSelfProvingAffidavitG1Pdf(
        regex(""".*Self_Proving_Affidavit_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Self Proving Affidavit - G1",
        ),
        1426,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTLastWillG1Word(
        regex(""".*Last_Will_and_Testament_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Last Will and Testament Doc - G1",
        ),
        1425,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTLastWillG1Pdf(
        regex(""".*Last_Will_and_Testament_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Last Will and Testament - G1",
        ),
        1424,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTTransferLetterWord(
        regex(""".*Transfer_Letters.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Transfer Letters Doc",
        ),
        1423,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTTransferLetterPdf(
        regex(""".*Transfer_Letters.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Transfer Letters",
        ),
        1422,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTPetCareInstructions(
        regex(""".*Pet_Care_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Pet Care Instructions",
        ),
        1442,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTGuideToFundingYourTrust(
        regex(""".*Guide_to_Funding_Your_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Guide to Funding Your Trust",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTBillOfTransferWord(
        regex(""".*Bill_of_Transfer.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Bill of Transfer Doc",
        ),
        1421,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTBillOfTransferPdf(
        regex(""".*Bill_of_Transfer.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Bill of Transfer",
        ),
        1420,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCertificationOfTrustWord(
        regex(""".*Certification_of_Trust.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Certification of Trust Doc",
        ),
        1417,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCertificationOfTrustPdf(
        regex(""".*Certification_of_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Certification of Trust",
        ),
        1416,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCertificateOfTrustAgreementWord(
        regex(""".*Certificate_of_Trust_Agreement.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Certificate of Trust Agreement Doc",
        ),
        1419,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCertificateOfTrustAgreementPdf(
        regex(""".*Certificate_of_Trust_Agreement.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Certificate of Trust Agreement",
        ),
        1418,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCertificateOfTrustWord(
        regex(""".*Certificate_of_Trust.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Certificate of Trust Doc",
        ),
        1415,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCertificateOfTrustPdf(
        regex(""".*Certificate_of_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Certificate of Trust",
        ),
        1414,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTDeclarationOfTrustWord(
        regex(""".*Declaration_of_Trust.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Declaration of Trust Doc",
        ),
        1413,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTDeclarationOfTrustPdf(
        regex(""".*Declaration_of_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Declaration of Trust",
        ),
        1412,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTHelpGuide(
        regex(""".*Help_Guide_to_LT.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Help Guide to LT",
        ),
        1411,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTSigningInstructions(
        regex(""".*Signing_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Signing Instructions",
        ),
        1410,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTCoverLetter(
        regex(""".*Cover_Letter.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Cover Letter",
        ),
        1409,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTLastWillG2Word(
        regex(""".*_Last_Will_and_Testament_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Last Will and Testament Doc - G2",
        ),
        1432,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTLastWillG2Pdf(
        regex(""".*_Last_Will_and_Testament_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Last Will and Testament - G2",
        ),
        1431,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTSelfProvingAffidavitG2Word(
        regex(""".*Self_Proving_Affidavit_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Self Proving Affidavit Doc - G2",
        ),
        1434,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTSelfProvingAffidavitG2Pdf(
        regex(""".*Self_Proving_Affidavit_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Self Proving Affidavit - G2",
        ),
        1433,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTStatementOfIntermentG2Word(
        regex(""".*Statement_of_Interment_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Statement of Interment Doc - G2",
        ),
        1436,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LTStatementOfIntermentG2Pdf(
        regex(""".*Statement_of_Interment_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingTrust.productName,
            "Statement of Interment - G2",
        ),
        1435,
        DOCGEN_UPLOAD,
        ProductType.LivingTrust.processId,
    ),
    LWTCoverLetter(
        regex(""".*COVER_LETTER.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Cover Letter",
        ),
        1363,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTSigningInstructions(
        regex(""".*Signing_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Signing Instructions",
        ),
        1364,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTHelpGuide(
        regex(""".*Help_Guide_to_LWT.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Help Guide to LWT",
        ),
        1365,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTPdf(
        regex(""".*Last_Will_and_Testament.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "LWT Doc",
        ),
        1366,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTWord(
        regex(""".*Last_Will_and_Testament.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "LWT Doc - word",
        ),
        1367,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTSelfProvingAffidavitPdf(
        regex(""".*Self_Proving_Affidavit.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Self-Proving Affidavit",
        ),
        1368,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTSelfProvingAffidavitWord(
        regex(""".*Self_Proving_Affidavit.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Self-Proving Affidavit - word",
        ),
        1369,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTStatementOfIntermentPdf(
        regex(""".*Statement_of_Interment.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Statement of Interment",
        ),
        1370,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTStatementOfIntermentWord(
        regex(""".*Statement_of_Interment.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Statement of Interment - word",
        ),
        1371,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTVaultInfo(
        regex(""".*Vaulting_Information.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Vaulting Information",
        ),
        1372,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWTPetCareInstructions(
        regex(""".*Pet_Care_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LastWillAndTestament.productName,
            "Pet Care Instructions",
        ),
        1443,
        DOCGEN_UPLOAD,
        ProductType.LastWillAndTestament.processId,
    ),
    LWCoverLetter(
        regex(""".*COVER_LETTER.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Cover Letter",
        ),
        1342,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWSigningInstructions(
        regex(""".*Signing_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Signing Instructions",
        ),
        1343,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWHelpGuide(
        regex(""".*Help_Guide_to_LW.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Help Guide to LW",
        ),
        1344,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWHealthCareDirectivePdf(
        regex(""".*Health_Care_Directive.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Health Care Directive",
        ),
        1345,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWHealthCareDirectiveWord(
        regex(""".*Health_Care_Directive.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Health Care Directive - word",
        ),
        1346,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWVaultInfo(
        regex(""".*Vaulting_Information.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Vaulting Information",
        ),
        1347,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWCard(
        regex(""".*_Card.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Card",
        ),
        1348,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWCHealthCarePowerOfAttorneyPdf(
        regex(""".*Health_Care_Power_of_Attorney.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Health Care Power of Attorney",
        ),
        1349,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWCHealthCarePowerOfAttorneyWord(
        regex(""".*Health_Care_Power_of_Attorney.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Health Care Power of Attorney - word",
        ),
        1350,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWAnatomicalGiftPdf(
        regex(""".*Anatomical_Gift.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Anatomical Gift",
        ),
        1351,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWAnatomicalGiftWord(
        regex(""".*Anatomical_Gift.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Anatomical Gift - word",
        ),
        1352,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWCardCarrier(
        regex(""".*Card_Carrier.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "Card Carrier",
        ),
        1353,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    LWReleaseAndAuthorisationPdf(
        regex(""".*HIPAA_Release_and_Authorization.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.LivingWill.productName,
            "HIPAA Release and Authorization",
        ),
        1444,
        DOCGEN_UPLOAD,
        ProductType.LivingWill.processId,
    ),
    POACoverLetter(
        regex(""".*COVER_LETTER.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Cover Letter",
        ),
        1332,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POASigningInstructions(
        regex(""".*Signing_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Signing Instructions",
        ),
        1333,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POAHelpGuide(
        regex(""".*Help_Guide_to_POA.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Help Guide to POA",
        ),
        1334,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POARevocationPdf(
        regex(""".*Revocation_of_Power_of_Attorney.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Revocation of Power of Attorney",
        ),
        1337,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POARevocationWord(
        regex(""".*Revocation_of_Power_of_Attorney.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Revocation of Power of Attorney - Word",
        ),
        1338,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POAPdf(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation).*Power_of_Attorney.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Power of Attorney",
        ),
        1335,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POAWord(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation).*Power_of_Attorney.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Power of Attorney - Word",
        ),
        1336,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    POAVaultInfo(
        regex(""".*Vaulting_Information.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.PowerOfAttorney.productName,
            "Vaulting Information",
        ),
        1339,
        DOCGEN_UPLOAD,
        ProductType.PowerOfAttorney.processId,
    ),
    NonProfitWelcomePacket(
        regex(""".*WELCOME_PACKET.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Welcome Packet",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitWelcomePacketWord(
        regex(""".*WELCOME_PACKET.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Welcome Packet - Word",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitApplicationForEIN(
        regex(""".*APPLICATION_FOR_EIN$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Application For EIN",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitArticlesOfIncorporation(
        regex(""".*ARTICLES_OF_INCORPORATION.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Articles of Incorporation",
        ),
        486,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitPreFilingArticles(
        regex(""".*Prefiling_Articles.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(NP.productName, "Articles (pre-filed)"),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitActionByWrittenConsent(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_INCORPORATOR.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Action by Written Consent Incorporator",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitActionByWrittenConsentWord(
        regex(""".*ACTION_BY_WRITTEN_CONSENT_INCORPORATOR.DOC$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Action by Written Consent Incorporator - Word",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitArticlesAttachment(
        regex(""".*ARTICLES_ATTACHMENT.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Articles Attachment",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    NonProfitRegisterOfDeedsAttachment(
        regex(""".*Register_of_Deeds_Attachment.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            NP.productName,
            "Register of Deeds Attachment",
        ),
        0,
        DOCGEN_UPLOAD,
        NP.processId,
    ),
    WillBundleCoverLetter(
        regex(""".*Cover_Letter.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Cover Letter",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleSigningInstructions(
        regex(""".*Signing_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Signing Instructions",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleLastWillTestament(
        regex(""".*Last_Will_and_Testament.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "LWT Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleLastWillTestamentDoc(
        regex(""".*Last_Will_and_Testament.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "LWT Doc - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleSelfProvingAffidavit(
        regex(""".*Self_Proving_Affidavit.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Self-Proving Affidavit",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleSelfProvingAffidavitDoc(
        regex(""".*Self_Proving_Affidavit.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Self-Proving Affidavit - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundlePowerOfAttorney(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation|.*Health).*Power_of_Attorney.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Power of Attorney",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundlePowerOfAttorneyDoc(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation|.*Health).*Power_of_Attorney.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Power of Attorney - Word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundlePOARevocationPdf(
        regex(""".*Revocation_of_Power_of_Attorney.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Revocation of Power of Attorney",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundlePOARevocationWord(
        regex(""".*Revocation_of_Power_of_Attorney.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Revocation of Power of Attorney - Word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHealthCareDirective(
        regex(""".*Health_Care_Directive.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Health Care Directive",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHealthCareDirectiveDoc(
        regex(""".*Health_Care_Directive.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Health Care Directive - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHealthCarePOA(
        regex(""".*Health_Care_Power_of_Attorney.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Health Care Power of Attorney",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHealthCarePOADoc(
        regex(""".*Health_Care_Power_of_Attorney.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Health Care Power of Attorney - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleAnatomicalGift(
        regex(""".*Anatomical_Gift.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Anatomical Gift",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleAnatomicalGiftDoc(
        regex(""".*Anatomical_Gift.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Anatomical Gift - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHIPAARelease(
        regex(""".*HIPAA_Release_and_Authorization.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "HIPAA Release and Authorization",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHIPAAReleaseDoc(
        regex(""".*HIPAA_Release_and_Authorization.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "HIPAA Release and Authorization Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleStatementOfInterment(
        regex(""".*Statement_of_Interment.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Statement of Interment",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleStatementOfIntermentDoc(
        regex(""".*Statement_of_Interment.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Statement of Interment - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundlePetCareInstructions(
        regex(""".*Pet_Care_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Pet Care Instructions",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleHelpGuideToLWT(
        regex(""".*HELP_GUIDE_TO_LWT.PDF$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Help Guide to LWT",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleVaultingInformation(
        regex(""".*Vaulting_Information.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Vaulting Information",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleLivingWillCard(
        regex(""".*_Card.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Living Will Card",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleLivingWillCardDoc(
        regex(""".*_Card.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Living Will Card - word",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    WillBundleLWCardCarrier(
        regex(""".*Card_Carrier.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.WillBundle.productName,
            "Card Carrier",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.WillBundle.processId,
    ),
    TrustBundleCoverLetter(
        regex(""".*Cover_Letter.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Cover Letter",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleSigningInstructions(
        regex(""".*Signing_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Signing Instructions",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleDeclarationOfTrust(
        regex(""".*Declaration_of_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Declaration of Trust",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleDeclarationOfTrustDoc(
        regex(""".*Declaration_of_Trust.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Declaration of Trust Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleCertificateOfTrust(
        regex(""".*Certificate_of_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Certificate of Trust",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleCertificateOfTrustDoc(
        regex(""".*Certificate_of_Trust.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Certificate of Trust Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleCertificationOfTrust(
        regex(""".*Certification_of_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Certification of Trust",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleCertificationOfTrustDoc(
        regex(""".*Certification_of_Trust.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Certification of Trust Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleCertificateOfTrustAgreement(
        regex(""".*Certificate_of_Trust_Agreement.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Certificate of Trust Agreement",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleCertificateOfTrustAgreementDoc(
        regex(""".*Certificate_of_Trust_Agreement.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Certificate of Trust Agreement Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleBillOfTransfer(
        regex(""".*Bill_of_Transfer.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Bill of Transfer",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleBillOfTransferDoc(
        regex(""".*Bill_of_Transfer.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Bill of Transfer Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleTransferLetters(
        regex(""".*Transfer_Letters.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Transfer Letters",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleTransferLettersDoc(
        regex(""".*Transfer_Letters.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Transfer Letters Doc",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLastWillAndTestamentG1(
        regex(""".*Last_Will_and_Testament_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Last Will and Testament - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLastWillAndTestamentDocG1(
        regex(""".*Last_Will_and_Testament_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Last Will and Testament Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleSelfProvingAffidavitG1(
        regex(""".*Self_Proving_Affidavit_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Self Proving Affidavit - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleSelfProvingAffidavitDocG1(
        regex(""".*Self_Proving_Affidavit_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Self Proving Affidavit Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLastWillAndTestamentG2(
        regex(""".*_Last_Will_and_Testament_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Last Will and Testament - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLastWillAndTestamentDocG2(
        regex(""".*_Last_Will_and_Testament_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Last Will and Testament Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleSelfProvingAffidavitG2(
        regex(""".*Self_Proving_Affidavit_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Self Proving Affidavit - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleSelfProvingAffidavitDocG2(
        regex(""".*Self_Proving_Affidavit_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Self Proving Affidavit Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePowerOfAttorneyG1(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation|.*Health).*Power_of_Attorney_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Power of Attorney - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePowerOfAttorneyDocG1(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation|.*Health).*Power_of_Attorney_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Power of Attorney Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePowerOfAttorneyG2(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation|.*Health).*Power_of_Attorney_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Power of Attorney - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePowerOfAttorneyDocG2(
        regex("""[0-9]+_[0-9]+_(?!.*Revocation|.*Health).*Power_of_Attorney_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Power of Attorney Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePOARevocationPdfG1(
        regex(""".*Revocation_of_Power_of_Attorney_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Revocation of Power of Attorney - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePOARevocationWordG1(
        regex(""".*Revocation_of_Power_of_Attorney_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Revocation of Power of Attorney Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePOARevocationPdfG2(
        regex(""".*Revocation_of_Power_of_Attorney_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Revocation of Power of Attorney - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePOARevocationWordG2(
        regex(""".*Revocation_of_Power_of_Attorney_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Revocation of Power of Attorney Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCareDirectiveG1(
        regex(""".*(?<!Advance_)Health_Care_Directive_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Directive - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCareDirectiveDocG1(
        regex(""".*(?<!Advance_)Health_Care_Directive_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Directive Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCareDirectiveG2(
        regex(""".*(?<!Advance_)Health_Care_Directive_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Directive - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCareDirectiveDocG2(
        regex(""".*(?<!Advance_)Health_Care_Directive_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Directive Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAdvanceHealthCareDirectiveG1(
        regex(""".*Advance_Health_Care_Directive_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Advance Health Care Directive - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAdvanceHealthCareDirectiveDocG1(
        regex(""".*Advance_Health_Care_Directive_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Advance Health Care Directive Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAdvanceHealthCareDirectiveG2(
        regex(""".*Advance_Health_Care_Directive_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Advance Health Care Directive - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAdvanceHealthCareDirectiveDocG2(
        regex(""".*Advance_Health_Care_Directive_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Advance Health Care Directive Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCarePOAG1(
        regex(""".*Health_Care_Power_of_Attorney_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Power of Attorney - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCarePOADocG1(
        regex(""".*Health_Care_Power_of_Attorney_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Power of Attorney Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCarePOAG2(
        regex(""".*Health_Care_Power_of_Attorney_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Power of Attorney - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHealthCarePOADocG2(
        regex(""".*Health_Care_Power_of_Attorney_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Health Care Power of Attorney Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAnatomicalGiftG1(
        regex(""".*Anatomical_Gift_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Anatomical Gift - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAnatomicalGiftDocG1(
        regex(""".*Anatomical_Gift_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Anatomical Gift Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAnatomicalGiftG2(
        regex(""".*Anatomical_Gift_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Anatomical Gift - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleAnatomicalGiftDocG2(
        regex(""".*Anatomical_Gift_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Anatomical Gift Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHIPAAReleaseAuthorization(
        regex(""".*HIPAA_Release_and_Authorization.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "HIPAA Release and Authorization",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleStatementOfIntermentG1(
        regex(""".*Statement_of_Interment_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Statement of Interment - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleStatementOfIntermentDocG1(
        regex(""".*Statement_of_Interment_g1.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Statement of Interment Doc - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleStatementOfIntermentG2(
        regex(""".*Self_Proving_Affidavit_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Statement of Interment - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleStatementOfIntermentDocG2(
        regex(""".*Self_Proving_Affidavit_g2.*.doc$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Statement of Interment Doc - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundlePetCareInstructions(
        regex(""".*Pet_Care_Instructions.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Pet Care Instructions",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleGuideToFundingYourTrust(
        regex(""".*Guide_to_Funding_Your_Trust.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Guide to Funding Your Trust",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleHelpGuideToLT(
        regex(""".*Help_Guide_to_LT.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Help Guide to LT",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleVaultingInformation(
        regex(""".*Vaulting_Information.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Vaulting Information",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLivingTrustCardG1(
        regex(""".*_Card_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Living Will Card - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLivingTrustCardG2(
        regex(""".*_Card_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Living Will Card - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLWCardCarrierG1(
        regex(""".*Card_Carrier_g1.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Card Carrier - G1",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    TrustBundleLWCardCarrierG2(
        regex(""".*Card_Carrier_g2.*.pdf$"""),
        CustomerDocumentType.findCustomerDocTypeFromProductName(
            ProductType.TrustBundle.productName,
            "Card Carrier - G2",
        ),
        0,
        DOCGEN_UPLOAD,
        ProductType.TrustBundle.processId,
    ),
    ;

    companion object {
        private val fromDocumentEventTypeProcessIdIdentifierMap =
            values().groupBy {
                DocumentEventTypeProcessIdIdentifierMap(it.documentEventType, it.processId)
            }.mapValues { docEventMap -> docEventMap.value.map { it.pattern } }

        val documentTypeFromPattern: Map<Regex, CustomerDocumentType> =
            values().associateBy(FileImportPattern::pattern, FileImportPattern::customerDocumentType)

        fun getPatternsFromDocumentEventType(
            documentEventType: DocumentEventType,
            processId: Int,
        ): List<Regex>? {
            return fromDocumentEventTypeProcessIdIdentifierMap[
                DocumentEventTypeProcessIdIdentifierMap(
                    documentEventType,
                    processId,
                ),
            ]
        }
    }

    data class DocumentEventTypeProcessIdIdentifierMap(
        val documentEventType: DocumentEventType,
        val processId: Int,
    )
}

private fun regex(pattern: String): Regex {
    return Regex(pattern, RegexOption.IGNORE_CASE)
}
