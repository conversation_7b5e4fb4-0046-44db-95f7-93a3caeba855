package com.legalzoom.fulfillment.service.service

import com.legalzoom.api.commerce.OrderApi
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.order.OrdersOrderItemsApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.data.UnifiedCommerceIds
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.mappers.findOrderItemByProductType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.UUID
import com.legalzoom.api.model.commerce.OrderItemDto as CP2OrderItemDto
import com.legalzoom.api.model.order.OrderItemDto as CP1OrderItemDto

@Service
class UnifiedCommerceServiceImpl(
    private val cp1OrderApi: OrdersApiService,
    private val cp1OrderItemsApi: OrdersOrderItemsApi,
    private val cp2OrderApi: OrderApi,
    private val completedOrderDetailApi: CompletedOrderDetailApi,
) : UnifiedCommerceService {
    override fun isCanceled(
        ids: UnifiedCommerceIds,
        processingOrderId: Int,
        customerId: String,
    ): Boolean {
        if (ids.isForSystem(CommerceSystem.CP1)) {
            val orderResponse: GetOrderResponse = cp1OrderApi.getOrders(ids.cp1OrderId, null, null, "1.0", customerId, true)
            // Fetch  OrderItem By line Item
            val orderItem =
                orderResponse.order?.orderItems?.firstOrNull {
                    it.processingOrder?.processingOrderId == processingOrderId && it.processingOrder?.complete == true
                }
            // Determine Cancellation and Default to false
            return orderItem?.isCancelled ?: false
        }

        return getCp2OrderItem(ids).status == CP2OrderItemDto.StatusEnum.CANCELED
    }

    override fun hasProductBeenPurchased(
        ids: UnifiedCommerceIds,
        customerId: String,
        product: ProductType,
    ): Boolean {
        if (ids.isForSystem(CommerceSystem.CP1)) {
            return getCp1OrderItemByProduct(ids, product, customerId) != null
        }

        val orderItem = getCp2OrderItem(ids)
        return this.getCP2OrderItemsByWorkspaceAndProduct(orderItem.workspaceId!!, product).isNotEmpty()
    }

    // Example method to check if the payment is in installments
    override fun hasPaymentInInstallments(ids: UnifiedCommerceIds): Boolean {
        TODO("checking the CP1 or CP2 systems for payment installments information")
    }

    override fun getUnifiedOrderId(ids: UnifiedCommerceIds): String {
        if (ids.isForSystem(CommerceSystem.CP1)) {
            return ids.cp1OrderId!!.toString()
        }

        return cp2OrderApi.getOrderItemById(UUID.fromString(ids.cp2OrderItemId!!), null)
            .blockSingle()
            .orderId!!
    }

    override fun getAttachedProductProcessingOrderId(
        ids: UnifiedCommerceIds,
        product: ProductType,
        customerId: String,
    ): Int? {
        if (ids.isForSystem(CommerceSystem.CP1)) {
            return getCp1OrderItemByProduct(ids, product, customerId)?.processingOrder?.processingOrderId
        }

        // Fetch order item to get workspace
        val orderItem = getCp2OrderItem(ids)
        val workspaceId = orderItem.workspaceId!!

        // Find order item for workspace and product
        val purchasedOrderItems = this.getCP2OrderItemsByWorkspaceAndProduct(orderItem.workspaceId!!, product)

        return when (purchasedOrderItems.size) {
            0 -> null
            1 -> {
                // Fetch COD records by workspace and filter by cp2OrderItemId
                val attachedOrderItem = purchasedOrderItems.first()
                completedOrderDetailApi.coreProcessingOrdersAccountsAccountIdCompletedOrderGet(
                    orderItem.workspaceId!!,
                    null,
                    customerId,
                    true,
                )
                    .blockSingle()
                    .results
                    ?.firstOrNull { it.getcP2OrderItemId() == attachedOrderItem.id }
                    ?.processingOrderId
            }
            else -> throw IllegalStateException(
                "Found > 1 order items for workspace $workspaceId and product $product, cannot determine processing order id.",
            )
        }
    }

    private fun getCp1OrderItemByProduct(
        ids: UnifiedCommerceIds,
        product: ProductType,
        customerId: String,
    ): CP1OrderItemDto? {
        return cp1OrderApi.getOrders(
            orderId = ids.cp1OrderId,
            productIdType = null,
            showOrderItemTree = null,
            xLzApiVersion = "1.0",
            xLzCustomerid = customerId,
            xLzAuthorize = true,
        ).order?.findOrderItemByProductType(product)
    }

    private fun getCp2OrderItem(ids: UnifiedCommerceIds): CP2OrderItemDto {
        return cp2OrderApi.getOrderItemById(ids.getCp2OrderItemIdAsUUID(), null)
            .blockSingle()
    }

    private fun getCP2OrderItemsByWorkspaceAndProduct(
        workspaceId: String,
        product: ProductType,
    ): List<CP2OrderItemDto> {
        val productInternalName = product.cp2ProductInternalName
        if (productInternalName == null) {
            logger.warn(
                "Attempting to look up purchased product $product in CP2, but it is not configured with a " +
                    "cp2ProductInternalName and will be skipped.",
            )
            return emptyList()
        }

        return cp2OrderApi.getProductPurchasedByWorkspace(
            workspaceId,
            productInternalName,
            false,
            null,
        )
            .toStream()
            .toList()
    }

    override fun getCommerceIdsByProcessingOrderId(processingOrderId: Int): UnifiedCommerceIds {
        // Call processing orders api and check for cp2OrderItemId to determine if order is CP1 or CP2
        val cp2OrderItemId =
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                processingOrderId,
                null,
                null,
                null,
            ).blockSingle().completedOrderDetail?.getcP2OrderItemId()

        return if (!cp2OrderItemId.isNullOrEmpty()) {
            UnifiedCommerceIds(cp2OrderItemId = cp2OrderItemId)
        } else {
            val orderId =
                cp1OrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(
                    processingOrderId,
                    null,
                    null,
                    null,
                ).blockSingle().orderId
            UnifiedCommerceIds(cp1OrderId = orderId)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(UnifiedCommerceServiceImpl::class.java)
    }
}
