package com.legalzoom.fulfillment.service.service

import com.legalzoom.fulfillment.service.data.UnifiedCommerceIds
import com.legalzoom.fulfillment.service.enumeration.ProductType

/**
 * This service intends to abstract both CP1 (orders api) and CP2 (commerce platform) information when necessary.
 */
interface UnifiedCommerceService {
    /**
     * Returns true if the order item is canceled.
     * For CP1 orders the [processingOrderId] is used to identify the order item.
     * For CP2 orders the [UnifiedCommerceIds.cp2OrderItemId] is used to identify the order item.
     */
    fun isCanceled(
        ids: UnifiedCommerceIds,
        processingOrderId: Int,
        customerId: String,
    ): Boolean

    /**
     * Returns true if the payment for the order is in installments.
     * A.K.A. "Three pay"
     */
    fun hasPaymentInInstallments(ids: UnifiedCommerceIds): Boolean

    /**
     * Returns true if the customer has purchased the given [product].
     */
    fun hasProductBeenPurchased(
        ids: UnifiedCommerceIds,
        customerId: String,
        product: ProductType,
    ): Boolean

    /**
     * For cp1 orders just returns the [UnifiedCommerceIds.cp1OrderId], for cp2 orders calls the commerce platform
     * to retrieve the order id.
     */
    fun getUnifiedOrderId(ids: UnifiedCommerceIds): String

    /**
     * For CP1 orders will call the orders service using the [UnifiedCommerceIds.cp1OrderId] and check for an
     * order item for the given [product].
     * For CP2 orders the COD record contains the relationship of order item to processing order id.
     *
     * In both cases, processing orders related to canceled order items will be filtered out.
     *
     * NOTE: This assumes only 1 product purchase per workspace and will not be useful in scenarios where multiple
     *       purchases of a product are possible within a single workspace.
     */
    fun getAttachedProductProcessingOrderId(
        ids: UnifiedCommerceIds,
        product: ProductType,
        customerId: String,
    ): Int?

    /**
     * Will call COD api with the [processingOrderId] to check for a CP2 Order Item ID.  Otherwise, it will call OrderItems API for CP1 orders.
     * In either case it will set [UnifiedCommerceIds] appropriately
     */
    fun getCommerceIdsByProcessingOrderId(processingOrderId: Int): UnifiedCommerceIds
}
