package com.legalzoom.fulfillment.service.service.helper

import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.fulfillment.common.exception.RetryableException
import com.legalzoom.fulfillment.common.mono.blockSingle
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClientResponseException

@Component
class ProcessingOrderHelper(
    private val processingOrdersApi: ProcessingOrdersApi,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    fun obtainProcessingOrder(processingOrderId: Int): ProcessingOrderDto? {
        try {
            return processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                "1",
                "",
                true,
            ).blockSingle().processingOrder
        } catch (ex: Exception) {
            logger.error(ex.message, ex)
            throw ex
        }
    }

    fun obtainProcessId(
        processingOrderId: Int,
        customerId: String?,
    ): Int {
        try {
            return processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                "1",
                customerId,
                true,
            ).blockSingle().processingOrder!!.processId!!
        } catch (wcre: WebClientResponseException) {
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        }
    }
}
