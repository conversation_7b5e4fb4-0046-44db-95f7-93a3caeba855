package com.legalzoom.fulfillment.service.data.revv

enum class RevvDocumentType(val revvTemplateName: String, val documentType: RevvTemplateType = RevvTemplateType.DOCX) {
    ANNUAL_REPORTS_FINAL_LETTER(revvTemplateName = "AR_FINAL_LETTER"),
    INITIAL_REPORTS_FINAL_LETTER(revvTemplateName = "IR_FINAL_LETTER"),
    CERTIFICATE_OF_GOODS_STANDING_FINAL_LETTER(revvTemplateName = "COGS_AND_CC_FINAL_LETTER"),
    CERTIFIED_COPIES_FINAL_LETTER(revvTemplateName = "COGS_AND_CC_FINAL_LETTER"),
    EIN_FINAL_LETTER(revvTemplateName = "EIN_FINAL_LETTER"),
    CORPORATE_DISSOLUTION_FINAL_LETTER(revvTemplateName = "CD_FINAL_LETTER"),
    CONVERSION_FINAL_LETTER(revvTemplateName = "CONVERSION_FINAL_LETTER"),
    FOREIGN_QUALIFICATION_FINAL_LETTER(revvTemplateName = "FQ_FINAL_LETTER"),
    LIMITED_PARTNERSHIP_FINAL_LETTER(revvTemplateName = "LP_AND_LLP_FINAL_LETTER"),
    LIMITED_LIABILITY_PARTNERSHIP_FINAL_LETTER(revvTemplateName = "LP_AND_LLP_FINAL_LETTER"),
    DBA_FINAL_LETTER(revvTemplateName = "DBA_FINAL_LETTER"),
    SS4_OBTAINED(revvTemplateName = "Form_SS4", documentType = RevvTemplateType.PDF),
    SS4_PREPARED(revvTemplateName = "Form_SS4", documentType = RevvTemplateType.PDF),
    REGISTER_OF_DEEDS_ATTACHMENT(revvTemplateName = "Register_Of_Deeds_Attachment", documentType = RevvTemplateType.PDF),
    AMENDMENT_FINAL_LETTER(revvTemplateName = "AMENDMENT_FINAL_LETTER"),
    BY_LAWS_AND_RESOLUTIONS_FINAL_LETTER(revvTemplateName = "OA_AND_BR_FINAL_LETTER"),
    OPERATING_AGREEMENTS_FINAL_LETTER(revvTemplateName = "OA_AND_BR_FINAL_LETTER"),
    CORPORATE_DISSOLUTION_LLC_CONSENT_LETTER(revvTemplateName = "CD_LLC_CONSENT_LETTER"),
    CORPORATE_DISSOLUTION_INC_CONSENT_LETTER(revvTemplateName = "CD_INC_CONSENT_LETTER"),
    FORM_501C3_FINAL_LETTER(revvTemplateName = "501C3_FINAL_LETTER"),
    FORM_3500A(revvTemplateName = "FORM_3500A", documentType = RevvTemplateType.PDF),
}
