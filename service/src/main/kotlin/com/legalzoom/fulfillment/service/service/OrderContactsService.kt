package com.legalzoom.fulfillment.service.service

import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.ordercontacts.OrdersContactsApi
import com.legalzoom.fulfillment.common.mono.blockSingle
import org.springframework.stereotype.Service

@Service
class OrderContactsService(
    private val orderContactsApi: OrdersContactsApi,
) {
    fun getOrderContactsByOrderId(
        orderId: Int,
        customerId: String,
    ): List<ContactInfoDto> {
        return orderContactsApi.coreOrdersOrderIdContactsGet(
            orderId,
            "1.0",
            customerId,
            true,
        ).blockSingle().contacts!!
    }
}
