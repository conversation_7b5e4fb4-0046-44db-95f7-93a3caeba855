package com.legalzoom.fulfillment.service.service.helper.documents

import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.model.storageplatform.PreSignedRequest
import com.legalzoom.api.model.storageplatform.PreSignedResponse
import com.legalzoom.api.model.storageplatform.RoleType
import com.legalzoom.api.model.storageplatform.SearchResponse
import com.legalzoom.api.model.storageplatform.SearchResult
import com.legalzoom.api.model.storageplatform.UpdateDocumentRequest
import com.legalzoom.api.storageplatform.ApiClient
import com.legalzoom.api.storageplatform.DocumentDetailsApi
import com.legalzoom.api.storageplatform.DocumentStorageApi
import com.legalzoom.fulfillment.common.Constants
import com.legalzoom.fulfillment.common.jwt.TokenDecoder
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.Constants.DEFAULT_SP_ROLE_TYPE
import com.legalzoom.fulfillment.service.Constants.SP_HEADER_ACCOUNT_ID
import com.legalzoom.fulfillment.service.Constants.SP_HEADER_FORCE_INDEX_FRESH
import com.legalzoom.fulfillment.service.Constants.SP_HEADER_ROLE_ID
import com.legalzoom.fulfillment.service.Constants.SP_HEADER_ROLE_TYPE
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.OrderContext
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus.Active
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus.Inactive
import com.legalzoom.fulfillment.service.enumeration.DocumentVisibility
import com.legalzoom.fulfillment.service.enumeration.DocumentVisibility.NotVisibleToCustomer
import com.legalzoom.fulfillment.service.enumeration.DocumentVisibility.VisibleToCustomer
import com.legalzoom.fulfillment.service.enumeration.ProductType.LLC
import com.legalzoom.fulfillment.service.exception.StoragePlatformException
import com.legalzoom.fulfillment.service.properties.MultipleUploadedDocumentProperties
import com.legalzoom.fulfillment.service.properties.WebClientProperties
import com.legalzoom.fulfillment.service.service.AccountService
import org.apache.commons.lang3.exception.ExceptionUtils
import org.apache.http.client.utils.URIBuilder
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.core.ParameterizedTypeReference
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.buffer.DataBuffer
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.MediaType.ALL
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.http.MediaType.APPLICATION_OCTET_STREAM
import org.springframework.http.MediaType.MULTIPART_FORM_DATA
import org.springframework.http.ResponseEntity
import org.springframework.http.client.MultipartBodyBuilder
import org.springframework.stereotype.Component
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import org.springframework.web.client.RestClientException
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.io.File
import java.net.URI
import java.time.Duration
import java.util.stream.Collectors

/**
 * Service abstraction for interfacing with Storage Platform
 * Uses DEFAULT_SP_ROLE_TYPE for the default headers for authorizing with the Storage Platform
 * @property documentStorageApiDefault - Codegen'd api for document storage
 * @property documentManagementApiDefault - Codegen'd api for document management
 * @property webClientProperties - Web Client properties to override API properties from codegen. Currently used for
 * timeouts
 */
@Component
@EnableConfigurationProperties(MultipleUploadedDocumentProperties::class)
class StoragePlatformClient(
    private val legalzoomApiClientStoragePlatform: ApiClient,
    private val documentStorageApiDefault: DocumentStorageApi,
    private val documentManagementApiDefault: DocumentDetailsApi,
    private val webClient: WebClient,
    private val webClientProperties: WebClientProperties,
    private val accountService: AccountService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    companion object {
        private const val DEFAULT_MAX_FILES = "400"

        private fun mapDocumentStatusToStoragePlatformQuery(
            documentStatus: DocumentStatus,
            documentVisibility: DocumentVisibility,
        ): String =
            when (documentStatus) {
                DocumentStatus.All ->
                    when (documentVisibility) {
                        VisibleToCustomer ->
                            DocumentResponse.DocumentStatusEnum.ACTIVE.value + " OR " +
                                DocumentResponse.DocumentStatusEnum.EXPIRED.value

                        NotVisibleToCustomer ->
                            DocumentResponse.DocumentStatusEnum.INACTIVE.value + " OR " +
                                DocumentResponse.DocumentStatusEnum.EXPIRED.value

                        DocumentVisibility.All ->
                            DocumentResponse.DocumentStatusEnum.ACTIVE.value + " OR " +
                                DocumentResponse.DocumentStatusEnum.INACTIVE.value + " OR " +
                                DocumentResponse.DocumentStatusEnum.EXPIRED.value
                    }

                Inactive -> DocumentResponse.DocumentStatusEnum.EXPIRED.value
                Active ->
                    when (documentVisibility) {
                        VisibleToCustomer -> DocumentResponse.DocumentStatusEnum.ACTIVE.value
                        NotVisibleToCustomer -> DocumentResponse.DocumentStatusEnum.INACTIVE.value
                        DocumentVisibility.All ->
                            DocumentResponse.DocumentStatusEnum.ACTIVE.value + " OR " +
                                DocumentResponse.DocumentStatusEnum.INACTIVE.value
                    }
            }

        private fun filterStoragePlatformResults(
            searchResult: SearchResult,
            documentVisibility: DocumentVisibility,
        ): Boolean =
            if (searchResult.documentStatus != SearchResult.DocumentStatusEnum.ACTIVE &&
                searchResult.documentStatus != SearchResult.DocumentStatusEnum.INACTIVE
            ) {
                val mappedCustomerDocumentType =
                    try {
                        CustomerDocumentType.findCustomerDocTypeFromStoragePlatformDocumentType(
                            LLC.productName,
                            searchResult.documentType.toString(),
                            searchResult.reference["documentSubtype"],
                        )
                    } catch (e: NoSuchElementException) {
                        null
                    }
                when (documentVisibility) {
                    VisibleToCustomer -> checkDocumentTypeVisibility(mappedCustomerDocumentType, true)
                    NotVisibleToCustomer -> checkDocumentTypeVisibility(mappedCustomerDocumentType, false)
                    DocumentVisibility.All -> true
                }
            } else {
                true
                // Being consistent in asserting that the regular document status search to storage platform passes.
            }

        private fun checkDocumentTypeVisibility(
            customerDocumentType: CustomerDocumentType?,
            isVisible: Boolean,
        ): Boolean =
            if (customerDocumentType == null) {
                true
            } else {
                customerDocumentType.isVisibleToCustomer == isVisible
            }

        private val threadLocalUserName = ThreadLocal<String>()

        // Method to fetch userName from retoolAccessToken
        fun setUserName(retoolAccessToken: String) {
            if (retoolAccessToken.isNotEmpty() && retoolAccessToken.isNotBlank()) {
                threadLocalUserName.set(
                    TokenDecoder().getClaim(retoolAccessToken, Constants.USERNAME_CLAIM).substringBefore('@'),
                )
            } else {
                threadLocalUserName.set(DEFAULT_SP_ROLE_TYPE)
            }
        }
    }

    private fun getAccountId(
        customerId: String,
        processingOrderId: String,
        processId: Int?,
    ): String? {
        return try {
            accountService.getAccountIdFromCustomerId(customerId, processingOrderId, processId).toString()
        } catch (ex: Exception) {
            logger.errorEvent(
                ex.stackTraceToString(),
                mapOf(
                    "customerId" to customerId,
                    "processingOrderId" to processingOrderId,
                ),
            )
            return null
        }
    }

    /**
     * Organizes the parameters into a queryParametersString where StoragePlatform will use this query
     * to retrieve the matching documents
     * @param processingOrderId
     * @param customerId
     * @param customerDocumentType
     *
     * // documentType Fulfillment  documentSubtype ARTICLES_PRE_FILED
     * // "ARTICLES_PRE_FILED"
     * @return SearchResponse object that contains the documents retrieved using the search parameters
     * @throws WebClientResponseException
     */
    fun findAllDocumentsBy(
        processingOrderId: Long?,
        customerId: Long?,
        customerDocumentType: CustomerDocumentType?,
        documentStatus: DocumentStatus,
        documentVisibility: DocumentVisibility,
        orderId: Long? = null,
        accountId: String? = null,
    ): SearchResponse? {
        val paramMap: MutableMap<String, String?> = HashMap()
        var queryParametersString = ""
        val userAccountId =
            if (accountId.isNullOrEmpty() && customerId != null && processingOrderId != null) {
                getAccountId(customerId.toString(), processingOrderId.toString(), null)
            } else {
                accountId
            }

        if (processingOrderId != null) {
            paramMap["processingOrderId"] = processingOrderId.toString()
        }
        orderId?.let {
            paramMap["orderId"] = orderId.toString()
        }
        if (customerId != null) {
            paramMap["ownerId"] = customerId.toString()
        }
        if (customerDocumentType?.productName != null) {
            paramMap["productType"] = customerDocumentType.productName
        }
        if (customerDocumentType?.storagePlatformDocumentType != null) {
            paramMap["documentType"] = customerDocumentType.storagePlatformDocumentType
            paramMap["documentSubtype"] = customerDocumentType.storagePlatformDocumentSubType
        }
        paramMap["documentStatus"] = mapDocumentStatusToStoragePlatformQuery(documentStatus, documentVisibility)

        paramMap["size"] = DEFAULT_MAX_FILES

        for (param in paramMap) {
            if (param.value != null && param.value != "null") {
                queryParametersString = queryParametersString.plus(param.key + "=" + param.value + "&")
            }
        }
        queryParametersString = queryParametersString.substring(0, queryParametersString.length - 1)
        logger.event(
            "document.search.storageplatform.queryparam",
            mapOf(
                "processingOrderId" to processingOrderId,
                "customerId" to customerId,
                "documentType" to customerDocumentType,
                "documentStatus" to documentStatus,
                "documentVisibility" to documentVisibility,
                "queryParametersString" to queryParametersString,
                "X_LZ_ROLE_ID" to threadLocalUserName.get(),
                "accountId" to userAccountId,
            ),
        )
        val responseEntity: ResponseEntity<SearchResponse>? =
            documentStorageApiDefault
                .getDocumentsWithHttpInfo(
                    DEFAULT_SP_ROLE_TYPE,
                    threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE,
                    userAccountId,
                    queryParametersString,
                ).blockSingle()

        return if (responseEntity?.body?.payload == null) {
            responseEntity?.body
        } else {
            responseEntity.body?.payload =
                responseEntity.body?.payload!!
                    .parallelStream().filter {
                        filterStoragePlatformResults(it, documentVisibility)
                    }?.collect(Collectors.toList())!!
            responseEntity.body
        }
    }

    /**
     * Downloads the document from StoragePlatform and extracts the file metadata from the
     * response headers into the ResourceWithType object
     * @param documentId
     * @param documentVersion
     * @return
     * @throws WebClientResponseException
     */
    fun downloadDocument(
        documentId: String?,
        documentVersion: String?,
        accountId: String? = null,
    ): ResourceWithType? {
        val responseEntity: ResponseEntity<ByteArray>? =
            documentStorageApiDefault.downloadDocumentVersionWithHttpInfo(
                DEFAULT_SP_ROLE_TYPE,
                threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE,
                documentId,
                accountId,
                documentVersion?.toInt(),
            ).block()
        logger.event(
            "document.download.storageplatform.queryparam",
            mapOf(
                "documentId" to documentId,
                "documentVersion" to documentVersion?.toInt(),
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to threadLocalUserName.get(),
            ),
        )
        return responseEntity?.body?.let {
            val contentType = responseEntity.headers.contentType ?: APPLICATION_OCTET_STREAM
            val filename = responseEntity.headers.contentDisposition.filename ?: ""
            val resource = ByteArrayResource(responseEntity.body!!)
            ResourceWithType(resource, contentType, filename)
        }
    }

    /**
     * Downloads large documents from StoragePlatform and extracts the file metadata from the
     * response headers into the ResourceWithType object
     * @param documentId
     * @param documentVersion
     * @return
     * @throws WebClientResponseException
     */
    fun downloadLargeDocument(
        documentId: String?,
        documentVersion: String?,
        accountId: String? = null,
    ): ResourceWithType? {
        logger.event(
            "document.download.storageplatform.large",
            mapOf(
                "documentId" to documentId,
                "documentVersion" to documentVersion,
            ),
        )
        val presignedUrl = getPresignedUrl(documentId, null, null, accountId, documentVersion?.toIntOrNull())

        var contentType: MediaType? = null
        val dataBufferFlux =
            webClient
                .get()
                .uri(URIBuilder(presignedUrl?.presignedURL).build())
                .exchangeToFlux { response ->
                    contentType = response.headers().asHttpHeaders().contentType
                    response.bodyToFlux(DataBuffer::class.java)
                }

        // Block first, so we guarantee we have the content type
        dataBufferFlux.blockFirst()

        logger.event(
            "document.download.storageplatform.large.complete",
            mapOf(
                "documentId" to documentId,
                "documentVersion" to documentVersion,
            ),
        )
        return ResourceWithType(dataBufferFlux, contentType ?: APPLICATION_OCTET_STREAM, documentId ?: "")
    }

    /**
     * Renames the folder in doc centre
     * @param customerID
     * @param currentFolderPath
     * @param newFolderPath
     * @throws RestClientException
     */
    fun storageRenameFolder(
        customerID: Long?,
        currentFolderPath: String?,
        newFolderPath: String?,
        accountId: String? = null,
    ) {
        logger.event(
            "document.download.storageplatform.queryparam",
            mapOf(
                "customerID" to customerID,
                "currentFolderPath" to currentFolderPath,
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to DEFAULT_SP_ROLE_TYPE,
                "X-LZ-FORCE-INDEX-REFRESH" to true,
            ),
        )
        val responseObject = object : ParameterizedTypeReference<Void>() {}

        val reqBody =
            mapOf(
                "ownerId" to customerID.toString(),
                "ownerType" to "Customer",
                "currentFolderPath" to currentFolderPath,
                "newFolderPath" to newFolderPath,
            )

        val headerParams = HttpHeaders()
        headerParams.add(SP_HEADER_ROLE_TYPE, DEFAULT_SP_ROLE_TYPE)
        headerParams.add(SP_HEADER_ROLE_ID, DEFAULT_SP_ROLE_TYPE)
        headerParams.add(SP_HEADER_FORCE_INDEX_FRESH, "true")

        if (accountId != null) {
            headerParams.add(SP_HEADER_ACCOUNT_ID, accountId)
        }

        val responseEntity =
            try {
                legalzoomApiClientStoragePlatform.invokeAPI(
                    "/storage-platform/v1/folders/rename",
                    HttpMethod.POST,
                    emptyMap(),
                    LinkedMultiValueMap(),
                    reqBody,
                    headerParams,
                    LinkedMultiValueMap(),
                    LinkedMultiValueMap(),
                    listOf(ALL),
                    APPLICATION_JSON,
                    arrayOf(),
                    responseObject,
                ).toEntity(responseObject).timeout(Duration.ofSeconds(webClientProperties.props.timeout)).block()
            } catch (wcre: WebClientResponseException) {
                if (wcre.statusCode != HttpStatus.NOT_FOUND && wcre.statusCode != HttpStatus.CONFLICT) {
                    logger.errorEvent(
                        "document.update.storageplatform.failed",
                        mapOf(
                            "customerId" to customerID,
                            "currentFolderPath" to currentFolderPath,
                            "newFolderPath" to newFolderPath,
                            "message" to wcre.message,
                            "stackTrace" to ExceptionUtils.getStackTrace(wcre),
                        ),
                    )
                    throw StoragePlatformException(wcre)
                } else {
                    logger.info("Acceptable HTTP status from rename API. HTTP status code : " + wcre.statusCode)
                }
            }

        return
    }

    /**
     * @throws RestClientException
     */
    fun uploadDocument(
        documentAsTemporaryFile: File,
        ownerType: RoleType,
        customerDocumentType: CustomerDocumentType,
        documentStatus: DocumentStatus,
        orderContext: OrderContext,
        documentPath: String,
        availableForImmediateCustomerDownload: Boolean,
    ): DocumentResponse? {
        var tempFile = documentAsTemporaryFile
        val documentStatusValue: DocumentResponse.DocumentStatusEnum =
            if (availableForImmediateCustomerDownload) {
                DocumentResponse.DocumentStatusEnum.ACTIVE
            } else if (documentStatus == Inactive) {
                DocumentResponse.DocumentStatusEnum.EXPIRED
            } else { // (documentStatus == DocumentStatus.Active) or other statuses
                DocumentResponse.DocumentStatusEnum.INACTIVE
            }
        val bodyBuilder = MultipartBodyBuilder()
        bodyBuilder.part("multiPartFile", FileSystemResource(documentAsTemporaryFile))
            .filename(documentAsTemporaryFile.name)

        bodyBuilder.part("ownerId", orderContext.customerId.toString())
        bodyBuilder.part("documentType", customerDocumentType.storagePlatformDocumentType)
        bodyBuilder.part("documentSubtype", customerDocumentType.storagePlatformDocumentSubType)
        bodyBuilder.part("documentStatus", documentStatusValue.value)
        bodyBuilder.part("ownerType", ownerType.value)
        bodyBuilder.part("processingOrderId", orderContext.processingOrderId)
        if (!orderContext.accountId.isNullOrEmpty()) {
            bodyBuilder.part("accountId", orderContext.accountId!!)
        }
        orderContext.orderId?.let {
            bodyBuilder.part("orderId", it)
        }
        orderContext.jurisdiction?.let {
            bodyBuilder.part("state", it)
        }
        bodyBuilder.part("documentPath", documentPath)
        bodyBuilder.part("productType", customerDocumentType.productName)
        bodyBuilder.part("productTypeId", customerDocumentType.productId)
        val bodyMap: MultiValueMap<String, Any> =
            LinkedMultiValueMap(
                bodyBuilder.build().mapValues { entry ->
                    entry.value.map {
                        it as Any
                    }
                },
            )
        val responseObject = object : ParameterizedTypeReference<DocumentResponse?>() {}
        val headerParams = HttpHeaders()
        headerParams.add(SP_HEADER_ROLE_TYPE, DEFAULT_SP_ROLE_TYPE)
        headerParams.add(SP_HEADER_ROLE_ID, threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE)

        // lwCards must be uploaded synchronously
        if (customerDocumentType.isLWCardDocumentType()) {
            headerParams.add(SP_HEADER_FORCE_INDEX_FRESH, "true")
        }

        if (orderContext.accountId.isNullOrEmpty()) {
            throw StoragePlatformException("AccountId not provided")
        }

        headerParams.add(SP_HEADER_ACCOUNT_ID, orderContext.accountId)

        logger.event(
            "document.upload.storageplatform.queryparam",
            mapOf(
                "ownerId" to orderContext.customerId.toString(),
                "documentType" to customerDocumentType.storagePlatformDocumentType,
                "documentSubtype" to customerDocumentType.storagePlatformDocumentSubType,
                "documentStatus" to documentStatusValue.value,
                "ownerType" to ownerType.value,
                "processingOrderId" to orderContext.processingOrderId,
                "documentPath" to documentPath,
                "productType" to customerDocumentType.productName,
                "productTypeId" to customerDocumentType.productId,
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to threadLocalUserName.get(),
                "X_LZ_ACCOUNTID" to orderContext.accountId,
            ),
        )

        // This endpoint does not use codegen directly because we need to add additional optional parameters that
        // prevents us from making 2 REST calls to storage platform, instead of 1
        val responseEntity =
            legalzoomApiClientStoragePlatform.invokeAPI(
                "/storage-platform/v1/documents",
                HttpMethod.POST,
                emptyMap(),
                LinkedMultiValueMap(),
                null,
                headerParams,
                LinkedMultiValueMap(),
                bodyMap,
                listOf(ALL),
                MULTIPART_FORM_DATA,
                arrayOf(),
                responseObject,
            ).toEntity(responseObject).timeout(Duration.ofSeconds(webClientProperties.props.timeout)).block()
        return responseEntity?.body
    }

    fun uploadLargeDocument(
        documentAsTemporaryFile: File,
        ownerType: RoleType,
        customerDocumentType: CustomerDocumentType,
        documentStatus: DocumentStatus,
        orderContext: OrderContext,
        documentPath: String,
        availableForImmediateCustomerDownload: Boolean,
    ): DocumentResponse? {
        if (orderContext.accountId.isNullOrEmpty()) {
            throw StoragePlatformException("AccountId not provided")
        }

        // Get presigned url for a new document
        val preSignedRequest =
            PreSignedRequest()
                .accountId(orderContext.accountId)
                .ownerId(orderContext.customerId.toString())
                .ownerType(ownerType.value)
                .documentType(customerDocumentType.storagePlatformDocumentType)
                .documentPath(documentPath)
                .documentName(documentAsTemporaryFile.name)
                .reference(
                    mapOf(
                        "productType" to customerDocumentType.productName,
                        "productTypeId" to customerDocumentType.productId.toString(),
                        "orderId" to orderContext.orderId.toString(),
                        "processingOrderId" to orderContext.processingOrderId.toString(),
                        "documentSubtype" to customerDocumentType.storagePlatformDocumentSubType,
                        "state" to orderContext.jurisdiction,
                    ),
                )

        logger.event(
            "document.upload.storageplatform.large",
            mapOf(
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ACCOUNTID" to orderContext.accountId,
                "preSignedRequest" to preSignedRequest,
            ),
        )

        val preSignedResponse =
            documentStorageApiDefault.getPreSignedUrl(
                DEFAULT_SP_ROLE_TYPE,
                DEFAULT_SP_ROLE_TYPE,
                preSignedRequest,
                orderContext.accountId,
            ).blockSingle()

        val presignedUrl = preSignedResponse.presignedURL
        val documentId = preSignedResponse.documentId
        val expires = preSignedResponse.expiryTime

        logger.info("presigned url for $documentId upload: $presignedUrl")

        // Send file to presigned url
        webClient
            .put()
            .uri(URI.create(presignedUrl))
            .contentType(APPLICATION_OCTET_STREAM)
            .contentLength(documentAsTemporaryFile.length())
            .body(BodyInserters.fromResource(FileSystemResource(documentAsTemporaryFile)))
            .retrieve()
            .toBodilessEntity()
            .block()

        // This is a new file upload, so the version is 1 and it's always active
        return DocumentResponse().documentId(documentId).documentVersion("1")
            .documentStatus(DocumentResponse.DocumentStatusEnum.ACTIVE)
    }

    /**
     * Populates the UpdateDocumentRequest with the input parameters and updates the Document Metadata
     * accordingly (matches using the Id)
     * @param documentId - matches the document on Storage Platform using this
     * @param documentStatus
     * @param processingOrderId
     * @param customerId
     * @return
     * @throws WebClientResponseException
     */
    fun updateDocumentRequest(
        documentId: String,
        storagePlatformDocumentSubType: String?,
        documentStatus: DocumentResponse.DocumentStatusEnum?,
        processingOrderId: Long?,
        customerId: Long?,
        accountId: String? = null,
        orderId: Long?,
    ): DocumentResponse? {
        val map: MutableMap<String, String> = HashMap()
        val updateDocumentRequest = UpdateDocumentRequest()
        processingOrderId?.let { map["processingOrderId"] = processingOrderId.toString() }
        orderId?.let { map["orderId"] = orderId.toString() }
        storagePlatformDocumentSubType?.let { map["documentSubtype"] = storagePlatformDocumentSubType }
        documentStatus?.let { updateDocumentRequest.documentStatus(documentStatus.value) }
        updateDocumentRequest.documentName("")

        updateDocumentRequest.reference(map)
        logger.event(
            "document.upload.storageplatform.update.request",
            mapOf(
                "updateRequest" to updateDocumentRequest.toString(),
            ),
        )
        val responseEntity =
            documentManagementApiDefault.updateDocumentDetailsWithHttpInfo(
                DEFAULT_SP_ROLE_TYPE,
                threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE,
                documentId,
                updateDocumentRequest,
                accountId,
            ).block()
        logger.event(
            "document.update.storageplatform.queryparam",
            mapOf(
                "updateDocumentRequest" to updateDocumentRequest,
                "documentId" to documentId,
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to threadLocalUserName.get(),
            ),
        )
        return responseEntity?.body
    }

    /**
     * @throws WebClientResponseException
     */
    fun updateDocument(
        documentId: String?,
        documentAsTemporaryFile: File?,
        accountId: String? = null,
    ): DocumentResponse? {
        val responseEntity: ResponseEntity<DocumentResponse>? =
            documentStorageApiDefault
                .updateDocumentWithHttpInfo(
                    DEFAULT_SP_ROLE_TYPE,
                    threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE,
                    documentId,
                    documentAsTemporaryFile,
                    accountId,
                ).block()
        logger.event(
            "document.update.storageplatform.queryparam",
            mapOf(
                "documentAsTemporaryFile" to documentAsTemporaryFile,
                "documentId" to documentId,
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to threadLocalUserName.get(),
            ),
        )
        return responseEntity?.body
    }

    /**
     * @throws WebClientResponseException
     */
    fun deleteDocument(
        documentId: String?,
        accountId: String? = null,
    ): Boolean {
        logger.event(
            "document.delete.storageplatform.queryparam",
            mapOf(
                "documentId" to documentId,
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to threadLocalUserName.get(),
            ),
        )
        val responseEntity: ResponseEntity<Void>? =
            documentStorageApiDefault.deleteDocumentWithHttpInfo(
                DEFAULT_SP_ROLE_TYPE,
                threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE,
                documentId,
                accountId,
                null,
            ).block()
        return responseEntity?.statusCode?.is2xxSuccessful ?: false
    }

    /**
     * Updates the document metadata based on the documentId and map provided
     * @param documentId - matches the document on Storage Platform using this
     * @param referenceData - mapOf name and value for the metadata. Check storage platform for Valid values.
     * @return DocumentResponse
     * @throws WebClientResponseException
     */
    fun updateDocumentMetaData(
        documentId: String,
        documentStatus: DocumentStatus,
        referenceData: MutableMap<String, String>,
        accountId: String? = null,
    ): DocumentResponse? {
        if (referenceData.isEmpty()) {
            throw IllegalArgumentException("referenceData can not be empty")
        }

        // this may be strange, but we need to pass those as empty string to make this metadata update work
        val updateDocumentRequest =
            UpdateDocumentRequest()
                .documentStatus(documentStatus.name)
                .documentName("")
                .reference(referenceData)

        logger.event(
            "document.metadata.storageplatform.update.request",
            mapOf(
                "updateRequest" to updateDocumentRequest.toString(),
            ),
        )
        val responseEntity =
            documentManagementApiDefault.updateDocumentDetailsWithHttpInfo(
                DEFAULT_SP_ROLE_TYPE,
                threadLocalUserName.get() ?: DEFAULT_SP_ROLE_TYPE,
                documentId,
                updateDocumentRequest,
                accountId,
            ).block()
        return responseEntity?.body
    }

    fun getPresignedUrl(
        documentId: String?,
        processingOrderId: Long?,
        customerId: Long?,
        accountId: String? = null,
        documentVersion: Int? = null,
    ): PreSignedResponse? {
        logger.event(
            "document.presigned.storageplatform.request",
            mapOf(
                "X_LZ_ROLE_TYPE" to DEFAULT_SP_ROLE_TYPE,
                "X_LZ_ROLE_ID" to DEFAULT_SP_ROLE_TYPE,
                "documentId" to documentId,
                "accountId" to accountId,
                "documentVersion" to documentVersion,
                "useDisposition" to false,
                "expirationInMins" to 10,
                "queryParameters" to null,
            ),
        )
        val responseEntity =
            documentStorageApiDefault.getPreSignedUrlToDownloadDocumentWithHttpInfo(
                DEFAULT_SP_ROLE_TYPE,
                DEFAULT_SP_ROLE_TYPE,
                documentId,
                accountId,
                documentVersion,
                false,
                10,
                null,
            ).blockSingle()

        logger.event(
            "document.presigned.storageplatform.request.complete",
            mapOf(
                "documentId" to documentId,
                "documentVersion" to documentVersion,
            ),
        )

        return responseEntity?.body
    }
}
