package com.legalzoom.fulfillment.service.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.commerce.OrderApi
import com.legalzoom.api.expertscollabplatform.WorkItemsApi
import com.legalzoom.api.model.answer.FieldAnswerResponseDto
import com.legalzoom.api.model.answer.FilingMappedDataResponse
import com.legalzoom.api.model.answer.SaveAnswerBankDto
import com.legalzoom.api.model.answer.SaveAnswerBankRequest
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerDto
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.commerce.OrderItemDto.StatusEnum
import com.legalzoom.api.model.expertscollabplatform.WorkItemPageDTO
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderDto
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.order.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.CompletedOrderDetailDto
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.order.OrdersOrderItemsApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.fulfillment.answersapi.model.AnswerSource.AnswerBank
import com.legalzoom.fulfillment.service.data.rpa.RPAValidationResult
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataComposite
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.core.io.ClassPathResource
import reactor.core.publisher.Mono
import java.util.UUID
import com.legalzoom.api.model.commerce.OrderItemDto as CP2OrderItemDto

@SpringBootTest
class FilingServiceTest(
    private val filingService: FilingService,
    private val objectMapper: ObjectMapper,
) {
    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    private lateinit var rPASchemaService: RpaSchemaService

    @MockkBean
    private lateinit var ordersApiService: OrdersApiService

    @MockkBean
    private lateinit var documentService: DocumentService

    @MockkBean
    private lateinit var ordersOrderItemsApi: OrdersOrderItemsApi

    @MockkBean
    private lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    private lateinit var orderService: OrderService

    @MockkBean
    private lateinit var workItemsApi: WorkItemsApi

    @MockkBean
    lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    lateinit var cp2OrderApi: OrderApi

    @BeforeEach
    fun init() {
        clearAllMocks()
    }

    @Test
    fun `find ALTM By Processing Order Id and check order present in WE`() {
        val answers = mockAltmAnswers()
        val mockOrder =
            OrderItemDto().apply {
                processingOrder = ProcessingOrderDto()
                processingOrder?.processingOrderId = 12345
                processingOrder?.processId = ProductType.ALTM.processId
            }

        every {
            orderService.getParentPackageOrderItem(any(), any())
        } returns mockOrder

        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns answers

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns emptyList()

        every {
            ordersApiService.getOrders(any(), null, null, null, null, null)
        } returns mockNonCancelledOrderResponse()

        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns RPAValidationResult(true, arrayListOf())

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
        } returns arrayListOf()

        every {
            workItemsApi.findAllWorkItems(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(mockWorkItemPageDTOResponse())
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(mockCp1CodResponse())
        every {
            orderService.getParentPackageOrderItem(any(), any())
        } returns mockOrderItemDto()
        every {
            ordersApiService.getOrders(any(), null, null, "1.0", any(), true)
        } returns mockNonCancelledOrderResponse()
        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(mockGetOrderItemsResponse())

        val result = filingService.getFilingData(12345, 150, "0", "ALTM", "FEDERAL", 1234)

        assertThat(result?.order?.expediteSpeed).isEqualTo("Standard")
        assertThat(result?.order?.trademark?.attorneyDocketNumber).isNotNull
    }

    @Test
    fun testFindByWorkOrderId() {
        val json = ClassPathResource("boirAnswers.json", javaClass).file
        val answers = objectMapper.readValue<JsonNode>(json)

        every {
            questionnaireAnswerService.getFilingDataForRPAByWorkOrder(any(), any(), any(), any(), any())
        } returns answers

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns emptyList()

        every {
            ordersApiService.getOrders(any(), null, null, null, null, null)
        } returns mockNonCancelledOrderResponse()

        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns RPAValidationResult(true, arrayListOf())

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
        } returns arrayListOf()
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(mockCp1CodResponse())
        every {
            orderService.getParentPackageOrderItem(any(), any())
        } returns mockOrderItemDto()
        every {
            ordersApiService.getOrders(any(), null, null, "1.0", any(), true)
        } returns mockNonCancelledOrderResponse()
        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(mockGetOrderItemsResponse())

        val result = filingService.getFilingData(1, 173, "0", "BOIR", "FEDERAL", 12345, "8ea1894a-8843-41dc-8f75-d699736acf51")

        assertThat(result?.order?.expediteSpeed).isEqualTo("Standard")
        assertThat(result?.order?.workOrderId).isNotNull
        assertThat(result?.order?.boirData?.businessName).isNotNull
    }

    @Test
    fun testFindByProcessingOrderId() {
        val answers = mockLlcAnswers()
        val mockOrder =
            OrderItemDto().apply {
                processingOrder = ProcessingOrderDto()
                processingOrder?.processingOrderId = 12345
                processingOrder?.processId = ProductType.LLC.processId
            }

        every {
            orderService.getParentPackageOrderItem(any(), any())
        } returns mockOrder

        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns answers

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns emptyList()

        every {
            ordersApiService.getOrders(any(), null, null, null, null, null)
        } returns mockNonCancelledOrderResponse()

        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns RPAValidationResult(true, arrayListOf())

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
        } returns arrayListOf()
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(mockCp1CodResponse())
        every {
            orderService.getParentPackageOrderItem(any(), any())
        } returns mockOrderItemDto()
        every {
            ordersApiService.getOrders(any(), null, null, "1.0", any(), true)
        } returns mockNonCancelledOrderResponse()
        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(mockGetOrderItemsResponse())

        val result = filingService.getFilingData(1, 2, "0", "LLC", "CA", 12345)

        assertThat(result?.order?.expediteSpeed).isEqualTo("Expedite")
        assertThat(result?.order?.company?.organizer).isNotNull
    }

    @Test
    fun testPreviewFilingData() {
        val processingOrderId = *********
        val orderDetails = GetProcessingOrderResponse()
        orderDetails.apply { processingOrder = com.legalzoom.api.model.processingorder.ProcessingOrderDto() }
        orderDetails.processingOrder!!.apply {
            customerId = 1
            processId = 1
            stateId = 41
        }
        val getOrderItemResponse =
            GetOrderItemResponse().apply {
                orderId = 35111656
                orderItem = OrderItemDto()
            }

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                any(),
                any(),
                true,
            )
        } returns Mono.just(orderDetails)

        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(getOrderItemResponse)

        val answers = mockLlcAnswers()

        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns answers

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns emptyList()

        every {
            ordersApiService.getOrders(any(), null, null, null, null, null)
        } returns mockNonCancelledOrderResponse()

        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns RPAValidationResult(true, arrayListOf())

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
        } returns arrayListOf()
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(mockCp1CodResponse())
        every {
            orderService.getParentPackageOrderItem(processingOrderId, any())
        } returns mockOrderItemDto()
        every {
            ordersApiService.getOrders(any(), null, null, "1.0", any(), true)
        } returns mockNonCancelledOrderResponse()

        val result = filingService.previewFilingData(processingOrderId)

        assertThat(result?.order?.expediteSpeed).isEqualTo("Expedite")
        assertThat(result?.order?.company?.organizer).isNotNull
    }

    @Test
    fun `Fetch parent answers with Attached EIN processingOrderId`() {
        val answers = mockLlcAnswers()

        val mockAttachedProcessingOrderId = *********
        val mockParentProcessingOrderId = *********
        val parentOrder =
            OrderItemDto().apply {
                processingOrder = ProcessingOrderDto()
                processingOrder?.processingOrderId = mockParentProcessingOrderId
                processingOrder?.processId = ProductType.LLC.processId
            }

        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns answers

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns emptyList()

        val orderResponse = mockNonCancelledOrderResponse()
        every {
            ordersApiService.getOrders(any(), null, null, null, null, null)
        } returns orderResponse

        every {
            orderService.getParentPackageOrderItem(any(), any())
        } returns parentOrder

        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns RPAValidationResult(true, arrayListOf())

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
        } returns arrayListOf()
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(mockCp1CodResponse())
        every {
            ordersApiService.getOrders(any(), null, null, "1.0", any(), true)
        } returns mockNonCancelledOrderResponse()
        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(mockGetOrderItemsResponse())

        filingService.getFilingData(mockAttachedProcessingOrderId, 49, "0", "EIN", "CA", 12345)

        verify {
            questionnaireAnswerService.getFilingDataForRPA(mockParentProcessingOrderId, ProductType.LLC.processId, any(), any())
        }
    }

    @Test
    fun `ssorco updates for answers - success`() {
        val testCustomerId = 1
        val processingOrderId = 123456
        val testUpdatedName = "updatedName"

        val saveAnswerBankRequest: SaveAnswerBankRequest =
            SaveAnswerBankRequest().apply {
                questionnaireFieldGroupAnswers =
                    SaveAnswerBankDto().apply {
                        this.processingOrderId = processingOrderId
                        createdBy = "FilingServiceTest"
                        questionireId = 1
                        isMajorRevision = false
                        fieldAnswers =
                            listOf(
                                FieldAnswerResponseDto().apply {
                                    optionId = 1
                                    fieldId = 1
                                    fieldName = "LLC_name"
                                    fieldValue = testUpdatedName
                                },
                            )
                    }
            }

        val orderDetails = GetProcessingOrderResponse()
        orderDetails.apply { processingOrder = com.legalzoom.api.model.processingorder.ProcessingOrderDto() }
        orderDetails.processingOrder!!.customerId(testCustomerId)

        val saveAnswerResponse =
            SaveAnswerComposite(
                SaveQuestionnaireAnswerResponse().apply {
                    saveAnswerResponse = SaveQuestionnaireAnswerDto().numberofRecordsAffected(1)
                },
            )

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                any(),
                any(),
                true,
            )
        } returns Mono.just(orderDetails)

        every {
            questionnaireAnswerService.saveSSOrcoUpdatedFields(testCustomerId.toString(), saveAnswerBankRequest)
        } returns saveAnswerResponse

        filingService.saveSSOrcoUpdatedFields(saveAnswerBankRequest)

        verify(exactly = 1) {
            questionnaireAnswerService.saveSSOrcoUpdatedFields(testCustomerId.toString(), saveAnswerBankRequest)
        }
    }

    @Test
    fun `ssorco updates for answers - failed`() {
        val testCustomerId = 1
        val processingOrderId = 123456
        val testUpdatedName = "updatedName"
        val saveAnswerBankRequest: SaveAnswerBankRequest =
            SaveAnswerBankRequest().apply {
                questionnaireFieldGroupAnswers =
                    SaveAnswerBankDto().apply {
                        this.processingOrderId = processingOrderId
                        createdBy = "FilingServiceTest"
                        questionireId = 1
                        isMajorRevision = false
                        fieldAnswers =
                            listOf(
                                FieldAnswerResponseDto().apply {
                                    optionId = 1
                                    fieldId = 1
                                    fieldName = "LLC_name"
                                    fieldValue = testUpdatedName
                                },
                            )
                    }
            }

        val orderDetails =
            GetProcessingOrderResponse().processingOrder(
                com.legalzoom.api.model.processingorder.ProcessingOrderDto().customerId(testCustomerId),
            )

        val saveAnswerResponse =
            SaveAnswerComposite(
                SaveQuestionnaireAnswerResponse().apply {
                    saveAnswerResponse = SaveQuestionnaireAnswerDto().numberofRecordsAffected(1)
                },
            )

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                processingOrderId,
                any(),
                any(),
                true,
            )
        } throws Exception()

        val result = filingService.saveSSOrcoUpdatedFields(saveAnswerBankRequest)

        verify(exactly = 0) {
            questionnaireAnswerService.saveSSOrcoUpdatedFields(any(), saveAnswerBankRequest)
        }

        assertEquals(0, result!!.numberOfRecordsAffected)
    }

    @Test
    fun `get filing data for CP2 order`() {
        // Arrange
        val processingOrderId = 1234
        val cp2OrderItemDto =
            CP2OrderItemDto().apply {
                status = StatusEnum.PLACED
            }
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(processingOrderId, null, null, null)
        } returns Mono.just(mockCp2CodResponse())
        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns mockLlcAnswers()
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any())
        } returns emptyList()
        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns mockRpaValidationResultPass()
        every {
            cp2OrderApi.getOrderItemById(any(), any())
        } returns Mono.just(cp2OrderItemDto)

        // Act
        val response = filingService.getFilingData(processingOrderId, ProductType.LLC.processId, "9876", "llc", "CO", 0, "")

        // Assert
        assertThat(response?.order).isNotNull
        assertTrue(response?.validationResult?.passed!!)
        // CP2 orders should not call getParentPackageOrderItem
        verify(exactly = 0) {
            orderService.getParentPackageOrderItem(processingOrderId, any())
        }
    }

    @Test
    fun `get filing data for a CP1 order`() {
        // Arrange
        val processingOrderId = 1234

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any())
        } returns emptyList()
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(mockCp1CodResponse())
        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(mockGetOrderItemsResponse())
        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns mockLlcAnswers()
        every {
            rPASchemaService.validateJSON(any(), any(), any())
        } returns mockRpaValidationResultPass()
        every {
            orderService.getParentPackageOrderItem(processingOrderId, any())
        } returns mockOrderItemDto()
        every {
            questionnaireAnswerService.getFilingDataForRPA(any(), any(), any(), any())
        } returns mockLlcAnswers()
        every {
            ordersApiService.getOrders(any(), null, null, "1.0", any(), true)
        } returns mockNonCancelledOrderResponse()

        // Act
        val response = filingService.getFilingData(processingOrderId, ProductType.LLC.processId, "9876", "llc", "CO", 0, "")

        // Assert
        assertThat(response?.order).isNotNull
        assertTrue(response?.validationResult?.passed!!)
        // CP1 orders should not call cp2OrderApi
        verify(exactly = 0) {
            cp2OrderApi.getOrderItemById(any(), any())
        }
    }

    private fun mockLlcAnswers(): FilingDataComposite {
        val json = ClassPathResource("answers.json", javaClass).file
        val responseAnswer = objectMapper.readValue<FilingMappedDataResponse>(json)
        val answers = FilingDataComposite(2, AnswerBank, responseAnswer, null)
        return answers
    }

    private fun mockAltmAnswers(): FilingDataComposite {
        val json = ClassPathResource("ALTM_answers.json", javaClass).file
        val responseAnswer = objectMapper.readValue<FilingMappedDataResponse>(json)
        val answers = FilingDataComposite(150, AnswerBank, responseAnswer, null)
        return answers
    }

    private fun mockWorkItemPageDTOResponse(): WorkItemPageDTO {
        val json = ClassPathResource("work_items_page_dto_we_response.json", javaClass).file
        return objectMapper.readValue<WorkItemPageDTO>(json)
    }

    private fun mockNonCancelledOrderResponse(): GetOrderResponse {
        val order = OrderDto()
        order.isCancelled = false
        val orderResponse = GetOrderResponse()
        orderResponse.order = order
        return orderResponse
    }

    private fun mockCp2CodResponse(): GetCompleteOrderDetailResponse {
        val codResponse =
            GetCompleteOrderDetailResponse().apply {
                completedOrderDetail =
                    CompletedOrderDetailDto().apply {
                        this.cP2OrderItemId(UUID.randomUUID().toString())
                    }
            }
        return codResponse
    }

    private fun mockCp1CodResponse(): GetCompleteOrderDetailResponse {
        val codResponse =
            GetCompleteOrderDetailResponse().apply {
                completedOrderDetail =
                    CompletedOrderDetailDto().apply {
                        this.cP2OrderItemId(null)
                    }
            }
        return codResponse
    }

    private fun mockGetOrderItemsResponse(): GetOrderItemResponse {
        return GetOrderItemResponse().apply {
            orderId = 98765
        }
    }

    private fun mockRpaValidationResultPass(): RPAValidationResult {
        return RPAValidationResult(
            passed = true,
            errors = emptyList(),
        )
    }

    private fun mockOrderItemDto(): OrderItemDto {
        val orderItemDto = OrderItemDto()
        val processingOrderDto =
            ProcessingOrderDto().apply {
                processId = 2468
                processingOrderId = 8642
            }
        orderItemDto.processingOrder = processingOrderDto
        return orderItemDto
    }
}
