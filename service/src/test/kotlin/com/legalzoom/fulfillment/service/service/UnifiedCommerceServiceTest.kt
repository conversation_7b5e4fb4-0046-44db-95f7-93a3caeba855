package com.legalzoom.fulfillment.service.service

import com.legalzoom.api.commerce.OrderApi
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.processingorder.CompletedOrderDetailDto
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.order.OrdersOrderItemsApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import reactor.core.publisher.Mono

@ExtendWith(MockKExtension::class)
class UnifiedCommerceServiceTest {
    @MockK
    private lateinit var cp1OrderApi: OrdersApiService

    @MockK
    private lateinit var cp1OrderItemsApi: OrdersOrderItemsApi

    @MockK
    private lateinit var cp2OrderApi: OrderApi

    @MockK
    private lateinit var codApi: CompletedOrderDetailApi

    @InjectMockKs
    private lateinit var unifiedCommerceService: UnifiedCommerceServiceImpl

    @Test
    fun `it should return a CP2 id if the order has a cp2OrderItemId`() {
        val cp2OrderItemId = "2468"
        val completedOrderDetailResponse =
            GetCompleteOrderDetailResponse().apply {
                completedOrderDetail =
                    CompletedOrderDetailDto().apply {
                        cP2OrderItemId(cp2OrderItemId)
                    }
            }
        every {
            codApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(completedOrderDetailResponse)
        assertTrue(unifiedCommerceService.getCommerceIdsByProcessingOrderId(1234).isForSystem(CommerceSystem.CP2))
    }

    @Test
    fun `it should return a CP1 id if the order does not have a cp2OrderItemId`() {
        val cp2OrderItemId = ""
        val completedOrderDetailResponse =
            GetCompleteOrderDetailResponse().apply {
                completedOrderDetail =
                    CompletedOrderDetailDto().apply {
                        cP2OrderItemId(cp2OrderItemId)
                    }
            }
        every {
            codApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(any(), any(), any(), any())
        } returns Mono.just(completedOrderDetailResponse)
        val getOrderItemResponse =
            GetOrderItemResponse().apply {
                orderId = 9876
            }
        every {
            cp1OrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(getOrderItemResponse)
        assertTrue(unifiedCommerceService.getCommerceIdsByProcessingOrderId(1234).isForSystem(CommerceSystem.CP1))
    }
}
