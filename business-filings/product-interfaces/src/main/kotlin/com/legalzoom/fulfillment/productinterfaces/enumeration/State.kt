package com.legalzoom.fulfillment.productinterfaces.enumeration

import com.legalzoom.fulfillment.productinterfaces.data.BotAddressDto
import com.legalzoom.fulfillment.productinterfaces.data.BotPersonInfoDto

enum class State(
    val id: Int,
    val displayName: String,
    val abbreviation: String,
    // TODO: Permanent source of truth? Should this be here or somewhere else?
    // https://docs.google.com/spreadsheets/d/1hMa8zTAZQH7vkc0sd6qRlygfQKbeVab6/edit?usp=sharing&ouid=105350546444647186498&rtpof=true&sd=true
    val lzRaInfo: BotPersonInfoDto? = null,
    val sos: String = "",
) {
    ALABAMA(51, "Alabama", "AL"),
    ALASKA(1, "Alaska", "AK"),
    ARIZONA(12, "Arizona", "AZ"),
    ARKANSAS(22, "Arkansas", "AR"),
    CALIFORNIA(5, "California", "CA"),
    COLORADO(11, "Colorado", "CO"),
    CONNECTICUT(27, "Connecticut", "CT"),
    DELAWARE(
        28,
        "Delaware",
        "DE",
        sos = "https://icis.corp.delaware.gov/ecorp/entitysearch/namesearch.aspx",
    ),
    DISTRICT_OF_COLUMBIA(29, "Dist. of Columbia", "DC"),
    FLORIDA(30, "Florida", "FL"),
    GEORGIA(31, "Georgia", "GA"),
    HAWAII(6, "Hawaii", "HI"),
    IDAHO(7, "Idaho", "ID"),
    ILLINOIS(
        26,
        "Illinois",
        "IL",
        sos = "https://apps.ilsos.gov/corporatellc/CorporateLlcController",
    ),
    INDIANA(17, "Indiana", "IN"),
    IOWA(33, "Iowa", "IA"),
    KANSAS(19, "Kansas", "KS"),
    KENTUCKY(32, "Kentucky", "KY"),
    LOUISIANA(23, "Louisiana", "LA"),
    MAINE(34, "Maine", "ME"),
    MARYLAND(35, "Maryland", "MD"),
    MASSACHUSETTS(36, "Massachusetts", "MA"),
    MICHIGAN(
        37,
        "Michigan",
        "MI",
        sos = "https://cofs.lara.state.mi.us/SearchApi/Search/Search",
    ),
    MINNESOTA(16, "Minnesota", "MN"),
    MISSISSIPPI(38, "Mississippi", "MS"),
    MISSOURI(24, "Missouri", "MO"),
    MONTANA(8, "Montana", "MT"),
    NEBRASKA(18, "Nebraska", "NE"),
    NEVADA(4, "Nevada", "NV"),
    NEW_HAMPSHIRE(39, "New Hampshire", "NH"),
    NEW_JERSEY(
        40,
        "New Jersey",
        "NJ",
        lzRaInfo =
            BotPersonInfoDto(
                firstName = "United States Corporation Agents, Inc",
                lastName = null,
                address =
                    BotAddressDto(
                        addressLine1 = "330 Changebridge Road, Suite 101",
                        city = "Pine Brook",
                        state = "NJ",
                        zipCode = "07058",
                    ),
                title = null,
                phone = null,
            ),
        sos = "https://www.njportal.com/DOR/BusinessNameSearch/Search/Availability",
    ),
    NEW_MEXICO(13, "New Mexico", "NM"),
    NEW_YORK(41, "New York", "NY"),
    NORTH_CAROLINA(42, "North Carolina", "NC"),
    NORTH_DAKOTA(14, "North Dakota", "ND"),
    OHIO(
        43,
        "Ohio",
        "OH",
        sos = "https://businesssearch.ohiosos.gov/",
    ),
    OKLAHOMA(20, "Oklahoma", "OK"),
    OREGON(3, "Oregon", "OR"),
    PENNSYLVANIA(44, "Pennsylvania", "PA"),
    RHODE_ISLAND(45, "Rhode Island", "RI"),
    SOUTH_CAROLINA(46, "South Carolina", "SC"),
    SOUTH_DAKOTA(15, "South Dakota", "SD"),
    TENNESSEE(47, "Tennessee", "TN"),
    TEXAS(21, "Texas", "TX"),
    UTAH(
        10,
        "Utah",
        "UT",
        sos = "https://secure.utah.gov/bes/index.html",
    ),
    VERMONT(48, "Vermont", "VT"),
    VIRGINIA(
        49,
        "Virginia",
        "VA",
        sos = "https://cis.scc.virginia.gov/EntitySearch/Index",
    ),
    WASHINGTON(2, "Washington", "WA"),
    WEST_VIRGINIA(50, "West Virginia", "WV"),
    WISCONSIN(25, "Wisconsin", "WI"),
    WYOMING(
        9,
        "Wyoming",
        "WY",
        sos = "https://wyobiz.wyo.gov/Business/FilingSearch.aspx",
    ),

    // Armed forces addresses, IDs match but the USPS abbreviaton
    // https://faq.usps.com/s/article/How-Do-I-Address-Military-Mail
    // See other State enum for source
    // https://github.legalzoom.com/engineering/api-business-entities-service/blob/3058c496652d5e3f7be10b56e68fe868f68c59fa/src/Common/LZ.Common.BusinessEntities/Enums/StateEnum.cs
    ARMED_FORCES_AFRICA(53, "Armed Forces Africa", "AE"), // Same abbv due to State enum implementation
    ARMED_FORCES_EUROPE(54, "Armed Forces Europe", "AE"),
    ARMED_FORCES_PACIFIC(55, "Armed Forces Pacific", "AP"),
    ARMED_FORCES_AMERICAS(52, "Armed Forces Americas", "AA"),

    PUERTO_RICO(61, "Puerto Rico", "PR"),
    AMERICAN_SAMOA(56, "American Samoa", "AS"),
    FEDERATED_STATES_OF_MICRONESIA(57, "Federated States of Micronesia", "FM"),
    GUAM(58, "Guam", "GU"),
    MARSHALL_ISLANDS(59, "Marshall Islands", "MH"),
    NORTHERN_MARIANA_ISLANDS(60, "Northern Mariana Islands", "MP"),
    PALAU(62, "Palau", "PW"),
    VIRGIN_ISLANDS(63, "Virgin Islands", "VI"),
    ;

    companion object {
        val asSet = enumValues<State>().toSet()

        fun allExcept(vararg states: State): Set<State> {
            return asSet - states.toSet()
        }

        fun fromAbbreviation(abbr: String): State? {
            return enumValues<State>().firstOrNull { it.abbreviation == abbr }
        }

        fun fromName(name: String): State? {
            return enumValues<State>().firstOrNull { it.displayName == name }
        }

        fun fromNameOrAbbre(nameOrAbbre: String): State? {
            return enumValues<State>().firstOrNull { it.displayName == nameOrAbbre || it.abbreviation == nameOrAbbre }
        }

        fun fromAbbreviationNotNull(nameOrAbbre: String): State =
            fromNameOrAbbre(nameOrAbbre)
                ?: throw NullPointerException("No state found for $nameOrAbbre")

        fun fromId(id: Int): State? {
            return enumValues<State>().firstOrNull { it.id == id }
        }
    }
}
