package com.legalzoom.fulfillment.productinterfaces.data

import com.legalzoom.api.model.dsd.printship.PrintShipJobShippingAddressDto
import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.model.ordercontacts.ContactType

fun List<ContactInfoDto>.getCustomerForFiling(): BotPersonInfoDto {
    val primaryContact =
        this.firstOrNull { it.contactType == ContactType.Primary }
            ?: throw IllegalArgumentException("No primary contact found")

    return BotPersonInfoDto(
        firstName = primaryContact.firstName,
        lastName = primaryContact.lastName,
        address =
            BotAddressDto(
                addressLine1 = primaryContact.addressLine1!!,
                addressLine2 = primaryContact.addressLine2,
                city = primaryContact.city!!,
                state = primaryContact.state!!,
                zipCode = primaryContact.zipCode!!,
            ),
        title = null,
        phone = primaryContact.mobilePhone ?: primaryContact.homePhone ?: primaryContact.workPhone,
    )
}

fun List<ContactInfoDto>.getPrintShippingAddress(): PrintShipJobShippingAddressDto {
    val shippingContact =
        this.firstOrNull { it.contactType == ContactType.Shipping }
            ?: throw IllegalArgumentException("No shipping contact found")

    return PrintShipJobShippingAddressDto()
        .name(shippingContact.firstName + " " + shippingContact.lastName)
        .addressLine1(shippingContact.addressLine1 + ", " + shippingContact.addressLine2)
}
