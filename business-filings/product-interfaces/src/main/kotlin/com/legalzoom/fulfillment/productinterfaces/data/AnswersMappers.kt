package com.legalzoom.fulfillment.productinterfaces.data

import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.BIZ_ADDRESS_CITY_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.BIZ_ADDRESS_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.BIZ_ADDRESS_L2_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.BIZ_ADDRESS_STATE_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.BIZ_ADDRESS_ZIP_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.DEFAULT_ENTITY_NAME_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.DEFAULT_ENTITY_TYPE_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.FILING_YEAR_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.LZ_RA_VALUE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MAIL_ADDRESS_CITY
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MAIL_ADDRESS_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MAIL_ADDRESS_STATE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MAIL_ADDRESS_ZIP
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MANAGER_ADDRESS_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MANAGER_CITY_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MANAGER_NAME_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MANAGER_STATE_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MANAGER_ZIP_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MEMBER_ADDRESS_CITY_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MEMBER_ADDRESS_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MEMBER_ADDRESS_STATE_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MEMBER_ADDRESS_ZIP_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MEMBER_NAME_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MULTI_MANAGER_NUM_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.MULTI_MEMBER_VALUE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.NUM_MEMBER_TYPE_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.RA_ADDRESS_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.RA_CITY_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.RA_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.RA_NAME_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.RA_STATE_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.RA_ZIP_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.SINGLE_MEMBER_VALUE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.SOS_NUMBER_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.STOCKHOLDERS_NUM_FIELD
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.STOCKHOLDER_ADDRESS_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.STOCKHOLDER_CITY_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.STOCKHOLDER_NAME_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.STOCKHOLDER_STATE_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.data.DefaultAnswerFieldNames.STOCKHOLDER_ZIP_FIELD_BASE
import com.legalzoom.fulfillment.productinterfaces.enumeration.State
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType

fun GetQuestionnaireAnswerResponse.getFieldAnswerValue(fieldName: String): String? {
    return this.questionnaireFieldGroupAnswers?.fieldAnswers?.firstOrNull {
        it.fieldName == fieldName
    }?.fieldValue
}

fun GetQuestionnaireAnswerResponse.getGroupAnswerValue(fieldName: String): String? {
    return this.questionnaireFieldGroupAnswers?.groupAnswers?.firstOrNull {
        it.fieldName == fieldName
    }?.fieldValue
}

/**
 * @receiver a string in "LastName, FirstName" format
 * @return Pair(lastName, firstName) separated
 */
private fun String.asLastNameFirstName(): Pair<String, String> {
    val split = this.split(", ")
    if (split.size != 2) {
        throw IllegalArgumentException("Name could not be parsed $this")
    }
    return Pair(split[0], split[1])
}

fun GetQuestionnaireAnswerResponse.getRegisteredAgent(state: State): BotPersonInfoDto {
    val registeredAgent = this.getFieldAnswerValue(RA_FIELD)

    if (registeredAgent == LZ_RA_VALUE) {
        return state.lzRaInfo!!
    }

    val (lastName, firstName) = this.getFieldAnswerValue(RA_NAME_FIELD)!!.asLastNameFirstName()

    return BotPersonInfoDto(
        firstName = firstName,
        lastName = lastName,
        address =
            BotAddressDto(
                addressLine1 = this.getFieldAnswerValue(RA_ADDRESS_FIELD)!!,
                city = this.getFieldAnswerValue(RA_CITY_FIELD)!!,
                state = this.getFieldAnswerValue(RA_STATE_FIELD)!!,
                zipCode = this.getFieldAnswerValue(RA_ZIP_FIELD)!!,
            ),
        title = null,
        phone = null,
    )
}

fun GetQuestionnaireAnswerResponse.getOfficers(): List<BotPersonInfoDto> {
    return when (this.getFieldAnswerValue(NUM_MEMBER_TYPE_FIELD)) {
        SINGLE_MEMBER_VALUE ->
            listOf(getSingleOfficer(this))

        MULTI_MEMBER_VALUE ->
            getMultipleOfficers(this)

        else -> throw IllegalStateException("Unsupported officers answers")
    }
}

private fun getSingleOfficer(answers: GetQuestionnaireAnswerResponse): BotPersonInfoDto {
    val (lastName, firstName) = answers.getFieldAnswerValue(MEMBER_NAME_FIELD)!!.asLastNameFirstName()

    return BotPersonInfoDto(
        firstName = firstName,
        lastName = lastName,
        address =
            BotAddressDto(
                addressLine1 = answers.getFieldAnswerValue(MEMBER_ADDRESS_FIELD)!!,
                city = answers.getFieldAnswerValue(MEMBER_ADDRESS_CITY_FIELD)!!,
                zipCode = answers.getFieldAnswerValue(MEMBER_ADDRESS_ZIP_FIELD)!!,
                state = answers.getFieldAnswerValue(MEMBER_ADDRESS_STATE_FIELD)!!,
            ),
        title = null,
        phone = null,
    )
}

private fun getMultipleOfficers(answers: GetQuestionnaireAnswerResponse): List<BotPersonInfoDto> {
    val officers = mutableListOf<BotPersonInfoDto>()

    // Managers
    answers.getFieldAnswerValue(MULTI_MANAGER_NUM_FIELD)?.let { count ->
        for (i in 1..(count.toInt())) {
            val (lastName, firstName) =
                answers
                    .getGroupAnswerValue(MANAGER_NAME_FIELD_BASE + i)!!
                    .asLastNameFirstName()

            officers.add(
                BotPersonInfoDto(
                    // TODO: Is this going to be LastName, FirstName and do we need to modify that?
                    // Can we update the questionnaire?
                    firstName = firstName,
                    lastName = lastName,
                    title = "MANAGER",
                    phone = null,
                    address =
                        BotAddressDto(
                            addressLine1 = answers.getGroupAnswerValue(MANAGER_ADDRESS_FIELD_BASE + i)!!,
                            city = answers.getGroupAnswerValue(MANAGER_CITY_FIELD_BASE + i)!!,
                            zipCode = answers.getGroupAnswerValue(MANAGER_ZIP_FIELD_BASE + i)!!,
                            state = answers.getGroupAnswerValue(MANAGER_STATE_FIELD_BASE + i)!!,
                        ),
                ),
            )
        }
    }

    // Stockholders
    // TODO: @Kelly - do we need stockholders as officers?
    answers.getFieldAnswerValue(STOCKHOLDERS_NUM_FIELD)?.let { count ->
        for (i in 1..(count.toInt())) {
            // TODO: add a business error here

            val (lastName, firstName) = answers.getGroupAnswerValue(STOCKHOLDER_NAME_FIELD_BASE + i)!!.asLastNameFirstName()

            officers.add(
                BotPersonInfoDto(
                    firstName = firstName,
                    lastName = lastName,
                    title = "STOCKHOLDER",
                    phone = null,
                    address =
                        BotAddressDto(
                            addressLine1 = answers.getGroupAnswerValue(STOCKHOLDER_ADDRESS_FIELD_BASE + i)!!,
                            city = answers.getGroupAnswerValue(STOCKHOLDER_CITY_FIELD_BASE + i)!!,
                            zipCode = answers.getGroupAnswerValue(STOCKHOLDER_ZIP_FIELD_BASE + i)!!,
                            state = answers.getGroupAnswerValue(STOCKHOLDER_STATE_FIELD_BASE + i)!!,
                        ),
                ),
            )
        }
    }

    // TODO: Phase 2+: Inc Officers (CEO, Secretary, Treasurer, CFO)

    return officers
}

/**
 * Principle address refers to the SOS-registered address for the business.
 *
 * @return the principle address used in bot filing
 */
fun GetQuestionnaireAnswerResponse.getPrincipleAddress(): BotAddressDto {
    return BotAddressDto(
        addressLine1 = this.getFieldAnswerValue(BIZ_ADDRESS_FIELD)!!,
        addressLine2 = this.getFieldAnswerValue(BIZ_ADDRESS_L2_FIELD),
        city = this.getFieldAnswerValue(BIZ_ADDRESS_CITY_FIELD)!!,
        state = State.fromNameOrAbbre(this.getFieldAnswerValue(BIZ_ADDRESS_STATE_FIELD)!!)!!.abbreviation,
        zipCode = this.getFieldAnswerValue(BIZ_ADDRESS_ZIP_FIELD)!!,
    )
}

/**
 * Main address refers to where the SOS should mail to.
 *
 * @return the main address used in bot filing
 */
fun GetQuestionnaireAnswerResponse.getMainAddress(): BotAddressDto {
    if (this.getFieldAnswerValue(MAIL_ADDRESS_FIELD) == null) {
        return this.getPrincipleAddress()
    }

    return BotAddressDto(
        addressLine1 = this.getFieldAnswerValue(MAIL_ADDRESS_FIELD)!!,
        city = this.getFieldAnswerValue(MAIL_ADDRESS_CITY)!!,
        state = this.getFieldAnswerValue(MAIL_ADDRESS_STATE)!!,
        zipCode = this.getFieldAnswerValue(MAIL_ADDRESS_ZIP)!!,
    )
}

fun GetQuestionnaireAnswerResponse.getEntityType(): AnswersEntityType =
    AnswersEntityType.fromLabel(this.getFieldAnswerValue(DEFAULT_ENTITY_TYPE_FIELD))
        ?: throw IllegalArgumentException("Invalid entity type in answers")

fun GetQuestionnaireAnswerResponse.getEntityName(): String {
    val rawEntityName = this.getFieldAnswerValue(DEFAULT_ENTITY_NAME_FIELD)

    if (rawEntityName.isNullOrEmpty()) {
        throw IllegalArgumentException("Missing entity name in answers")
    }

    return rawEntityName
}

fun GetQuestionnaireAnswerResponse.getEntityId(): String =
    this.getFieldAnswerValue(SOS_NUMBER_FIELD)
        ?: throw IllegalArgumentException("Missing entity id in answers")

fun GetQuestionnaireAnswerResponse.getFilingYear(): String =
    this.getFieldAnswerValue(FILING_YEAR_FIELD)
        ?: throw IllegalArgumentException("Missing filing year in answers")
