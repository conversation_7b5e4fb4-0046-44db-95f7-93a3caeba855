package com.legalzoom.fulfillment.productinterfaces.data

object DefaultAnswerFieldNames {
    const val LZ_RA_VALUE = "<PERSON><PERSON><PERSON> serves as my registered agent."

    const val RA_FIELD = "registered_agent"
    const val RA_NAME_FIELD = "registered_agent_2"
    const val RA_EMAIL_FIELD = "registered_agent_email"
    const val RA_ADDRESS_FIELD = "registered_agent_3"
    const val RA_CITY_FIELD = "registered_agent_4"
    const val RA_ZIP_FIELD = "registered_agent_5"
    const val RA_STATE_FIELD = "registered_agent_6"

    const val NUM_MEMBER_TYPE_FIELD = "member_of_LLC_MC"
    const val SINGLE_MEMBER_VALUE = "One member"

    const val MEMBER_NAME_FIELD = "member_info_name"
    const val MEMBER_ADDRESS_FIELD = "member_info_street_address"
    const val MEMBER_ADDRESS_CITY_FIELD = "member_info_city"
    const val MEMBER_ADDRESS_STATE_FIELD = "member_info_state_MC"
    const val MEMBER_ADDRESS_ZIP_FIELD = "member_info_zip"

    const val MULTI_MEMBER_VALUE = "Two or more members"
    const val MANAGEMENT_TYPE_FIELD = "MM_Management_of_LLC_MC"

    // For multiple, member-managed
    const val MEMBER_MANAGED_VALUE = "By all members (member-managed)"
    const val STOCKHOLDERS_NUM_FIELD = "stockholders"
    const val STOCKHOLDER_NAME_FIELD_BASE = "stockholder_name_"
    const val STOCKHOLDER_ADDRESS_FIELD_BASE = "stockholder_st_"
    const val STOCKHOLDER_CITY_FIELD_BASE = "stockholder_city_"
    const val STOCKHOLDER_STATE_FIELD_BASE = "stockholder_state_"
    const val STOCKHOLDER_ZIP_FIELD_BASE = "stockholder_zip_"
    const val STOCKHOLDER_PERCENTAGE_FIELD_BASE = "stockholder_member_info_ownership_percentage"

    // For multiple, ?manager-managed?
    const val MULTI_MANAGER_NUM_FIELD = "managers2_info"
    const val MANAGER_NAME_FIELD_BASE = "managers2_info_name_"
    const val MANAGER_ADDRESS_FIELD_BASE = "managers2_info_street_"
    const val MANAGER_CITY_FIELD_BASE = "managers2_info_city_"
    const val MANAGER_STATE_FIELD_BASE = "managers2_info_state_"
    const val MANAGER_ZIP_FIELD_BASE = "managers2_info_zip_"

    const val BIZ_ADDRESS_FIELD = "business_address"
    const val BIZ_ADDRESS_L2_FIELD = "business_address_apt"
    const val BIZ_ADDRESS_CITY_FIELD = "business_city_state_zip"
    const val BIZ_ADDRESS_STATE_FIELD = "business_state"
    const val BIZ_ADDRESS_ZIP_FIELD = "business_zip"

    const val MAIL_ADDRESS_FIELD = "mailing_address"
    const val MAIL_ADDRESS_CITY = "mailing_city"
    const val MAIL_ADDRESS_STATE = "mailing_state"
    const val MAIL_ADDRESS_ZIP = "mailing_zip"

    const val FILING_YEAR_FIELD = "filing_year"

    const val DEFAULT_ENTITY_NAME_FIELD = "Company_name"

    const val DEFAULT_ENTITY_TYPE_FIELD = "Type_of_entity"

    const val SOS_NUMBER_FIELD = "SOS_number"
}
