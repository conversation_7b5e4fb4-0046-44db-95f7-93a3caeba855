package com.legalzoom.fulfillment.annualreports.workflow.listener

import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsFilingStateConfig
import com.legalzoom.fulfillment.annualreports.service.ComplianceAnswerService
import com.legalzoom.fulfillment.annualreports.workflow.defaultCorrelationMap
import com.legalzoom.fulfillment.annualreports.workflow.input
import com.legalzoom.fulfillment.annualreports.workflow.output
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.productinterfaces.data.getEntityType
import com.legalzoom.fulfillment.productinterfaces.enumeration.requiresHoldArea
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.ExecutionListener
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.config.BeanDefinition.SCOPE_PROTOTYPE
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

@Component
@Scope(SCOPE_PROTOTYPE)
class HoldAreaListener(
    private val complianceAnswerService: ComplianceAnswerService,
) : ExecutionListener {
    @WithSpan
    override fun notify(execution: DelegateExecution) {
        val input = execution.input
        val entityType =
            complianceAnswerService.getAnswersForPackage(
                input.orderId,
                input.customerId,
            ).getEntityType()
        val typeOfFiling = AnnualReportsFilingStateConfig.determineFilingType(execution.input.state, entityType)

        logger.event(
            "compliance.annualReports.holdAreaListener",
            execution.defaultCorrelationMap.plus(
                mapOf(
                    "processInstanceId" to execution.processInstanceId,
                    "entityType" to entityType,
                    "typeOfFiling" to typeOfFiling,
                    "requiresHoldArea" to typeOfFiling.requiresHoldArea(),
                ),
            ),
        )

        execution.output {
            useHoldArea = typeOfFiling.requiresHoldArea()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(HoldAreaListener::class.java)
    }
}
