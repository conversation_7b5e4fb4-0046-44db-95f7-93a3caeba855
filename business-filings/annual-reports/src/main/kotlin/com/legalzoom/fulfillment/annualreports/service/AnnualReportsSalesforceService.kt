package com.legalzoom.fulfillment.annualreports.service

import com.legalzoom.api.model.fulfillment.CompleteTaskRequest
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsFilingStateConfig
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsMessageRefs
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsSalesforceQueues
import com.legalzoom.fulfillment.annualreports.configuration.Constants
import com.legalzoom.fulfillment.annualreports.workflow.AnnualReportsVariables
import com.legalzoom.fulfillment.annualreports.workflow.ArVerifyPostHoldAreaDelegate
import com.legalzoom.fulfillment.annualreports.workflow.annualReportsVariables
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.productinterfaces.enumeration.requiresHoldArea
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class AnnualReportsSalesforceService(
    private val annualReportsVerificationService: AnnualReportsVerificationService,
    private val runtimeService: RuntimeService,
) {
    fun resolveCaseCompletion(
        processInstance: ProcessInstance,
        completeTaskRequest: CompleteTaskRequest,
    ): String? {
        // TODO: DSD-937 - Improve factory methods to resolve each queue
        val sfQueue = getCurrentQueue(processInstance.id)
        return when (sfQueue) {
            AnnualReportsSalesforceQueues.PRE_FILING -> validateAndResolvePreFiling(processInstance, completeTaskRequest)
            AnnualReportsSalesforceQueues.POST_FILING -> validateAndResolvePostFiling(processInstance, completeTaskRequest)
            AnnualReportsSalesforceQueues.PRINT_EXCEPTION -> validateAndResolvePrintException(processInstance, completeTaskRequest)
        }
    }

    private fun validateAndResolvePreFiling(
        processInstance: ProcessInstance,
        completeTaskRequest: CompleteTaskRequest,
    ): String? {
        val newVariables =
            annualReportsVariables {
                this.filingSubmitted = false
                this.filingSuccessful = false
                this.caseDisposition = completeTaskRequest.disposition
                this.errorMessage = null
            }

        var error: String? = null

        if (isWaitingForPreHoldAreaVerification(processInstance)) {
            logger.event(
                "compliance.annualreports.salesforce.preHoldAreaVerification",
                mapOf(
                    "processInstanceId" to processInstance.id,
                    "processingOrderId" to processInstance.businessKey,
                    "disposition" to completeTaskRequest.disposition,
                    "queue" to AnnualReportsSalesforceQueues.PRE_FILING.name,
                ),
            )

            error = annualReportsVerificationService.instantFilingVerification()
            if (error == null) {
                newVariables.filingSubmitted = true
            }
        } else if (isWaitingForInstantFileVerification(processInstance)) {
            logger.event(
                "compliance.annualreports.salesforce.instantFileVerification",
                mapOf(
                    "processInstanceId" to processInstance.id,
                    "processingOrderId" to processInstance.businessKey,
                    "disposition" to completeTaskRequest.disposition,
                    "queue" to AnnualReportsSalesforceQueues.PRE_FILING.name,
                ),
            )

            error = annualReportsVerificationService.preHoldAreaVerification()
            if (error == null) {
                newVariables.filingSubmitted = true
                newVariables.filingSuccessful = true
            }
        } else {
            throw IllegalArgumentException("Order is in an invalid state for pre-filing verification")
        }

        if (error.isNullOrEmpty()) {
            completeCase(processInstance, newVariables)
            return null
        } else {
            return error
        }
    }

    private fun isWaitingForPreHoldAreaVerification(processInstance: ProcessInstance): Boolean {
        val variables = getVariables(processInstance)
        return !(variables.filingSubmitted ?: false) &&
            AnnualReportsFilingStateConfig.determineFilingType(variables.state).requiresHoldArea()
    }

    private fun isWaitingForInstantFileVerification(processInstance: ProcessInstance): Boolean {
        val variables = getVariables(processInstance)
        return !AnnualReportsFilingStateConfig.determineFilingType(variables.state).requiresHoldArea() &&
            !(variables.filingSubmitted ?: false) &&
            !(variables.filingSuccessful ?: false)
    }

    private fun validateAndResolvePostFiling(
        processInstance: ProcessInstance,
        completeTaskRequest: CompleteTaskRequest,
    ): String? {
        // TODO

        logger.event(
            "compliance.annualreports.salesforce.postFilingVerification",
            mapOf(
                "processInstanceId" to processInstance.id,
                "processingOrderId" to processInstance.businessKey,
                "disposition" to completeTaskRequest.disposition,
                "queue" to AnnualReportsSalesforceQueues.POST_FILING.name,
            ),
        )
        completeCase(processInstance, completeTaskRequest)
        return null
    }

    private fun validateAndResolvePrintException(
        processInstance: ProcessInstance,
        completeTaskRequest: CompleteTaskRequest,
    ): String? {
        // TODO

        logger.event(
            "compliance.annualreports.salesforce.printException",
            mapOf(
                "processInstanceId" to processInstance.id,
                "processingOrderId" to processInstance.businessKey,
                "disposition" to completeTaskRequest.disposition,
                "queue" to AnnualReportsSalesforceQueues.PRINT_EXCEPTION.name,
            ),
        )
        completeCase(processInstance, completeTaskRequest)
        return null
    }

    fun completeCase(
        processInstance: ProcessInstance,
        completeTaskRequest: CompleteTaskRequest,
    ) {
        completeCase(
            processInstance,
            annualReportsVariables {
                this.caseDisposition = completeTaskRequest.disposition
                this.errorMessage = null
            },
        )
    }

    private fun completeCase(
        processInstance: ProcessInstance,
        variables: AnnualReportsVariables,
    ) {
        logger.event(
            "compliance.annualreports.salesforce.completeCase",
            mapOf(
                "disposition" to variables.caseDisposition,
                "processInstanceId" to processInstance.id,
                "processingOrderId" to processInstance.businessKey,
                "queue" to getCurrentQueue(processInstance.id).name,
            ),
        )

        runtimeService.correlateMessage(
            AnnualReportsMessageRefs.SALESFORCE,
            processInstance.businessKey,
            variables,
        )
    }

    private fun getCurrentQueue(processInstanceId: String): AnnualReportsSalesforceQueues {
        val queueVar =
            runtimeService.createVariableInstanceQuery()
                .processInstanceIdIn(processInstanceId)
                .variableName("sfQueue")
                .singleResult()

        return AnnualReportsSalesforceQueues.valueOf(queueVar.value as String)
    }

    private fun getVariables(processInstance: ProcessInstance): AnnualReportsVariables {
        val execution =
            runtimeService.createExecutionQuery()
                .activityId(Constants.COMPLIANCE_SALESFORCE_COMPLETE_ACTIVITY_ID)
                .processInstanceId(processInstance.id)
                .singleResult()

        return AnnualReportsVariables(runtimeService.getVariables(execution.id))
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArVerifyPostHoldAreaDelegate::class.java)
    }
}
