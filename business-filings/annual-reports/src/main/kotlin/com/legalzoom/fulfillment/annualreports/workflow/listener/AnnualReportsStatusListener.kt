package com.legalzoom.fulfillment.annualreports.workflow.listener

import com.legalzoom.api.model.notificationsplatform.TargetChannel
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsProcessingStatuses
import com.legalzoom.fulfillment.annualreports.mappers.createSOSNotificationRequest
import com.legalzoom.fulfillment.annualreports.service.AnnualReportsStatusService
import com.legalzoom.fulfillment.annualreports.workflow.defaultCorrelationMap
import com.legalzoom.fulfillment.annualreports.workflow.input
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.service.service.NotificationsPlatformService
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.ExecutionListener
import org.camunda.bpm.engine.delegate.Expression
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.config.BeanDefinition.SCOPE_PROTOTYPE
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

@Component
@Scope(SCOPE_PROTOTYPE)
class AnnualReportsStatusListener(
    private val annualReportsStatusService: AnnualReportsStatusService,
    private val notificationsPlatformService: NotificationsPlatformService,
) : ExecutionListener {
    var processingStatus: Expression? = null

    @WithSpan
    override fun notify(execution: DelegateExecution) {
        val rawStatus = processingStatus?.getValue(execution).toString()
        val status =
            AnnualReportsProcessingStatuses
                .from(rawStatus)

        if (status == null) {
            logger.errorEvent("compliance.annualReportsStatusListener.invalidStatus")
            execution.defaultCorrelationMap.plus(
                "processingStatus" to rawStatus,
            )
            throw IllegalArgumentException(
                "Invalid processing status: $rawStatus",
            )
        }

        logger.event(
            "compliance.annualReportsStatusListener.notify",
            execution.defaultCorrelationMap.plus(
                mapOf(
                    "processingStatus" to status.name,
                    "processingStatusId" to status.processingStatusId,
                ),
            ),
        )
        annualReportsStatusService.updateStatus(
            input = execution.input,
            status = status,
        )

        // idempotency note - it's intentional that we do this AFTER (not before) making the API call to update the status.  By being here,
        // if the API call to change the status fails, we won't spam the customer.  Conversely, if the call to send the notification fails,
        // it's fine that the listener will execute again resulting in repeat calls to the status API.

        if (status == AnnualReportsProcessingStatuses.AnnualReportsStateFilingComplete) {
            notificationsPlatformService.sendNotification(
                execution.input.createSOSNotificationRequest(),
                channelType = TargetChannel.ChannelTypeEnum.SMS,
            )
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AnnualReportsStatusListener::class.java)
    }
}
