package com.legalzoom.fulfillment.annualreports.workflow.delegate

import com.legalzoom.api.dsd.webautomation.RpaBotsApi
import com.legalzoom.fulfillment.annualreports.bots.NjLlcBotInput
import com.legalzoom.fulfillment.annualreports.service.AnnualReportsDataService
import com.legalzoom.fulfillment.annualreports.workflow.input
import com.legalzoom.fulfillment.annualreports.workflow.output
import com.legalzoom.fulfillment.productinterfaces.enumeration.State
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.springframework.stereotype.Service

@Service
class AnnualReportsQueueBotJob(
    val dsdWebAutomationClient: RpaBotsApi,
    val annualReportsDataService: AnnualReportsDataService,
    val njLlcBotInput: NjLlcBotInput,
) : JavaDelegate {
    @WithSpan
    override fun execute(execution: DelegateExecution) {
        // todo: once we have more than one bot, introduce an interface with descriminator / factory.  I don't want to
        // do that yet because I'm not clear on how similar the payloads are across states.  Maybe it'll make sense
        // to have a base class don't know.

        val dto =
            njLlcBotInput.createBotJobDto(
                execution.input.processingOrderId,
                execution.input.orderId,
                execution.input.customerId,
                execution.input.accountId,
                State.fromAbbreviationNotNull(execution.input.state),
            )

        val response = dsdWebAutomationClient.startBot(dto).block()!!

        execution.output {
            dsdWebAutomationJobId = response.id
        }
    }
}
