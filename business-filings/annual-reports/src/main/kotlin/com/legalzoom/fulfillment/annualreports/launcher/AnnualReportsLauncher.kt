package com.legalzoom.fulfillment.annualreports.launcher

import com.legalzoom.fulfillment.annualreports.configuration.Constants.AR_PROCESS_ID
import com.legalzoom.fulfillment.annualreports.configuration.ProcessDefinitionKeys
import com.legalzoom.fulfillment.annualreports.service.ComplianceAnswerService
import com.legalzoom.fulfillment.annualreports.workflow.AnnualReportsVariables
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.productinterfaces.launcher.LauncherInterface
import com.legalzoom.fulfillment.productinterfaces.launcher.ProductLaunchMessage
import com.legalzoom.fulfillment.service.service.AccountService
import org.camunda.bpm.engine.HistoryService
import org.camunda.bpm.engine.RuntimeService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.core.env.Environment
import org.springframework.stereotype.Component

@Component
class AnnualReportsLauncher(
    private val runtimeService: RuntimeService,
    private val historyService: HistoryService,
    private val environment: Environment,
    private val accountService: AccountService,
    private val complianceAnswerService: ComplianceAnswerService,
) : LauncherInterface {
    override fun getProcessIdDiscriminator(): Int = AR_PROCESS_ID

    override fun launch(order: ProductLaunchMessage): String {
        // TODO: Remove before launch - enables re-using dev orders easily
        if (environment.activeProfiles.contains("production") &&
            (hasRunningInstance(order) || hasHistoricInstances(order))
        ) {
            logger.errorEvent("compliance.annualreports.launcher.existingInstances")
            throw IllegalStateException("Order is already launched or has historic instances")
        }

        val variables = populateVariables(order)

        val id = launchWorkflow(order.processingOrderId.toString(), variables)
        if (id == null) {
            logger.errorEvent("compliance.annualreports.launcher.launchFailed")
            throw IllegalStateException("Failed to launch workflow")
        }

        // TODO: FulfillmentEvent/"Compliance"Event for journal events

        return id
    }

    private fun populateVariables(order: ProductLaunchMessage): AnnualReportsVariables {
        val accountId =
            accountService.getAccountIdFromCustomerId(
                order.customerId,
                order.processingOrderId.toString(),
                AR_PROCESS_ID,
            ).toString()

        return AnnualReportsVariables().apply {
            this.accountId = accountId
            customerId = order.customerId
            orderId = order.orderId
            processingOrderId = order.processingOrderId
            processId = order.processId
            state = order.state
            cp2OrderItemId = order.cp2OrderItemId
        }
    }

    private fun launchWorkflow(
        businessKey: String,
        variables: AnnualReportsVariables,
    ): String? {
        logger.event("compliance.annualreports.orderLaunched")

        val processInstance =
            runtimeService.startProcessInstanceByKey(
                ProcessDefinitionKeys.ANNUAL_REPORTS,
                businessKey,
                variables,
            )

        return processInstance.id
    }

    private fun hasRunningInstance(order: ProductLaunchMessage): Boolean {
        val runningInstances =
            runtimeService.createProcessInstanceQuery()
                .processDefinitionKey(ProcessDefinitionKeys.ANNUAL_REPORTS)
                .processInstanceBusinessKey(order.processingOrderId.toString()).list()
        // TODO: We may want to check variables further, but I assume we never want 2 instances of AR
        return runningInstances.isNotEmpty()
    }

    private fun hasHistoricInstances(order: ProductLaunchMessage): Boolean {
        val historicInstances =
            historyService.createHistoricProcessInstanceQuery()
                .processDefinitionKey(ProcessDefinitionKeys.ANNUAL_REPORTS)
                .processInstanceBusinessKey(order.processingOrderId.toString()).list()

        return historicInstances.isNotEmpty()
    }

    companion object {
        val logger: Logger = LoggerFactory.getLogger(AnnualReportsLauncher::class.java)
    }
}
