package com.legalzoom.fulfillment.annualreports.workflow.listener

import com.legalzoom.fulfillment.annualreports.domain.FilingMethod
import com.legalzoom.fulfillment.annualreports.service.ComplianceAnswerService
import com.legalzoom.fulfillment.annualreports.service.ComplianceFeatureToggleService
import com.legalzoom.fulfillment.annualreports.workflow.input
import com.legalzoom.fulfillment.annualreports.workflow.output
import com.legalzoom.fulfillment.domain.model.OrderExpiryNotification_.customerId
import com.legalzoom.fulfillment.productinterfaces.data.getEntityType
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.ExecutionListener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class AnnualReportsDetermineFilingMethod(
    private val featureToggleService: ComplianceFeatureToggleService,
    private val complianceAnswerService: ComplianceAnswerService,
) : ExecutionListener {
    @WithSpan
    override fun notify(execution: DelegateExecution) {
        val botEnabled =
            featureToggleService.botEnabled(
                processId = execution.input.processId,
                jurisdiction = execution.input.state,
                entityType =
                    complianceAnswerService.getAnswersForPackage(
                        execution.input.orderId,
                        execution.input.customerId,
                    ).getEntityType()
                        .processId,
            )

        execution.output {
            filingMethodOrdinal =
                if (botEnabled) {
                    FilingMethod.Bot.ordinalValue
                } else {
                    FilingMethod.Manual.ordinalValue
                }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AnnualReportsDetermineFilingMethod::class.java)
    }
}
