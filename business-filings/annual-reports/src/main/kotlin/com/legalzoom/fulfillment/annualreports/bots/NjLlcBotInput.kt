package com.legalzoom.fulfillment.annualreports.bots

import com.legalzoom.api.model.dsd.webautomation.StartBotRequestDto
import com.legalzoom.api.model.dsd.webautomation.WebAutomationReferenceIdsDto
import com.legalzoom.fulfillment.annualreports.configuration.Constants.AR_PROCESS_ID
import com.legalzoom.fulfillment.annualreports.service.AnnualReportsDataService
import com.legalzoom.fulfillment.annualreports.service.ComplianceAnswerService
import com.legalzoom.fulfillment.annualreports.service.ComplianceFeatureToggleService
import com.legalzoom.fulfillment.domain.enumeration.EntityType
import com.legalzoom.fulfillment.productinterfaces.data.getCustomerForFiling
import com.legalzoom.fulfillment.productinterfaces.data.getEntityId
import com.legalzoom.fulfillment.productinterfaces.data.getEntityName
import com.legalzoom.fulfillment.productinterfaces.data.getFilingYear
import com.legalzoom.fulfillment.productinterfaces.data.getMainAddress
import com.legalzoom.fulfillment.productinterfaces.data.getOfficers
import com.legalzoom.fulfillment.productinterfaces.data.getPrincipleAddress
import com.legalzoom.fulfillment.productinterfaces.data.getRegisteredAgent
import com.legalzoom.fulfillment.productinterfaces.enumeration.State
import com.legalzoom.fulfillment.service.service.OrderContactsService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class NjLlcBotInput(
    private val complianceFeatureToggleService: ComplianceFeatureToggleService,
    private val annualReportsDataService: AnnualReportsDataService,
    private val complianceAnswerService: ComplianceAnswerService,
    private val orderContactsService: OrderContactsService,
) {
    fun createBotJobDto(
        processingOrderId: Int,
        orderId: Int,
        customerId: String,
        accountId: String,
        state: State,
    ): StartBotRequestDto {
        // As of 5/16/2024 the NJ bot is still under development and not ready to call but we want
        // to get further through the order.  However at times we'll want to test the NJ bot.  So
        // let's temporarily base the bot to run on a feature flag that we can easily change in LD.
        // There's a test bot TEST_BOT that we can reliably get through.

        val botName =
            complianceFeatureToggleService.botNameOverride(
                AR_PROCESS_ID,
                "NJ",
                EntityType.LLC.ordinalValue,
                defaultValue = "AR_NJ",
            )

        return StartBotRequestDto().apply {
            this.priority = StartBotRequestDto.PriorityEnum.NORMAL
            this.botName = botName
            this.accountId = accountId
            this.referenceIds =
                WebAutomationReferenceIdsDto().apply {
                    this.processingOrderId = processingOrderId.toString()
                    this.cp1OrderId = orderId.toString()
                }
            this.payload = createPayload(processingOrderId, orderId, customerId, accountId, state)
        }
    }

    private fun createPayload(
        processingOrderId: Int,
        orderId: Int,
        customerId: String,
        accountId: String,
        state: State,
    ): Map<String, Any> {
        val answers = complianceAnswerService.getAnswersForPackage(orderId, customerId)
        val contacts = orderContactsService.getOrderContactsByOrderId(orderId, customerId)

        val payload =
            mapOf(
                "processingOrderId" to processingOrderId,
                "accountId" to accountId,
                "orderId" to orderId,
                "customerId" to customerId,
                "principleAddress" to answers.getPrincipleAddress(),
                "mainAddress" to answers.getMainAddress(),
                "customer" to contacts.getCustomerForFiling(),
                "officers" to answers.getOfficers(),
                "entityId" to answers.getEntityId(),
                "entityName" to answers.getEntityName(),
                "filingYear" to answers.getFilingYear(),
                "registeredAgent" to answers.getRegisteredAgent(state),
            )

        return payload
    }

    companion object {
        private val logger = LoggerFactory.getLogger(NjLlcBotInput::class.java)
    }
}
