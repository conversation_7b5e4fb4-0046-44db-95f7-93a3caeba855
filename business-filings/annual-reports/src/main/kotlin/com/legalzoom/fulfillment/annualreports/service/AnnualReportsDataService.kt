package com.legalzoom.fulfillment.annualreports.service

import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailRequest
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.fulfillment.annualreports.configuration.Constants
import com.legalzoom.fulfillment.common.exception.RetryableException
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.productinterfaces.data.getEntityName
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.service.DocumentAutomationService
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.time.Clock

/**
 * Used to modify data for Annual reports orders
 */
@Service
class AnnualReportsDataService(
    private val complianceAnswerService: ComplianceAnswerService,
    private val completedOrderDetailApi: CompletedOrderDetailApi,
    private val documentAutomationService: DocumentAutomationService,
    private val complianceNotificationService: ComplianceNotificationService,
    private val clock: Clock,
) {
    /**
     * TODO: Implement right before docgen
     * 1. Grabs the latest entityName from the questionnaire,
     * 2. Saves to COD and fieldAnswers
     * 3. Packsuitcase job in document automation?
     * 4. Emit notifications that the field was updated (FulfillmentEvent)
     */
    fun saveLatestEntityNameAndPackSuitcase(
        executionId: String,
        processingOrderId: Int,
        orderId: Int,
        customerId: String,
    ) {
        val entityName =
            complianceAnswerService.getAnswersForPackage(orderId, customerId)
                .getEntityName()

        saveEntityNameToCOD(processingOrderId, entityName)
        complianceAnswerService.saveEntityNameFieldAnswers(processingOrderId, customerId, entityName, AR_QUESTIONNAIRE_ID)

        // TODO: Still needed?
        documentAutomationService.packSuiteCase(processingOrderId)

        notifyEntityNameFieldUpdated(executionId, processingOrderId, orderId, customerId)
    }

    private fun notifyEntityNameFieldUpdated(
        executionId: String,
        processingOrderId: Int,
        orderId: Int,
        customerId: String,
    ) {
        // TODO: Do we still want this and what eventPhase?
        val event =
            FulfillmentEvent(
                eventType = EventType.FIELD_UPDATED,
                eventPhase = EventPhase.ANNUAL_REPORTS,
                customerId = customerId,
                orderId = orderId.toString(),
                workOrderId = null,
                executionId = executionId,
                processingOrderId = processingOrderId.toString(),
                processId = Constants.AR_PROCESS_ID.toString(),
                timestamp = clock.instant(),
                data =
                    mapOf(
                        "processingOrderId" to processingOrderId,
                        "processId" to Constants.AR_PROCESS_ID,
                        "customerId" to customerId,
                        "fieldName" to DEFAULT_ENTITY_NAME_FIELD,
                    ),
            )
        complianceNotificationService.emitFulfillmentEvent(event)
    }

    private fun saveEntityNameToCOD(
        processingOrderId: Int,
        entityName: String,
    ) {
        try {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                processingOrderId,
                UpdateCompletedOrderDetailRequest().entityName(entityName),
                "1.0",
                "",
                false,
            ).blockSingle()
        } catch (wcre: WebClientResponseException) {
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AnnualReportsDataService::class.java)
        private const val DEFAULT_ENTITY_NAME_FIELD = "Company_name"

        const val AR_QUESTIONNAIRE_ID = 309
    }
}
