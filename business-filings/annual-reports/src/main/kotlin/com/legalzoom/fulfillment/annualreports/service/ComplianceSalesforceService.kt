package com.legalzoom.fulfillment.annualreports.service

import com.legalzoom.api.model.fulfillment.CompleteTaskRequest
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsSalesforceQueues
import com.legalzoom.fulfillment.annualreports.configuration.Constants.AR_PROCESS_ID
import com.legalzoom.fulfillment.annualreports.configuration.ProcessDefinitionKeys
import com.legalzoom.fulfillment.annualreports.configuration.generateSalesforceCaseObject
import com.legalzoom.fulfillment.annualreports.workflow.AnnualReportsVariables
import com.legalzoom.fulfillment.annualreports.workflow.defaultCorrelationMap
import com.legalzoom.fulfillment.common.exception.RetryableException
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.productinterfaces.data.getEntityName
import com.legalzoom.fulfillment.productinterfaces.data.getEntityType
import com.legalzoom.fulfillment.salesforce.SalesforceService
import com.legalzoom.fulfillment.salesforce.exception.SalesforceCasePreconditionException
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import io.github.resilience4j.retry.annotation.Retry
import org.camunda.bpm.engine.RuntimeService
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException

/**
 * Placeholder for salesforce integration, this will be called by a listener or endpoint from SF
 * TODO: Work w/ Salesforce team to improve toasts
 */
@Service
class ComplianceSalesforceService(
    private val runtimeService: RuntimeService,
    private val salesforceApi: SalesforceService,
    private val complianceAnswerService: ComplianceAnswerService,
    // TODO: DSD-937 - Improve factory methods to resolve each queue to service
    private val annualReportsSalesforceService: AnnualReportsSalesforceService,
) {
    /**
     * Completes a salesforce case by correlating to processInstanceId.
     *
     * This is only supported by the newer compliance salesforce process, ProcessDefinitionKeys.COMPLIANCE_SALESFORCE.
     *
     * @param processInstanceId The ID of the process instance to complete.
     * @param completeTaskRequest The request containing the disposition and other details.
     * @return true if the case was found and completed, false otherwise.
     * @throws SalesforceCasePreconditionException
     */
    fun findAndCompleteCase(
        processInstanceId: String,
        completeTaskRequest: CompleteTaskRequest,
    ): Boolean {
        // TODO: Factory down to AR-salesforce service
        val instance =
            runtimeService.createProcessInstanceQuery()
                .processDefinitionKey(ProcessDefinitionKeys.COMPLIANCE_SALESFORCE)
                .processInstanceId(processInstanceId)
                .list()
                .firstOrNull()

        if (instance == null) {
            logger.warnEvent(
                "compliance.salesforce.caseNotFound",
                mapOf(
                    "processInstanceId" to processInstanceId,
                    "completeTaskRequest" to completeTaskRequest,
                ),
            )
            return false
        }

        val processId =
            runtimeService.createVariableInstanceQuery()
                .processInstanceIdIn(processInstanceId)
                .variableName("processId")
                .singleResult()

        val error =
            when (processId.value as Int) {
                AR_PROCESS_ID ->
                    annualReportsSalesforceService.resolveCaseCompletion(
                        instance,
                        completeTaskRequest,
                    )
                else -> {
                    logger.warnEvent(
                        "compliance.salesforce.unsupportedProcessId",
                        mapOf(
                            "processInstanceId" to processInstanceId,
                            "processId" to processId,
                        ),
                    )
                    "Product not supported by compliance salesforce service: $processId. " +
                        "Please escalate with engineering"
                }
            }

        error?.let { throw SalesforceCasePreconditionException(error) }

        return true
    }

    fun createSalesforceCase(
        processInstanceId: String,
        input: AnnualReportsVariables,
    ): String {
        val queue =
            input.sfQueue
                ?: throw IllegalArgumentException("sfQueue is required to create Salesforce case")

        val answers = complianceAnswerService.getAnswersForPackage(input.orderId, input.customerId)
        // TODO: Actually create a case in salesforce
        val caseRequest =
            AnnualReportsSalesforceQueues
                .valueOf(queue)
                .generateSalesforceCaseObject(
                    processInstanceId,
                    input,
                    entityName = answers.getEntityName(),
                    entityType = answers.getEntityType(),
                )

        val response = completeCreateCaseRequest(caseRequest)

        val crmId = response.recordId

        logger.event(
            "compliance.salesforce.caseCreated",
            input.defaultCorrelationMap.plus(
                mapOf(
                    "crmId" to crmId,
                    "queue" to queue,
                    "queueName" to AnnualReportsSalesforceQueues.valueOf(queue).queueName,
                    "validationErrors" to caseRequest.exceptions.toString(),
                ),
            ),
        )
        return crmId
    }

    @Retry(name = "default")
    fun completeCreateCaseRequest(request: SalesforceCaseRequest): SalesforceCaseResponse =
        try {
            salesforceApi.createCase(request)
        } catch (wcre: WebClientResponseException) {
            logger.info("Retrying create case ${wcre.message}", wcre)
            if (wcre.statusCode.series() == HttpStatus.Series.SERVER_ERROR) throw RetryableException(wcre) else throw wcre
        }

    companion object {
        private val logger = LoggerFactory.getLogger(ComplianceSalesforceService::class.java)
    }
}
