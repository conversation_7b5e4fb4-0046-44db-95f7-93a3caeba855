package com.legalzoom.fulfillment.annualreports.configuration

import com.legalzoom.fulfillment.annualreports.workflow.AnnualReportsVariables
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceException
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.ValidationResult
import java.time.ZonedDateTime

enum class AnnualReportsSalesforceQueues(
    val queueName: String,
    val eventType: String,
    val eventPhase: String,
    val queueType: SalesforceExceptionType,
) {
    // TODO: Insert actual values
    PRE_FILING(
        queueName = "NGF Pre-Filing: Special Filings",
        eventType = "MANUAL_PRE_FILING",
        eventPhase = "MANUAL_PRE_FILING",
        queueType = SalesforceExceptionType.QC,
    ),
    POST_FILING(
        queueName = "Final Package Review",
        eventType = "MANUAL_POST_FILING",
        eventPhase = "MANUAL_POST_FILING",
        queueType = SalesforceExceptionType.QC,
    ),
    PRINT_EXCEPTION(
        queueName = "Print & Ship Exceptions",
        eventType = "VALIDATION_COMPLETE",
        eventPhase = "PRINT",
        queueType = SalesforceExceptionType.Exception,
    ),
}

fun AnnualReportsSalesforceQueues.generateSalesforceCaseObject(
    correlationId: String,
    input: AnnualReportsVariables,
    entityName: String,
    entityType: AnswersEntityType,
): SalesforceCaseRequest {
    // TODO: Generate actual Salesforce case object

    // TODO: Check Validation errors?
    val validationResult =
        ValidationResult(
            passed = true,
            errors =
                input.errorMessage?.let {
                    // TODO: ?
                    listOf(
                        ValidationError(
                            message = it,
                            data = input,
                        ),
                    )
                },
            isSelfServeOrco = false,
            problemType = null,
        )

    val sfExceptions =
        listOf(
            SalesforceException(
                application = "fulfillment-compliance",
                contractVersion = "1.0",
                eventType = this.eventType,
                eventPhase = this.eventPhase,
                // TODO: JobId vs correlationId? (jobId used as taskId in NGF/X)
                jobId = correlationId,
                type = this.queueType,
                optionalData =
                    mapOf(
                        "validationResult" to validationResult,
                        "typeOfFiling" to AnnualReportsFilingStateConfig.determineFilingType(input.state, entityType),
                    ),
            ),
        )

    val caseRequest =
        SalesforceCaseRequest(
            // TODO: Why the shared clock in fulfillment SalesforceListener?
            exceptionDateTime = ZonedDateTime.now().toString(),
            correlationId = correlationId,
            orderNumber = input.orderId.toString(),
            processingNumber = input.processingOrderId.toString(),
            customerId = input.customerId,
            processId = input.processId.toString(),
            entityType = entityType.name,
            entityName = entityName,
            exceptions = sfExceptions,
        )

    return caseRequest
}
