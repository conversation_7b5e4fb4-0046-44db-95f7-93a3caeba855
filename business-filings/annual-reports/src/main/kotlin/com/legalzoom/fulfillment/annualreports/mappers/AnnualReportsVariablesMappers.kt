package com.legalzoom.fulfillment.annualreports.mappers

import com.legalzoom.fulfillment.annualreports.workflow.AnnualReportsVariables
import com.legalzoom.fulfillment.service.data.notificationsPlatform.NotificationDataRequest
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale

fun AnnualReportsVariables.createSOSNotificationRequest(): NotificationDataRequest {
    val now = Instant.now()
    val formatter =
        DateTimeFormatter.ofPattern("MMM d", Locale.ENGLISH)
            // for elegance we ought to use the same timezone as the customer but whatever
            .withZone(ZoneId.of("America/Los_Angeles"))
    val formattedDate = formatter.format(now)

    return NotificationDataRequest(
        customerId = this.customerId,
        orderId = this.orderId.toString(),
        processingOrderId = this.processingOrderId.toString(),
        notificationType = SENT_TO_SOS_NOTIFICATIONID,
        templateData =
            mapOf(
                "product" to "annual report",
                "date" to formattedDate,
                "orderId" to this.orderId.toString(),
            ),
    )
}

const val SENT_TO_SOS_NOTIFICATIONID = "fulfillment_statusupdate_order-status-special-filings-sent-to-sos-sms_20240211"
