package com.legalzoom.fulfillment.annualreports.configuration

import com.legalzoom.fulfillment.productinterfaces.data.BotPersonInfoDto
import com.legalzoom.fulfillment.productinterfaces.enumeration.FilingMethod
import com.legalzoom.fulfillment.productinterfaces.enumeration.State
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.INC
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.LLC
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.LLP
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.LP
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.NP

enum class AnnualReportsFilingStateConfig(
    val state: State,
    val filingMethod: FilingMethod,
    // TODO: Does entity type matter for determining filing type?
    val entityTypes: Set<AnswersEntityType> = setOf(),
    // TODO: Add all lzRaAddress
    val lzRaAddress: BotPersonInfoDto? = null,
) {
    AK(
        state = State.ALASKA,
        filingMethod = FilingMethod.Instant,
        entityTypes = setOf(INC, LLC, NP, LLP),
    ),
    AZ(
        state = State.ARIZONA,
        filingMethod = FilingMethod.Instant,
        entityTypes = setOf(INC, NP),
    ),
    AR(
        state = State.ARKANSAS,
        filingMethod = FilingMethod.Online,
        entityTypes = setOf(INC, LLC, NP),
    ),
    CA(
        state = State.CALIFORNIA,
        filingMethod = FilingMethod.Instant,
        entityTypes = setOf(INC, LLC, NP, LLP, LP),
    ),
    NJ(
        state = State.NEW_JERSEY,
        filingMethod = FilingMethod.Instant,
        entityTypes = setOf(INC, LLC, NP, LLP, LP),
    ),
    ;

    // TODO: Add remaining states

    companion object {
        fun determineFilingType(
            state: String,
            entityType: AnswersEntityType,
        ): FilingMethod {
            val stateEnum =
                State.fromNameOrAbbre(state)
                    ?: throw IllegalArgumentException("Invalid state: $state")
            return determineFilingType(stateEnum, entityType)
        }

        fun determineFilingType(
            state: State,
            entityType: AnswersEntityType,
        ): FilingMethod {
            return entries
                .firstOrNull { it.state == state && it.entityTypes.contains(entityType) }?.filingMethod
                ?: throw IllegalArgumentException("No filing type found for state: $state and entity type: $entityType")
        }

        fun determineFilingType(state: String): FilingMethod {
            val s = State.fromNameOrAbbre(state)!!
            return entries.firstOrNull { it.state == s }?.filingMethod
                ?: throw IllegalArgumentException("No filing type found for state: $state")
        }
    }
}
