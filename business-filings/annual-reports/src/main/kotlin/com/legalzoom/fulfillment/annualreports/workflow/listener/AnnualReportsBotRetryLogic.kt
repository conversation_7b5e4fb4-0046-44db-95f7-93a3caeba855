package com.legalzoom.fulfillment.annualreports.workflow.listener

import com.legalzoom.api.dsd.webautomation.RpaBotsApi
import com.legalzoom.fulfillment.annualreports.workflow.input
import com.legalzoom.fulfillment.annualreports.workflow.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.ExecutionListener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class AnnualReportsBotRetryLogic(val dsdWebAutomationClient: RpaBotsApi) : ExecutionListener {
    @WithSpan
    override fun notify(execution: DelegateExecution) {
        val job = dsdWebAutomationClient.findById(execution.input.dsdWebAutomationJobId!!).block()!!
        logger.info("Bot job completed with status ${job.status}")

        if (job.status.equals("succeeded", ignoreCase = true)) {
            execution.output {
                botOutcome = "success"
                retryBot = false
            }
        } else {
            // TODO: Throw for system error w/ max retries

            // TODO: Pass out business errors to manual filing

            execution.output {
                botOutcome = "failure"
                retryBot = true
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AnnualReportsBotRetryLogic::class.java)
    }
}
