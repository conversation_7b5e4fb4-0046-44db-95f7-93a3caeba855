package com.legalzoom.fulfillment.annualreports.service

import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.model.answer.AnswerSource
import com.legalzoom.api.model.answer.FieldAnswerResponseDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.SaveAnswerBankDto
import com.legalzoom.api.model.answer.SaveAnswerBankRequest
import com.legalzoom.api.model.questionnaire.GetQuestionnaireInfoResponse
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.fulfillment.annualreports.configuration.Constants
import com.legalzoom.fulfillment.common.extensions.packageOrderItem
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.service.OrdersApiService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ComplianceAnswerService(
    private val answerApi: AnswerApi,
    private val ordersApiService: OrdersApiService,
    private val questionnaireApi: QuestionnaireApi,
) {
    fun getAnswersForPackage(
        orderId: Int,
        customerId: String,
    ): GetQuestionnaireAnswerResponse {
        return answerApi.answersUserOrderIdSourceGet(
            getProcessingOrderIdForAnswers(orderId.toInt()),
            AnswerSource.NUMBER_0,
            null,
            null,
            null,
            "1.0",
            customerId,
            null,
        ).blockSingle()
    }

    fun saveEntityNameFieldAnswers(
        processingOrderId: Int,
        customerId: String,
        entityName: String,
        questionnaireId: Int,
    ) {
        // TODO: Is this always the same?
        getQuestionnaire(questionnaireId)?.questionnaireFields?.firstOrNull {
            it.fieldName == DEFAULT_ENTITY_NAME_FIELD
        }?.let {
            val response =
                answerApi.answersAnswerBankPartialUpdatePut(
                    "1.0",
                    customerId,
                    false,
                    SaveAnswerBankRequest().questionnaireFieldGroupAnswers(
                        SaveAnswerBankDto()
                            .fieldAnswers(
                                mutableListOf(
                                    FieldAnswerResponseDto()
                                        .fieldId(it.questionnaireFieldId)
                                        .fieldName(DEFAULT_ENTITY_NAME_FIELD)
                                        .fieldValue(entityName)
                                        .optionId(0),
                                ),
                            )
                            .processingOrderId(processingOrderId)
                            .createdBy(Constants.COMPLIANCE_SYS_NAME)
                            .questionireId(questionnaireId)
                            .isMajorRevision(false),
                    ),
                ).blockSingle()

            logger.event(
                "compliance.answers.saveEntityNameFieldAnswers",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "customerId" to customerId,
                    "entityName" to entityName,
                    "numberOfRecordsChanged" to response.saveAnswerResponse?.numberofRecordsAffected,
                ),
            )
        } ?: run {
            logger.warnEvent(
                "compliance.answers.skipSaveEntityNameAnswersNoField",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "customerId" to customerId,
                    "entityName" to entityName,
                ),
            )
        }
    }

    private fun getQuestionnaire(questionnaireId: Int): GetQuestionnaireInfoResponse? {
        // TODO: SaveEntityNameDelegate caches this, why?

        return questionnaireApi.questionnaireFieldsQuestionnaireIdGet(
            questionnaireId,
            "1.0",
            "",
            false,
        ).blockSingle()
    }

    private fun getProcessingOrderIdForAnswers(orderId: Int): Int {
        val orderResponse =
            ordersApiService.getOrders(
                orderId,
                productIdType = null,
                showOrderItemTree = null,
                xLzApiVersion = "1.0",
                xLzCustomerid = null,
                xLzAuthorize = null,
            )

        return orderResponse.order!!
            .packageOrderItem()!!
            .processingOrder!!
            .processingOrderId!!
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ComplianceAnswerService::class.java)

        private const val DEFAULT_ENTITY_NAME_FIELD = "Company_name"
    }
}
