{"questionnaireFieldGroupAnswers": {"userOrderId": 580282625, "createdBy": null, "isMajorRevision": false, "revision": 0, "subRevision": 3, "fieldAnswers": [{"fieldId": 295202, "fieldName": "Affirmation_Full_Name", "fieldValue": "A Guy", "optionId": null, "responseType": 0, "questionText": "Affirmation_Full_Name", "sortOrder": 49, "questionnaireId": 295202, "groupAnswers": null}, {"fieldId": 295201, "fieldName": "Affirmation_MC", "fieldValue": "Yes, the above statement is true", "optionId": 440336, "responseType": 0, "questionText": "Affirmation_MC", "sortOrder": 48, "questionnaireId": 295201, "groupAnswers": null}, {"fieldId": 295203, "fieldName": "Affirmation_Title", "fieldValue": "Authorized Agent", "optionId": null, "responseType": 0, "questionText": "Affirmation_Title", "sortOrder": 50, "questionnaireId": 295203, "groupAnswers": null}, {"fieldId": 295141, "fieldName": "biz_email", "fieldValue": "<EMAIL>", "optionId": null, "responseType": 0, "questionText": "<PERSON>iz <PERSON>", "sortOrder": 46, "questionnaireId": 295141, "groupAnswers": null}, {"fieldId": 295140, "fieldName": "biz_phone", "fieldValue": "(*************", "optionId": null, "responseType": 0, "questionText": "Biz Phone", "sortOrder": 47, "questionnaireId": 295140, "groupAnswers": null}, {"fieldId": 261584, "fieldName": "business_address", "fieldValue": "1 Main St", "optionId": null, "responseType": 0, "questionText": "Business Address", "sortOrder": 13, "questionnaireId": 261584, "groupAnswers": null}, {"fieldId": 261587, "fieldName": "business_city_state_zip", "fieldValue": "Cityville", "optionId": null, "responseType": 0, "questionText": "Business City", "sortOrder": 15, "questionnaireId": 261587, "groupAnswers": null}, {"fieldId": 299497, "fieldName": "business_county", "fieldValue": "Fakecounty", "optionId": null, "responseType": 0, "questionText": "business_county", "sortOrder": 999, "questionnaireId": 299497, "groupAnswers": null}, {"fieldId": 261588, "fieldName": "business_state", "fieldValue": "New Jersey", "optionId": 330991, "responseType": 0, "questionText": "Business State", "sortOrder": 16, "questionnaireId": 261588, "groupAnswers": null}, {"fieldId": 261589, "fieldName": "business_zip", "fieldValue": "12345", "optionId": null, "responseType": 0, "questionText": "Business Zip", "sortOrder": 17, "questionnaireId": 261589, "groupAnswers": null}, {"fieldId": 295228, "fieldName": "combined_affirmation2_LLC", "fieldValue": "", "optionId": 440355, "responseType": 0, "questionText": "combined_affirmation2_LLC", "sortOrder": 999, "questionnaireId": 295228, "groupAnswers": null}, {"fieldId": 261515, "fieldName": "Company_name", "fieldValue": "test_entity_name", "optionId": null, "responseType": 0, "questionText": "Company_name", "sortOrder": 3, "questionnaireId": 261515, "groupAnswers": null}, {"fieldId": 299506, "fieldName": "filing_year", "fieldValue": "2025", "optionId": null, "responseType": 0, "questionText": "filing_year", "sortOrder": 999, "questionnaireId": 299506, "groupAnswers": null}, {"fieldId": 299505, "fieldName": "formationOn", "fieldValue": "2021-10-07", "optionId": null, "responseType": 0, "questionText": "formationOn", "sortOrder": 999, "questionnaireId": 299505, "groupAnswers": null}, {"fieldId": 295149, "fieldName": "member_of_LLC_MC", "fieldValue": "Two or more members", "optionId": 440041, "responseType": 0, "questionText": "member_of_LLC_MC", "sortOrder": 99, "questionnaireId": 295149, "groupAnswers": null}, {"fieldId": 261593, "fieldName": "registered_agent", "fieldValue": "<PERSON><PERSON><PERSON> serves as my registered agent.", "optionId": 330957, "responseType": 0, "questionText": "Registered Agent", "sortOrder": 6, "questionnaireId": 261593, "groupAnswers": null}, {"fieldId": 299373, "fieldName": "shipping_MC", "fieldValue": "standard", "optionId": 445228, "responseType": 0, "questionText": "shipping_MC", "sortOrder": 999, "questionnaireId": 299373, "groupAnswers": null}, {"fieldId": 294706, "fieldName": "SOS_number", "fieldValue": "05555550", "optionId": null, "responseType": 0, "questionText": "SOS_number", "sortOrder": 999, "questionnaireId": 294706, "groupAnswers": null}, {"fieldId": 261583, "fieldName": "State_of_formation", "fieldValue": "New Jersey", "optionId": 330917, "responseType": 0, "questionText": "State_of_formation", "sortOrder": 1, "questionnaireId": 261583, "groupAnswers": null}, {"fieldId": 261592, "fieldName": "Type_of_entity", "fieldValue": "Limited Liability Company (LLC)", "optionId": 330951, "responseType": 0, "questionText": "Type_of_entity", "sortOrder": 2, "questionnaireId": 261592, "groupAnswers": null}, {"fieldId": null, "fieldName": "LZ_STATE_ID", "fieldValue": "0", "optionId": null, "responseType": 0, "questionText": null, "sortOrder": 0, "questionnaireId": null, "groupAnswers": null}, {"fieldId": null, "fieldName": "POST_OPTION", "fieldValue": "205", "optionId": null, "responseType": 0, "questionText": null, "sortOrder": 0, "questionnaireId": null, "groupAnswers": null}, {"fieldId": null, "fieldName": "SHIPPING_METHOD", "fieldValue": "0", "optionId": null, "responseType": 0, "questionText": null, "sortOrder": 0, "questionnaireId": null, "groupAnswers": null}, {"fieldId": 294736, "fieldName": "managers2_info", "fieldValue": "1", "optionId": null, "responseType": 0, "questionText": "managers2_info", "sortOrder": 999, "questionnaireId": 294736, "groupAnswers": null}, {"fieldId": 261616, "fieldName": "stockholders", "fieldValue": "2", "optionId": null, "responseType": 0, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616, "groupAnswers": null}], "groupAnswers": [{"groupId": 294736, "groupName": "managers2_info", "fieldId": 95908, "fieldName": "managers2_info_city_1", "fieldValue": "Fakecity", "optionId": null, "groupIndex": 1, "questionText": "managers2_info", "sortOrder": 999, "questionnaireId": 294736}, {"groupId": 294736, "groupName": "managers2_info", "fieldId": 95906, "fieldName": "managers2_info_name_1", "fieldValue": "LastName, FirstName", "optionId": null, "groupIndex": 1, "questionText": "managers2_info", "sortOrder": 999, "questionnaireId": 294736}, {"groupId": 294736, "groupName": "managers2_info", "fieldId": 95909, "fieldName": "managers2_info_state_1", "fieldValue": "NJ", "optionId": 87917, "groupIndex": 1, "questionText": "managers2_info", "sortOrder": 999, "questionnaireId": 294736}, {"groupId": 294736, "groupName": "managers2_info", "fieldId": 95907, "fieldName": "managers2_info_street_1", "fieldValue": "1 Main St", "optionId": null, "groupIndex": 1, "questionText": "managers2_info", "sortOrder": 999, "questionnaireId": 294736}, {"groupId": 294736, "groupName": "managers2_info", "fieldId": 95910, "fieldName": "managers2_info_zip_1", "fieldValue": "08322", "optionId": null, "groupIndex": 1, "questionText": "managers2_info", "sortOrder": 999, "questionnaireId": 294736}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98184, "fieldName": "stockholder_city_1", "fieldValue": "Faketown", "optionId": null, "groupIndex": 1, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98182, "fieldName": "stockholder_name_1", "fieldValue": "LastName, FirstName", "optionId": null, "groupIndex": 1, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98183, "fieldName": "stockholder_st_1", "fieldValue": "1 Main St", "optionId": null, "groupIndex": 1, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98185, "fieldName": "stockholder_state_1", "fieldValue": "NJ", "optionId": 59919, "groupIndex": 1, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98186, "fieldName": "stockholder_zip_1", "fieldValue": "08322", "optionId": null, "groupIndex": 1, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98184, "fieldName": "stockholder_city_2", "fieldValue": "Fakecity2", "optionId": null, "groupIndex": 2, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98182, "fieldName": "stockholder_name_2", "fieldValue": "LastName2, FirstName2", "optionId": null, "groupIndex": 2, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98183, "fieldName": "stockholder_st_2", "fieldValue": "2 Main St", "optionId": null, "groupIndex": 2, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98185, "fieldName": "stockholder_state_2", "fieldValue": "NJ", "optionId": 59919, "groupIndex": 2, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}, {"groupId": 261616, "groupName": "stockholders", "fieldId": 98186, "fieldName": "stockholder_zip_2", "fieldValue": "08322", "optionId": null, "groupIndex": 2, "questionText": "# of Owners", "sortOrder": 30, "questionnaireId": 261616}]}}