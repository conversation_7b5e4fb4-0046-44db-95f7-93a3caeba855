package com.legalzoom.fulfillment.annualreports.data

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.fulfillment.productinterfaces.data.BotAddressDto
import com.legalzoom.fulfillment.productinterfaces.data.BotPersonInfoDto
import com.legalzoom.fulfillment.productinterfaces.data.getCustomerForFiling
import com.legalzoom.fulfillment.productinterfaces.data.getEntityId
import com.legalzoom.fulfillment.productinterfaces.data.getEntityType
import com.legalzoom.fulfillment.productinterfaces.data.getFilingYear
import com.legalzoom.fulfillment.productinterfaces.data.getMainAddress
import com.legalzoom.fulfillment.productinterfaces.data.getOfficers
import com.legalzoom.fulfillment.productinterfaces.data.getPrincipleAddress
import com.legalzoom.fulfillment.productinterfaces.data.getRegisteredAgent
import com.legalzoom.fulfillment.productinterfaces.enumeration.State
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@ExtendWith(MockKExtension::class)
class AnnualReportsDataMappersTest {
    private fun getQuestionnaireResponse(fieldAnswerOverrides: List<Pair<String, String>>? = null): GetQuestionnaireAnswerResponse {
        val json =
            ClassPathResource(
                "../GetQuestionnaireAnswerResponse_manager_and_stockholder.json",
                javaClass,
            ).file
        val response = objectMapper.readValue<GetQuestionnaireAnswerResponse>(json)

        fieldAnswerOverrides?.forEach { (fieldName, fieldValue) ->
            val entry =
                response.questionnaireFieldGroupAnswers!!.fieldAnswers!!.firstOrNull {
                    it.fieldName == fieldName
                }

            if (entry == null) {
                response.questionnaireFieldGroupAnswers!!.fieldAnswers!!.add(
                    FieldAnswerDto()
                        .fieldName(fieldName)
                        .fieldValue(fieldValue),
                )
            } else {
                entry.fieldValue(fieldValue)
            }
        }

        return response
    }

    @Test
    fun `non-lz RA, separate main address, single officer`() {
        val answers =
            getQuestionnaireResponse(
                fieldAnswerOverrides = registeredAgentFields + mainAddressFields + singleOfficerFields,
            )

        val ra = answers.getRegisteredAgent(State.NEW_JERSEY)

        assertThat(ra).isEqualTo(
            BotPersonInfoDto(
                firstName = "Joe",
                lastName = "Mo",
                address =
                    BotAddressDto(
                        addressLine1 = "1 Register St",
                        city = "Ratown",
                        state = "NJ",
                        zipCode = "10000",
                    ),
                title = null,
                phone = null,
            ),
        )

        val mainAddressForFiling = answers.getMainAddress()
        assertThat(mainAddressForFiling).isEqualTo(
            BotAddressDto(
                addressLine1 = "1 New Ln",
                city = "smalltown",
                state = "NJ",
                zipCode = "89000",
            ),
        )

        val officers = answers.getOfficers()
        assertThat(officers).isEqualTo(listOf(singleOfficer))
    }

    @Test
    fun `happypath - default`() {
        val answers = getQuestionnaireResponse()

        val officers = answers.getOfficers()

        assertThat(officers).hasSize(3)
        assertThat(officers).containsAll(defaultOfficers)

        val customerForFiling = testContacts.getCustomerForFiling()
        assertThat(customerForFiling).isEqualTo(defaultCustomer)

        val ra = answers.getRegisteredAgent(State.NEW_JERSEY)
        assertThat(ra).isEqualTo(
            BotPersonInfoDto(
                firstName = "United States Corporation Agents, Inc",
                lastName = null,
                address =
                    BotAddressDto(
                        addressLine1 = "330 Changebridge Road, Suite 101",
                        city = "Pine Brook",
                        state = "NJ",
                        zipCode = "07058",
                    ),
                title = null,
                phone = null,
            ),
        )

        val filingYear = answers.getFilingYear()
        assertThat(filingYear).isEqualTo("2025")

        val entityId = answers.getEntityId()
        assertThat(entityId).isEqualTo("05555550")

        val entityType = answers.getEntityType()
        assertThat(entityType).isEqualTo(AnswersEntityType.LLC)

        val principleAddressForFiling = answers.getPrincipleAddress()
        assertThat(principleAddressForFiling).isEqualTo(defaultPrincipleAddress)

        val mainAddressForFiling = answers.getMainAddress()
        assertThat(mainAddressForFiling).isEqualTo(defaultPrincipleAddress)
    }

    companion object {
        private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

        private val defaultOfficers =
            listOf(
                BotPersonInfoDto(
                    firstName = "FirstName",
                    lastName = "LastName",
                    title = "MANAGER",
                    address =
                        BotAddressDto(
                            addressLine1 = "1 Main St",
                            city = "Fakecity",
                            state = "NJ",
                            zipCode = "08322",
                        ),
                    phone = null,
                ),
                BotPersonInfoDto(
                    firstName = "FirstName",
                    lastName = "LastName",
                    title = "STOCKHOLDER",
                    address =
                        BotAddressDto(
                            addressLine1 = "1 Main St",
                            city = "Faketown",
                            state = "NJ",
                            zipCode = "08322",
                        ),
                    phone = null,
                ),
                BotPersonInfoDto(
                    firstName = "FirstName2",
                    lastName = "LastName2",
                    title = "STOCKHOLDER",
                    address =
                        BotAddressDto(
                            addressLine1 = "2 Main St",
                            city = "Fakecity2",
                            state = "NJ",
                            zipCode = "08322",
                        ),
                    phone = null,
                ),
            )

        private val singleOfficer =
            BotPersonInfoDto(
                firstName = "Main",
                lastName = "Officer",
                address =
                    BotAddressDto(
                        addressLine1 = "1 Officer Ave",
                        city = "Faketown",
                        state = "NJ",
                        zipCode = "08322",
                    ),
                title = null,
                phone = null,
            )
        private val singleOfficerFields =
            listOf(
                Pair("member_of_LLC_MC", "One member"),
                Pair("member_info_name", singleOfficer.lastName!! + ", " + singleOfficer.firstName!!),
                Pair("member_info_street_address", singleOfficer.address.addressLine1),
                Pair("member_info_city", singleOfficer.address.city),
                Pair("member_info_zip", singleOfficer.address.zipCode),
                Pair("member_info_state_MC", singleOfficer.address.state),
            )

        private val defaultPrimaryAddress =
            ContactInfoDto().contactType(ContactType.Primary)
                .addressLine1("1 Test St")
                .city("City")
                .state("NJ")
                .zipCode("08000")
                .country("USA")
                .mobilePhone("55555555555")
                .firstName("John")
                .lastName("Smith")

        private val defaultShippingAddress =
            ContactInfoDto().contactType(ContactType.Shipping)
                .addressLine1("1 Test St")
                .addressLine2("Apt 1")
                .city("City")
                .state("NJ")
                .zipCode("08000")
                .country("USA")
                .mobilePhone("55555555555")
                .firstName("John")
                .lastName("Shipper")

        private val defaultPrincipleAddress =
            BotAddressDto(
                addressLine1 = "1 Main St",
                city = "Cityville",
                state = "NJ",
                zipCode = "12345",
            )

        private val registeredAgentFields =
            listOf(
                Pair("registered_agent", "I am my own registered agent"),
                Pair("registered_agent_email", "<EMAIL>"),
                Pair("registered_agent_2", "Mo, Joe"),
                Pair("registered_agent_3", "1 Register St"),
                Pair("registered_agent_4", "Ratown"),
                Pair("registered_agent_5", "10000"),
                Pair("registered_agent_6", "NJ"),
            )

        private val mainAddressFields =
            listOf(
                Pair("mailing_address", "1 New Ln"),
                Pair("mailing_city", "smalltown"),
                Pair("mailing_state", "NJ"),
                Pair("mailing_zip", "89000"),
            )

        private val defaultCustomer =
            BotPersonInfoDto(
                firstName = defaultPrimaryAddress.firstName,
                lastName = defaultPrimaryAddress.lastName,
                address =
                    BotAddressDto(
                        addressLine1 = defaultPrimaryAddress.addressLine1!!,
                        city = defaultPrimaryAddress.city!!,
                        state = defaultPrimaryAddress.state!!,
                        zipCode = defaultPrimaryAddress.zipCode!!,
                    ),
                title = null,
                phone = defaultPrimaryAddress.mobilePhone,
            )

        private val testContacts =
            mutableListOf<ContactInfoDto>(
                defaultPrimaryAddress,
                defaultShippingAddress,
            )
    }
}
