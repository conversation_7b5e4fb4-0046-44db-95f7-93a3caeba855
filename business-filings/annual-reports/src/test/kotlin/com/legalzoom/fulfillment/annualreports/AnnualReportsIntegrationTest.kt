package com.legalzoom.fulfillment.annualreports

import com.legalzoom.api.model.dsd.webautomation.StartBotResponseDto
import com.legalzoom.api.model.notificationsplatform.TargetChannel
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsProcessingStatuses
import com.legalzoom.fulfillment.annualreports.listener.DsdWebAutomationJobCompletion
import com.legalzoom.fulfillment.annualreports.mappers.SENT_TO_SOS_NOTIFICATIONID
import com.legalzoom.fulfillment.annualreports.workflow.annualReportsVariables
import com.legalzoom.fulfillment.dsdwebautomation.DsdWebAutomationJobCompletionEvent
import com.legalzoom.fulfillment.dsdwebautomation.WebAutomationReferenceIdsDto
import com.legalzoom.fulfillment.rest.exception.ErrorResponse
import com.legalzoom.fulfillment.salesforce.exception.SalesforceCasePreconditionException
import io.mockk.every
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus

class AnnualReportsIntegrationTest : ComplianceBaseIntegrationTest() {
    @Autowired
    lateinit var dsdWebAutomationJobCompletion: DsdWebAutomationJobCompletion

    @Test
    fun happyPathManualNoHoldAreaNoPrint() {
        val launchEvent = generateLaunchEvent()

        setupDefaultMocksFor(
            launchEvent,
            botEnabled = false,
        )

        val instance = launchProcess(launchEvent)

        verifyAndCompleteManualFiling(
            businessKey = launchEvent.processingOrderId.toString(),
            processInstance = instance,
        )

        verifyStatusUpdate(
            launchEvent.processingOrderId,
            AnnualReportsProcessingStatuses.AnnualReportsPreliminaryValidationComplete.processingStatusId,
        )

        verifyAndCompleteDocgen(instance)

        // todo - not sure whether to expect this or not
//        verifyStatusUpdate(
//            launchEvent.processingOrderId.toInt(),
//            AnnualReportsProcessingStatuses.AnnualReportsDocumentsReceivedFromState.processingStatusId,
//        )

        camundaTestHelpers.executeJobsUntilProcessCompleted(instance)

        verifyStatusUpdate(
            launchEvent.processingOrderId.toInt(),
            AnnualReportsProcessingStatuses.AnnualReportsDocumentsPrepared.processingStatusId,
        )

        verifyStatusUpdate(
            launchEvent.processingOrderId.toInt(),
            AnnualReportsProcessingStatuses.AnnualReportsShippingComplete.processingStatusId,
        )
    }

    @Test
    fun happyPathWithBot() {
        val launchEvent = generateLaunchEvent()

        val accountId =
            setupDefaultMocksFor(
                launchEvent,
                botEnabled = true,
            )

        val processInstance = launchProcess(launchEvent)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "await-bot-job")

        verifyBotJobRequest(
            processInstance,
            launchEvent.processingOrderId,
            launchEvent.orderId,
            accountId.toString(),
            launchEvent.customerId,
        )

        dsdWebAutomationJobCompletion.onDsdWebAutomationJobCompletion(
            DsdWebAutomationJobCompletionEvent(
                this,
                StartBotResponseDto().apply {
                    id = "1234"
                    status = "SUCCEEDED"
                    referenceIds =
                        com.legalzoom.api.model.dsd.webautomation.WebAutomationReferenceIdsDto().apply {
                            processingOrderId = launchEvent.processingOrderId.toString()
                        }
                },
            ),
        )

        verifyAndCompleteDocgen(processInstance)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verifyNotificationPlatformCall(
            customerId = launchEvent.customerId,
            orderId = launchEvent.orderId.toString(),
            processingOrderId = launchEvent.processingOrderId.toString(),
            notificationType = SENT_TO_SOS_NOTIFICATIONID,
            templateData =
                mapOf(
                    "product" to "annual report",
                    "date" to "Jan 1",
                    "orderId" to launchEvent.orderId.toString(),
                ),
            TargetChannel.ChannelTypeEnum.SMS,
        )

        verifyStatusUpdates(
            launchEvent.processingOrderId,
            AnnualReportsProcessingStatuses.AnnualReportsPreliminaryValidationComplete.processingStatusId,
            AnnualReportsProcessingStatuses.AnnualReportsStateFilingComplete.processingStatusId,
            AnnualReportsProcessingStatuses.AnnualReportsDocumentsReceivedFromState.processingStatusId,
            AnnualReportsProcessingStatuses.AnnualReportsDocumentsPrepared.processingStatusId,
        )
    }

    @Test
    fun salesforcePreconditionError() {
        val launchEvent = generateLaunchEvent()
        setupDefaultMocksFor(launchEvent)
        val instance = launchProcess(launchEvent)

        every {
            complianceFeatureToggleService.botEnabled(
                processId = launchEvent.processId,
                jurisdiction = launchEvent.state,
                entityType = 2,
            )
        } returns(false)

        val sfInstance = waitForSalesforceCaseReady(instance, "PRE_FILING")
        every {
            // TODO: Once implemented, mock the precondition failure state instead of service
            annualReportsVerificationService.preHoldAreaVerification()
        } throws SalesforceCasePreconditionException("Salesforce case precondition failed")

        val response =
            workflowTaskController.completeTask(
                sfInstance.id,
                com.legalzoom.fulfillment.domain.model.CompleteTaskRequest(
                    disposition = "proceed",
                ),
            )

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.statusCode)
        Assertions.assertEquals("Salesforce case precondition failed", (response.body as ErrorResponse).message)
    }

    fun happyPathManualWithHoldAreaAndPrint() {
        val launchEvent = generateLaunchEvent()
        val processInstanceId = annualReportsListener.annualReportsListener(launchEvent)
        val instance = camundaTestHelpers.waitForStartup(processInstanceId!!)
        // TODO: Implement hold area and print tests

        verifyAndCompleteHoldArea(
            businessKey = launchEvent.processingOrderId.toString(),
            processInstance = instance,
        )

        verifyAndCompleteManualFiling(
            businessKey = launchEvent.processingOrderId.toString(),
            processInstance = instance,
        )

        verifyAndCompleteDocgen(instance)

        verifyAndCompletePrint(instance)

        camundaTestHelpers.executeJobsUntilProcessCompleted(instance)
    }

    private fun verifyAndCompleteManualFiling(
        businessKey: String,
        processInstance: ProcessInstance,
        completed: Boolean = true,
    ) {
        val sfInstance = waitForSalesforceCaseReady(processInstance, "PRE_FILING")

        workflowTaskController.completeTask(
            sfInstance.id,
            com.legalzoom.fulfillment.domain.model.CompleteTaskRequest(
                disposition = if (completed) "proceed" else "problem",
            ),
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(sfInstance)

        assertThat(processInstance).hasPassed("sf-manual-filing")
    }

    private fun verifyAndCompleteHoldArea(
        businessKey: String,
        processInstance: ProcessInstance,
        completed: Boolean = true,
    ) {
        TODO("Create HA test")
    }

    private fun verifyAndCompleteDocgen(processInstance: ProcessInstance) {
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "doc-gen")

        val docGenProcessInstance = camundaTestHelpers.getProcessInstanceByParentAndTemplateKey(processInstance, "docgen-dsd-process")

        camundaTestHelpers.executeJobsIncludingActivity(docGenProcessInstance, "wait-for-completion-receive-task")

        camundaTestHelpers.correlateMessage(
            "Message_DSD_DocGenJob_Complete",
            docGenProcessInstance,
            annualReportsVariables {
            },
        )

        camundaTestHelpers.executeJobsUntilProcessCompleted(docGenProcessInstance)
    }

    private fun verifyAndCompletePrint(
        processInstance: ProcessInstance,
        completed: Boolean = true,
    ) {
        TODO("Create Print test")
    }
}
