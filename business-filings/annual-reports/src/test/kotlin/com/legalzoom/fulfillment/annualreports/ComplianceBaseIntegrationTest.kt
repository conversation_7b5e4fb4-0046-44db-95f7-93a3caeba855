package com.legalzoom.fulfillment.annualreports

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.auth0.OAuthService
import com.legalzoom.api.dsd.webautomation.RpaBotsApi
import com.legalzoom.api.model.answer.AnswerSource
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.dsd.webautomation.StartBotRequestDto
import com.legalzoom.api.model.dsd.webautomation.StartBotResponseDto
import com.legalzoom.api.model.dsd.webautomation.WebAutomationReferenceIdsDto
import com.legalzoom.api.model.notificationsplatform.NotificationResponse
import com.legalzoom.api.model.notificationsplatform.TargetChannel
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderDto
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.order.ProcessingOrderDto
import com.legalzoom.api.model.order.ProductConfigurationDto
import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.api.ordercontacts.OrdersContactsApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.revv.RevvAuthService
import com.legalzoom.fulfillment.annualreports.configuration.AnnualReportsMessageRefs
import com.legalzoom.fulfillment.annualreports.configuration.Constants.AR_PROCESS_ID
import com.legalzoom.fulfillment.annualreports.configuration.ProcessDefinitionKeys.ANNUAL_REPORTS
import com.legalzoom.fulfillment.annualreports.configuration.ProcessDefinitionKeys.COMPLIANCE_SALESFORCE
import com.legalzoom.fulfillment.annualreports.listener.AnnualReportsListener
import com.legalzoom.fulfillment.annualreports.service.AnnualReportsVerificationService
import com.legalzoom.fulfillment.annualreports.service.ComplianceFeatureToggleService
import com.legalzoom.fulfillment.common.enumeration.RelationshipType
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.domain.enumeration.EntityType
import com.legalzoom.fulfillment.dsddocgen.workflow.StartDsdDocGenJobDelegate
import com.legalzoom.fulfillment.dsddocgen.workflow.VerifyDsdDocGenOutcomeDelegate
import com.legalzoom.fulfillment.productinterfaces.data.BotAddressDto
import com.legalzoom.fulfillment.productinterfaces.data.BotPersonInfoDto
import com.legalzoom.fulfillment.productinterfaces.launcher.ProductLaunchMessage
import com.legalzoom.fulfillment.rest.controller.WorkflowTaskController
import com.legalzoom.fulfillment.salesforce.SalesforceService
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.service.data.notificationsPlatform.NotificationDataRequest
import com.legalzoom.fulfillment.service.service.AccountService
import com.legalzoom.fulfillment.service.service.LegacyEventHandlerApi
import com.legalzoom.fulfillment.service.service.NotificationsPlatformService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.testing.Await.await
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.bpmn.helpers.CamundaTestHelpers
import com.ninjasquad.springmockk.MockkBean
import com.ninjasquad.springmockk.SpykBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.awaitility.kotlin.untilNotNull
import org.camunda.bpm.engine.HistoryService
import org.camunda.bpm.engine.ManagementService
import org.camunda.bpm.engine.ProcessEngineConfiguration
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.runtime.Job
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.RequiredHistoryLevel
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.job
import org.junit.Assert.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.test.context.EmbeddedKafka
import reactor.core.publisher.Mono
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration.Companion.seconds

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = ["camunda.bpm.job-execution.enabled=false"],
)
@EmbeddedKafka
@RequiredHistoryLevel(ProcessEngineConfiguration.HISTORY_FULL)
class ComplianceBaseIntegrationTest() {
    @Autowired
    protected lateinit var camundaTestHelpers: CamundaTestHelpers

    @Autowired
    protected lateinit var workflowTaskController: WorkflowTaskController

    @Autowired
    protected lateinit var annualReportsListener: AnnualReportsListener

    @Autowired
    protected lateinit var runtimeService: RuntimeService

    @Autowired
    protected lateinit var historyService: HistoryService

    @Autowired
    protected lateinit var managementService: ManagementService

    @Autowired
    lateinit var kafkaTemplate: KafkaTemplate<String, ProductLaunchMessage>

    @SpykBean
    protected lateinit var annualReportsVerificationService: AnnualReportsVerificationService

    @MockkBean
    protected lateinit var legacyEventHandlerApi: LegacyEventHandlerApi

    @MockkBean
    protected lateinit var rpaBotsApi: RpaBotsApi

    @MockkBean
    protected lateinit var accountService: AccountService

    @MockkBean
    protected lateinit var startDsdDocGenJobDelegate: StartDsdDocGenJobDelegate

    @MockkBean
    protected lateinit var verifyDsdDocGenOutcomeDelegate: VerifyDsdDocGenOutcomeDelegate

    @MockkBean
    private lateinit var notificationsPlatformService: NotificationsPlatformService

    protected fun mockRpaBotsApi() {
        every {
            rpaBotsApi.startBot(any())
        } returns
            Mono.just(
                StartBotResponseDto().apply {
                    id = "1234"
                    status = "RUNNING"
                },
            )

        every {
            rpaBotsApi.findById("1234")
        } returns
            Mono.just(
                StartBotResponseDto().apply {
                    id = "1234"
                    status = "SUCCEEDED"
                },
            )
    }

    @MockkBean
    protected lateinit var answerApi: AnswerApi

    protected fun mockAnswerApi(processingOrderId: Int) {
        val json =
            ClassPathResource(
                "GetQuestionnaireAnswerResponse_manager_and_stockholder.json",
                javaClass,
            ).file
        val response = objectMapper.readValue<GetQuestionnaireAnswerResponse>(json)

        every {
            answerApi.answersUserOrderIdSourceGet(
                match { it == processingOrderId },
                match { it == AnswerSource.NUMBER_0 },
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            ).blockSingle()
        } returns response
    }

    protected fun mockLegacyEventHandlerApi() {
        every {
            legacyEventHandlerApi.postToEventHandler(any(), any())
        } returns Unit
    }

    protected fun verifyLegacyNotification(
        processingOrderId: Int,
        statusId: Int,
        times: Int = 1,
    ) {
        verify(exactly = 1) {
            legacyEventHandlerApi.postToEventHandler(statusId.toString(), processingOrderId.toString())
        }
    }

    @MockkBean
    protected lateinit var complianceFeatureToggleService: ComplianceFeatureToggleService

    protected fun mockFeatureToggleService() {
        every {
            complianceFeatureToggleService.isAnnualReportsLaunchProcessingEnabled(any(), any())
        } returns true
    }

    @MockkBean
    protected lateinit var salesforceApi: SalesforceService

    protected fun mockSalesforceApi() {
        every {
            salesforceApi.createCase(any())
        } returns
            mockk<SalesforceCaseResponse> {
                every { recordId } returns CRM_ID
            }
    }

    @MockkBean
    protected lateinit var processingOrdersApi: ProcessingOrdersApi

    protected fun mockProcessingOrdersApi() {
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(mockk())
    }

    protected fun verifyProcessingStatusUpdate(
        processingOrderId: Int,
        statusId: Int,
        times: Int = 1,
    ) {
        verify(exactly = times) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                statusId,
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @MockkBean
    private lateinit var ordersApiService: OrdersApiService

    protected fun mockOrdersApiService(
        processingOrderId: Int,
        packageProcessingOrderId: Int? = null,
    ) {
        packageProcessingOrderId ?: processingOrderId

        // Get answers processingOrderId
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns
            GetOrderResponse().order(
                OrderDto().orderItems(
                    listOf(
                        OrderItemDto()
                            .processingOrder(
                                ProcessingOrderDto()
                                    .processingOrderId(packageProcessingOrderId ?: processingOrderId),
                            )
                            .productConfiguration(
                                ProductConfigurationDto().productTypeId(
                                    com.legalzoom.api.model.order.RelationshipType.fromValue(RelationshipType.PACKAGE.Id),
                                ),
                            ),
                    ),
                ),
            )
    }

    protected fun mockInstantNow() {
        mockkStatic(Instant::class)
        every {
            Instant.now()
        } returns Instant.parse("2024-01-01T10:00:00.00Z")
    }

    @MockkBean
    private lateinit var ordersContactsApi: OrdersContactsApi

    private fun mockOrdersContactsApi(
        orderId: Int,
        customerId: String,
    ) {
        every {
            ordersContactsApi.coreOrdersOrderIdContactsGet(
                orderId,
                "1.0",
                customerId,
                true,
            ).blockSingle().contacts!!
        } returns
            mutableListOf<ContactInfoDto>(
                defaultPrimaryAddress, defaultShippingAddress,
            )
    }

    /**
     * Individual & Custom Mocks
     */
    protected fun mockDSDDocgenDelegates() {
        every {
            startDsdDocGenJobDelegate.execute(any())
        } returns Unit

        every {
            verifyDsdDocGenOutcomeDelegate.execute(any())
        } returns Unit
    }

    protected fun setupDefaultMocksFor(
        launchMessage: ProductLaunchMessage,
        botEnabled: Boolean = false,
    ): UUID {
        every {
            complianceFeatureToggleService.botNameOverride(
                AR_PROCESS_ID,
                launchMessage.state,
                EntityType.LLC.ordinalValue,
                any(),
            )
        } returns "AR_" + launchMessage.state

        val accountId = UUID.randomUUID()
        every {
            accountService.getAccountIdFromCustomerId(
                launchMessage.customerId,
                launchMessage.processingOrderId.toString(),
                launchMessage.processId,
            )
        } returns accountId

        mockOrdersApiService(launchMessage.processingOrderId)
        mockAnswerApi(launchMessage.processingOrderId)

        if (botEnabled) {
            mockRpaBotsApi()
        }

        every {
            complianceFeatureToggleService.botEnabled(any(), any(), any())
        } returns(botEnabled)

        mockOrdersContactsApi(launchMessage.orderId, launchMessage.customerId)

        return accountId
    }

    @BeforeEach
    fun setup() {
        mockLegacyEventHandlerApi()
        mockFeatureToggleService()
        mockSalesforceApi()
        mockProcessingOrdersApi()
        mockDSDDocgenDelegates()

        every {
            notificationsPlatformService.sendNotification(
                any(),
                any(),
            )
        } returns NotificationResponse()
        mockInstantNow()
    }

    @TestConfiguration
    class BaseIntegrationTestConfiguration {
        @Bean
        fun oauthService(): OAuthService {
            return object : OAuthService {
                override suspend fun getAccessToken(): String {
                    return "fake_token"
                }
            }
        }

        @MockkBean
        lateinit var revvAuthService: RevvAuthService
    }

    /**
     * Launch Helpers
     */
    protected fun launchProcess(launchEvent: ProductLaunchMessage): ProcessInstance {
        kafkaTemplate.send("fulfillment-order-update", launchEvent)
        val instance =
            await(5.seconds).untilNotNull {
                runtimeService.createProcessInstanceQuery()
                    .processInstanceBusinessKey(launchEvent.processingOrderId.toString())
                    .processDefinitionKey(ANNUAL_REPORTS)
                    .singleResult()
            }

        return camundaTestHelpers.waitForStartup(instance.id)
    }

    protected fun generateLaunchEvent(): ProductLaunchMessage {
        return ProductLaunchMessage(
            customerId = UniqueId.nextIdString(),
            orderId = UniqueId.nextInt(),
            processingOrderId = UniqueId.nextInt(),
            processId = 81,
            state = "NJ",
            cp2OrderItemId = null,
        )
    }

    /**
     * Camunda Job Executor Helpers
     */

    protected fun executeNextJob(processInstance: ProcessInstance): Job {
        val job = job(processInstance)
        assertThat(job).isNotNull
            .withFailMessage("No job found for process instance ${processInstance.id}")

        managementService.executeJob(job.id)

        logger.debug(
            "Executed job ${job.id} for process instance ${processInstance.id} for activityId: " +
                (getCurrentActivityId(processInstance)),
        )

        return job
    }

    protected fun executeJobsUntilWaitingAt(
        processInstance: ProcessInstance,
        activityId: String,
    ) {
        while (getCurrentActivityId(processInstance) != activityId && job(processInstance) != null) {
            executeNextJob(processInstance)
        }

        assertThat(processInstance).isWaitingAt(activityId)
    }

    protected fun getCurrentActivityId(processInstance: ProcessInstance): String? {
        val executionId =
            runtimeService.createExecutionQuery()
                .processInstanceId(processInstance.id)
                .list()?.firstOrNull()?.id
        return executionId?.let {
            runtimeService.getActiveActivityIds(
                executionId,
            )?.firstOrNull()
        }
    }

    protected fun getChildSalesforceInstance(parentProcessInstance: ProcessInstance): ProcessInstance {
        val processInstance =
            runtimeService.createProcessInstanceQuery()
                .superProcessInstanceId(parentProcessInstance.id)
                .processDefinitionKey(COMPLIANCE_SALESFORCE)
                .singleResult()

        camundaTestHelpers.waitForStartup(processInstance.id)
        return processInstance
    }

    protected fun isSalesforceInstanceActive(parentProcessInstance: ProcessInstance): Boolean {
        val sfInstance =
            runtimeService.createProcessInstanceQuery()
                .superProcessInstanceId(parentProcessInstance.id)
                .processDefinitionKey(COMPLIANCE_SALESFORCE)
                .singleResult()

        return sfInstance != null
    }

    protected fun waitForSalesforceCaseReady(
        processInstance: ProcessInstance,
        queue: String,
    ): ProcessInstance {
        while (job(processInstance) != null && !isSalesforceInstanceActive(processInstance)) {
            managementService.executeJob(job(processInstance).id)
        }

        val sfInstance = getChildSalesforceInstance(processInstance)

        camundaTestHelpers.executeJobsIncludingActivity(sfInstance, "complete-sf-case")

        assertThat(sfInstance).isWaitingFor(AnnualReportsMessageRefs.SALESFORCE)

        val actualQueue =
            runtimeService.createVariableInstanceQuery()
                .processInstanceIdIn(sfInstance.id)
                .variableNameIn("sfQueue")
                .list()

        assertEquals(queue, actualQueue.first().value as String)

        return sfInstance
    }

    /**
     * Verify methods
     */

    protected fun verifyStatusUpdates(
        processingOrderId: Int,
        vararg statusIds: Int,
    ) {
        for (statusId in statusIds) {
            verifyProcessingStatusUpdate(processingOrderId, statusId)
            verifyLegacyNotification(processingOrderId, statusId)
        }
    }

    protected fun verifyNotificationPlatformCall(
        customerId: String,
        orderId: String,
        processingOrderId: String,
        notificationType: String,
        templateData: Map<String, String>,
        channelTypeEnum: TargetChannel.ChannelTypeEnum,
    ) {
        verify(exactly = 1) {
            notificationsPlatformService.sendNotification(
                NotificationDataRequest(
                    customerId,
                    orderId,
                    processingOrderId,
                    notificationType,
                    templateData,
                ),
                channelTypeEnum,
            )
        }
    }

    protected fun verifyStatusUpdate(
        processingOrderId: Int,
        statusId: Int,
    ) {
        verifyProcessingStatusUpdate(processingOrderId, statusId)
        verifyLegacyNotification(processingOrderId, statusId)
    }

    protected fun getTestBotPayload(
        processingOrderId: Int,
        orderId: Int,
        accountId: String,
        customerId: String,
    ) = mapOf(
        "processingOrderId" to processingOrderId,
        "accountId" to accountId,
        "orderId" to orderId,
        "customerId" to customerId,
    ).plus(defaultBaseBotPayload)

    protected fun verifyBotJobRequest(
        processInstance: ProcessInstance,
        processingOrderId: Int,
        orderId: Int,
        accountId: String,
        customerId: String,
    ) {
        verify(exactly = 1) {
            rpaBotsApi.startBot(
                StartBotRequestDto()
                    .priority(StartBotRequestDto.PriorityEnum.NORMAL)
                    .botName("AR_NJ")
                    .accountId(accountId)
                    .referenceIds(
                        WebAutomationReferenceIdsDto()
                            .processingOrderId(processingOrderId.toString())
                            .cp1OrderId(orderId.toString()),
                    )
                    .payload(getTestBotPayload(processingOrderId, orderId, accountId, customerId)),
            )
        }
    }

    companion object {
        protected val logger: Logger = LoggerFactory.getLogger(ComplianceBaseIntegrationTest::class.java)
        protected val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

        protected const val CRM_ID = "fake-crm-id"
        protected const val TEST_ENTITY_NAME = "test_entity_name"
        protected val TEST_ENTITY_TYPE = AnswersEntityType.LLC

        private val defaultPrimaryAddress =
            ContactInfoDto().contactType(ContactType.Primary)
                .addressLine1("1 Test St")
                .city("City")
                .state("NJ")
                .zipCode("08000")
                .country("USA")
                .mobilePhone("***********")
                .firstName("John")
                .lastName("Smith")

        private val defaultShippingAddress =
            ContactInfoDto().contactType(ContactType.Shipping)
                .addressLine1("1 Test St")
                .addressLine2("Apt 1")
                .city("City")
                .state("NJ")
                .zipCode("08000")
                .country("USA")
                .mobilePhone("***********")
                .firstName("John")
                .lastName("Shipper")

        private val defaultCustomer =
            BotPersonInfoDto(
                firstName = defaultPrimaryAddress.firstName,
                lastName = defaultPrimaryAddress.lastName,
                address =
                    BotAddressDto(
                        addressLine1 = defaultPrimaryAddress.addressLine1!!,
                        city = defaultPrimaryAddress.city!!,
                        state = defaultPrimaryAddress.state!!,
                        zipCode = defaultPrimaryAddress.zipCode!!,
                    ),
                title = null,
                phone = defaultPrimaryAddress.mobilePhone,
            )

        private val defaultOfficers =
            listOf(
                BotPersonInfoDto(
                    firstName = "FirstName",
                    lastName = "LastName",
                    title = "MANAGER",
                    address =
                        BotAddressDto(
                            addressLine1 = "1 Main St",
                            city = "Fakecity",
                            state = "NJ",
                            zipCode = "08322",
                        ),
                    phone = null,
                ),
                BotPersonInfoDto(
                    firstName = "FirstName",
                    lastName = "LastName",
                    title = "STOCKHOLDER",
                    address =
                        BotAddressDto(
                            addressLine1 = "1 Main St",
                            city = "Faketown",
                            state = "NJ",
                            zipCode = "08322",
                        ),
                    phone = null,
                ),
                BotPersonInfoDto(
                    firstName = "FirstName2",
                    lastName = "LastName2",
                    title = "STOCKHOLDER",
                    address =
                        BotAddressDto(
                            addressLine1 = "2 Main St",
                            city = "Fakecity2",
                            state = "NJ",
                            zipCode = "08322",
                        ),
                    phone = null,
                ),
            )

        private val defaultPrincipleAddress =
            BotAddressDto(
                addressLine1 = "1 Main St",
                city = "Cityville",
                state = "NJ",
                zipCode = "12345",
            )

        private val defaultRegisteredAgent =
            BotPersonInfoDto(
                firstName = "United States Corporation Agents, Inc",
                lastName = null,
                address =
                    BotAddressDto(
                        addressLine1 = "330 Changebridge Road, Suite 101",
                        city = "Pine Brook",
                        state = "NJ",
                        zipCode = "07058",
                    ),
                title = null,
                phone = null,
            )

        private const val DEFAULT_ENTITY_ID = "05555550"
        private const val DEFAULT_FILING_YEAR = "2025"

        protected val defaultBaseBotPayload =
            mapOf(
                "principleAddress" to defaultPrincipleAddress,
                "mainAddress" to defaultPrincipleAddress,
                "customer" to defaultCustomer,
                "officers" to defaultOfficers,
                "entityId" to DEFAULT_ENTITY_ID,
                "entityName" to TEST_ENTITY_NAME,
                "filingYear" to DEFAULT_FILING_YEAR,
                "registeredAgent" to defaultRegisteredAgent,
            )
    }
}
