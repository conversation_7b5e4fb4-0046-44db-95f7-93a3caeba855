<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0fr9mxs" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.35.0">
  <bpmn:process id="pre-filing-process" name="pre-filing-process" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0v0gxeu</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_Task_Resolved">
      <bpmn:incoming>Flow_0lcv575</bpmn:incoming>
      <bpmn:outgoing>Flow_1n1fni8</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m4obbp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1n1fni8" name="proceed" sourceRef="Gateway_Task_Resolved" targetRef="Gateway_Proceed">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${disposition == 'proceed'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0w0m419">
      <bpmn:incoming>Flow_12v8gcu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:subProcess id="Activity_0no994n">
      <bpmn:incoming>Flow_1m4obbp</bpmn:incoming>
      <bpmn:incoming>Flow_0q3khrb</bpmn:incoming>
      <bpmn:outgoing>Flow_0r32v6w</bpmn:outgoing>
      <bpmn:startEvent id="Event_1lmkeh6">
        <bpmn:outgoing>Flow_00h9d4y</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="entity-data-fetch" name="Entity Data Fetch Delegate" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationEntityDataFetchDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle>R5/PT3M</camunda:failedJobRetryTimeCycle>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_007v7jv</bpmn:incoming>
        <bpmn:outgoing>Flow_0d5q3k9</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0d5q3k9" sourceRef="entity-data-fetch" targetRef="Activity_0cmub4v" />
      <bpmn:sequenceFlow id="Flow_00h9d4y" sourceRef="Event_1lmkeh6" targetRef="prelim-name-available-order-status-update" />
      <bpmn:endEvent id="Event_1we7hmd">
        <bpmn:incoming>Flow_08x2lqy</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask id="prelim-name-available-order-status-update" name="Assigned For Name Check Order Status Update" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationAssignedForNameCheckStatusDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_00h9d4y</bpmn:incoming>
        <bpmn:outgoing>Flow_007v7jv</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_007v7jv" sourceRef="prelim-name-available-order-status-update" targetRef="entity-data-fetch" />
      <bpmn:serviceTask id="Activity_0cmub4v" name="Fraud Validation" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${detectFraudDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0d5q3k9</bpmn:incoming>
        <bpmn:outgoing>Flow_08x2lqy</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_08x2lqy" sourceRef="Activity_0cmub4v" targetRef="Event_1we7hmd" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0v0gxeu" sourceRef="StartEvent_1" targetRef="create-business-entity" />
    <bpmn:boundaryEvent id="Event_1earjng" name="Error_NoRetries" attachedToRef="Activity_0no994n">
      <bpmn:outgoing>Flow_0gtg16s</bpmn:outgoing>
      <bpmn:errorEventDefinition id="ErrorEventDefinition_0yqxybd" errorRef="Error_0br6cmd" camunda:errorCodeVariable="errorCode" camunda:errorMessageVariable="errorMessage" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0gtg16s" sourceRef="Event_1earjng" targetRef="validation-error-check" />
    <bpmn:serviceTask id="validation-error-check" name="Validation Error Check" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${validationErrorDelegate}">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle>R5/PT3M</camunda:failedJobRetryTimeCycle>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0r32v6w</bpmn:incoming>
      <bpmn:incoming>Flow_0gtg16s</bpmn:incoming>
      <bpmn:incoming>Flow_16lpnt2</bpmn:incoming>
      <bpmn:incoming>Flow_0ybw4q6</bpmn:incoming>
      <bpmn:outgoing>Flow_13cwpch</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0r32v6w" sourceRef="Activity_0no994n" targetRef="validation-error-check" />
    <bpmn:exclusiveGateway id="Gateway_QC" default="Flow_0610bum">
      <bpmn:incoming>Flow_13cwpch</bpmn:incoming>
      <bpmn:outgoing>Flow_1cpa0jp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0610bum</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_13cwpch" sourceRef="validation-error-check" targetRef="Gateway_QC" />
    <bpmn:sequenceFlow id="Flow_1cpa0jp" name="QC/Exception" sourceRef="Gateway_QC" targetRef="pre-filing-salesforce-activity">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('validationError') == true || (empty execution.getVariable('disposition') &amp;&amp; execution.getVariable('preFilingQcEnabled') == true )}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0610bum" name="else" sourceRef="Gateway_QC" targetRef="Gateway_Proceed" />
    <bpmn:exclusiveGateway id="Gateway_Proceed" default="Flow_09b1nja">
      <bpmn:incoming>Flow_1n1fni8</bpmn:incoming>
      <bpmn:incoming>Flow_0610bum</bpmn:incoming>
      <bpmn:outgoing>Flow_09b1nja</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09b1nja" sourceRef="Gateway_Proceed" targetRef="entity-name-prelim-available-status-update" />
    <bpmn:sequenceFlow id="Flow_12v8gcu" sourceRef="pre-filing-save-entity-name" targetRef="Event_0w0m419" />
    <bpmn:serviceTask id="pre-filing-save-entity-name" name="Save Entity Name" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationSaveEntityNameDelegate}">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1knvx81</bpmn:incoming>
      <bpmn:outgoing>Flow_12v8gcu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:boundaryEvent id="Event_09g9yp8" attachedToRef="pre-filing-save-entity-name">
      <bpmn:outgoing>Flow_16lpnt2</bpmn:outgoing>
      <bpmn:errorEventDefinition id="ErrorEventDefinition_14qps6w" errorRef="Error_0br6cmd" camunda:errorCodeVariable="errorCode" camunda:errorMessageVariable="errorMessage" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_16lpnt2" sourceRef="Event_09g9yp8" targetRef="validation-error-check" />
    <bpmn:serviceTask id="entity-name-prelim-available-status-update" name="Entity Name Prelim. Available Status Update" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationPrelimNameAvailableStatusDelegate}">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_09b1nja</bpmn:incoming>
      <bpmn:outgoing>Flow_1knvx81</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:boundaryEvent id="Event_0pbq2h8" attachedToRef="entity-name-prelim-available-status-update">
      <bpmn:outgoing>Flow_0ybw4q6</bpmn:outgoing>
      <bpmn:errorEventDefinition id="ErrorEventDefinition_1jw91me" errorRef="Error_0br6cmd" camunda:errorCodeVariable="errorCode" camunda:errorMessageVariable="errorMessage" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1knvx81" sourceRef="entity-name-prelim-available-status-update" targetRef="pre-filing-save-entity-name" />
    <bpmn:sequenceFlow id="Flow_0ybw4q6" sourceRef="Event_0pbq2h8" targetRef="validation-error-check" />
    <bpmn:sequenceFlow id="Flow_1m4obbp" name="retry" sourceRef="Gateway_Task_Resolved" targetRef="Activity_0no994n">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${disposition == 'retry'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="pre-filing-salesforce-activity" name="salesforce-activity" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce-process">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cpa0jp</bpmn:incoming>
      <bpmn:outgoing>Flow_0lcv575</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0lcv575" sourceRef="pre-filing-salesforce-activity" targetRef="Gateway_Task_Resolved" />
    <bpmn:serviceTask id="create-business-entity" name="Create Business Entity" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationCreateBusinessEntityDelegate}">
      <bpmn:incoming>Flow_0v0gxeu</bpmn:incoming>
      <bpmn:outgoing>Flow_0q3khrb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0q3khrb" sourceRef="create-business-entity" targetRef="Activity_0no994n" />
  </bpmn:process>
  <bpmn:error id="Error_0br6cmd" name="Error_NoRetries" errorCode="Error_NoRetries" camunda:errorMessage="Error Boundary Event" />
  <bpmn:message id="Message_1ubbcnt" name="Message_SIGNATURE" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="pre-filing-process">
      <bpmndi:BPMNShape id="Gateway_0ba24p0_di" bpmnElement="Gateway_Task_Resolved" isMarkerVisible="true">
        <dc:Bounds x="1665" y="555" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0w0m419_di" bpmnElement="Event_0w0m419">
        <dc:Bounds x="2282" y="562" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ixb744_di" bpmnElement="create-business-entity">
        <dc:Bounds x="240" y="350" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0no994n_di" bpmnElement="Activity_0no994n" isExpanded="true">
        <dc:Bounds x="400" y="110" width="690" height="562" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1we7hmd_di" bpmnElement="Event_1we7hmd">
        <dc:Bounds x="992" y="369" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lmkeh6_di" bpmnElement="Event_1lmkeh6">
        <dc:Bounds x="452" y="369" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0t7vlsg" bpmnElement="Activity_0cmub4v">
        <dc:Bounds x="830" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zd4qcc_di" bpmnElement="entity-data-fetch">
        <dc:Bounds x="680" y="347" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o34l9h" bpmnElement="prelim-name-available-order-status-update">
        <dc:Bounds x="530" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0d5q3k9_di" bpmnElement="Flow_0d5q3k9">
        <di:waypoint x="780" y="387" />
        <di:waypoint x="830" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00h9d4y_di" bpmnElement="Flow_00h9d4y">
        <di:waypoint x="488" y="387" />
        <di:waypoint x="530" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_007v7jv_di" bpmnElement="Flow_007v7jv">
        <di:waypoint x="630" y="387" />
        <di:waypoint x="680" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08x2lqy_di" bpmnElement="Flow_08x2lqy">
        <di:waypoint x="930" y="387" />
        <di:waypoint x="992" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1vfji9n_di" bpmnElement="validation-error-check">
        <dc:Bounds x="1200" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0sotvpb_di" bpmnElement="Gateway_QC" isMarkerVisible="true">
        <dc:Bounds x="1375" y="555" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1577" y="2002" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_13uiaea_di" bpmnElement="Gateway_Proceed" isMarkerVisible="true">
        <dc:Bounds x="1815" y="555" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16yj5tn_di" bpmnElement="pre-filing-save-entity-name">
        <dc:Bounds x="2110" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kck4s4" bpmnElement="entity-name-prelim-available-status-update">
        <dc:Bounds x="1930" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lhk548_di" bpmnElement="pre-filing-salesforce-activity">
        <dc:Bounds x="1510" y="540" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0amh295" bpmnElement="Event_0pbq2h8">
        <dc:Bounds x="2012" y="522" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1jj7x1q_di" bpmnElement="Event_09g9yp8">
        <dc:Bounds x="2192" y="522" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1nbrusc_di" bpmnElement="Event_1earjng">
        <dc:Bounds x="822" y="654" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="860" y="685" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1n1fni8_di" bpmnElement="Flow_1n1fni8">
        <di:waypoint x="1715" y="580" />
        <di:waypoint x="1815" y="580" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1746" y="562" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v0gxeu_di" bpmnElement="Flow_0v0gxeu">
        <di:waypoint x="188" y="390" />
        <di:waypoint x="240" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gtg16s_di" bpmnElement="Flow_0gtg16s">
        <di:waypoint x="840" y="690" />
        <di:waypoint x="840" y="870" />
        <di:waypoint x="1250" y="870" />
        <di:waypoint x="1250" y="620" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r32v6w_di" bpmnElement="Flow_0r32v6w">
        <di:waypoint x="1090" y="580" />
        <di:waypoint x="1200" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13cwpch_di" bpmnElement="Flow_13cwpch">
        <di:waypoint x="1300" y="580" />
        <di:waypoint x="1375" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cpa0jp_di" bpmnElement="Flow_1cpa0jp">
        <di:waypoint x="1425" y="580" />
        <di:waypoint x="1510" y="580" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1409" y="562" width="68" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0610bum_di" bpmnElement="Flow_0610bum">
        <di:waypoint x="1400" y="555" />
        <di:waypoint x="1400" y="310" />
        <di:waypoint x="1840" y="310" />
        <di:waypoint x="1840" y="555" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1633" y="288" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09b1nja_di" bpmnElement="Flow_09b1nja">
        <di:waypoint x="1865" y="580" />
        <di:waypoint x="1930" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12v8gcu_di" bpmnElement="Flow_12v8gcu">
        <di:waypoint x="2210" y="580" />
        <di:waypoint x="2282" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16lpnt2_di" bpmnElement="Flow_16lpnt2">
        <di:waypoint x="2210" y="522" />
        <di:waypoint x="2210" y="180" />
        <di:waypoint x="1250" y="180" />
        <di:waypoint x="1250" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1knvx81_di" bpmnElement="Flow_1knvx81">
        <di:waypoint x="2030" y="580" />
        <di:waypoint x="2110" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ybw4q6_di" bpmnElement="Flow_0ybw4q6">
        <di:waypoint x="2030" y="522" />
        <di:waypoint x="2030" y="240" />
        <di:waypoint x="1250" y="240" />
        <di:waypoint x="1250" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m4obbp_di" bpmnElement="Flow_1m4obbp">
        <di:waypoint x="1690" y="605" />
        <di:waypoint x="1690" y="930" />
        <di:waypoint x="590" y="930" />
        <di:waypoint x="590" y="672" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1130" y="912" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lcv575_di" bpmnElement="Flow_0lcv575">
        <di:waypoint x="1610" y="580" />
        <di:waypoint x="1665" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q3khrb_di" bpmnElement="Flow_0q3khrb">
        <di:waypoint x="340" y="390" />
        <di:waypoint x="400" y="390" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
