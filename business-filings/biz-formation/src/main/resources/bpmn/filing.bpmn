<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0f34v8i" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.34.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:collaboration id="filing">
    <bpmn:participant id="Participant_1t5d3js" name="Bot" />
    <bpmn:participant id="Participant_0aonio6" name="Orchestrator" />
    <bpmn:participant id="Participant_0q08ffs" name="Answer/Filing-data" />
    <bpmn:participant id="Participant_0htsnbl" name="Filing" processRef="filing-process" />
    <bpmn:messageFlow id="Flow_1im7s8g" sourceRef="Participant_0aonio6" targetRef="Participant_1t5d3js" />
    <bpmn:messageFlow id="Flow_05gavz1" sourceRef="Participant_1t5d3js" targetRef="Participant_0q08ffs" />
    <bpmn:messageFlow id="Flow_0pbpep3" sourceRef="Participant_1t5d3js" targetRef="rpa-task" />
    <bpmn:messageFlow id="Flow_0fwayb8" sourceRef="queue-rpa-task" targetRef="Participant_0aonio6" />
    <bpmn:textAnnotation id="TextAnnotation_0uykq2t">
      <bpmn:text>Send out a ledger note and a fulfillment event about the retry</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1ej2294" associationDirection="None" sourceRef="Activity_08cobvt" targetRef="TextAnnotation_0uykq2t" />
    <bpmn:textAnnotation id="TextAnnotation_0ickdk9">
      <bpmn:text>Validation Error
And need Agent review</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_02sebod" associationDirection="None" sourceRef="Flow_0r5owo6" targetRef="TextAnnotation_0ickdk9" />
    <bpmn:textAnnotation id="TextAnnotation_0ep75q7">
      <bpmn:text>Resolved</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0rm7pm0" associationDirection="None" sourceRef="Flow_1p4g0o1" targetRef="TextAnnotation_0ep75q7" />
  </bpmn:collaboration>
  <bpmn:message id="Message_0y0ig5m" name="Message_RPA" />
  <bpmn:process id="filing-process" isExecutable="true">
    <bpmn:startEvent id="Event_0yrtavm">
      <bpmn:outgoing>Flow_0v3pt9f</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:subProcess id="Activity_1qour72">
      <bpmn:incoming>Flow_0v3pt9f</bpmn:incoming>
      <bpmn:incoming>sf-case-resolved</bpmn:incoming>
      <bpmn:outgoing>Flow_0yl9rxz</bpmn:outgoing>
      <bpmn:endEvent id="Event_0s9skhu">
        <bpmn:incoming>Flow_0vcpr1d</bpmn:incoming>
        <bpmn:incoming>Flow_0ur440p</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask id="rpa-callback-task" name="Process CallBack FROM RPA" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${robotCallbackDelegate}">
        <bpmn:extensionElements>
          <camunda:executionListener expression="${execution.setVariable(&#39;sfDelayTimer&#39;, null)}" event="end" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1nlos3r</bpmn:incoming>
        <bpmn:outgoing>Flow_1m4rala</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:receiveTask id="rpa-task" name="Receive CallBack FROM RPA" messageRef="Message_0y0ig5m">
        <bpmn:incoming>Flow_1ez5nnt</bpmn:incoming>
        <bpmn:incoming>Flow_19w7ept</bpmn:incoming>
        <bpmn:outgoing>Flow_1mfslbt</bpmn:outgoing>
      </bpmn:receiveTask>
      <bpmn:serviceTask id="queue-rpa-task" name="Queue RPA Job" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationQueueRPAJobDelegate}">
        <bpmn:incoming>Flow_0izhsc0</bpmn:incoming>
        <bpmn:outgoing>Flow_1ez5nnt</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:startEvent id="StartEvent_1">
        <bpmn:outgoing>Flow_1qnexjg</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:exclusiveGateway id="Gateway_0s1wze4" default="Flow_0difanc">
        <bpmn:incoming>Flow_0ipp7w8</bpmn:incoming>
        <bpmn:outgoing>Flow_0difanc</bpmn:outgoing>
        <bpmn:outgoing>Flow_02lhjdz</bpmn:outgoing>
        <bpmn:outgoing>Flow_18bfg6e</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1qnexjg" sourceRef="StartEvent_1" targetRef="Gateway_00w0ozh" />
      <bpmn:sequenceFlow id="Flow_0difanc" sourceRef="Gateway_0s1wze4" targetRef="Gateway_0mwhxdm" />
      <bpmn:sequenceFlow id="Flow_02lhjdz" name="retry-callback" sourceRef="Gateway_0s1wze4" targetRef="Gateway_1aa7ay8">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('disposition') == 'retry' &amp;&amp; execution.getVariable('status') == 'Success' &amp;&amp; !bypassRpaService.isBypassRpa(execution)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:exclusiveGateway id="Gateway_1aa7ay8" default="Flow_1nlos3r">
        <bpmn:incoming>Flow_1mfslbt</bpmn:incoming>
        <bpmn:incoming>Flow_02lhjdz</bpmn:incoming>
        <bpmn:incoming>Flow_1umf4x5</bpmn:incoming>
        <bpmn:incoming>Flow_04yu5jq</bpmn:incoming>
        <bpmn:outgoing>Flow_1nlos3r</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1mfslbt" sourceRef="rpa-task" targetRef="Gateway_1aa7ay8" />
      <bpmn:sequenceFlow id="Flow_1nlos3r" sourceRef="Gateway_1aa7ay8" targetRef="rpa-callback-task">
        <bpmn:extensionElements />
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_18bfg6e" name="already-filed" sourceRef="Gateway_0s1wze4" targetRef="Gateway_0z6syka">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('alreadyFiled') == true || execution.getVariable('disposition') == 'proceed'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:serviceTask id="check-filing-status" name="Check Filing Status" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${checkFilingStatusDelegate}">
        <bpmn:incoming>Flow_0h5opix</bpmn:incoming>
        <bpmn:incoming>Flow_00att7b</bpmn:incoming>
        <bpmn:incoming>Flow_0zco6jo</bpmn:incoming>
        <bpmn:outgoing>Flow_0ipp7w8</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_0z6syka" default="Flow_076phsf">
        <bpmn:incoming>Flow_1m4rala</bpmn:incoming>
        <bpmn:incoming>Flow_18bfg6e</bpmn:incoming>
        <bpmn:outgoing>Flow_1bv0cdw</bpmn:outgoing>
        <bpmn:outgoing>Flow_076phsf</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1m4rala" sourceRef="rpa-callback-task" targetRef="Gateway_0z6syka" />
      <bpmn:sequenceFlow id="Flow_18bdfjt" name="Should save filing metadata" sourceRef="Gateway_0hrlf8t" targetRef="save-document-upload-fields">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${instantFilingService.shouldSaveFilingMetadata(execution)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:exclusiveGateway id="Gateway_00qsmjr" default="Flow_0vcpr1d">
        <bpmn:incoming>Flow_0coa4j5</bpmn:incoming>
        <bpmn:incoming>Flow_1bjx9a5</bpmn:incoming>
        <bpmn:incoming>Flow_13961ey</bpmn:incoming>
        <bpmn:incoming>Flow_1sawvw2</bpmn:incoming>
        <bpmn:outgoing>Flow_0vcpr1d</bpmn:outgoing>
        <bpmn:outgoing>Flow_0ru50jj</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_0vcpr1d" sourceRef="Gateway_00qsmjr" targetRef="Event_0s9skhu" />
      <bpmn:sequenceFlow id="Flow_0coa4j5" name="non instant filing" sourceRef="Gateway_0hrlf8t" targetRef="Gateway_00qsmjr" />
      <bpmn:serviceTask id="Activity_Get_RPA_Job" name="Get RPA Job" camunda:asyncBefore="true" camunda:delegateExpression="${bizFormationCheckRPAJobStatusDelegate}">
        <bpmn:incoming>Flow_0qbv8td</bpmn:incoming>
        <bpmn:outgoing>Flow_1jlvicr</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1jlvicr" sourceRef="Activity_Get_RPA_Job" targetRef="Gateway_04q9j1y" />
      <bpmn:boundaryEvent id="filing-one-day-expired-timer" name="1 day" attachedToRef="rpa-task">
        <bpmn:extensionElements />
        <bpmn:outgoing>Flow_1g6m8jr</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_1thoa2z">
          <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${execution.getVariable('sfDelayTimer')}</bpmn:timeDate>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0ru50jj" name="Success" sourceRef="Gateway_00qsmjr" targetRef="filing-success-order-status-update">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('validationError') != true}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:serviceTask id="save-document-upload-fields" name="Save Document Upload Fields" camunda:asyncBefore="true" camunda:delegateExpression="${saveDocumentUploadFieldsDelegate}">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_18bdfjt</bpmn:incoming>
        <bpmn:outgoing>Flow_07rnr04</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_07rnr04" sourceRef="save-document-upload-fields" targetRef="instant-filing-success-order-status-update" />
      <bpmn:exclusiveGateway id="Gateway_0mwhxdm" default="Flow_1ma5dqw">
        <bpmn:incoming>Flow_0difanc</bpmn:incoming>
        <bpmn:outgoing>Flow_1ma5dqw</bpmn:outgoing>
        <bpmn:outgoing>Flow_1qsju6i</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="generate-documents-task" name="Generate Pre Filing Documents" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationGeneratePreFilingDocumentsDelegate}">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_1se2z9u</bpmn:incoming>
        <bpmn:outgoing>Flow_0tt5txh</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0tt5txh" sourceRef="generate-documents-task" targetRef="documents-generated-task" />
      <bpmn:receiveTask id="documents-generated-task" name="Documents Generated" messageRef="Message_2k9q037">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_0tt5txh</bpmn:incoming>
        <bpmn:outgoing>Flow_10njbcu</bpmn:outgoing>
      </bpmn:receiveTask>
      <bpmn:sequenceFlow id="Flow_0qyusnt" sourceRef="Event_0lpus12" targetRef="upload-documents-doc-gen-task" />
      <bpmn:sequenceFlow id="Flow_10njbcu" sourceRef="documents-generated-task" targetRef="upload-documents-doc-gen-task" />
      <bpmn:serviceTask id="upload-documents-doc-gen-task" name="Upload Documents" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationUploadPrefilingDocumentsDelegate}">
        <bpmn:incoming>Flow_10njbcu</bpmn:incoming>
        <bpmn:incoming>Flow_0qyusnt</bpmn:incoming>
        <bpmn:outgoing>Flow_16z45vo</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0ipp7w8" sourceRef="check-filing-status" targetRef="Gateway_0s1wze4" />
      <bpmn:sequenceFlow id="Flow_1ma5dqw" sourceRef="Gateway_0mwhxdm" targetRef="Gateway_0vtj3pw" />
      <bpmn:exclusiveGateway id="Gateway_0vtj3pw" default="Flow_0izhsc0">
        <bpmn:incoming>Flow_1ma5dqw</bpmn:incoming>
        <bpmn:incoming>Flow_16z45vo</bpmn:incoming>
        <bpmn:incoming>Flow_0c06drr</bpmn:incoming>
        <bpmn:outgoing>Flow_0izhsc0</bpmn:outgoing>
        <bpmn:outgoing>Flow_1bjx9a5</bpmn:outgoing>
        <bpmn:outgoing>Flow_1tdof3s</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_16z45vo" sourceRef="upload-documents-doc-gen-task" targetRef="Gateway_0vtj3pw" />
      <bpmn:sequenceFlow id="Flow_0izhsc0" sourceRef="Gateway_0vtj3pw" targetRef="queue-rpa-task" />
      <bpmn:sequenceFlow id="Flow_1bv0cdw" name="retriable" sourceRef="Gateway_0z6syka" targetRef="delay-retry">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('retriable') == true &amp;&amp; execution.getVariable('validationError') != true &amp;&amp; execution.getVariable('status') != 'Success'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1uqzynv" sourceRef="delay-retry" targetRef="Activity_08cobvt" />
      <bpmn:intermediateCatchEvent id="delay-retry">
        <bpmn:incoming>Flow_1bv0cdw</bpmn:incoming>
        <bpmn:outgoing>Flow_1uqzynv</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_04zsa54">
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${rpaRetryService.getRetryWaitDuration(execution)}</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_1bjx9a5" name="validation error" sourceRef="Gateway_0vtj3pw" targetRef="Gateway_00qsmjr">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('validationError') == true}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1ez5nnt" sourceRef="queue-rpa-task" targetRef="rpa-task" />
      <bpmn:sequenceFlow id="Flow_04yu5jq" sourceRef="delete-rpa-job-from-queue-task" targetRef="Gateway_1aa7ay8" />
      <bpmn:serviceTask id="delete-rpa-job-from-queue-task" name="Delete RPA job from queue" camunda:asyncBefore="true" camunda:delegateExpression="${bizFormationDeleteRPAJobDelegate}">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_1g6m8jr</bpmn:incoming>
        <bpmn:outgoing>Flow_04yu5jq</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="filing-success-order-status-update" name="Delayed Sent To SOS or Instant Document Received and RA Complete Order Status Update" default="Flow_0ur440p" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationFilingCompleteDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0ru50jj</bpmn:incoming>
        <bpmn:outgoing>Flow_0ur440p</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0ur440p" sourceRef="filing-success-order-status-update" targetRef="Event_0s9skhu" />
      <bpmn:serviceTask id="save-entity-name" name="Save Entity Name" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationSaveEntityNameDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0b7z7r4</bpmn:incoming>
        <bpmn:outgoing>Flow_0h5opix</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="entity-data-fetch" name="Entity Data Fetch Delegate" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationEntityDataFetchDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle>R5/PT3M</camunda:failedJobRetryTimeCycle>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0swgipy</bpmn:incoming>
        <bpmn:outgoing>Flow_0b7z7r4</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_00w0ozh" default="Flow_149r2aq">
        <bpmn:incoming>Flow_1qnexjg</bpmn:incoming>
        <bpmn:outgoing>Flow_0swgipy</bpmn:outgoing>
        <bpmn:outgoing>Flow_149r2aq</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_0swgipy" name="Retrying after solved ssorco" sourceRef="Gateway_00w0ozh" targetRef="entity-data-fetch">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('sfCreatedSelfServeOrco') == true &amp;&amp; execution.getVariable('disposition') == 'retry'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0b7z7r4" sourceRef="entity-data-fetch" targetRef="save-entity-name" />
      <bpmn:sequenceFlow id="Flow_0h5opix" sourceRef="save-entity-name" targetRef="check-filing-status" />
      <bpmn:sequenceFlow id="Flow_149r2aq" sourceRef="Gateway_00w0ozh" targetRef="check-migrated-manual-bypass" />
      <bpmn:serviceTask id="instant-filing-success-order-status-update" name="Instant Filing Sent To SOS Order Status Update" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationInstantFilingCompleteDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_07rnr04</bpmn:incoming>
        <bpmn:outgoing>Flow_13961ey</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_13961ey" sourceRef="instant-filing-success-order-status-update" targetRef="Gateway_00qsmjr" />
      <bpmn:userTask id="manual-filing-task" name="Manual  Filing Case" camunda:asyncBefore="true">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="salesforceEventPhase">MANUAL_FILING</camunda:inputParameter>
            <camunda:inputParameter name="eventType">MANUAL_FILING</camunda:inputParameter>
            <camunda:inputParameter name="validationError">${true}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0bt6usy</bpmn:incoming>
        <bpmn:incoming>Flow_1tdof3s</bpmn:incoming>
        <bpmn:incoming>manual-migrated-bypass</bpmn:incoming>
        <bpmn:outgoing>Flow_1sawvw2</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:boundaryEvent id="Event_0a45d2w" attachedToRef="manual-filing-task">
        <bpmn:outgoing>Flow_1h3fal4</bpmn:outgoing>
        <bpmn:errorEventDefinition id="ErrorEventDefinition_0955t61" errorRef="Error_0tjp1nk" camunda:errorCodeVariable="errorCode" camunda:errorMessageVariable="errorMessage" />
      </bpmn:boundaryEvent>
      <bpmn:intermediateCatchEvent id="Event_1jk1232">
        <bpmn:incoming>Flow_1h3fal4</bpmn:incoming>
        <bpmn:outgoing>Flow_0bt6usy</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0vzdrlt">
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT1H</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0bt6usy" sourceRef="Event_1jk1232" targetRef="manual-filing-task" />
      <bpmn:sequenceFlow id="Flow_1h3fal4" sourceRef="Event_0a45d2w" targetRef="Event_1jk1232" />
      <bpmn:sequenceFlow id="Flow_1tdof3s" name="manual SOS filing required" sourceRef="Gateway_0vtj3pw" targetRef="manual-filing-task">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${manualFilingService.manualSosFilingRequired(execution) &amp;&amp; execution.getVariable('validationError') != true}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1sawvw2" sourceRef="manual-filing-task" targetRef="Gateway_00qsmjr" />
      <bpmn:boundaryEvent id="Event_0lpus12" name="timer max 1 hour" attachedToRef="documents-generated-task">
        <bpmn:outgoing>Flow_0qyusnt</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_16yxgnb">
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT1H</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:serviceTask id="Activity_08cobvt" name="Retry Notice" camunda:asyncBefore="true" camunda:delegateExpression="${rpaRetryDelegate}">
        <bpmn:incoming>Flow_1uqzynv</bpmn:incoming>
        <bpmn:outgoing>Flow_00att7b</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_00att7b" sourceRef="Activity_08cobvt" targetRef="check-filing-status" />
      <bpmn:exclusiveGateway id="Gateway_0hrlf8t">
        <bpmn:incoming>Flow_076phsf</bpmn:incoming>
        <bpmn:outgoing>Flow_18bdfjt</bpmn:outgoing>
        <bpmn:outgoing>Flow_0coa4j5</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_076phsf" sourceRef="Gateway_0z6syka" targetRef="Gateway_0hrlf8t" />
      <bpmn:exclusiveGateway id="check-migrated-manual-bypass" default="Flow_0zco6jo">
        <bpmn:incoming>Flow_149r2aq</bpmn:incoming>
        <bpmn:outgoing>Flow_0zco6jo</bpmn:outgoing>
        <bpmn:outgoing>manual-migrated-bypass</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_0zco6jo" sourceRef="check-migrated-manual-bypass" targetRef="check-filing-status" />
      <bpmn:sequenceFlow id="manual-migrated-bypass" name="Migrated &#38; Manual Filing Passed" sourceRef="check-migrated-manual-bypass" targetRef="manual-filing-task">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('orderMigrated') &amp;&amp; execution.getVariable('manualSosFilingRequired') }</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:exclusiveGateway id="Gateway_04q9j1y" default="Flow_1umf4x5">
        <bpmn:incoming>Flow_1jlvicr</bpmn:incoming>
        <bpmn:outgoing>Flow_1umf4x5</bpmn:outgoing>
        <bpmn:outgoing>Flow_19w7ept</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1umf4x5" sourceRef="Gateway_04q9j1y" targetRef="Gateway_1aa7ay8" />
      <bpmn:sequenceFlow id="Flow_19w7ept" sourceRef="Gateway_04q9j1y" targetRef="rpa-task">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('status') == 'InProgress' || execution.getVariable('status') == 'New' }</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:boundaryEvent id="filing-two-hours-expired-timer" name="2 hours" attachedToRef="rpa-task">
        <bpmn:outgoing>Flow_0qbv8td</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_17w2ki0">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R5/PT2H</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_1g6m8jr" sourceRef="filing-one-day-expired-timer" targetRef="delete-rpa-job-from-queue-task" />
      <bpmn:sequenceFlow id="Flow_0qbv8td" sourceRef="filing-two-hours-expired-timer" targetRef="Activity_Get_RPA_Job" />
      <bpmn:exclusiveGateway id="Gateway_09ogf1z" default="Flow_0c06drr">
        <bpmn:incoming>Flow_0epgd34</bpmn:incoming>
        <bpmn:outgoing>Flow_0c06drr</bpmn:outgoing>
        <bpmn:outgoing>Flow_0r5owo6</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_0c06drr" sourceRef="Gateway_09ogf1z" targetRef="Gateway_0vtj3pw" />
      <bpmn:callActivity id="filing-dsd-docgen-salesforce-activity" name="Salesforce Activity" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce-process">
        <bpmn:extensionElements>
          <camunda:in variables="all" />
          <camunda:in businessKey="#{execution.processBusinessKey}" />
          <camunda:out variables="all" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0r5owo6</bpmn:incoming>
        <bpmn:outgoing>Flow_1p4g0o1</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:sequenceFlow id="Flow_0r5owo6" sourceRef="Gateway_09ogf1z" targetRef="filing-dsd-docgen-salesforce-activity">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable(validationError) == true}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:callActivity id="post-filing-dsd-doc-gen" name="DSD Doc Gen" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="callable-dsd-doc-gen-process">
        <bpmn:extensionElements>
          <camunda:in source="preFilingDSDDocGenRequests" target="requests" />
          <camunda:in businessKey="#{execution.processBusinessKey}" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1p3y9dl</bpmn:incoming>
        <bpmn:incoming>Flow_1p4g0o1</bpmn:incoming>
        <bpmn:outgoing>Flow_0epgd34</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:serviceTask id="biz-formation-determine-pre-filing-doc-gen" name="Determine pre Filing Doc Gen" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${bizFormationDeterminePreFilingDocGenDelegate}">
        <bpmn:incoming>Flow_1qsju6i</bpmn:incoming>
        <bpmn:outgoing>Flow_1f42k3j</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_0fj5v9r" default="Flow_1se2z9u">
        <bpmn:incoming>Flow_1f42k3j</bpmn:incoming>
        <bpmn:outgoing>Flow_1se2z9u</bpmn:outgoing>
        <bpmn:outgoing>Flow_1p3y9dl</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1qsju6i" name="doc filing" sourceRef="Gateway_0mwhxdm" targetRef="biz-formation-determine-pre-filing-doc-gen">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${documentFilingService.isDocumentFilingState(execution) }</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1f42k3j" sourceRef="biz-formation-determine-pre-filing-doc-gen" targetRef="Gateway_0fj5v9r" />
      <bpmn:sequenceFlow id="Flow_1se2z9u" sourceRef="Gateway_0fj5v9r" targetRef="generate-documents-task" />
      <bpmn:sequenceFlow id="Flow_1p3y9dl" sourceRef="Gateway_0fj5v9r" targetRef="post-filing-dsd-doc-gen">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('preFilingDSDDocGenRequests') != null}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0epgd34" sourceRef="post-filing-dsd-doc-gen" targetRef="Gateway_09ogf1z" />
      <bpmn:sequenceFlow id="Flow_1p4g0o1" sourceRef="filing-dsd-docgen-salesforce-activity" targetRef="post-filing-dsd-doc-gen" />
    </bpmn:subProcess>
    <bpmn:exclusiveGateway id="Gateway_13zp4s2" default="Flow_1k79yqm">
      <bpmn:incoming>Flow_1vh6m6f</bpmn:incoming>
      <bpmn:outgoing>Fail</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k79yqm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="validation-error-task" name="validation error check" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${validationErrorDelegate}">
      <bpmn:incoming>Flow_0kvvk94</bpmn:incoming>
      <bpmn:outgoing>Flow_1vh6m6f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="orco-creation" name="Orco Creation" camunda:asyncBefore="true" camunda:delegateExpression="${createOrcoDelegate}">
      <bpmn:incoming>Flow_1uuj8py</bpmn:incoming>
      <bpmn:outgoing>Flow_0kvvk94</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="filing-salesforce-activity" name="Salesforce Activity" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce-process">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Fail</bpmn:incoming>
      <bpmn:outgoing>sf-case-resolved</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:endEvent id="Event_1oqoca5">
      <bpmn:incoming>Flow_1k79yqm</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1seir5u" default="Flow_1uuj8py">
      <bpmn:incoming>Flow_0at0fq0</bpmn:incoming>
      <bpmn:incoming>Flow_0yl9rxz</bpmn:incoming>
      <bpmn:outgoing>Flow_1uuj8py</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:boundaryEvent id="Event_17ur9ic" name="Error_NoRetries" attachedToRef="Activity_1qour72">
      <bpmn:outgoing>Flow_0at0fq0</bpmn:outgoing>
      <bpmn:errorEventDefinition id="ErrorEventDefinition_0bg1aku" errorRef="Error_0tjp1nk" camunda:errorCodeVariable="errorCode" camunda:errorMessageVariable="errorMessage" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0v3pt9f" sourceRef="Event_0yrtavm" targetRef="Activity_1qour72" />
    <bpmn:sequenceFlow id="sf-case-resolved" name="resolved" sourceRef="filing-salesforce-activity" targetRef="Activity_1qour72" />
    <bpmn:sequenceFlow id="Flow_0yl9rxz" sourceRef="Activity_1qour72" targetRef="Gateway_1seir5u" />
    <bpmn:sequenceFlow id="Flow_1vh6m6f" sourceRef="validation-error-task" targetRef="Gateway_13zp4s2" />
    <bpmn:sequenceFlow id="Fail" name="fail" sourceRef="Gateway_13zp4s2" targetRef="filing-salesforce-activity">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('validationError') == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1k79yqm" sourceRef="Gateway_13zp4s2" targetRef="Event_1oqoca5" />
    <bpmn:sequenceFlow id="Flow_0kvvk94" sourceRef="orco-creation" targetRef="validation-error-task" />
    <bpmn:sequenceFlow id="Flow_1uuj8py" sourceRef="Gateway_1seir5u" targetRef="orco-creation" />
    <bpmn:sequenceFlow id="Flow_0at0fq0" name="Error" sourceRef="Event_17ur9ic" targetRef="Gateway_1seir5u" />
  </bpmn:process>
  <bpmn:error id="Error_0tjp1nk" name="Error_NoRetries" errorCode="Error_NoRetries" camunda:errorMessage="Error Boundary Event" />
  <bpmn:message id="Message_1mnlpkj" name="Message_Route_Legacy" />
  <bpmn:message id="Message_2k9q037" name="Message_DOCGEN" />
  <bpmn:message id="Message_19gtlnc" name="Message_FAX" />
  <bpmn:error id="Error_0br6cmd" name="Error_NoRetries" errorCode="Error_NoRetries" camunda:errorMessage="Error Boundary Event" />
  <bpmn:message id="Message_3jtfk2v" name="Message_DSD_DocGenJob_Complete" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="filing">
      <bpmndi:BPMNShape id="Participant_1n3kzm8_di" bpmnElement="Participant_1t5d3js" isHorizontal="true">
        <dc:Bounds x="1890" y="190" width="300" height="60" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_0vfx3kz_di" bpmnElement="Participant_0aonio6" isHorizontal="true">
        <dc:Bounds x="1520" y="190" width="300" height="60" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_0q08ffs_di" bpmnElement="Participant_0q08ffs" isHorizontal="true">
        <dc:Bounds x="1860" y="80" width="300" height="60" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_0htsnbl_di" bpmnElement="Participant_0htsnbl" isHorizontal="true">
        <dc:Bounds x="160" y="510" width="4160" height="1190" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yrtavm_di" bpmnElement="Event_0yrtavm">
        <dc:Bounds x="242" y="972" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qour72_di" bpmnElement="Activity_1qour72" isExpanded="true">
        <dc:Bounds x="330" y="580" width="2818" height="1060" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0s9skhu_di" bpmnElement="Event_0s9skhu">
        <dc:Bounds x="3078" y="1022" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0senc31_di" bpmnElement="rpa-callback-task">
        <dc:Bounds x="2306" y="1000" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qs6jm5_di" bpmnElement="rpa-task">
        <dc:Bounds x="1960" y="1000" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sybxao_di" bpmnElement="queue-rpa-task">
        <dc:Bounds x="1760" y="1000" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="372" y="1022" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0s1wze4_di" bpmnElement="Gateway_0s1wze4" isMarkerVisible="true">
        <dc:Bounds x="935" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1aa7ay8_di" bpmnElement="Gateway_1aa7ay8" isMarkerVisible="true">
        <dc:Bounds x="2211" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_118b845_di" bpmnElement="check-filing-status">
        <dc:Bounds x="790" y="1000" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0z6syka_di" bpmnElement="Gateway_0z6syka" isMarkerVisible="true">
        <dc:Bounds x="2441" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00qsmjr_di" bpmnElement="Gateway_00qsmjr" isMarkerVisible="true">
        <dc:Bounds x="2980" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06rx6zc" bpmnElement="Activity_Get_RPA_Job">
        <dc:Bounds x="2010" y="1150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ulluz6" bpmnElement="save-document-upload-fields">
        <dc:Bounds x="2690" y="1000" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mwhxdm_di" bpmnElement="Gateway_0mwhxdm" isMarkerVisible="true">
        <dc:Bounds x="1025" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1hcobid_di" bpmnElement="upload-documents-doc-gen-task">
        <dc:Bounds x="1490" y="1150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vtj3pw_di" bpmnElement="Gateway_0vtj3pw" isMarkerVisible="true">
        <dc:Bounds x="1615" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cgpbpr_di" bpmnElement="delay-retry">
        <dc:Bounds x="2448" y="1132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1525" y="645" width="90" height="20" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xa2ccx_di" bpmnElement="delete-rpa-job-from-queue-task">
        <dc:Bounds x="1910" y="1270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kck4s4" bpmnElement="filing-success-order-status-update">
        <dc:Bounds x="2955" y="1180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16yj5tn_di" bpmnElement="save-entity-name">
        <dc:Bounds x="670" y="1170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zd4qcc_di" bpmnElement="entity-data-fetch">
        <dc:Bounds x="530" y="1170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c3pxbx" bpmnElement="Gateway_00w0ozh" isMarkerVisible="true">
        <dc:Bounds x="465" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0p9vlba" bpmnElement="instant-filing-success-order-status-update">
        <dc:Bounds x="2840" y="1000" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0t58e9a" bpmnElement="manual-filing-task">
        <dc:Bounds x="2955" y="760" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_194qcgm" bpmnElement="Event_1jk1232">
        <dc:Bounds x="3092" y="742" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3093" y="550" width="14" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01zj6rm_di" bpmnElement="Activity_08cobvt">
        <dc:Bounds x="2416" y="1250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hrlf8t_di" bpmnElement="Gateway_0hrlf8t" isMarkerVisible="true">
        <dc:Bounds x="2525" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jynblc_di" bpmnElement="check-migrated-manual-bypass" isMarkerVisible="true">
        <dc:Bounds x="635" y="1015" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04q9j1y_di" bpmnElement="Gateway_04q9j1y" isMarkerVisible="true">
        <dc:Bounds x="2145" y="1165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10u34bj" bpmnElement="Gateway_09ogf1z" isMarkerVisible="true">
        <dc:Bounds x="1615" y="1335" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wjkkvi" bpmnElement="filing-dsd-docgen-salesforce-activity">
        <dc:Bounds x="1590" y="1450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0175uk9_di" bpmnElement="post-filing-dsd-doc-gen">
        <dc:Bounds x="1310" y="1320" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1lw2y1o_di" bpmnElement="biz-formation-determine-pre-filing-doc-gen">
        <dc:Bounds x="930" y="1230" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pvvf1g" bpmnElement="Gateway_0fj5v9r" isMarkerVisible="true">
        <dc:Bounds x="1075" y="1245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mnks1i_di" bpmnElement="generate-documents-task">
        <dc:Bounds x="1160" y="1150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oczfad_di" bpmnElement="documents-generated-task">
        <dc:Bounds x="1320" y="1150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0qxzp5n_di" bpmnElement="filing-two-hours-expired-timer">
        <dc:Bounds x="2042" y="1062" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2082" y="1102" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_03wpfoy" bpmnElement="Event_0a45d2w">
        <dc:Bounds x="3037" y="742" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3010" y="550" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1hw69o1_di" bpmnElement="filing-one-day-expired-timer">
        <dc:Bounds x="1942" y="1062" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1917" y="1102" width="28" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0qwv86b_di" bpmnElement="Event_0lpus12">
        <dc:Bounds x="1402" y="1172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1400" y="1233" width="83" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1qnexjg_di" bpmnElement="Flow_1qnexjg">
        <di:waypoint x="408" y="1040" />
        <di:waypoint x="465" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0difanc_di" bpmnElement="Flow_0difanc">
        <di:waypoint x="985" y="1040" />
        <di:waypoint x="1025" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02lhjdz_di" bpmnElement="Flow_02lhjdz">
        <di:waypoint x="960" y="1015" />
        <di:waypoint x="960" y="940" />
        <di:waypoint x="2236" y="940" />
        <di:waypoint x="2236" y="1015" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1124" y="922" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mfslbt_di" bpmnElement="Flow_1mfslbt">
        <di:waypoint x="2060" y="1040" />
        <di:waypoint x="2211" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nlos3r_di" bpmnElement="Flow_1nlos3r">
        <di:waypoint x="2261" y="1040" />
        <di:waypoint x="2306" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18bfg6e_di" bpmnElement="Flow_18bfg6e">
        <di:waypoint x="960" y="1015" />
        <di:waypoint x="960" y="900" />
        <di:waypoint x="2466" y="900" />
        <di:waypoint x="2466" y="1015" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1137" y="883" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m4rala_di" bpmnElement="Flow_1m4rala">
        <di:waypoint x="2406" y="1040" />
        <di:waypoint x="2441" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18bdfjt_di" bpmnElement="Flow_18bdfjt">
        <di:waypoint x="2575" y="1040" />
        <di:waypoint x="2690" y="1040" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2595" y="1012" width="72" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vcpr1d_di" bpmnElement="Flow_0vcpr1d">
        <di:waypoint x="3030" y="1040" />
        <di:waypoint x="3078" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0coa4j5_di" bpmnElement="Flow_0coa4j5">
        <di:waypoint x="2550" y="1015" />
        <di:waypoint x="2550" y="960" />
        <di:waypoint x="3005" y="960" />
        <di:waypoint x="3005" y="1015" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2739" y="942" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jlvicr_di" bpmnElement="Flow_1jlvicr">
        <di:waypoint x="2110" y="1190" />
        <di:waypoint x="2145" y="1190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ru50jj_di" bpmnElement="Flow_0ru50jj">
        <di:waypoint x="3005" y="1065" />
        <di:waypoint x="3005" y="1180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3025" y="1102" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07rnr04_di" bpmnElement="Flow_07rnr04">
        <di:waypoint x="2790" y="1040" />
        <di:waypoint x="2840" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tt5txh_di" bpmnElement="Flow_0tt5txh">
        <di:waypoint x="1260" y="1190" />
        <di:waypoint x="1320" y="1190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qyusnt_di" bpmnElement="Flow_0qyusnt">
        <di:waypoint x="1438" y="1190" />
        <di:waypoint x="1490" y="1190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="980" y="649" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10njbcu_di" bpmnElement="Flow_10njbcu">
        <di:waypoint x="1370" y="1150" />
        <di:waypoint x="1370" y="1110" />
        <di:waypoint x="1540" y="1110" />
        <di:waypoint x="1540" y="1150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ipp7w8_di" bpmnElement="Flow_0ipp7w8">
        <di:waypoint x="890" y="1040" />
        <di:waypoint x="935" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ma5dqw_di" bpmnElement="Flow_1ma5dqw">
        <di:waypoint x="1075" y="1040" />
        <di:waypoint x="1615" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16z45vo_di" bpmnElement="Flow_16z45vo">
        <di:waypoint x="1590" y="1190" />
        <di:waypoint x="1640" y="1190" />
        <di:waypoint x="1640" y="1065" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0izhsc0_di" bpmnElement="Flow_0izhsc0">
        <di:waypoint x="1665" y="1040" />
        <di:waypoint x="1760" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bv0cdw_di" bpmnElement="Flow_1bv0cdw">
        <di:waypoint x="2466" y="1065" />
        <di:waypoint x="2466" y="1132" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2416" y="1096" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uqzynv_di" bpmnElement="Flow_1uqzynv">
        <di:waypoint x="2466" y="1168" />
        <di:waypoint x="2466" y="1250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bjx9a5_di" bpmnElement="Flow_1bjx9a5">
        <di:waypoint x="1640" y="1015" />
        <di:waypoint x="1640" y="980" />
        <di:waypoint x="3005" y="980" />
        <di:waypoint x="3005" y="1015" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1682" y="962" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ez5nnt_di" bpmnElement="Flow_1ez5nnt">
        <di:waypoint x="1860" y="1040" />
        <di:waypoint x="1960" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04yu5jq_di" bpmnElement="Flow_04yu5jq">
        <di:waypoint x="2010" y="1310" />
        <di:waypoint x="2236" y="1310" />
        <di:waypoint x="2236" y="1065" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ur440p_di" bpmnElement="Flow_0ur440p">
        <di:waypoint x="3055" y="1220" />
        <di:waypoint x="3096" y="1220" />
        <di:waypoint x="3096" y="1058" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0swgipy_di" bpmnElement="Flow_0swgipy">
        <di:waypoint x="490" y="1065" />
        <di:waypoint x="490" y="1210" />
        <di:waypoint x="530" y="1210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="416" y="1127" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b7z7r4_di" bpmnElement="Flow_0b7z7r4">
        <di:waypoint x="630" y="1210" />
        <di:waypoint x="670" y="1210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0h5opix_di" bpmnElement="Flow_0h5opix">
        <di:waypoint x="770" y="1220" />
        <di:waypoint x="820" y="1220" />
        <di:waypoint x="820" y="1080" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_149r2aq_di" bpmnElement="Flow_149r2aq">
        <di:waypoint x="515" y="1040" />
        <di:waypoint x="635" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13961ey_di" bpmnElement="Flow_13961ey">
        <di:waypoint x="2940" y="1040" />
        <di:waypoint x="2980" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bt6usy_di" bpmnElement="Flow_0bt6usy">
        <di:waypoint x="3110" y="778" />
        <di:waypoint x="3110" y="800" />
        <di:waypoint x="3055" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h3fal4_di" bpmnElement="Flow_1h3fal4">
        <di:waypoint x="3073" y="760" />
        <di:waypoint x="3092" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tdof3s_di" bpmnElement="Flow_1tdof3s">
        <di:waypoint x="1640" y="1015" />
        <di:waypoint x="1640" y="800" />
        <di:waypoint x="2955" y="800" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1673" y="767" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sawvw2_di" bpmnElement="Flow_1sawvw2">
        <di:waypoint x="3005" y="840" />
        <di:waypoint x="3005" y="1015" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00att7b_di" bpmnElement="Flow_00att7b">
        <di:waypoint x="2466" y="1330" />
        <di:waypoint x="2466" y="1580" />
        <di:waypoint x="860" y="1580" />
        <di:waypoint x="860" y="1080" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_076phsf_di" bpmnElement="Flow_076phsf">
        <di:waypoint x="2491" y="1040" />
        <di:waypoint x="2525" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zco6jo_di" bpmnElement="Flow_0zco6jo">
        <di:waypoint x="685" y="1040" />
        <di:waypoint x="790" y="1040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0psqmvh_di" bpmnElement="manual-migrated-bypass">
        <di:waypoint x="660" y="1015" />
        <di:waypoint x="660" y="800" />
        <di:waypoint x="2955" y="800" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="672" y="750" width="66" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1umf4x5_di" bpmnElement="Flow_1umf4x5">
        <di:waypoint x="2195" y="1190" />
        <di:waypoint x="2236" y="1190" />
        <di:waypoint x="2236" y="1065" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19w7ept_di" bpmnElement="Flow_19w7ept">
        <di:waypoint x="2170" y="1165" />
        <di:waypoint x="2170" y="1050" />
        <di:waypoint x="2060" y="1050" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g6m8jr_di" bpmnElement="Flow_1g6m8jr">
        <di:waypoint x="1960" y="1098" />
        <di:waypoint x="1960" y="1270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qbv8td_di" bpmnElement="Flow_0qbv8td">
        <di:waypoint x="2060" y="1098" />
        <di:waypoint x="2060" y="1150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c06drr_di" bpmnElement="Flow_0c06drr">
        <di:waypoint x="1640" y="1335" />
        <di:waypoint x="1640" y="1065" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r5owo6_di" bpmnElement="Flow_0r5owo6">
        <di:waypoint x="1640" y="1385" />
        <di:waypoint x="1640" y="1450" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qsju6i_di" bpmnElement="Flow_1qsju6i">
        <di:waypoint x="1050" y="1065" />
        <di:waypoint x="1050" y="1140" />
        <di:waypoint x="980" y="1140" />
        <di:waypoint x="980" y="1230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="928" y="1201" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f42k3j_di" bpmnElement="Flow_1f42k3j">
        <di:waypoint x="1030" y="1270" />
        <di:waypoint x="1075" y="1270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1se2z9u_di" bpmnElement="Flow_1se2z9u">
        <di:waypoint x="1100" y="1245" />
        <di:waypoint x="1100" y="1190" />
        <di:waypoint x="1160" y="1190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p3y9dl_di" bpmnElement="Flow_1p3y9dl">
        <di:waypoint x="1100" y="1295" />
        <di:waypoint x="1100" y="1360" />
        <di:waypoint x="1310" y="1360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0epgd34_di" bpmnElement="Flow_0epgd34">
        <di:waypoint x="1410" y="1360" />
        <di:waypoint x="1615" y="1360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p4g0o1_di" bpmnElement="Flow_1p4g0o1">
        <di:waypoint x="1590" y="1490" />
        <di:waypoint x="1360" y="1490" />
        <di:waypoint x="1360" y="1400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Gateway_13zp4s2_di" bpmnElement="Gateway_13zp4s2" isMarkerVisible="true">
        <dc:Bounds x="3715" y="965" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_12p0fgw_di" bpmnElement="validation-error-task">
        <dc:Bounds x="3550" y="950" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06uii10" bpmnElement="orco-creation">
        <dc:Bounds x="3390" y="950" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qp3uko_di" bpmnElement="filing-salesforce-activity">
        <dc:Bounds x="3690" y="1240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1oqoca5_di" bpmnElement="Event_1oqoca5">
        <dc:Bounds x="3852" y="972" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1seir5u_di" bpmnElement="Gateway_1seir5u" isMarkerVisible="true">
        <dc:Bounds x="3255" y="965" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_13ndfoj_di" bpmnElement="Event_17ur9ic">
        <dc:Bounds x="3130" y="1154" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3158" y="1203" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0v3pt9f_di" bpmnElement="Flow_0v3pt9f">
        <di:waypoint x="278" y="990" />
        <di:waypoint x="330" y="990" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_128ql9p_di" bpmnElement="sf-case-resolved">
        <di:waypoint x="3690" y="1280" />
        <di:waypoint x="3148" y="1280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3500" y="1243" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yl9rxz_di" bpmnElement="Flow_0yl9rxz">
        <di:waypoint x="3148" y="990" />
        <di:waypoint x="3255" y="990" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vh6m6f_di" bpmnElement="Flow_1vh6m6f">
        <di:waypoint x="3650" y="990" />
        <di:waypoint x="3715" y="990" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0golzx1_di" bpmnElement="Fail">
        <di:waypoint x="3740" y="1015" />
        <di:waypoint x="3740" y="1240" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3749" y="1144" width="14" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k79yqm_di" bpmnElement="Flow_1k79yqm">
        <di:waypoint x="3765" y="990" />
        <di:waypoint x="3852" y="990" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3390" y="972" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kvvk94_di" bpmnElement="Flow_0kvvk94">
        <di:waypoint x="3490" y="990" />
        <di:waypoint x="3550" y="990" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uuj8py_di" bpmnElement="Flow_1uuj8py">
        <di:waypoint x="3305" y="990" />
        <di:waypoint x="3390" y="990" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0at0fq0_di" bpmnElement="Flow_0at0fq0">
        <di:waypoint x="3166" y="1172" />
        <di:waypoint x="3280" y="1172" />
        <di:waypoint x="3280" y="1015" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3210" y="1176" width="26" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_0uykq2t_di" bpmnElement="TextAnnotation_0uykq2t">
        <dc:Bounds x="2500" y="1350" width="113" height="70" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0ickdk9_di" bpmnElement="TextAnnotation_0ickdk9">
        <dc:Bounds x="1670" y="1398" width="319.9941222570533" height="40.75235109717868" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0ep75q7_di" bpmnElement="TextAnnotation_0ep75q7">
        <dc:Bounds x="1430" y="1510" width="99.98694034205708" height="29.992818085003794" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1ej2294_di" bpmnElement="Association_1ej2294">
        <di:waypoint x="2483" y="1330" />
        <di:waypoint x="2512" y="1350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_02sebod_di" bpmnElement="Association_02sebod">
        <di:waypoint x="1640" y="1418" />
        <di:waypoint x="1670" y="1417" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0rm7pm0_di" bpmnElement="Association_0rm7pm0">
        <di:waypoint x="1430" y="1490" />
        <di:waypoint x="1459" y="1510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1im7s8g_di" bpmnElement="Flow_1im7s8g">
        <di:waypoint x="1820" y="220" />
        <di:waypoint x="1890" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05gavz1_di" bpmnElement="Flow_05gavz1">
        <di:waypoint x="2010" y="190" />
        <di:waypoint x="2010" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pbpep3_di" bpmnElement="Flow_0pbpep3">
        <di:waypoint x="2010" y="250" />
        <di:waypoint x="2010" y="1000" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fwayb8_di" bpmnElement="Flow_0fwayb8">
        <di:waypoint x="1789" y="1000" />
        <di:waypoint x="1789" y="250" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
