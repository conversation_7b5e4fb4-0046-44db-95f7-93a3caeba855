package com.legalzoom.fulfillment.products.bizformation.delegate

import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.springframework.stereotype.Component

@Deprecated("No longer in use but needs to stay for old process instances.")
@Component
class AddressValidationDelegate : JavaDelegate {
    @WithSpan("addressValidation")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        // I am only here due to old process instances.  Sometime, have some mercy on me and finish me off
        // so I can go enjoy the delegate afterlife with my friends.
        return
    }
}
