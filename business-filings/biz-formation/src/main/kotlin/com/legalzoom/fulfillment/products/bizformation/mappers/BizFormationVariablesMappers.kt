package com.legalzoom.fulfillment.products.bizformation.mappers

import com.launchdarkly.sdk.LDUser
import com.legalzoom.fulfillment.common.extensions.createLDUser
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.workflow.variable.Variables
import java.time.Clock
import java.time.Instant

/**
 * TODO: Turn this file into the BizFormationsVariables data class instead of using the shared [Variables] class.
 */
fun Variables.getProcessingOrderIdNotNull() =
    checkNotNull(this.processingOrderId) {
        "Biz Formation Variables missing processing order id!"
    }

fun Variables.getProcessingOrderIdNotNullAsString() = this.getProcessingOrderIdNotNull().toString()

fun Variables.getCustomerIdNotNull() =
    checkNotNull(this.customerId) {
        "Biz Formation Variables missing customer id!"
    }

fun Variables.getAccountIdNotNull() =
    checkNotNull(this.accountId) {
        "Biz Formation Variables missing account id!"
    }

fun Variables.getAccountIdNotNullAsString() = this.getAccountIdNotNull().toString()

fun Variables.getProcessIdNotNull() =
    checkNotNull(this.processId) {
        "Biz Formation Variables missing process id!"
    }

fun Variables.getProductNotNull() = ProductType.fromProcessId(this.getProcessIdNotNull())

fun Variables.getJurisdictionNotNull(): State =
    checkNotNull(this.jurisdiction) {
        "Biz Formation Variables missing jurisdiction!"
    }.let { State.fromAbbreviationNotNull(it) }

fun Variables.getLDUser(): LDUser = createLDUser(ldUserKey = null, jurisdiction = this.jurisdiction, processId = this.processId)

fun Variables.toFulfillmentEvent(
    clock: Clock,
    executionId: String,
    processDefinitionKey: String,
): FulfillmentEvent {
    return FulfillmentEvent(
        customerId = this.customerId.toString(),
        processId = this.processId.toString(),
        orderId = this.unifiedOrderId,
        processingOrderId = this.processingOrderId!!.toString(),
        workOrderId = null,
        executionId = executionId,
        eventType = EventType.FIELD_UPDATED,
        eventPhase = EventPhase.fromWorkflowName(processDefinitionKey),
        timestamp = Instant.now(clock),
        jurisdiction = this.jurisdiction,
        data =
            mapOf(
                "processId" to this.processId,
                "processingOrderId" to this.processingOrderId.toString(),
                "customerId" to this.customerId,
                "fieldName" to this,
            ),
    )
}
