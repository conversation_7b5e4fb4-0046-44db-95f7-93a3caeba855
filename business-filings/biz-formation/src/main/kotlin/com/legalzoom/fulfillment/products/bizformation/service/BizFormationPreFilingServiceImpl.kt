package com.legalzoom.fulfillment.products.bizformation.service

import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.model.answer.AnswerSource
import com.legalzoom.api.model.dsd.docgen.DocGenJobRequestDto
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.products.bizformation.mappers.generateLlcPreFilingArticlesDelawareDsdRequest
import com.legalzoom.fulfillment.products.bizformation.mappers.getCustomerIdNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getJurisdictionNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getProcessIdNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getProcessingOrderIdNotNull
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.UnifiedCommerceService
import com.legalzoom.fulfillment.workflow.variable.Variables
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * The primary service class for the  biz-formation pre filing flow.
 * To be used both from Controllers and Delegates but should be isolated from HTTP and Delegate Execution semantics.
 */
@Service
class BizFormationPreFilingServiceImpl(
    private val answerApi: AnswerApi,
    private val unifiedCommerceService: UnifiedCommerceService,
    private val featureToggleService: FeatureToggleService,
) : BizFormationPreFilingService {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun generatePreFilingDocGen(variables: Variables): List<DocGenJobRequestDto>? {
        // Check DSD DocGen is enabled for the process ID
        if (!featureToggleService.isBizFormationPreFilingDsdDocGenEnabled(variables.getProcessIdNotNull())) {
            logger.info("DSD Pre Filing DocGen is disabled for process ID: ${variables.getProcessIdNotNull()}")
            return null
        }

        return when (variables.processId) {
            ProductType.LLC.processId -> generateLlcPreFilingDsdDocGenRequest(variables)
            else -> throw Exception("Invalid process ID: ${variables.processId} for PreFiling DocGen")
        }
    }

    /******************************
     * Private methods below here
     *****************************/

    private fun generateLlcPreFilingDsdDocGenRequest(variables: Variables): List<DocGenJobRequestDto> {
        val docGenRequests = mutableListOf<DocGenJobRequestDto>()
        logger.event(
            "Started Generating LLC Pre Filing DocGen Requests",
            mapOf(
                "processId" to variables.getProcessIdNotNull(),
                "commerceIds" to variables.commerceIds,
                "jurisdiction" to variables.getJurisdictionNotNull(),
                "processingOrderId" to variables.getProcessingOrderIdNotNull(),
                "customerId" to variables.getCustomerIdNotNull(),
            ),
        )
        when (variables.getJurisdictionNotNull()) {
            State.DELAWARE -> {
                val preFilingArticlesRequest =
                    variables.generateLlcPreFilingArticlesDelawareDsdRequest(
                        orderId = unifiedCommerceService.getUnifiedOrderId(variables.commerceIds),
                        questionnaireAnswers =
                            answerApi.answersUserOrderIdSourceGet(
                                variables.getProcessingOrderIdNotNull(),
                                AnswerSource.NUMBER_0,
                                null,
                                null,
                                null,
                                "1.0",
                                variables.getCustomerIdNotNull(),
                                true,
                            ).blockSingle(),
                    )
                logger.info("Successfully Generated LLC preFiling articles request for Delaware")
                docGenRequests.add(preFilingArticlesRequest)
            }
            else -> {
                logger.event("Invalid jurisdiction for LLC preFiling DocGen ${variables.jurisdiction}")
                throw Exception("Invalid jurisdiction for LLC preFiling DocGen ${variables.jurisdiction}")
            }
        }
        logger.event(
            "completed Generating LLC Pre Filing DocGen Requests",
            mapOf(
                "processId" to variables.getProcessIdNotNull(),
                "commerceIds" to variables.commerceIds,
                "jurisdiction" to variables.getJurisdictionNotNull(),
                "processingOrderId" to variables.getProcessingOrderIdNotNull(),
                "customerId" to variables.getCustomerIdNotNull(),
            ),
        )
        return docGenRequests
    }
}
