package com.legalzoom.fulfillment.products.bizformation.mappers

import com.legalzoom.api.model.notificationsplatform.Account
import com.legalzoom.api.model.notificationsplatform.NotificationRequest
import com.legalzoom.api.model.notificationsplatform.TargetChannel
import com.legalzoom.api.model.notificationsplatform.Topic
import com.legalzoom.fulfillment.service.data.OrderCancelMessage
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.workflow.variable.Variables
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID

private val notificationDateFormatter = DateTimeFormatter.ofPattern("MM/dd")

fun Variables.createSentToSOSEmailNotification(customerFirstName: String): NotificationRequest {
    return this.createNotification(
        notificationType = "fulfillment_statusupdate_biz-formation-sent-to-sos_20250509",
        channelType = TargetChannel.ChannelTypeEnum.EMAIL,
        templateData =
            mapOf(
                "firstName" to customerFirstName,
                "accountId" to this.getAccountIdNotNull().toString(),
            ),
    )
}

fun Variables.createSentToSOSSMSNotification(product: ProductType): NotificationRequest {
    return this.createNotification(
        notificationType = "fulfillment_statusupdate_order-status-sent-to-sos-sms_20240209",
        channelType = TargetChannel.ChannelTypeEnum.SMS,
        templateData =
            mapOf(
                "product" to product.productName,
                "orderId" to this.unifiedOrderId,
                "date" to LocalDate.now().format(notificationDateFormatter),
            ),
    )
}

fun Variables.createReadyForDownloadEmailNotification(
    customerFirstName: String,
    hasAttachedEIN: Boolean,
): NotificationRequest {
    return this.createNotification(
        notificationType =
            if (hasAttachedEIN) {
                "fulfillment_statusupdate_biz-formation-ready-for-download-ein_20250509"
            } else {
                "fulfillment_statusupdate_biz-formation-ready-for-download-no-ein_20250509"
            },
        channelType = TargetChannel.ChannelTypeEnum.EMAIL,
        templateData =
            mapOf(
                "firstName" to customerFirstName,
                "accountId" to this.getAccountIdNotNull().toString(),
            ),
    )
}

fun Variables.createReadyForDownloadSMSNotification(): NotificationRequest {
    return this.createNotification(
        notificationType = "fulfillment_statusupdate_order-status-order-complete-sms_20240209",
        channelType = TargetChannel.ChannelTypeEnum.SMS,
        templateData = emptyMap(),
    )
}

private fun Variables.createNotification(
    notificationType: String,
    channelType: TargetChannel.ChannelTypeEnum,
    templateData: Map<String, Any?>,
): NotificationRequest {
    return NotificationRequest().also { request ->
        request.requestId = UUID.randomUUID().toString()
        request.notificationType = notificationType
        request.account =
            Account().also { account ->
                account.accountId = this.getCustomerIdNotNull()
                account.accountType = Account.AccountTypeEnum.PERSONAL
                account.contactType = Account.ContactTypeEnum.PRIMARY
            }
        request.topic =
            Topic().also { topic ->
                topic.processingOrderId = this.getProcessingOrderIdNotNull().toString()
                topic.orderId = this.unifiedOrderId
            }
        request.targetChannels =
            listOf(
                TargetChannel().also { targetChannel ->
                    targetChannel.channelType = channelType
                    targetChannel.dynamicTemplateData = templateData
                },
            )
    }
}

fun Variables.createOrderCancelMessage(): OrderCancelMessage {
    return OrderCancelMessage(
        processId = this.getProcessIdNotNull().toLong(),
        customerId = this.getCustomerIdNotNull().toLong(),
        processingOrderId = this.getProcessingOrderIdNotNullAsString(),
        workOrderId = null,
        orderId =
            checkNotNull(this.orderId?.toLong()) {
                "TODO: Support order cancellation for CP2 orders."
            },
        state = this.getJurisdictionNotNull().abbreviation,
    )
}
