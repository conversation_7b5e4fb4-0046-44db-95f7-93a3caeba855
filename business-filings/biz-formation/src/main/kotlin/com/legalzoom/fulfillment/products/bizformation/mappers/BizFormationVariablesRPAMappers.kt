package com.legalzoom.fulfillment.products.bizformation.mappers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.rpa.AddQueueItemRequest
import com.legalzoom.api.model.rpa.QueueItemDataDto
import com.legalzoom.api.model.rpa.QueueItemDataDto.PriorityEnum.HIGH
import com.legalzoom.api.model.rpa.QueueItemDataDto.PriorityEnum.LOW
import com.legalzoom.api.model.rpa.QueueItemDataDto.PriorityEnum.NORMAL
import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.ABANDONED
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.DELETED
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.FAILED
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.IN_PROGRESS
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.NEW
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.SUCCESSFUL
import com.legalzoom.fulfillment.workflow.data.RobotOutputData
import com.legalzoom.fulfillment.workflow.variable.Variables
import java.time.LocalDateTime

fun Variables.getRPAQueueItemIdNotNull(): Long =
    checkNotNull(this.rpaQueueItemId) {
        "Unexpected System Error. RPA Queue Item Id was not stored"
    }

fun Variables.createUIPathAddQueueItemRequest(botName: String): AddQueueItemRequest {
    return AddQueueItemRequest().also { request ->
        request.itemData =
            QueueItemDataDto().also { body ->
                body.name = botName

                // QueueItem priority based on the expediteSpeed of the order
                val priority =
                    when (this.expediteSpeed) {
                        "ExpeditePlusPlus" -> HIGH
                        "Expedite", "ExpeditePlus" -> NORMAL
                        else -> LOW
                    }

                body.priority = priority
                body.specificContent =
                    mapOf(
                        "customerId" to this.customerId,
                        "orderId" to this.orderId,
                        "processingOrderId" to this.processingOrderId,
                        "processId" to this.processId,
                        "correlationId" to this.correlationId,
                    )
                body.reference(this.processingOrderId.toString())
            }
    }
}

fun Variables.updateWithUIPathQueueItemResponse(response: QueueItemDto): Variables {
    // used in filing process for 1 day timer going to keep this as hardcoded 1 since this value has never changed since start
    this.sfDelayTimer = LocalDateTime.now().plusDays(1).toString()
    this.rpaQueueItemId =
        checkNotNull(response.id) {
            "No id found in ui path queue item response!"
        }
    return this
}

fun Variables.updateWithRPAJobStatus(
    queueItemDto: QueueItemDto,
    objectMapper: ObjectMapper,
): Variables {
    when (queueItemDto.status) {
        SUCCESSFUL -> {
            val outputData =
                queueItemDto.outputData?.let {
                    objectMapper.readValue<RobotOutputData>(it)
                }

            this.status = outputData?.dynamicProperties?.status ?: queueItemDto.status?.value
            this.evidenceFilePath = outputData?.dynamicProperties?.evidenceFilePath
            this.evidenceTransactionNumber = outputData?.dynamicProperties?.evidenceTransactionNumber
            this.evidenceTransactionMessage = outputData?.dynamicProperties?.evidenceTransactionMessage
        }
        DELETED, ABANDONED, FAILED -> {
            val processingException = queueItemDto.processingException

            this.status = processingException?.type?.value ?: queueItemDto.status?.value
            this.evidenceFilePath = processingException?.details
            this.message = processingException?.reason
        }
        NEW, IN_PROGRESS -> {
            this.status = queueItemDto.status?.value
        }
        else -> {
            "Unexpected System Error. RPA Queue Item Issue ${queueItemDto.status}".let {
                throw Exception(it)
            }
        }
    }
    return this
}

fun Variables.updateWithRPAJobDeleted(): Variables {
    this.message = "This order could not be filed via RPA. This order likely requires manual filing."
    this.status = "RPA Timeout"
    return this
}
