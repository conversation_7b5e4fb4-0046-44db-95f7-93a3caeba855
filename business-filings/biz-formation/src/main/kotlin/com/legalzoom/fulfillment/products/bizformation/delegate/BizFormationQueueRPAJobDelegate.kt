package com.legalzoom.fulfillment.products.bizformation.delegate

import com.legalzoom.fulfillment.products.bizformation.service.BizFormationService
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.variable.input
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.springframework.stereotype.Component

@Component
class BizFormationQueueRPAJobDelegate(
    private val bizFormationService: BizFormationService,
) : JavaDelegate {
    @WithSpan("BizFormationQueueRPAJobDelegate")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        execution.variables = bizFormationService.queueRPAJob(execution.input, execution.id)
    }
}
