package com.legalzoom.fulfillment.products.bizformation.delegate

import com.legalzoom.fulfillment.printandship.service.BusinessEntityService
import com.legalzoom.fulfillment.products.bizformation.service.BizFormationService
import com.legalzoom.fulfillment.workflow.variable.input
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.springframework.stereotype.Component

/**
 * Ensure a business entity exists in the legacy [BusinessEntityService] before continuing.
 * For CP1 orders this should be created by the order state machine, but for CP2 orders it must be done here.
 */
@Component
class BizFormationCreateBusinessEntityDelegate(
    private val bizFormationService: BizFormationService,
) : JavaDelegate {
    @WithSpan("CreateBusinessEntityDelegate")
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        bizFormationService.createBusinessEntity(execution.input)
    }
}
