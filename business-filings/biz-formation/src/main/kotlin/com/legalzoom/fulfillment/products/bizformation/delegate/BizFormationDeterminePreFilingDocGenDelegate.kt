package com.legalzoom.fulfillment.products.bizformation.delegate

import com.legalzoom.fulfillment.products.bizformation.service.BizFormationPreFilingService
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.springframework.stereotype.Component

@Component
class BizFormationDeterminePreFilingDocGenDelegate(
    private val bizFormationPreFilingService: BizFormationPreFilingService,
) : JavaDelegate {
    @WithSpan("bizFormationDeterminePreFilingDocGenDelegate")
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        execution.output {
            preFilingDSDDocGenRequests = bizFormationPreFilingService.generatePreFilingDocGen(execution.input)
        }
    }
}
