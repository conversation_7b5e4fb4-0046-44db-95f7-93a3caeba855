package com.legalzoom.fulfillment.products.bizformation.mappers

import com.legalzoom.api.model.rpa.ODataValueOfIEnumerableOfQueueItemDto
import com.legalzoom.api.model.rpa.QueueItemDto

// TODO: Put into a common package
fun ODataValueOfIEnumerableOfQueueItemDto.getSingleQueueItem(): QueueItemDto {
    return checkNotNull(this.value?.singleOrNull()) {
        "Could not retrieve a single queue item from ui path response $this"
    }
}
