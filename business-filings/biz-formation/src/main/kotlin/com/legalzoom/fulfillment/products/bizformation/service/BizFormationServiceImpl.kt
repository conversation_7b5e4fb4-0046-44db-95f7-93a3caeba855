package com.legalzoom.fulfillment.products.bizformation.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.model.answer.FieldAnswerResponseDto
import com.legalzoom.api.model.answer.SaveAnswerBankDto
import com.legalzoom.api.model.answer.SaveAnswerBankRequest
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.dsd.docgen.DocGenJobRequestDto
import com.legalzoom.api.model.notificationsplatform.NotificationRequest
import com.legalzoom.api.model.processingorder.PutProcessingOrderRequest
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailRequest
import com.legalzoom.api.notificationplatform.notificationplatform.NotificationsServiceApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.fulfillment.answersapi.Constants.QA_HEADER_LZ_API_VERSION
import com.legalzoom.fulfillment.answersapi.Constants.QA_HEADER_LZ_AUTHORIZE
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.orco.entity.enumeration.ReferenceType
import com.legalzoom.fulfillment.orco.entity.enumeration.ResolutionType
import com.legalzoom.fulfillment.orco.service.OrcoService
import com.legalzoom.fulfillment.printandship.service.BusinessEntityService
import com.legalzoom.fulfillment.products.bizformation.configuration.BizFormationConfigurationProperties
import com.legalzoom.fulfillment.products.bizformation.mappers.checkIsSuccessful
import com.legalzoom.fulfillment.products.bizformation.mappers.createAskNicelySurveyRequest
import com.legalzoom.fulfillment.products.bizformation.mappers.createOrderCancelMessage
import com.legalzoom.fulfillment.products.bizformation.mappers.createReadyForDownloadEmailNotification
import com.legalzoom.fulfillment.products.bizformation.mappers.createReadyForDownloadSMSNotification
import com.legalzoom.fulfillment.products.bizformation.mappers.createSentToSOSEmailNotification
import com.legalzoom.fulfillment.products.bizformation.mappers.createSentToSOSSMSNotification
import com.legalzoom.fulfillment.products.bizformation.mappers.createUIPathAddQueueItemRequest
import com.legalzoom.fulfillment.products.bizformation.mappers.getAccountIdNotNullAsString
import com.legalzoom.fulfillment.products.bizformation.mappers.getContactFirstName
import com.legalzoom.fulfillment.products.bizformation.mappers.getCustomerIdNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getJurisdictionNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getLDUser
import com.legalzoom.fulfillment.products.bizformation.mappers.getProcessIdNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getProcessingOrderIdNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getProductNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getRPAQueueItemIdNotNull
import com.legalzoom.fulfillment.products.bizformation.mappers.getSingleQueueItem
import com.legalzoom.fulfillment.products.bizformation.mappers.toCreateBusinessEntityRequest
import com.legalzoom.fulfillment.products.bizformation.mappers.toFulfillmentEvent
import com.legalzoom.fulfillment.products.bizformation.mappers.updateWithRPAJobDeleted
import com.legalzoom.fulfillment.products.bizformation.mappers.updateWithRPAJobStatus
import com.legalzoom.fulfillment.products.bizformation.mappers.updateWithUIPathQueueItemResponse
import com.legalzoom.fulfillment.products.bizformation.utils.withBizFormationContext
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.enumeration.DMNModelDependency.ORDERS_API
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ArticlesFiled
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ProofOfWork
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition.Proceed
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.OrderStatusDescription
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.QueueItemsService
import com.legalzoom.fulfillment.service.service.UnifiedCommerceService
import com.legalzoom.fulfillment.service.service.asknicely.AskNicelyService
import com.legalzoom.fulfillment.service.service.questionnaire.AnswerFieldType
import com.legalzoom.fulfillment.service.service.questionnaire.Constants
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.delegate.helper.RpaHelper
import com.legalzoom.fulfillment.workflow.extensions.isFiled
import com.legalzoom.fulfillment.workflow.service.AddQueuesItemService
import com.legalzoom.fulfillment.workflow.service.BypassRpaService
import com.legalzoom.fulfillment.workflow.service.CourierFilingService
import com.legalzoom.fulfillment.workflow.service.DocumentFilingService
import com.legalzoom.fulfillment.workflow.service.DocumentFilingStateLookupService
import com.legalzoom.fulfillment.workflow.service.InstantFilingService
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService
import com.legalzoom.fulfillment.workflow.service.ValidationService
import com.legalzoom.fulfillment.workflow.service.helper.dmnVariables
import com.legalzoom.fulfillment.workflow.variable.Variables
import org.camunda.bpm.engine.RepositoryService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.LocalDate

/**
 * The primary service class for the entire biz-formation flow.
 * To be used both from Controllers and Delegates but should be isolated from HTTP and Delegate Execution semantics.
 */
@Service
class BizFormationServiceImpl(
    private val processingOrdersApi: ProcessingOrdersApi,
    private val customerApi: CustomerApi,
    @Qualifier("notificationsServiceInternalApi") private val notificationsServiceInternalApi: NotificationsServiceApi,
    private val unifiedCommerceService: UnifiedCommerceService,
    private val courierFilingService: CourierFilingService,
    private val instantFilingService: InstantFilingService,
    private val askNicelyService: AskNicelyService,
    private val bizFormationConfigProps: BizFormationConfigurationProperties,
    private val launchDarkly: LDClient,
    private val businessEntityService: BusinessEntityService,
    private val answerApi: AnswerApi,
    private val validationService: ValidationService,
    private val completedOrderDetailApi: CompletedOrderDetailApi,
    private val questionnaireApi: QuestionnaireApi,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val fulfillmentEventService: FulfillmentEventService,
    private val clock: Clock,
    private val repositoryService: RepositoryService,
    private val documentService: DocumentService,
    private val documentFilingService: DocumentFilingService,
    private val bypassRpaService: BypassRpaService,
    private val rpaSosSsorcoService: RpaSosSsorcoService,
    private val documentFilingStateLookupService: DocumentFilingStateLookupService,
    private val featureToggleService: FeatureToggleService,
    private val orcoService: OrcoService,
    private val orderCancellationService: OrderCancellationService,
    private val rpaHelper: RpaHelper,
    private val objectMapper: ObjectMapper,
    private val addQueuesItemService: AddQueuesItemService,
    private val queueItemsService: QueueItemsService,
) : BizFormationService {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun createBusinessEntity(variables: Variables) {
        withBizFormationContext(variables) {
            val businessEntityResponse =
                businessEntityService.getBusinessEntityResponseByProcessingOrderIdOrNull(
                    variables.getProcessingOrderIdNotNull(),
                )

            if (businessEntityResponse?.entity != null) {
                logger.event(
                    "bizFormation.businessEntityAlreadyExists",
                    mapOf(
                        "processingOrderId" to variables.processingOrderId,
                    ),
                )
                return
            }

            logger.event("bizFormation.createBusinessEntity")

            val entityData =
                answerApi.answersProcessingOrdersProcessingOrderIdProcessesProcessIdEntityDataGet(
                    variables.getProcessingOrderIdNotNull(),
                    variables.getProcessIdNotNull(),
                    QA_HEADER_LZ_API_VERSION,
                    variables.customerId!!,
                    QA_HEADER_LZ_AUTHORIZE,
                ).blockSingle()

            businessEntityService.createBusinessEntityByCreateEntityInformationRequest(
                entityData.toCreateBusinessEntityRequest(
                    processingOrderId = variables.getProcessingOrderIdNotNull(),
                    unifiedCommerceIds = variables.commerceIds,
                ),
            )
        }
    }

    override fun preFilingVariablesFetch(variables: Variables): Variables =
        withBizFormationContext(variables) {
            // TODO: Remove dependency on validationService and just use answers directly, also consider just
            //       doing this at launch and removing the delegate.
            val fetchedVariables =
                when (variables.getProductNotNull()) {
                    ProductType.LLC ->
                        // Not providing ADDRESS_VALIDATION_API as an allowed dependency here since it's being called by AddressValidationDelegate downstream in the workflow
                        validationService.buildDMNDataV2(
                            variables.processingOrderId,
                            variables.commerceIds,
                            variables.customerId,
                            AnswerSource.AnswerBank,
                            variables.processId,
                            allowedDependencies = setOf(ORDERS_API),
                        ).dmnData

                    ProductType.INC ->
                        // Not providing ADDRESS_VALIDATION_API as an allowed dependency here since it's being called by AddressValidationDelegate downstream in the workflow
                        validationService.buildDMNData(
                            variables.processingOrderId,
                            variables.orderId,
                            variables.customerId,
                            AnswerSource.AnswerBank,
                            variables.processId,
                            allowedDependencies = setOf(ORDERS_API),
                        ).dmnData

                    ProductType.NP ->
                        validationService.buildDMNData(
                            variables.processingOrderId,
                            variables.orderId,
                            variables.customerId,
                            AnswerSource.AnswerBank,
                            variables.processId,
                            allowedDependencies = setOf(ORDERS_API),
                        ).dmnData

                    else -> throw IllegalArgumentException("Unsupported product type ${variables.getProductNotNull()}")
                }.let {
                    dmnVariables(it)
                }

            if (fetchedVariables.jurisdiction == null) {
                fetchedVariables.jurisdiction = variables.getJurisdictionNotNull().abbreviation
            }

            fetchedVariables
        }

    override fun assignedForNameCheck(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateProcessingOrderStatusByDescription(variables, OrderStatusDescription.AssignedForNameCheck)
        }
    }

    override fun entityPrelimNameAvailable(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateProcessingOrderStatusByDescription(variables, OrderStatusDescription.EntityNamePrelimAvailable2)
        }
    }

    override fun saveEntityName(
        variables: Variables,
        executionId: String,
        processDefinitionId: String,
    ): ValidationError? =
        withBizFormationContext(variables) {
            val product = variables.getProductNotNull()
            val fieldType = Constants.FieldType.EntityName
            val field = AnswerFieldType.fromFieldTypeAndProcessId(fieldType, product.processId)
            val fieldValue = variables.entityName

            if (fieldValue.isNullOrBlank()) {
                return "Unable to store ${field.fieldName} with value $fieldValue".let {
                    logger.error(it)
                    ValidationError(it)
                }
            }

            val codResponse =
                completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                    variables.getProcessingOrderIdNotNull(),
                    UpdateCompletedOrderDetailRequest().entityName(fieldValue),
                    "1.0",
                    "",
                    false,
                ).blockSingle()

            logger.info("Save ${field.fieldName} into completed order detail with success: ${codResponse?.entityName}")

            val questionnaire =
                questionnaireApi.questionnaireFieldsQuestionnaireIdGet(
                    product.questionnaireId,
                    "1.0",
                    "",
                    false,
                ).blockSingle()
            val questionnaireField = questionnaire?.questionnaireFields?.firstOrNull { it.fieldName == field.fieldName }
            checkNotNull(questionnaireField) {
                val errorMessage = "save ${field.fieldName} into answers failed (questionnaireField not found)"
                logger.errorEvent(
                    errorMessage,
                    mapOf(
                        "questionnaireId" to product.questionnaireId,
                        "fieldName" to field.fieldName,
                        "value" to fieldValue,
                        "processingOrderId" to variables.processingOrderId,
                    ),
                )
                errorMessage
            }
            val saveAnswersResponse =
                questionnaireAnswerService.putPartialUpdate(
                    "1.0",
                    SaveAnswerBankRequest().questionnaireFieldGroupAnswers(
                        SaveAnswerBankDto()
                            .fieldAnswers(
                                mutableListOf(
                                    FieldAnswerResponseDto()
                                        .fieldId(questionnaireField.questionnaireFieldId)
                                        .fieldName(questionnaireField.fieldName!!)
                                        .fieldValue(fieldValue)
                                        .optionId(0),
                                ),
                            )
                            .processingOrderId(variables.processingOrderId!!)
                            .createdBy("NGF")
                            .questionireId(product.questionnaireId)
                            .isMajorRevision(false),
                    ),
                )
            logger.event(
                "Save $fieldValue into answers with number of records affected ${saveAnswersResponse?.numberOfRecordsAffected}",
                mapOf(
                    "fieldId" to questionnaireField.questionnaireFieldId,
                    "fieldName" to field.fieldName,
                    "value" to fieldValue,
                    "processingOrderIdInRequest" to variables.processingOrderId,
                ),
            )

            val processDefinitionKey = repositoryService.getProcessDefinition(processDefinitionId).key

            // Generate event notifying a field has been updated
            fulfillmentEventService.send(variables.toFulfillmentEvent(clock, executionId, processDefinitionKey))

            null
        }

    override fun checkFilingStatus(variables: Variables): Variables =
        withBizFormationContext(variables) {
            val processingOrderId = variables.getProcessingOrderIdNotNull()
            val productType = variables.getProductNotNull()
            val customerId = variables.getCustomerIdNotNull()
            val isInstantFiling =
                instantFilingService.isInstantFilingStateByVariable(variables) ||
                    instantFilingService.isSemiInstantFilingStateByVariable(variables)

            val articles =
                documentService.findDocumentsBy(
                    processingOrderId = processingOrderId.toLong(),
                    customerId = customerId.toLong(),
                    customerDocumentType =
                        if (isInstantFiling) {
                            CustomerDocumentType.findCustomerDocTypeFromProductName(
                                productType.productName,
                                ArticlesFiled.name,
                            )
                        } else {
                            null
                        },
                    currentVersionOnly = true,
                    accountId = variables.getAccountIdNotNullAsString(),
                )

            val alreadyFiled = articles.isFiled(isInstantFiling)
            var documentType: String? = null
            if (alreadyFiled) {
                documentType =
                    if (isInstantFiling) {
                        CustomerDocumentType.findCustomerDocTypeFromProductName(
                            productType.productName,
                            ArticlesFiled.name,
                        ).documentType
                    } else {
                        CustomerDocumentType.findCustomerDocTypeFromProductName(
                            productType.productName,
                            ProofOfWork.name,
                        ).documentType
                    }
            }

            val documentFilingState =
                documentFilingStateLookupService.getService(ProductType.fromProcessIdNullable(variables.processId))
                    ?.documentRequiredByVariables(variables) ?: false

            // Fixing broken prod orders, see NGX-4442
            val shouldSkipRetry = variables.disposition == Proceed.value

            // Retrieve ORCOs for the order
            val orcos =
                orcoService.search(
                    referenceId = processingOrderId.toString(),
                    referenceType = ReferenceType.PROCESSING_ORDER_ID,
                    status = null,
                    type = null,
                )

            val questionOrcosCount =
                orcos.count { orco ->
                    orco.reasons.any { reason ->
                        reason.resolution.type == ResolutionType.QUESTION
                    }
                }

            val manualSosFilingRequired = questionOrcosCount >= 2

            variables.alreadyFiled = alreadyFiled || variables.alreadyFiled == true
            variables.documentType = documentType
            variables.effectiveDate =
                if (alreadyFiled && isInstantFiling && variables.effectiveDate.isNullOrEmpty()) {
                    LocalDate.now(clock).toString()
                } else {
                    variables.effectiveDate
                }
            variables.documentFilingState = documentFilingState
            variables.retriable = if (shouldSkipRetry) false else variables.retriable
            variables.retryCount = if (shouldSkipRetry) 0 else variables.retryCount
            variables.manualSosFilingRequired = manualSosFilingRequired || (variables.manualSosFilingRequired == true)

            logger.info(
                "Document Type: $documentType, Already Filed: $alreadyFiled, Document Filing State: $documentFilingState" +
                    ", Manual SOS Filing Required: $manualSosFilingRequired",
            )

            // Checks for cheat codes to force specific errors.
            rpaSosSsorcoService.checkCheatCodes(variables)

            if (
                bypassRpaService.isBypassRpaByVariables(variables) &&
                !documentFilingService.isFaxFilingStateByVariables(variables) &&
                variables.disposition != "proceed" &&
                featureToggleService.rpaBypassExceptionsEnabled(variables.customerId, variables.jurisdiction, variables.processId)
            ) {
                variables.addValidationError(
                    ValidationError(
                        "Manual filing required",
                        data =
                            mapOf(
                                "Detail" to "This order requires manual filing. Please manually file with the SOS and then select " +
                                    "\"Skip-I have manually filed\"",
                            ),
                    ),
                )
            }

            return variables
        }

    override fun queueRPAJob(
        variables: Variables,
        executionId: String,
    ): Variables =
        withBizFormationContext(variables) {
            val isCancelled =
                unifiedCommerceService.isCanceled(
                    ids = variables.commerceIds,
                    processingOrderId = variables.processingOrderId!!,
                    customerId = variables.customerId!!,
                )

            if (isCancelled) {
                orderCancellationService.cancelOrder(variables.createOrderCancelMessage())
                return variables
            }

            // TODO: Why is correlationId set everywhere and is it actually necessary?
            variables.correlationId = executionId
            val request =
                variables.createUIPathAddQueueItemRequest(
                    botName = rpaHelper.getRpaProcessId(variables),
                )

            logger.info("Queuing RPA request: ${objectMapper.writeValueAsString(request)}")
            val response =
                addQueuesItemService.addQueuesItem(
                    null,
                    null,
                    null,
                    request,
                    request.itemData.name!!,
                )
            logger.info("Queue RPA response: $response")
            variables.updateWithUIPathQueueItemResponse(response)
        }

    override fun checkRPAJobStatus(variables: Variables): Variables =
        withBizFormationContext(variables) {
            val response =
                queueItemsService.queueItemsGet(
                    null,
                    null,
                    null,
                    "Id eq ${variables.getRPAQueueItemIdNotNull()}",
                    null,
                    null,
                    10,
                    null,
                    null,
                    null,
                    rpaHelper.getRpaProcessId(variables),
                )
                    .blockSingle()
                    .also { logger.info("Response when fetching RPA Job $it") }

            variables.updateWithRPAJobStatus(response.getSingleQueueItem(), objectMapper)
        }

    override fun deleteRPAJob(variables: Variables): Variables =
        withBizFormationContext(variables) {
            queueItemsService.queueItemsDeleteById(
                rpaQueueItemId = variables.getRPAQueueItemIdNotNull(),
                xUipathOrganizationUnitId = null,
                rpaProcessId = rpaHelper.getRpaProcessId(variables),
            ).block()
            variables.updateWithRPAJobDeleted()
        }

    override fun instantFilingComplete(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateSentToSOSStatusAndSendNotifications(variables)
        }
    }

    override fun filingComplete(variables: Variables) {
        withBizFormationContext(variables) {
            if (!courierFilingService.isCourierFilingStateByVariables(variables) &&
                !instantFilingService.entityRequiresDelayFileByVariables(variables) &&
                variables.documentType == "ArticlesFiled"
            ) {
                this.updateProcessingOrderStatusByDescription(
                    variables,
                    OrderStatusDescription.DocumentsReceivedFromSOS,
                )
            } else {
                this.updateSentToSOSStatusAndSendNotifications(variables)
            }

            this.updateAttachedProcessingOrderStatusIfExists(
                variables,
                ProductType.RegisteredAgentService,
                ProcessingOrderStatus.RaFilingComplete,
            )
        }
    }

    override fun readyForDownload(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateProcessingOrderStatusByDescription(variables, OrderStatusDescription.ReadyForDownload)
            val customer = this.getCustomer(variables)
            this.sendNotification(
                variables.createReadyForDownloadEmailNotification(
                    customerFirstName = customer.getContactFirstName(),
                    hasAttachedEIN = getAttachedProcessingOrderId(variables, ProductType.EIN) != null,
                ),
            )
            this.sendNotification(variables.createReadyForDownloadSMSNotification())
        }
    }

    override fun documentsPrepared(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateProcessingOrderStatusByDescription(variables, OrderStatusDescription.DocumentsPrepared)
        }
    }

    override fun californiaSOIValidationErrorStatusUpdate(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateAttachedProcessingOrderStatusIfExists(
                variables,
                ProductType.InitialReports,
                ProcessingOrderStatus.InitialReportsPreliminaryNameValidationComplete,
            )
        }
    }

    override fun sendNPSSurvey(variables: Variables) {
        withBizFormationContext(variables) {
            askNicelyService.sendSurvey(
                variables.createAskNicelySurveyRequest(
                    customerDetails = this.getCustomer(variables),
                    askNicelyConfig = bizFormationConfigProps.askNicely,
                ),
            )
        }
    }

    override fun californiaSOIShippingCompleteStatusUpdate(variables: Variables) {
        withBizFormationContext(variables) {
            this.updateAttachedProcessingOrderStatusIfExists(
                variables,
                ProductType.InitialReports,
                ProcessingOrderStatus.InitialReportsShippingComplete,
            )
        }
    }

    override fun determinePostFilingDocGen(variables: Variables): List<DocGenJobRequestDto>? {
        return if (launchDarkly.boolVariation("biz-formation-post-filing-dsd-doc-gen", variables.getLDUser(), false)) {
            emptyList()
        } else {
            null
        }
    }

    /******************************
     * Private methods below here
     *****************************/
    private fun updateProcessingOrderStatusByDescription(
        variables: Variables,
        statusDescription: OrderStatusDescription,
    ) {
        val processingOrderId = variables.getProcessingOrderIdNotNull()
        val processId = variables.getProcessIdNotNull()
        val status =
            ProcessingOrderStatus.fromProcessIdAndDescriptionNotNull(
                checkNotNull(processId) {
                    "Null process id for processing order $processingOrderId"
                },
                statusDescription,
            )
        val customerId = variables.getCustomerIdNotNull()

        logger.info("Updating order status to $status")
        processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
            processingOrderId,
            status.processingStatusId,
            "1",
            customerId,
            true,
            PutProcessingOrderRequest()
                .comment("Updated processing status to $status")
                .updatedBy("NGF"),
        ).blockSingle()
    }

    /**
     * Will search for an attached [product] purchase and return the processing order if it exists.
     */
    private fun getAttachedProcessingOrderId(
        variables: Variables,
        product: ProductType,
    ): Int? {
        return unifiedCommerceService.getAttachedProductProcessingOrderId(
            ids = variables.commerceIds,
            product = product,
            customerId = variables.getCustomerIdNotNull(),
        )
    }

    /**
     * Will search for an attached [product] purchase and if it is found will update to [status].
     */
    private fun updateAttachedProcessingOrderStatusIfExists(
        variables: Variables,
        product: ProductType,
        status: ProcessingOrderStatus,
    ) {
        check(status.processId == product.processId) {
            "Attempted to update $product to $status but the process id does not match!"
        }

        val customerId = variables.getCustomerIdNotNull()
        val attachedProcessingOrderId = getAttachedProcessingOrderId(variables, product)

        if (attachedProcessingOrderId == null) {
            logger.info("No attached processing order found for product $product, not updating status to $status.")
            return
        }

        logger.info("Updating attached processing order $attachedProcessingOrderId order status to $status")
        processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
            attachedProcessingOrderId,
            status.processingStatusId,
            "1",
            customerId,
            true,
            PutProcessingOrderRequest()
                .comment("Updated processing status to $status")
                .updatedBy("NGF"),
        ).blockSingle()
    }

    private fun getCustomer(variables: Variables): CustomerDetailResponse {
        val customerId = variables.getCustomerIdNotNull()
        return customerApi.customersCustomerIdGet(
            customerId.toInt(),
            "1",
            customerId,
            true,
        ).blockSingle()
    }

    private fun updateSentToSOSStatusAndSendNotifications(variables: Variables) {
        this.updateProcessingOrderStatusByDescription(variables, OrderStatusDescription.SentToSOS)
        val customer = this.getCustomer(variables)
        this.sendNotification(
            variables.createSentToSOSEmailNotification(
                customerFirstName = customer.getContactFirstName(),
            ),
        )
        this.sendNotification(
            variables.createSentToSOSSMSNotification(
                product = ProductType.fromProcessId(variables.getProcessIdNotNull()),
            ),
        )
    }

    // TODO: Move to shared service.
    private fun sendNotification(request: NotificationRequest) {
        notificationsServiceInternalApi.createNotification1(
            request,
            null,
        )
            .blockLast()
            .also {
                it.checkIsSuccessful()
                logger.info(
                    "Notification ${request.notificationType} sent for topic ${request.topic}.",
                )
            }
    }
}
