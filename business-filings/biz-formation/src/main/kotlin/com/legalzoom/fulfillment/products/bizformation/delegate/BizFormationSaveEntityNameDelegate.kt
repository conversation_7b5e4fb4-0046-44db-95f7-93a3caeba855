package com.legalzoom.fulfillment.products.bizformation.delegate

import com.legalzoom.fulfillment.products.bizformation.service.BizFormationService
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.extensions.addValidationError
import com.legalzoom.fulfillment.workflow.variable.input
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.springframework.stereotype.Component

/**
 * Formations that go through ORCO which may have updated the entity name, save the entity name to COD and answers.
 */
@Component
class BizFormationSaveEntityNameDelegate(
    private val bizFormationService: BizFormationService,
) : JavaDelegate {
    @WithSpan("BizFormationSaveEntityNameDelegate")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        bizFormationService.saveEntityName(
            variables = execution.input,
            executionId = execution.id,
            processDefinitionId = execution.processDefinitionId,
        )?.let { validationError ->
            execution.addValidationError(validationError)
        }
    }
}
