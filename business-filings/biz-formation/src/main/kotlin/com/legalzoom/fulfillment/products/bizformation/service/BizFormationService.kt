package com.legalzoom.fulfillment.products.bizformation.service

import com.legalzoom.api.model.dsd.docgen.DocGenJobRequestDto
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.workflow.variable.Variables

/**
 * The primary service class for the entire biz-formation flow.
 * When being called from a delegate [Variables] can be passed in to avoid extra service calls.
 * If being used from a controller, just processingOrderId should be passed and the [Variables] can be fetched from
 * the process instance.
 *
 * TODO: Fork [Variables] into [BizFormationVariables].
 */
interface BizFormationService {
    fun createBusinessEntity(variables: Variables)

    fun preFilingVariablesFetch(variables: Variables): Variables

    fun assignedForNameCheck(variables: Variables)

    fun entityPrelimNameAvailable(variables: Variables)

    fun saveEntityName(
        variables: Variables,
        executionId: String,
        processDefinitionId: String,
    ): ValidationError?

    fun checkFilingStatus(variables: Variables): Variables

    fun queueRPAJob(
        variables: Variables,
        executionId: String,
    ): Variables

    fun checkRPAJobStatus(variables: Variables): Variables

    fun deleteRPAJob(variables: Variables): Variables

    fun instantFilingComplete(variables: Variables)

    fun filingComplete(variables: Variables)

    fun readyForDownload(variables: Variables)

    fun documentsPrepared(variables: Variables)

    fun californiaSOIValidationErrorStatusUpdate(variables: Variables)

    fun californiaSOIShippingCompleteStatusUpdate(variables: Variables)

    fun sendNPSSurvey(variables: Variables)

    fun determinePostFilingDocGen(variables: Variables): List<DocGenJobRequestDto>?
}
