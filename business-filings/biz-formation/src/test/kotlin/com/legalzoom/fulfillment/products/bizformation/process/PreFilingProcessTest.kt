package com.legalzoom.fulfillment.products.bizformation.process

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.documentAutomation.DocumentAutomationApi
import com.legalzoom.api.fulfillment.RpaApi
import com.legalzoom.api.model.answer.FilingMappedDataResponse
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.businessentities.EntityDetailDto
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.fulfillment.RPAValidationResult
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailResponse
import com.legalzoom.api.model.questionnaire.GetQuestionnaireInfoResponse
import com.legalzoom.api.model.questionnaire.QuestionnaireFieldDto
import com.legalzoom.api.model.workorders.WorkOrderItemDtoV2
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.fulfillment.answersapi.model.AnswerSource.AnswerBank
import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants.PRE_FILING_PROCESS
import com.legalzoom.fulfillment.domain.enumeration.State.CALIFORNIA
import com.legalzoom.fulfillment.domain.enumeration.State.TEXAS
import com.legalzoom.fulfillment.printandship.service.BusinessEntityService
import com.legalzoom.fulfillment.salesforce.SalesforceService
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.service.data.SDNEntry
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition.Proceed
import com.legalzoom.fulfillment.service.enumeration.ProductType.LLC
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FieldUpdaterService
import com.legalzoom.fulfillment.service.service.LegacyEventHandlerService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.PreFilingValidationStarted
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.PreliminaryNameValidationComplete
import com.legalzoom.fulfillment.service.service.WorkOrderService
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataComposite
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.testing.configuration.FixedClockConfiguration
import com.legalzoom.fulfillment.workflow.bpmn.helpers.BaseProcessTest
import com.legalzoom.fulfillment.workflow.data.DMNModel
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.apache.lucene.search.TotalHits
import org.apache.lucene.search.TotalHits.Relation.EQUAL_TO
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.RepositoryService
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.opensearch.action.search.MultiSearchResponse
import org.opensearch.action.search.MultiSearchResponse.Item
import org.opensearch.action.search.SearchResponse
import org.opensearch.client.RestHighLevelClient
import org.opensearch.common.bytes.BytesArray
import org.opensearch.common.bytes.BytesReference
import org.opensearch.common.unit.TimeValue
import org.opensearch.search.SearchHit
import org.opensearch.search.SearchHits
import org.opensearch.search.aggregations.Aggregations
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.http.ResponseEntity
import reactor.core.publisher.Mono
import java.time.Clock
import java.time.LocalDate
import java.time.LocalTime.NOON
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.Map.entry

@Import(FixedClockConfiguration::class)
@Deployment(resources = ["bpmn/pre-filing.bpmn", "bpmn/salesforce.bpmn"])
class PreFilingProcessTest(
    private val clock: Clock,
    private val objectMapper: ObjectMapper,
) : BaseProcessTest() {
    @MockkBean
    private lateinit var ldClient: LDClient

    @MockkBean(relaxUnitFun = true)
    private lateinit var legacyEventHandlerService: LegacyEventHandlerService

    @MockkBean
    private lateinit var questionnaireApi: QuestionnaireApi

    @MockkBean
    private lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    private lateinit var workOrderService: WorkOrderService

    @MockkBean
    private lateinit var salesforceApi: SalesforceService

    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var ssorcoFeatureToggleService: ORCOFeatureToggleService

    @MockkBean
    private lateinit var rpaApi: RpaApi

    @MockkBean
    private lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    private lateinit var ordersApiService: OrdersApiService

    @MockkBean
    private lateinit var openSearchClient: RestHighLevelClient

    @MockkBean
    private lateinit var documentService: DocumentService

    @MockK
    private lateinit var searchResponse: SearchResponse

    @MockK
    private lateinit var multiSearchResponse: MultiSearchResponse

    @MockK
    private lateinit var searchHits: SearchHits

    @MockK
    private lateinit var aggregations: Aggregations

    @MockkBean
    private lateinit var customerApi: CustomerApi

    @MockkBean
    private lateinit var notificationEventService: NotificationEventService

    @MockkBean
    private lateinit var documentAutomationApi: DocumentAutomationApi

    @MockK
    private lateinit var repositoryService: RepositoryService

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    private lateinit var fieldUpdaterService: FieldUpdaterService

    @MockkBean
    private lateinit var businessEntityService: BusinessEntityService

    @MockkBean
    private lateinit var ordersOrderItemsApiService: OrdersOrderItemsApiService

    @BeforeEach
    fun setup() {
        clearAllMocks()
        mockCustomerDataFetch()
        mockSnsServiceData()
        mockEntityDataFetch()
        mockSalesforceApi()
        mockFeatureToggleService()
        mockProcessingOrdersApi()
        mockWorkOrderService()
        mockRpaApi()
        mockCompletedOrderDetailApi()
        mockOpenSearchClient(0)
        mockOrdersApi()
        mockQuestionnaireApi()
        mockDocumentAutomationApi()
        mockFieldUpdaterService()
        mockBusinessEntityService()
        mockOrdersOrderItemsApiService()
    }

    private fun mockFieldUpdaterService() {
        every {
            fieldUpdaterService.saveFieldValue(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Unit
    }

    private fun mockAnswersService(
        answersMock: FilingMappedDataResponse,
        processId: Int,
    ) {
        val filingDataComposite = FilingDataComposite(processId, AnswerBank, answersMock, null)
        every {
            questionnaireAnswerService.getFilingData(any(), any(), any())
        } returns filingDataComposite

        every {
            questionnaireAnswerService.putPartialUpdate(any(), any())
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())
    }

    private fun mockDocumentAutomationApi() {
        every {
            documentAutomationApi.documentAutomationAutomatePostWithHttpInfo(any(), any(), any(), any())
        } returns Mono.just(ResponseEntity.ok(""))
    }

    private fun mockCustomerDataFetch() {
        val customerByIdResponseJson =
            ClassPathResource("customer_api_customer_by_customerId_response.json", javaClass).file
        val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        } returns Mono.just(customerByIdResponse)
    }

    private fun mockSnsServiceData() {
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns "d413681a-9810-5dc7-b485-349134df00ad"
    }

    private fun mockEntityDataFetch() {
        val answersApiJSON = ClassPathResource("ca_valid_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)
        mockAnswersService(answersApiResponse, LLC.processId)
    }

    private fun mockSalesforceApi(caseId: String = "test") {
        every {
            salesforceApi.createCase(any())
        } returns SalesforceCaseResponse("test", caseId)

        every {
            salesforceApi.updateCase(any())
        } returns SalesforceCaseUpdateResponse(caseId, "test", "test")

        every {
            salesforceApi.addLedgerNote(any())
        } returns AddLedgerNoteResponse("test", "test", emptyList())

        every {
            salesforceApi.notifySSOrcoChange(any())
        } returns ""
    }

    private fun mockFeatureToggleService() {
        every {
            featureToggleService.isOfacEnabled(any(), any())
        } returns true
        every {
            featureToggleService.isNameCheckDisabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.isSmartyStreetsValidationEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isManualOrcoEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns false
        every {
            ssorcoFeatureToggleService.isOrcoEventPublisherEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isGeneralOrcoLedgerNoteEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.isSSORCORepeatNotificationsEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.discardNonOrcoValidationsInPreFiling(any(), any(), any())
        } returns false
    }

    private fun mockProcessingOrdersApi(processingOrderStatus: ProcessingOrderStatus? = null) {
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns
            Mono.just(
                GetProcessingOrderResponse().also { response ->
                    ProcessingOrderDto().also { dto ->
                        dto.processingStatusId = processingOrderStatus?.processingStatusId
                    }
                },
            )
    }

    private fun mockWorkOrderService() {
        every {
            workOrderService.searchWorkOrderItemHelper(any(), any(), any(), any())
        } returns WorkOrderItemDtoV2()
        every {
            workOrderService.updateWorkOrderEnumStatus(any(), any())
        } returns Unit
        every {
            workOrderService.updateWorkOrderStringStatus(any(), any())
        } returns Unit
    }

    private fun mockRpaApi() {
        every {
            rpaApi.validateJson(any())
        } returns Mono.just(RPAValidationResult().passed(true))
    }

    private fun mockCompletedOrderDetailApi() {
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            Mono.just(
                UpdateCompletedOrderDetailResponse().effectiveDate(
                    OffsetDateTime.of(LocalDate.now(clock), NOON, UTC),
                ),
            )

        val completedOrderDetailResponseJson =
            ClassPathResource("GetCompletedOrderDetailByProcessingOrderId.json", javaClass).file
        val completedOrderDetailResponse =
            objectMapper.readValue<GetCompleteOrderDetailResponse>(completedOrderDetailResponseJson)

        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(completedOrderDetailResponse)
    }

    private fun mockOpenSearchClient(hit: Long) {
        val json = ClassPathResource("SDN_Entry.json", javaClass).file
        val entry = objectMapper.readValue<SDNEntry>(json)
        val searchHit = SearchHit(1)
        val source: BytesReference =
            BytesArray(
                objectMapper.writeValueAsString(entry),
            )
        searchHit.sourceRef(source)
        multiSearchResponse = MultiSearchResponse(arrayOf(Item(searchResponse, null)), 1)
        every { searchHits.hits } returns arrayOf(searchHit)
        every { searchHits.totalHits } returns TotalHits(hit, EQUAL_TO)
        every { searchResponse.hits } returns searchHits
        every { searchResponse.took } returns TimeValue(1L)
        every { searchResponse.aggregations } returns aggregations
        every {
            openSearchClient.msearch(any(), any())
        } returns multiSearchResponse
    }

    private fun mockDocumentService(match: Boolean) {
        if (match) {
            val expectedBothResource =
                ClassPathResource("documentBoth.json", javaClass).file
            val expectedBothResponse = objectMapper.readValue<List<Document>>(expectedBothResource)
            every {
                documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
            } returns expectedBothResponse
        } else {
            every {
                documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
            } returns emptyList()
        }
    }

    private fun mockOrdersApi() {
        val ordersApiJSON = ClassPathResource("ca_llc_order_response.json", javaClass).file
        val ordersApiResponse = objectMapper.readValue<GetOrderResponse>(ordersApiJSON)

        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns ordersApiResponse
    }

    private fun mockQuestionnaireApi() {
        every {
            questionnaireApi.questionnaireFieldsQuestionnaireIdGet(any(), any(), any(), any())
        } returns
            Mono.just(
                GetQuestionnaireInfoResponse().also { response ->
                    // Instead of mocking each different questionnaire type, we only need this for entity name,
                    //   so just return all the field names for llc/inc/np we need.
                    response.questionnaireFields =
                        listOf(
                            QuestionnaireFieldDto().also { field ->
                                field.questionnaireFieldId = 1
                                field.fieldName = "LLC_name"
                            },
                            QuestionnaireFieldDto().also { field ->
                                field.questionnaireFieldId = 2
                                field.fieldName = "Corporate_Name"
                            },
                            QuestionnaireFieldDto().also { field ->
                                field.questionnaireFieldId = 3
                                field.fieldName = "Name_of_non_profit"
                            },
                        )
                },
            )
    }

    private fun mockBusinessEntityService() {
        every {
            businessEntityService.getBusinessEntityResponseByProcessingOrderIdOrNull(any())
        } returns
            GetEntityByEntityIdResponse().also {
                it.entity = EntityDetailDto()
            }
    }

    private fun mockOrdersOrderItemsApiService() {
        every {
            ordersOrderItemsApiService.getOrdersOrderItems(any(), null, null, null)
        } returns
            GetOrderItemResponse().also {
                it.orderItem = OrderItemDto()
            }
    }

    fun buildVariables() =
        variables {
            customerId = UniqueId.nextIdString()
            orderId = UniqueId.nextInt()
            processingOrderId = UniqueId.nextInt()
            accountId = UniqueId.nextUUID()
            processId = LLC.processId
            humanTaskCreationEnabled = true
            jurisdiction = CALIFORNIA.abbreviation
            preFilingQcEnabled = true
            fieldName = "EntityName"
            fieldValue = "test_FieldValue"
        }

    fun startPreFilingProcessInstance(variables: Variables): ProcessInstance {
        return camundaTestHelpers.startProcessInstanceAndWaitForStart(
            PRE_FILING_PROCESS,
            variables.processingOrderId.toString(),
            variables,
        )
    }

    @Test
    fun testFixedClock() {
        assertThat(clock.javaClass.simpleName).isEqualTo("FixedClock")
    }

    @Test
    fun testPreFilingProcess() {
        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(preFilingProcess, "pre-filing-salesforce-activity")

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                PreFilingValidationStarted.processingStatusId,
                any(),
                variables.customerId!!,
                any(),
                any(),
            )
        }

        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = preFilingProcess,
            disposition = "retry",
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(preFilingProcess)
        camundaTestHelpers.assertProcessInstancesFinishedWithoutIncidentsOrErrors(
            businessKey = preFilingProcess.businessKey,
            errorKeys = emptySet(),
        )

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                PreliminaryNameValidationComplete.processingStatusId,
                any(),
                variables.customerId!!,
                any(),
                any(),
            )
        }
    }

    @Test
    fun testDMNDataMapping() {
        val answersApiJSON = ClassPathResource("ca_valid_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)
        val filingDataPayLoad = FilingDataComposite(LLC.processId, answersApiResponse)
        val dmnData = DMNModel.fromFilingDataPayload(filingDataPayLoad, objectMapper)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        assertThat(preFilingProcess).variables().containsAllEntriesOf(
            mapOf(
                "expediteSpeed" to dmnData?.expediteSpeed,
                "company" to dmnData?.company,
                "orderIncludesEIN" to true,
                "orderIncludesInitialReports" to true,
                "orderIncludesOperatingAgreement" to true,
            ),
        )
    }

    @Test
    fun `testing error boundary when SF is down`() {
        every {
            salesforceApi.createCase(any())
        } throws Exception("SF createCase is down test")

        every {
            featureToggleService.isCustomHandlerEnabled(any(), any(), any())
        } returns false

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        val salesforceProcess = camundaTestHelpers.getActiveSalesforceProcessInstanceByParent(preFilingProcess)
        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = salesforceProcess,
            activityId = "salesforce-task",
        )

        verify(exactly = 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                PreFilingValidationStarted.processingStatusId,
                any(),
                variables.customerId!!,
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                PreliminaryNameValidationComplete.processingStatusId,
                any(),
                variables.customerId!!,
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            salesforceApi.updateCase(any())
        }

        assertThat(salesforceProcess).hasPassed("salesforce-error-boundary")
        assertThat(salesforceProcess).isWaitingAt("salesforce-timer")
    }

    @Test
    fun testValidationInactive() {
        val answersApiJSON = ClassPathResource("ca_invalid_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        assertThat(
            preFilingProcess,
        ).hasPassed(
            "create-business-entity",
            "prelim-name-available-order-status-update",
            "entity-data-fetch",
            "validation-error-check",
        )
        assertThat(preFilingProcess).variables().contains(entry("validationError", false))
    }

    @Test
    fun testInvalidAgentIgnored() {
        val answersApiJSON = ClassPathResource("ca_invalid_agent_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )
        assertThat(preFilingProcess).hasPassed("validation-error-check")
        assertThat(preFilingProcess).variables().containsEntry("validationError", false)
    }

    @Test
    fun `Testing validation rule that should be bypassed does not create SF case`() {
        val answersApiJSON = ClassPathResource("ca_invalid_agent_bypass_llc_answers_api.json", javaClass).file

        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockFeatureToggleService()
        mockAnswersService(answersApiResponse, LLC.processId)

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )
        assertThat(preFilingProcess).variables().contains(entry("validationError", false))
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = preFilingProcess,
            disposition = Proceed.value,
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(preFilingProcess)
    }

    @Test
    fun testValidationInactiveLA() {
        val answersApiJSON = ClassPathResource("la_invalid_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        assertThat(preFilingProcess).variables().contains(entry("validationError", false))
    }

    @Test
    fun testValidationInactiveDE() {
        val answersApiJSON = ClassPathResource("de_invalid_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        assertThat(preFilingProcess).variables().contains(entry("validationError", false))
    }

    @Test
    fun testPreFilingSignatureCheckPass() {
        mockDocumentService(true)
        val answersApiJSON = ClassPathResource("tx_self_ra_llc_answer_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables =
            buildVariables().apply {
                this.jurisdiction = TEXAS.abbreviation
            }
        val preFilingProcess = startPreFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = preFilingProcess,
            disposition = "retry",
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance = preFilingProcess)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                PreliminaryNameValidationComplete.processingStatusId,
                any(),
                variables.customerId!!,
                any(),
                any(),
            )
        }
    }

    @Test
    fun testSignatureCheckIsSkipped() {
        mockDocumentService(false)
        val answersApiJSON = ClassPathResource("tx_self_ra_llc_answer_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        mockAnswersService(answersApiResponse, LLC.processId)

        val variables =
            buildVariables().apply {
                jurisdiction = TEXAS.abbreviation
                preFilingQcEnabled = false
            }
        val preFilingProcess = startPreFilingProcessInstance(variables)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance = preFilingProcess)
    }

    @Test
    fun `Save entity name failure`() {
        every { repositoryService.getProcessDefinition(any()).key } returns PRE_FILING_PROCESS

        every {
            questionnaireAnswerService.putPartialUpdate(
                any(),
                any(),
            )
        } throws RuntimeException("Test Exception")

        val variables =
            buildVariables().apply {
                this.preFilingQcEnabled = false
            }
        val preFilingProcess = startPreFilingProcessInstance(variables)
        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        assertThat(preFilingProcess).variables().contains(
            entry(
                "validationErrors",
                listOf(
                    ValidationError(
                        "Please escalate to Engineering. Unknown error unrelated to Fulfillment",
                        mapOf(
                            Pair(
                                "errorMessage",
                                "Test Exception",
                            ),
                        ),
                    ),
                ),
            ),
        )

        every {
            questionnaireAnswerService.putPartialUpdate(
                any(),
                any(),
            )
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())

        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = preFilingProcess,
            disposition = "proceed",
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(preFilingProcess)
    }

    @Test
    fun `alreadyFiled process variable is propagated from the SF subprocess to the parent process`() {
        val variables = buildVariables()
        val preFilingProcess = startPreFilingProcessInstance(variables)
        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = preFilingProcess,
            activityId = "pre-filing-salesforce-activity",
        )

        assertThat(
            preFilingProcess,
        ).hasPassed(
            "create-business-entity",
            "prelim-name-available-order-status-update",
            "entity-data-fetch",
            "validation-error-check",
        )

        BpmnAwareTests.assertThat(preFilingProcess)
            .variables()
            .doesNotContainKey("alreadyFiled")

        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = preFilingProcess,
            disposition = "proceed",
            alreadyFiled = true,
        )

        BpmnAwareTests.assertThat(preFilingProcess)
            .variables()
            .containsEntry("disposition", Proceed.value)
            .containsEntry("alreadyFiled", true)
    }
}
