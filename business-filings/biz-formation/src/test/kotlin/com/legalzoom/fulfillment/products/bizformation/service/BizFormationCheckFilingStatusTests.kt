package com.legalzoom.fulfillment.products.bizformation.service

import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.enumeration.DocumentType
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.service.IncDocumentFilingStateService
import com.legalzoom.fulfillment.workflow.service.LlcDocumentFilingStateService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService
import com.legalzoom.fulfillment.workflow.variable.variables
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.verify
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.core.io.ClassPathResource
import java.time.Instant
import kotlin.random.Random

class BizFormationCheckFilingStatusTests : BaseBizFormationServiceTest() {
    private val time = "2023-02-13T21:10:42.968349Z"

    @InjectMockKs
    private lateinit var llcDocumentFilingStateService: LlcDocumentFilingStateService

    @InjectMockKs
    private lateinit var incDocumentFilingStateService: IncDocumentFilingStateService

    companion object {
        @JvmStatic
        fun cheatCodes() =
            listOf(
                Arguments.arguments(
                    RpaSosSsorcoService.Companion.FORCE_NAME_ERROR,
                    RpaSosSsorcoService.Companion.INVALID_NAME_PREFIX,
                ),
                Arguments.arguments(
                    RpaSosSsorcoService.Companion.FORCE_ADDRESS_ERROR,
                    RpaSosSsorcoService.Companion.INVALID_ADDRESS_PREFIX,
                ),
            )

        @JvmStatic
        fun products() =
            listOf(
                Arguments.arguments(ProductType.LLC),
                Arguments.arguments(ProductType.INC),
            )
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
        every { clock.instant() } returns Instant.parse(time)
        val documentApiJSON = ClassPathResource("documents.json", javaClass).file
        val documentApiResponse = objectMapper.readValue<List<Document>>(documentApiJSON)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns documentApiResponse
        every {
            instantFilingService.isInstantFilingStateByVariable(any())
        } returns false

        every {
            instantFilingService.isSemiInstantFilingStateByVariable(any())
        } returns false

        every {
            rpaSosSsorcoService.createValidationError(any(), any())
        } returns ValidationError("test error")

        every {
            rpaSosSsorcoService.checkCheatCodes(any())
        } returns Unit

        every {
            bypassRpaService.isBypassRpaByVariables(any())
        } returns false

        every {
            documentFilingStateLookupService.getService(ProductType.LLC)
        } returns llcDocumentFilingStateService

        every {
            featureToggleService.rpaBypassExceptionsEnabled(any(), any(), any())
        } returns false
        every {
            orcoService.search(any(), any(), any(), any())
        } returns listOf()
    }

    @ParameterizedTest
    @MethodSource("products")
    fun testExecution(productType: ProductType) {
        val variables =
            variables {
                processId = productType.processId
                processingOrderId = *********
                customerId = "********"
                jurisdiction = "CA"
                accountId = UniqueId.nextUUID()
            }

        every {
            bypassRpaService.isBypassRpaByVariables(any())
        } returns false

        if (productType == ProductType.LLC) {
            every {
                documentFilingStateLookupService.getService(ProductType.LLC)
            } returns llcDocumentFilingStateService
        } else {
            every {
                documentFilingStateLookupService.getService(ProductType.INC)
            } returns incDocumentFilingStateService
        }

        bizFormationService.checkFilingStatus(variables)

        Assertions.assertThat(variables.alreadyFiled).isTrue
        Assertions.assertThat(
            variables.documentType.equals(
                CustomerDocumentType.Companion.findCustomerDocTypeFromProductName(
                    productType.productName,
                    DocumentType.ProofOfWork.name,
                ).documentType,
            ),
        )
        Assertions.assertThat(variables.validationErrors).isNullOrEmpty()
    }

    @Test
    fun testExecuteBypassRPA() {
        val variables =
            variables {
                processId = 2
                processingOrderId = *********
                customerId = "********"
                jurisdiction = "ME"
                accountId = UniqueId.nextUUID()
            }
        every {
            bypassRpaService.isBypassRpaByVariables(any())
        } returns true

        every {
            documentFilingService.isFaxFilingStateByVariables(any())
        } returns false

        every {
            documentFilingStateLookupService.getService(ProductType.LLC)
        } returns llcDocumentFilingStateService

        every {
            featureToggleService.rpaBypassExceptionsEnabled("********", "ME", 2)
        } returns true

        val updatedVariables = bizFormationService.checkFilingStatus(variables)

        Assertions.assertThat(updatedVariables.validationError).isTrue
        Assertions.assertThat(updatedVariables.validationErrors).hasSize(1)
        Assertions.assertThat(updatedVariables.validationErrors?.first()?.message).isEqualTo("Manual filing required")
        Assertions.assertThat(updatedVariables.validationErrors?.first()?.data).isEqualTo(
            mapOf(
                "Detail" to "This order " +
                    "requires manual filing. Please manually file with the SOS and then select \"Skip-I have manually filed\"",
            ),
        )
    }

    @Test
    fun testResetRetry() {
        val variables =
            variables {
                processId = 2
                processingOrderId = Random.Default.nextInt()
                customerId = Random.Default.nextInt().toString()
                jurisdiction = "ME"
                retriable = true
                retryCount = 2
                disposition = FulfillmentDisposition.Proceed.value
                accountId = UniqueId.nextUUID()
            }

        bizFormationService.checkFilingStatus(variables)

        assertAll(
            { Assertions.assertThat(variables.retriable).isFalse() },
            { Assertions.assertThat(variables.retryCount).isEqualTo(0) },
        )
    }

    @Test
    fun testExecuteBypassRPASkip() {
        val variables =
            variables {
                processId = 2
                processingOrderId = *********
                customerId = "********"
                jurisdiction = "ME"
                disposition = "proceed"
                accountId = UniqueId.nextUUID()
            }
        every {
            bypassRpaService.isBypassRpaByVariables(any())
        } returns true

        every {
            documentFilingService.isFaxFilingStateByVariables(any())
        } returns false

        every {
            documentFilingStateLookupService.getService(ProductType.LLC)
        } returns llcDocumentFilingStateService

        every {
            documentFilingStateLookupService.getService(ProductType.INC)
        } returns incDocumentFilingStateService

        bizFormationService.checkFilingStatus(variables)

        Assertions.assertThat(variables.validationError).isNull()
        Assertions.assertThat(variables.validationErrors).isNullOrEmpty()
    }

    @ParameterizedTest
    @MethodSource("cheatCodes")
    fun testCheatCodes(
        cheatCode: String,
        expectedError: String,
    ) {
        val variables =
            variables {
                processId = 2
                processingOrderId = 123
                customerId = "123"
                jurisdiction = "CA"
                entityName = cheatCode
                accountId = UniqueId.nextUUID()
            }

        every {
            bypassRpaService.isBypassRpaByVariables(any())
        } returns false

        every {
            documentFilingService.isFaxFilingStateByVariables(any())
        } returns false

        every {
            documentFilingStateLookupService.getService(ProductType.LLC)
        } returns llcDocumentFilingStateService

        bizFormationService.checkFilingStatus(variables)

        verify(exactly = 1) {
            rpaSosSsorcoService.checkCheatCodes(any())
        }
    }

    @Test
    fun testProductTypeNotSupported() {
        val variables =
            variables {
                processId = 0
                processingOrderId = *********
                customerId = "********"
                jurisdiction = "CA"
                accountId = UniqueId.nextUUID()
            }
        every {
            bypassRpaService.isBypassRpaByVariables(any())
        } returns false

        assertThrows<RuntimeException>("Could NOT retrieve product type for processId ${variables.processId}") {
            bizFormationService.checkFilingStatus(variables)
        }
    }
}
