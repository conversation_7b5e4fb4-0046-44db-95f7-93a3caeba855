package com.legalzoom.fulfillment.products.bizformation.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.notificationplatform.notificationplatform.NotificationsServiceApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.fulfillment.orco.service.OrcoService
import com.legalzoom.fulfillment.printandship.service.BusinessEntityService
import com.legalzoom.fulfillment.products.bizformation.configuration.BizFormationConfigurationProperties
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.QueueItemsService
import com.legalzoom.fulfillment.service.service.UnifiedCommerceService
import com.legalzoom.fulfillment.service.service.asknicely.AskNicelyService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.delegate.helper.RpaHelper
import com.legalzoom.fulfillment.workflow.service.AddQueuesItemService
import com.legalzoom.fulfillment.workflow.service.BypassRpaService
import com.legalzoom.fulfillment.workflow.service.CourierFilingService
import com.legalzoom.fulfillment.workflow.service.DocumentFilingService
import com.legalzoom.fulfillment.workflow.service.DocumentFilingStateLookupService
import com.legalzoom.fulfillment.workflow.service.InstantFilingService
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService
import com.legalzoom.fulfillment.workflow.service.ValidationService
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.SpyK
import io.mockk.junit5.MockKExtension
import org.camunda.bpm.engine.RepositoryService
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import java.time.Clock

@ExtendWith(MockKExtension::class)
abstract class BaseBizFormationServiceTest {
    @MockK
    lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockK
    lateinit var customerApi: CustomerApi

    @MockK
    lateinit var answerApi: AnswerApi

    @MockK
    lateinit var notificationsServiceInternalApi: NotificationsServiceApi

    @MockK
    lateinit var unifiedCommerceService: UnifiedCommerceService

    @MockK
    lateinit var courierFilingService: CourierFilingService

    @MockK
    lateinit var instantFilingService: InstantFilingService

    @MockK
    lateinit var askNicelyService: AskNicelyService

    @MockK
    lateinit var bizFormationConfigProps: BizFormationConfigurationProperties

    @MockK
    lateinit var launchDarkly: LDClient

    @MockK
    lateinit var businessEntityService: BusinessEntityService

    @MockK
    lateinit var validationService: ValidationService

    @MockK
    lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockK
    lateinit var questionnaireApi: QuestionnaireApi

    @MockK
    lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockK
    lateinit var fulfillmentEventService: FulfillmentEventService

    @MockK
    lateinit var clock: Clock

    @MockK
    lateinit var repositoryService: RepositoryService

    @MockK
    lateinit var documentService: DocumentService

    @MockK
    lateinit var documentFilingService: DocumentFilingService

    @MockK
    lateinit var bypassRpaService: BypassRpaService

    @MockK
    lateinit var rpaSosSsorcoService: RpaSosSsorcoService

    @MockK
    lateinit var documentFilingStateLookupService: DocumentFilingStateLookupService

    @MockK
    lateinit var featureToggleService: FeatureToggleService

    @MockK
    lateinit var orcoService: OrcoService

    @MockK
    lateinit var orderCancellationService: OrderCancellationService

    @MockK
    lateinit var rpaHelper: RpaHelper

    // Made into a spy so auto-injected with @InjectMockKs
    @SpyK
    var objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    @MockK
    lateinit var addQueuesItemService: AddQueuesItemService

    @MockK
    lateinit var queueItemsService: QueueItemsService

    @InjectMockKs
    lateinit var bizFormationService: BizFormationServiceImpl
}
