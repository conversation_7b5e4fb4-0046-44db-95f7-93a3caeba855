package com.legalzoom.fulfillment.products.bizformation.process

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.launchdarkly.sdk.LDUser
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.address.AddressValidationApi
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.auth0.OAuthServiceImpl
import com.legalzoom.api.businessentities.BusinessEntitiesApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.dds.DocumentRequestApi
import com.legalzoom.api.model.answer.Company
import com.legalzoom.api.model.answer.FilingMappedDataResponse
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.businessentities.EntityDetailDto
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.businessentities.UpdateEntityDto
import com.legalzoom.api.model.businessentities.UpdateEntityRequest
import com.legalzoom.api.model.businessentities.UpdateEntityResponse
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.notificationsplatform.NotificationResponse
import com.legalzoom.api.model.notificationsplatform.TargetChannel
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailResponse
import com.legalzoom.api.model.questionnaire.GetQuestionnaireInfoResponse
import com.legalzoom.api.model.rpa.ODataValueOfIEnumerableOfQueueItemDto
import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.notificationplatform.notificationplatform.NotificationsServiceApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.api.revv.RevvAuthService
import com.legalzoom.fulfillment.common.exception.ParameterArgumentNotValidException
import com.legalzoom.fulfillment.common.orco.OrcoToggleHelper
import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants.FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.PROF_LICENSE_NEEDED_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.FILING
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.domain.enumeration.State.CALIFORNIA
import com.legalzoom.fulfillment.domain.enumeration.State.MAINE
import com.legalzoom.fulfillment.domain.model.CompleteTaskRequest
import com.legalzoom.fulfillment.orco.entity.Category
import com.legalzoom.fulfillment.orco.entity.Orco
import com.legalzoom.fulfillment.orco.entity.OrcoNotificationHistory
import com.legalzoom.fulfillment.orco.entity.Reason
import com.legalzoom.fulfillment.orco.entity.Resolution
import com.legalzoom.fulfillment.orco.entity.enumeration.OrcoStatus.OPEN
import com.legalzoom.fulfillment.orco.entity.enumeration.OrcoType.SELF_SERVE
import com.legalzoom.fulfillment.orco.entity.enumeration.ResolutionType
import com.legalzoom.fulfillment.orco.repository.OrcoRepository
import com.legalzoom.fulfillment.orco.service.NotificationToBeSent
import com.legalzoom.fulfillment.orco.service.OrcoNotificationLogic
import com.legalzoom.fulfillment.orco.service.OrcoNotificationSender
import com.legalzoom.fulfillment.orco.service.OrcoNotificationVariables
import com.legalzoom.fulfillment.orco.service.OrcoService
import com.legalzoom.fulfillment.orco.service.model.OrcoRequest
import com.legalzoom.fulfillment.orco.service.model.OrcoResponse
import com.legalzoom.fulfillment.orco.service.model.ReasonResponse
import com.legalzoom.fulfillment.orco.service.model.ResolutionResponse
import com.legalzoom.fulfillment.orco.service.model.toEntity
import com.legalzoom.fulfillment.orco.service.model.toResponse
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.LLC_RegisteredAgentAcceptance
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ArticlesFiled
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ArticlesPreFiled
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ProofOfWork
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition.Proceed
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition.Retry
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.LLC
import com.legalzoom.fulfillment.service.enumeration.ProductType.RegisteredAgentService
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.EmailSenderService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.LegacyEventHandlerService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.DocumentsReceivedFromState
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.IncStateFilingComplete
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.RaFilingComplete
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.StateFilingComplete
import com.legalzoom.fulfillment.service.service.QueueItemsService
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataComposite
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.testing.configuration.FixedClockConfiguration
import com.legalzoom.fulfillment.workflow.bpmn.helpers.BaseProcessTest
import com.legalzoom.fulfillment.workflow.data.DMNCompany
import com.legalzoom.fulfillment.workflow.service.AddQueuesItemService
import com.legalzoom.fulfillment.workflow.service.CourierFilingService
import com.legalzoom.fulfillment.workflow.service.FaxFilingService
import com.legalzoom.fulfillment.workflow.service.OrcoNotificationIntervalService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.WorkflowService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.RepositoryService
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.task
import org.joda.time.DateTimeUtils
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.http.MediaType.APPLICATION_PDF
import org.springframework.http.MediaType.IMAGE_PNG
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.io.File
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime.NOON
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.Map.entry
import java.util.Optional
import java.util.UUID

@Suppress("DEPRECATED")
@Import(FixedClockConfiguration::class)
@Deployment(resources = ["bpmn/filing.bpmn"])
class FilingProcessTest(
    private val clock: Clock,
    private val objectMapper: ObjectMapper,
    private val workflowService: WorkflowService,
) : BaseProcessTest() {
    @MockkBean
    private lateinit var addQueuesItemService: AddQueuesItemService

    @MockkBean
    private lateinit var queueItemsService: QueueItemsService

    @MockkBean
    private lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var ssorcoFeatureToggleService: ORCOFeatureToggleService

    @MockkBean
    private lateinit var salesforceApiService: SalesforceApiService

    @MockkBean
    private lateinit var fulfillmentEventService: FulfillmentEventService

    @MockkBean
    private lateinit var documentService: DocumentService

    @MockkBean
    private lateinit var faxFilingService: FaxFilingService

    @MockkBean
    private lateinit var s3Service: S3Service

    @MockkBean
    private lateinit var ordersApiService: OrdersApiService

    @MockkBean
    private lateinit var businessEntitiesApi: BusinessEntitiesApi

    @MockkBean
    private lateinit var questionnaireApi: QuestionnaireApi

    @MockkBean
    private lateinit var answerApi: AnswerApi

    @MockkBean
    private lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    private lateinit var documentRequestApi: DocumentRequestApi

    @MockkBean
    private lateinit var courierFilingService: CourierFilingService

    @MockkBean
    private lateinit var emailSenderService: EmailSenderService

    @MockkBean
    private lateinit var customerApi: CustomerApi

    @MockkBean
    private lateinit var notificationEventService: NotificationEventService

    @MockK
    private lateinit var repositoryService: RepositoryService

    @MockkBean
    private lateinit var revvAuthService: RevvAuthService

    @MockkBean
    private lateinit var oAuthServiceImpl: OAuthServiceImpl

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    private lateinit var ldClient: LDClient

    @MockK
    private lateinit var addressValidationApi: AddressValidationApi

    @MockkBean
    private lateinit var orcoRepository: OrcoRepository

    @MockkBean
    private lateinit var orcoNotificationIntervalService: OrcoNotificationIntervalService

    @MockkBean
    private lateinit var orcoNotificationSender: OrcoNotificationSender

    @MockkBean
    private lateinit var orcoNotificationLogic: OrcoNotificationLogic

    @MockkBean
    private lateinit var orcoNotificationVariables: OrcoNotificationVariables

    @MockkBean
    private lateinit var orcoService: OrcoService

    @MockkBean
    private lateinit var orcoToggleHelper: OrcoToggleHelper

    @MockkBean(relaxUnitFun = true)
    private lateinit var legacyEventHandlerService: LegacyEventHandlerService

    @MockkBean
    private lateinit var notificationsServiceInternalApi: NotificationsServiceApi

    @BeforeEach
    fun setup() {
        clearAllMocks()
        mockCustomerDataFetch()
        mockSnsServiceData()
        mockAddQueueItem()
        mockFeatureToggleService()
        mockLDClient()
        mockFulfillmentEventService()
        mockSalesforceApi()
        mockDocumentService()
        mockS3Service()
        mockBusinessEntitiesApi()
        mockOrderService()
        mockQuestionnaireApi()
        mockAnswerApi()
        mockCompletedOrderDetailApi()
        mockDocumentRequestApi()
        mockCourierFilingService()
        mockRepositoryService()
        mockFaxFilingService()
        mockEntityDataFetch()
        mockQuestionnaireAnswerService()
        mockOrcoNotifications()
        mockRevvAuthServiceService()
        mockOAuthServiceImpl()
        mockOrcoService()
        mockOrcoToggleHelper()
        mockNotificationPlatform()

        every {
            featureToggleService.isOrderExpiryEnabled()
        } returns false
    }

    private fun mockOrcoToggleHelper() {
        every {
            orcoToggleHelper.getApplicableOrcoValidationErrors(any(), any(), any(), any())
        } returns
            OrcoToggleHelper.OrcoValidationErrors(
                isSelfServe = true,
            )
    }

    private fun simpleResolutionResponse() =
        ResolutionResponse(
            type = ResolutionType.MANUAL,
            categoryId = UUID.randomUUID(),
            id = UUID.randomUUID(),
            createdBy = "NGX",
            modifiedBy = "NGX",
            createdDate = Instant.now(),
            modifiedDate = Instant.now(),
        )

    private fun mockOrcoService() {
        every {
            orcoService.save(any())
        } answers {
            val arg = it.invocation.args[0] as OrcoRequest
            arg.toEntity().toResponse()
        }

        every {
            orcoService.getOpenOrco(any())
        } returns null

        every {
            orcoService.hasOpenOrco(any())
        } returns false

        every {
            orcoService.search(any(), any(), any(), any())
        } returns
            listOf(
                OrcoResponse(
                    type = SELF_SERVE,
                    status = OPEN,
                    reasons =
                        listOf(
                            ReasonResponse(
                                "1234",
                                OPEN,
                                "note",
                                simpleResolutionResponse(),
                                UUID.randomUUID(),
                                null,
                                null,
                                null,
                                null,
                            ),
                        ),
                    references = listOf(),
                    id = UUID.randomUUID(),
                    createdBy = "NGX",
                    modifiedBy = "NGX",
                    createdDate = Instant.now(),
                    modifiedDate = Instant.now(),
                ),
            )
    }

    private fun mockOAuthServiceImpl() {
        every {
            oAuthServiceImpl.init()
        } returns Unit
    }

    private fun mockRevvAuthServiceService() {
        every {
            revvAuthService.init()
        } returns Unit
    }

    private fun mockLDClient() {
        every {
            ldClient.boolVariation(any(), any<LDUser>(), any())
        } returns true
    }

    private fun mockFaxFilingService() {
        every {
            faxFilingService.isFaxFilingState(
                any(),
            )
        } returns false
    }

    private fun mockCustomerDataFetch() {
        val customerByIdResponseJson =
            ClassPathResource("customer_api_customer_by_customerId_response.json", javaClass).file
        val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        } returns Mono.just(customerByIdResponse)
    }

    private fun mockSnsServiceData() {
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns "d413681a-9810-5dc7-b485-349134df00ad"
    }

    private fun mockNotificationPlatform() {
        every {
            notificationsServiceInternalApi.createNotification1(any(), any())
        } returns
            Flux.just(
                NotificationResponse().also { response ->
                    response.status = NotificationResponse.StatusEnum.ACCEPTED
                },
            )
    }

    private fun mockCourierFilingService() {
        every { courierFilingService.isCourierFilingState(any()) } returns false
        every { courierFilingService.isCourierFilingStateByVariables(any()) } returns false
    }

    private fun mockRepositoryService() {
        every {
            repositoryService.getProcessDefinition(any()).key
        } returns FILING_PROCESS
    }

    private fun mockEntityDataFetch() {
        val answersApiJSON = ClassPathResource("ca_valid_llc_answers_api.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<FilingMappedDataResponse>(answersApiJSON)

        every {
            answerApi.answersProcessingOrdersProcessingOrderIdProcessesProcessIdFilingDataGet(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(answersApiResponse)
    }

    private fun mockQuestionnaireAnswerService() {
        every {
            questionnaireAnswerService.putPartialUpdate(
                any(),
                any(),
            )
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())

        every {
            questionnaireAnswerService.getFilingData(
                any(),
                any(),
                any(),
            )
        } returns
            FilingDataComposite(
                testProcessId,
                FilingMappedDataResponse().company(
                    Company().apply {
                        this.nameWithDesignator = testQuestionnaireNameWithDesignator
                        this.isProfessional = false
                    },
                ),
            )
    }

    @AfterEach
    fun tearDown() {
        DateTimeUtils.setCurrentMillisSystem()
    }

    private fun mockAddQueueItem() {
        val json = ClassPathResource("rpa_response.json", javaClass).file
        val response = objectMapper.readValue<QueueItemDto>(json)
        every {
            addQueuesItemService.addQueuesItem(any(), any(), any(), any(), any())
        } returns response
        mockGetQueueItem(true)
    }

    private fun mockGetQueueItem(success: Boolean) {
        val fileName = if (success) "rpa_get_job_success.json" else "rpa_get_job_failure.json"
        val json = ClassPathResource(fileName, javaClass).file
        val response = objectMapper.readValue<ODataValueOfIEnumerableOfQueueItemDto>(json)
        every {
            queueItemsService.queueItemsGet(null, null, null, any(), null, null, any(), null, null, null, any())
        } returns Mono.just(response)
    }

    private fun mockProcessingOrderService(variables: Variables) {
        val testProcessingOrderId = variables.processingOrderId!!

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(testProcessingOrderId, any(), any(), any())
        } returns
            Mono.just(
                GetProcessingOrderResponse().also { response ->
                    response.processingOrder =
                        ProcessingOrderDto().apply {
                            this.processingOrderId = testProcessingOrderId
                            this.processId = processId
                            this.processingStatusId = ProcessingOrderStatus.NotStarted.processingStatusId
                        }
                },
            )
    }

    private fun mockBusinessEntitiesApi() {
        every {
            businessEntitiesApi.businessEntitiesEntityIdPut(any(), any(), any(), any(), any(), any())
        } returns Mono.just(UpdateEntityResponse())

        every {
            businessEntitiesApi.businessEntitiesProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(GetEntityByEntityIdResponse().entity(EntityDetailDto().entityId(testEntityId)))
    }

    private fun mockFeatureToggleService() {
        every {
            featureToggleService.isAutoRetryEnabled(any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isSsSosNameOrcoEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isSsSosAddressOrcoEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isManualOrcoEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns false
        every {
            ssorcoFeatureToggleService.isGeneralOrcoLedgerNoteEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.rpaBypassExceptionsEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isSSORCORepeatNotificationsEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isCustomHandlerEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isSsOrcoNotificationsFilingEnabled(any(), any(), any())
        } returns true
        every {
            ssorcoFeatureToggleService.isSsOrcoNotificationEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isDocumentValidationEnabled()
        } returns false
        every {
            featureToggleService.isBizFormationPreFilingDsdDocGenEnabled(any())
        } returns false
    }

    private fun mockSalesforceApi() {
        every {
            salesforceApiService.createCase(any())
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(any())
        } returns AddLedgerNoteResponse("test", "test", emptyList())
    }

    private fun mockFulfillmentEventService() {
        val capturedFulfillmentEvents = mutableListOf<FulfillmentEvent>()

        every {
            fulfillmentEventService.send(capture(capturedFulfillmentEvents))
        } returns Unit
    }

    private fun mockDocumentService(success: Boolean) {
        val uploadResponse =
            DocumentResponse()
                .documentId("Test")
                .documentStatus(DocumentResponse.DocumentStatusEnum.ACTIVE)
                .documentVersion("1")
        if (success) {
            every {
                documentService.uploadDocument(any(), any(), any(), any())
            } returns uploadResponse
        } else {
            every {
                documentService.uploadDocument(any(), any(), any(), any())
            } throws Exception("Unexpected System Error. Filing completed but proof was not stored")
        }

        every {
            documentService.findDocumentsBy(
                processingOrderId = any(),
                customerId = any(),
                customerDocumentType = any(),
                documentStatus = any(),
                documentVisibility = any(),
                sortOption = any(),
                sortDirection = any(),
                currentVersionOnly = any(),
                orderId = any(),
                accountId = any(),
            )
        } returns emptyList()
    }

    private fun mockAnswerApi() {
        every {
            answerApi.answersAnswerBankPartialUpdatePut(any(), any(), any(), any())
        } returns Mono.just(SaveQuestionnaireAnswerResponse())
    }

    private fun mockDocumentService() {
        mockDocumentService(true)
    }

    private fun testDocuments(documentType: CustomerDocumentType? = null): List<Document> {
        val documentResponse: File =
            when (documentType) {
                null -> {
                    ClassPathResource("documentBoth.json", javaClass).file
                }

                CustomerDocumentType.findCustomerDocTypeFromProductName(LLC.productName, ProofOfWork.name) -> {
                    ClassPathResource("documentProofOfWork.json", javaClass).file
                }

                else -> {
                    ClassPathResource("documentBoth.json", javaClass).file
                }
            }

        return objectMapper.readValue(documentResponse)
    }

    private fun mockS3Service() {
        val testFile = ClassPathResource("test_file.png", javaClass).file
        val resource =
            ResourceWithType(
                FileSystemResource(testFile),
                IMAGE_PNG,
                "test_file.png",
            )

        every {
            s3Service.getDocument(any())
        } returns resource
    }

    private fun mockOrderService() {
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse
    }

    private fun mockQuestionnaireApi() {
        val json = ClassPathResource("llc_questionnaire.json", javaClass).file
        val response = objectMapper.readValue<GetQuestionnaireInfoResponse>(json)
        every {
            questionnaireApi.questionnaireFieldsQuestionnaireIdGet(any(), any(), any(), any())
        } returns Mono.just(response)
    }

    private fun mockCompletedOrderDetailApi() {
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            Mono.just(
                UpdateCompletedOrderDetailResponse().effectiveDate(
                    OffsetDateTime.of(LocalDate.now(clock), NOON, UTC),
                ),
            )

        val completedOrderDetailResponseJson =
            ClassPathResource("GetCompletedOrderDetailByProcessingOrderId.json", javaClass).file
        val completedOrderDetailResponse =
            objectMapper.readValue<GetCompleteOrderDetailResponse>(completedOrderDetailResponseJson)

        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(completedOrderDetailResponse)
    }

    private fun buildCategory(categoryId: String): Category {
        return objectMapper.readValue<Category>(
            """
            {
                "name": "Prof License Needed",
                "description": "Sig Required for RA",
                "parentId": null,
                "helpText": "Tax Clearance",
                "active": true,
                "id": "$categoryId",
                "createdBy": "tchadalavada",
                "createdDate": "2023-03-16T22:01:07.399052Z",
                "modifiedBy": "<EMAIL>",
                "modifiedDate": "2023-04-22T05:48:51.395501Z"
            }
            """.trimIndent(),
        )
    }

    private fun mockOrcoNotifications() {
        val category: Category = mockk()

        every { category.id } returns UUID.fromString(PROF_LICENSE_NEEDED_ORCO_CAT_ID)

        val reasonMock: Reason = mockk()
        val resolutionMock: Resolution = mockk()
        every { resolutionMock.initialEmailTemplateId } returns "initialemail"
        every { resolutionMock.initialSmsTemplateId } returns "initialsms"
        every { resolutionMock.smsNotificationProblemDetail } returns "snsproblemdetail"
        every { resolutionMock.emailNotificationProblemDetail } returns "emailproblemdetail"
        every { resolutionMock.uploadDocumentType } returns "uploaddoctype"
        every { resolutionMock.type } returns ResolutionType.QUESTION
        every { reasonMock.resolution } returns resolutionMock
        every { reasonMock.getEffectiveResolution() } returns resolutionMock
        every { reasonMock.category } returns buildCategory(PROF_LICENSE_NEEDED_ORCO_CAT_ID)

        val orco =
            Orco(
                SELF_SERVE,
                OPEN,
                mutableListOf(
                    reasonMock,
                ),
                mutableListOf(),
                mutableListOf(),
            )
        orco.createdDate = Instant.now()
        orco.notificationHistories =
            mutableListOf(
                OrcoNotificationHistory(orco, UUID.fromString("10e10e24-69e7-471c-b664-1f86f82ff431")),
            )

        val saveSlot = slot<Orco>()
        every {
            orcoRepository.save(capture(saveSlot))
        } answers {
            saveSlot.captured
        }

        every {
            orcoRepository.findAllByReferencesExternalIdAndReferencesType(
                any(),
                any(),
            )
        }.returns(Optional.of(listOf(orco)))

        every {
            orcoNotificationLogic.getNotificationsToBeSent(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            listOf(
                NotificationToBeSent(
                    "notificationType",
                    TargetChannel.ChannelTypeEnum.EMAIL,
                    UUID.fromString("01547b6f-6368-45df-96c4-c3b02dc25bd6"),
                ),
                NotificationToBeSent(
                    "notificationType",
                    TargetChannel.ChannelTypeEnum.SMS,
                    UUID.fromString("26613a68-ebf7-4486-9046-8a761f457adf"),
                ),
            )

        every {
            orcoNotificationVariables.populateNotificationTemplateVariables(
                customerId = any(),
                category = any(),
                problemDetailOverride = any(),
                processId = any(),
                orderId = any(),
                uploadDocumentType = any(),
            )
        } returns
            mapOf(
                "firstName" to "firstName",
                "product" to "product",
                "orderId" to "orderId",
                "problemDetail" to "problemDetail",
            )

        every {
            orcoNotificationSender.sendNotificationAndGetResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns NotificationResponse()

        every {
            orcoNotificationIntervalService.getCycleValue(any())
        } returns "R5/PT3S"
    }

    private fun mockDocumentRequestApi() {
        every {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(DocumentsGenerationResponse())
    }

    val testProcessId = LLC.processId
    val testStateIssuedId = "20220223184559"
    val testEntityId = 123
    private val orderResponse =
        objectMapper.readValue<GetOrderResponse>(ClassPathResource("ca_llc_order_response.json", javaClass).file)
    val raOrderItem =
        orderResponse.order?.orderItems?.firstOrNull {
            it.processingOrder?.processId == RegisteredAgentService.processId && it.isCancelled == false
        }
    val testQuestionnaireNameWithDesignator: String = "Fetched LLC"

    private fun buildVariables() =
        variables {
            this.customerId = UniqueId.nextIdString()
            this.orderId = UniqueId.nextInt()
            this.processingOrderId = UniqueId.nextInt()
            this.processId = 2
            this.jurisdiction = "CA"
            this.postFilingEnabled = true
            this.entityName = "Random-Name-${UUID.randomUUID()}"
            this.postFilingEnabled = true
            this.accountId = UUID.randomUUID()
        }

    private fun startFilingProcessInstance(variables: Variables): ProcessInstance =
        camundaTestHelpers.runtimeService.startProcessInstanceByKey(
            FILING_PROCESS,
            variables.processingOrderId.toString(),
            variables,
        )

    data class RpaCallback(val payload: Map<String, Any?>) {
        companion object {
            fun success(evidenceTransactionNumber: String) =
                RpaCallback(
                    payload =
                        mapOf<String, Any?>(
                            "status" to "Success",
                            "evidenceTransactionNumber" to evidenceTransactionNumber,
                            "evidenceFilePath" to "s3://uipath-rpa-storage-dev/Jobs_Data/LLC_CA/" +
                                "PoW_60839537-63fb-11ec-b4f8-62ed787118e7.pdf",
                        ),
                )

            fun semiSuccess(evidenceTransactionNumber: String) =
                RpaCallback(
                    payload =
                        mapOf<String, Any?>(
                            "status" to "Success",
                            "evidenceTransactionNumber" to evidenceTransactionNumber,
                            "documentPaths" to
                                listOf(
                                    "s3://uipath-rpa-storage-dev/Jobs_Data/LLC_CA/" +
                                        "PoW_60839537-63fb-11ec-b4f8-62ed787118e7.pdf",
                                ),
                        ),
                )

            fun failure(
                status: String = "SystemError",
                retriable: Boolean = false,
            ) = RpaCallback(
                payload =
                    mapOf<String, Any?>(
                        "status" to status,
                        "message" to "An application error occurred.",
                        "retriable" to retriable,
                    ),
            )

            fun failureRetriable(
                status: String = "SystemError",
                retriable: Boolean = true,
                validationError: Boolean = false,
            ) = RpaCallback(
                payload =
                    mapOf<String, Any?>(
                        "status" to status,
                        "message" to "An application error occurred. - Payment was not submitted. The Order can be retried.",
                        "retriable" to retriable,
                        "validationError" to validationError,
                    ),
            )

            fun nameError() =
                RpaCallback(
                    payload =
                        mapOf<String, Any?>(
                            "status" to "BusinessError",
                            "message" to RpaSosSsorcoService.INVALID_NAME_PREFIX,
                            "evidenceFilePath" to "s3://uipath-rpa-storage-dev/Jobs_Data/DOC_RETRIEVAL_PA/" +
                                "0495689f-bc3d-11ec-a7ca-4e7a83e6458a/Proof_Of_Work/0495689f-bc3d-11ec-a7ca-4e7a83e6458a.pdf",
                            "retriable" to false,
                        ),
                )
        }
    }

    fun correlateRpaMessage(
        callback: RpaCallback,
        businessKey: String,
    ) {
        camundaTestHelpers.correlateMessage(
            messageRef = "Message_RPA",
            businessKey = businessKey,
            processDefinitionKey = FILING_PROCESS,
            variablesToSet = callback.payload,
        )
    }

    private fun docGenCallback(
        processInstance: ProcessInstance,
        status: String = "success",
        documentPaths: List<String> = listOf(),
    ) {
        camundaTestHelpers.correlateMessage(
            "Message_DOCGEN",
            processInstance,
            mapOf(
                "status" to status,
                "documentPaths" to documentPaths,
            ),
        )
    }

    private fun executeJobsUntilPreFilingDocGen(processInstance: ProcessInstance) {
        camundaTestHelpers.executeJobsUntilWaitingForActivity(processInstance, "documents-generated-task")
    }

    private fun executeJobsUntilRPA(processInstance: ProcessInstance) {
        camundaTestHelpers.executeJobsUntilWaitingForActivity(processInstance, "rpa-task")
    }

    @Test
    fun testFixedClock() {
        assertThat(clock.javaClass.simpleName).isEqualTo("FixedClock")
    }

    @Test
    fun testFilingProcessSuccess() {
        val variables = buildVariables()
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)

        correlateRpaMessage(
            callback = RpaCallback.success(testStateIssuedId),
            businessKey = variables.processingOrderId!!.toString(),
        )

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }
    }

    @Test
    fun testFilingProcessAlreadyFiled() {
        val variables = buildVariables()
        mockProcessingOrderService(variables)
        every {
            documentService.findDocumentsBy(
                processingOrderId = variables.processingOrderId!!.toLong(),
                customerId = variables.customerId!!.toLong(),
                customerDocumentType =
                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                        LLC.productName,
                        ProofOfWork.name,
                    ),
                currentVersionOnly = true,
            )
        } returns
            testDocuments(
                CustomerDocumentType.findCustomerDocTypeFromProductName(LLC.productName, ProofOfWork.name),
            )
        every {
            documentService.findDocumentsBy(
                processingOrderId = variables.processingOrderId!!.toLong(),
                customerId = variables.customerId!!.toLong(),
                customerDocumentType = null,
                currentVersionOnly = true,
                accountId = any(),
            )
        } returns
            testDocuments(
                CustomerDocumentType.findCustomerDocTypeFromProductName(LLC.productName, ProofOfWork.name),
            )

        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify(exactly = 0) {
            addQueuesItemService.addQueuesItem(any(), any(), any(), any(), any())
        }

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }
    }

    @Test
    fun testFilingProcessInstantFilingAlreadyFiled() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "CO"
                this.evidenceTransactionNumber = "123"
            }
        mockProcessingOrderService(variables)
        every {
            documentService.findDocumentsBy(
                processingOrderId = variables.processingOrderId!!.toLong(),
                customerId = variables.customerId!!.toLong(),
                customerDocumentType =
                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                        LLC.productName,
                        ArticlesFiled.name,
                    ),
                currentVersionOnly = true,
                accountId = any(),
            )
        } returns testDocuments()

        val processInstance = startFilingProcessInstance(variables)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        // We should verify entity number and effective date when retrying
        assertThat(processInstance).hasPassed(
            "check-filing-status",
            "save-document-upload-fields",
        )

        assertThat(processInstance).variables().containsAllEntriesOf(
            variables {
                effectiveDate = LocalDate.now(clock).toString()
                stateEntityNumber = "123"
            },
        )

        verify(exactly = 0) {
            addQueuesItemService.addQueuesItem(any(), any(), any(), any(), any())
        }

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                DocumentsReceivedFromState.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }
    }

    @Test
    fun testFilingProcessWithRetry() {
        val variables =
            buildVariables().apply {
                this.evidenceTransactionNumber = "123"
            }
        mockProcessingOrderService(variables)
        val businessKey = variables.processingOrderId.toString()

        val processInstance = startFilingProcessInstance(variables)

        // Run RPA with failure and salesforce case
        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.failure(), businessKey)

        // Salesforce case closed with "retry"
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = processInstance,
            disposition = Retry.value,
        )

        // Run RPA again and finish
        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }
    }

    @Test
    fun `RPA is skipped if manual filing already happened in pre-filing`() {
        val variables =
            buildVariables().apply {
                postFilingEnabled = true
                alreadyFiled = true
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        assertThat(processInstance).hasNotPassed("queue-rpa-task")

        verifyProcessingOrderStatus(StateFilingComplete)
        verifyProcessingOrderStatus(RaFilingComplete)
    }

    @Test
    fun `Manual filing isn't started if already filed`() {
        val variables =
            buildVariables().apply {
                postFilingEnabled = true
                alreadyFiled = true
                manualSosFilingRequired = true
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        assertThat(processInstance).hasNotPassed("queue-rpa-task")

        verifyProcessingOrderStatus(StateFilingComplete)
        verifyProcessingOrderStatus(RaFilingComplete)
    }

    @Test
    fun rpaBypassStateCreatesManualFilingCaseWhenExceptionsDisabled() {
        every {
            featureToggleService.rpaBypassExceptionsEnabled(any(), any(), any())
        } returns false

        val variables =
            buildVariables().apply {
                postFilingEnabled = true
                jurisdiction = MAINE.abbreviation
                processId = 1
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "manual-filing-task")
        camundaTestHelpers.completeTask(
            "manual-filing-task",
            processInstance,
            variables {
                disposition = Proceed.value
            },
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        // Make sure fulfillment is telling SalesForce to create a manual filing case.
        verify(exactly = 1) { salesforceApiService.createCase(match(::requestCreatesManualFilingCase)) }

        verifyProcessingOrderStatus(IncStateFilingComplete)
        verifyProcessingOrderStatus(RaFilingComplete)
    }

    @Test
    fun professionalOrderForcesManualFiling() {
        every {
            featureToggleService.rpaBypassExceptionsEnabled(any(), any(), any())
        } returns false

        val variables =
            buildVariables().apply {
                postFilingEnabled = true
                jurisdiction = CALIFORNIA.abbreviation
                processId = LLC.processId
                company = DMNCompany(isProfessional = true)
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "manual-filing-task")
        camundaTestHelpers.completeTask(
            "manual-filing-task",
            processInstance,
            variables {
                disposition = Proceed.value
            },
        )

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        // Make sure fulfillment is telling SalesForce to create a manual filing case.
        verify(exactly = 1) { salesforceApiService.createCase(match(::requestCreatesManualFilingCase)) }
    }

    @Test
    fun testFilingProcessWithValidationErrors() {
        val variables = buildVariables()
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)

        correlateRpaMessage(callback = RpaCallback.failure(), businessKey)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        verify(exactly = 0) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun testUnkownDispositionCanNotMoveWorkflow() {
        val variables = buildVariables()
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.failure(), businessKey)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsUntilTaskByParentProcess(processInstance)

        assertThrows<ParameterArgumentNotValidException> {
            val userTask = task(camundaTestHelpers.getActiveSalesforceProcessInstanceByParent(processInstance))
            workflowService.completeTask(userTask.id, CompleteTaskRequest(disposition = "unknown disposition"))
        }
        assertThat(camundaTestHelpers.getActiveSalesforceProcessInstanceByParent(processInstance))
            .variables()
            .doesNotContainKey("disposition")
    }

    @Test
    fun testInstantFilingProcessSuccess() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "CO"
            }
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        val effectiveDate = LocalDate.now(clock)
        assertThat(processInstance).variables().contains(entry("effectiveDate", effectiveDate.toString()))

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                DocumentsReceivedFromState.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }

        verify {
            businessEntitiesApi.businessEntitiesEntityIdPut(
                testEntityId,
                false,
                "1.0",
                "",
                false,
                UpdateEntityRequest().request(UpdateEntityDto().stateEntityNumber(testStateIssuedId)),
            )
        }
    }

    @Test
    fun testCourierFilingProcessSuccess() {
        every { courierFilingService.sendCourier(any(), any(), any(), any()) } returns Unit
        every { courierFilingService.isCourierFilingState(any()) } returns true
        every { courierFilingService.isCourierFilingStateByVariables(any()) } returns true

        val variables =
            buildVariables().apply {
                this.jurisdiction = State.TENNESSEE.abbreviation
            }
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)

        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }

        verify {
            courierFilingService.sendCourier("TN", any(), any(), variables.processingOrderId.toString())
        }
    }

    @Test
    fun testFaxFilingProcessFail() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "WV"
            }
        mockProcessingOrderService(variables)
        every { emailSenderService.send(any(), any(), any(), any(), any(), any()) } returns Unit
        every {
            s3Service.getDocument(match { it.toString().contains("PreFiling_Articles.pdf") })
        } returns
            ResourceWithType(
                FileSystemResource(File("PreFiling_Articles.pdf")),
                APPLICATION_PDF,
                "PreFiling_Articles.pdf",
            )

        every {
            documentService.findDocumentsBy(
                processingOrderId = variables.processingOrderId!!.toLong(),
                customerId = variables.customerId!!.toLong(),
                customerDocumentType =
                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                        LLC.productName,
                        ArticlesPreFiled.name,
                    ),
                currentVersionOnly = true,
            )
        } returns emptyList()

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).hasPassed("generate-documents-task")
        verify(exactly = 1) {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                any(),
                any(),
                match { it.initiator == "Pre-Filing-State" },
            )
        }
        verify(exactly = 0) {
            documentService.uploadDocument(
                any(),
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    LLC.productName,
                    ArticlesPreFiled.name,
                ),
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }

        verify(exactly = 0) {
            emailSenderService.send("${variables.processingOrderId} - Random-Name", any(), any(), any(), any(), any())
        }
    }

    @Test
    fun testSemiInstantFilingProcessSuccess() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "MS"
            }
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)

        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)
        val effectiveDate = LocalDate.now(clock)
        assertThat(processInstance).variables().contains(entry("effectiveDate", effectiveDate.toString()))

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                DocumentsReceivedFromState.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }

        verify {
            businessEntitiesApi.businessEntitiesEntityIdPut(
                testEntityId,
                false,
                "1.0",
                "",
                false,
                UpdateEntityRequest().request(UpdateEntityDto().stateEntityNumber(testStateIssuedId)),
            )
        }
    }

    @Test
    fun testSemiInstantFilingProcessSemiSuccess() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "MS"
            }
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)

        correlateRpaMessage(callback = RpaCallback.semiSuccess(testStateIssuedId), businessKey)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            businessEntitiesApi.businessEntitiesEntityIdPut(
                testEntityId,
                false,
                "1.0",
                "",
                false,
                UpdateEntityRequest().request(UpdateEntityDto().stateEntityNumber(testStateIssuedId)),
            )
        }
    }

    @Test
    fun testFilingProcessExceedUploadRetries() {
        mockDocumentService(false)

        val variables = buildVariables()
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)

        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).variables().contains(
            entry(
                "validationErrors",
                listOf(
                    ValidationError(
                        "Please escalate to Engineering. Unknown error unrelated to Fulfillment",
                        mapOf(
                            Pair(
                                "errorMessage",
                                "Unexpected System Error. Filing completed but proof was not stored",
                            ),
                        ),
                    ),
                ),
            ),
        )
    }

    @Test
    fun testNonDocFilingOrderDoesNotGenerateDocs() {
        val variables = buildVariables()
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        assertThat(processInstance).hasNotPassed("generate-documents-task")
    }

    @Test
    fun testDocFilingOrderDoesGenerateDocs() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "DE"
            }
        val businessKey = variables.processingOrderId.toString()
        val documentPaths =
            listOf(
                "s3://dds-document-storage-dev/181121/181121_${businessKey}_PreFiling_Articles.pdf",
                "s3://dds-document-storage-dev/181121/181121_${businessKey}_RA_Acceptance.pdf",
            )
        mockProcessingOrderService(variables)
        every {
            s3Service.getDocument(match { it.toString().contains("PreFiling_Articles.pdf") })
        } returns
            ResourceWithType(
                FileSystemResource(File("PreFiling_Articles.pdf")),
                APPLICATION_PDF,
                "PreFiling_Articles.pdf",
            )

        val expectedPreFilingDocsResponse =
            objectMapper.readValue<List<Document>>(ClassPathResource("documentPreFiled.json", javaClass).file)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns expectedPreFilingDocsResponse

        every {
            documentService.downloadDocument(
                expectedPreFilingDocsResponse.first().documentId,
                null,
                withContentType = true,
                any(),
            )
        } returns
            ResourceWithType(
                FileSystemResource(File("PreFiling_Articles.pdf")),
                APPLICATION_PDF,
                "PreFiling_Articles.pdf",
            )

        every {
            documentService.updateDocument(
                expectedPreFilingDocsResponse.first().documentId,
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            DocumentResponse().documentId(expectedPreFilingDocsResponse.first().documentId)
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE")).documentVersion("1")

        every {
            s3Service.getDocument(match { it.toString().contains("RA_Acceptance.pdf") })
        } returns
            ResourceWithType(
                FileSystemResource(File("RA_Acceptance.pdf")),
                APPLICATION_PDF,
                "RA_Acceptance.pdf",
            )
        val expectedRAAcceptanceResponse =
            objectMapper.readValue<List<Document>>(ClassPathResource("documentsRA_Acceptance.json", javaClass).file)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns expectedRAAcceptanceResponse

        every {
            documentService.downloadDocument(
                expectedRAAcceptanceResponse.first().documentId,
                null,
                withContentType = true,
                any(),
            )
        } returns
            ResourceWithType(
                FileSystemResource(File("PreFiling_Articles.pdf")),
                APPLICATION_PDF,
                "PreFiling_Articles.pdf",
            )

        every {
            documentService.updateDocument(
                expectedRAAcceptanceResponse.first().documentId,
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            DocumentResponse().documentId(expectedRAAcceptanceResponse.first().documentId)
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE")).documentVersion("1")

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance, documentPaths = documentPaths)
        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        assertThat(processInstance).hasPassed("generate-documents-task")
        verify(exactly = 1) {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                any(),
                any(),
                match { it.initiator == "Pre-Filing-State" },
            )
        }
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    LLC.productName,
                    ArticlesPreFiled.name,
                ),
                any(),
                any(),
            )
        }
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                LLC_RegisteredAgentAcceptance,
                any(),
                any(),
            )
        }
        assertThat(processInstance).hasPassed("upload-documents-doc-gen-task")
    }

    @Test
    fun testFilingProcessBypassRPA() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "ME"
            }
        val documentPaths =
            listOf(
                "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_PreFiling_Articles.pdf",
            )
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance, documentPaths = documentPaths)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).hasNotPassed("queue-rpa-task")
        assertThat(processInstance).hasPassed("validation-error-task")
    }

    @Test
    fun testFilingProcessBypassRPARetry() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "ME"
            }
        val documentPaths =
            listOf(
                "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_PreFiling_Articles.pdf",
            )
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance, documentPaths = documentPaths)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).hasNotPassed("queue-rpa-task")
        assertThat(processInstance).hasNotPassed("rpa-callback-task")
        assertThat(processInstance).hasPassed("validation-error-task")
    }

    @Test
    fun testFilingProcessBypassRPAProceed() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "ME"
            }
        mockProcessingOrderService(variables)
        val documentPaths =
            listOf(
                "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_PreFiling_Articles.pdf",
            )

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance, documentPaths = documentPaths)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).hasNotPassed("queue-rpa-task")
        assertThat(processInstance).hasNotPassed("rpa-callback-task")
        assertThat(processInstance).hasPassed("validation-error-task")
    }

    @Test
    fun testNoopDocGenCreatesSFCaseImmediately() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "DE"
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).hasNotPassed("Activity_Send_Fax", "queue-rpa-task")
    }

    @Test
    fun testUploadFailureDoesntChangeOrderStatus() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "DE"
            }

        mockDocumentService(false)
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        verify(exactly = 0) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                match {
                    listOf(
                        StateFilingComplete.processingStatusId,
                        DocumentsReceivedFromState.processingStatusId,
                    ).contains(it)
                },
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun testRetryAfterDocUploadFailureQueuesRPA() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "DE"
            }
        val businessKey = variables.processingOrderId.toString()
        mockDocumentService(false)
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(processInstance)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        mockDocumentService(true)

        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(processInstance, Retry.value)

        executeJobsUntilPreFilingDocGen(processInstance)
        docGenCallback(
            processInstance,
            documentPaths =
                listOf(
                    "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_PreFiling_Articles.pdf",
                    "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_RA_Acceptance.pdf",
                ),
        )

        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.success(""), businessKey)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify(exactly = 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                StateFilingComplete.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun testFilingProcessWithInvalidProcessId() {
        every {
            featureToggleService.isCustomHandlerEnabled(any(), any(), any())
        } returns false

        val variables =
            buildVariables().apply {
                this.postFilingEnabled = false
                this.processId = 18
            }
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsUntilWaitingForActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).variables().contains(
            entry(
                "validationErrors",
                listOf(
                    ValidationError(
                        "Please escalate to Engineering. Unknown error unrelated to Fulfillment",
                        mapOf(
                            Pair(
                                "errorMessage",
                                "${ProductType.DBA} DocumentFilingState service has not been implemented in NGF.",
                            ),
                        ),
                    ),
                ),
            ),
        )

        verify(exactly = 0) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun testSolvedSsorcoUpdatesCodEntityName() {
        val variables = buildVariables()
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)
        val orco =
            OrcoResponse(
                type = SELF_SERVE,
                status = OPEN,
                mutableListOf(),
                mutableListOf(),
                null,
                null,
                null,
                null,
                null,
            )

        every {
            orcoService.getOpenOrco(variables.processingOrderId.toString())
        } returns orco

        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.nameError(), businessKey)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsUntilTaskByParentProcess(processInstance)
        assertThat(camundaTestHelpers.getActiveSalesforceProcessInstanceByParent(processInstance))
            .variables()
            .contains(entry("sfCreatedSelfServeOrco", true))
        camundaTestHelpers.completeSalesforceTaskByParentProcess(processInstance, Retry.value)
        executeJobsUntilRPA(processInstance)

        assertThat(processInstance).hasPassed("entity-data-fetch")
        assertThat(processInstance).hasPassed("save-entity-name")
        assertThat(processInstance).variables().contains(entry("entityName", testQuestionnaireNameWithDesignator))

        verify {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun testInstantFilingProcessRetry() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "CO"
            }
        val businessKey = variables.processingOrderId.toString()
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        clearMocks(completedOrderDetailApi)
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } throws Exception("test")

        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = RpaCallback.success(testStateIssuedId), businessKey)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        clearMocks(completedOrderDetailApi)
        mockCompletedOrderDetailApi()

        // saveFieldDelegate should fail. Check hasNotPassed the task after saving effectiveDate/entityNumber
        assertThat(processInstance).hasNotPassed("order-status-update")

        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(processInstance, Retry.value)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        val effectiveDate = LocalDate.now(clock)
        assertThat(processInstance).variables().contains(entry("effectiveDate", effectiveDate.toString()))

        // We should save entity number and effective date when retrying
        assertThat(processInstance).hasPassed("save-document-upload-fields")
    }

    @Test
    fun testFilingProcessRetryWithRpaResolution() {
        val variables =
            buildVariables().apply {
                this.jurisdiction = "MD"
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        // Retry 6 times, which is the current retry policy quantity
        for (i in 1..6) {
            emulateRPA(processInstance)
            fastForwardDelayRetryTimer(processInstance)
            assertThat(processInstance).variables().contains(entry("retryCount", i))
        }

        emulateRPA(processInstance)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = processInstance,
            disposition = Retry.value,
        )
        verifyProcessingOrderStatus()

        emulateRPA(processInstance, RpaCallback.success("123"))

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify(exactly = 6) {
            salesforceApiService.addLedgerNote(match(::requestCreatesRetryLedgerNote))
        }

        verify(exactly = 6) {
            fulfillmentEventService.send(match(::requestCreatesRpaRetryFulfillmentEvent))
        }
    }

    // This is about the bug referenced in NGX-4442
    @Test
    fun testBuggedLoopingOrdersAreFixed() {
        val variables =
            buildVariables().apply {
                jurisdiction = "MD"
                retriable = true
                retryCount = 2
                disposition = Proceed.value
                status = "PROBLEM"
                message = "THERE WAS A PROBLEM"
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verifyProcessingOrderStatus(StateFilingComplete)
    }

    @Test
    fun `route to manual filing when migrated and manualFilingSoS var is passed`() {
        val variables =
            buildVariables().apply {
                postFilingEnabled = true
                jurisdiction = MAINE.abbreviation
                processId = 1
                // From FastForwardService set at start
                manualSosFilingRequired = true
                orderMigrated = true
            }
        mockProcessingOrderService(variables)

        val processInstance = startFilingProcessInstance(variables)

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "manual-filing-task")
        camundaTestHelpers.completeTask(
            "manual-filing-task",
            processInstance,
            variables {
                disposition = Proceed.value
            },
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)
    }

    private fun requestCreatesManualFilingCase(request: SalesforceCaseRequest) =
        request.exceptions.any {
            it.eventPhase == "MANUAL_FILING" &&
                it.eventType == "MANUAL_FILING" &&
                it.type == SalesforceExceptionType.Exception
        }

    private fun requestCreatesRetryLedgerNote(request: AddLedgerNoteRequest) =
        request.takeIf {
            it.objectType == "note" &&
                it.description.contains("RPA is being retried")
        } != null

    private fun requestCreatesRpaRetryFulfillmentEvent(request: FulfillmentEvent) =
        request.takeIf {
            val data = it.data as? Map<*, *>
            it.eventType == EventType.RPA_RETRY &&
                it.eventPhase == FILING &&
                data?.containsKey("retryCount")!! &&
                data.containsKey("remainingRetries")
        } != null

    private fun verifyProcessingOrderStatus(status: ProcessingOrderStatus? = null) {
        verify(exactly = if (status == null) 0 else 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                status?.processingStatusId ?: any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    private fun emulateRPA(
        processInstance: ProcessInstance,
        rpaCallback: RpaCallback = RpaCallback.failureRetriable(),
    ) {
        executeJobsUntilRPA(processInstance)
        correlateRpaMessage(callback = rpaCallback, processInstance.businessKey)
    }

    private fun fastForwardDelayRetryTimer(processInstance: ProcessInstance) {
        camundaTestHelpers.executeJobsUntilWaitingForActivity(processInstance, "delay-retry")
        camundaTestHelpers.executeTimer(processInstance, "delay-retry")
    }

    @Test
    fun testFilingProcessRetryWhen1DayTimerExpiresRouteCaseToRetry() {
        every {
            queueItemsService.queueItemsDeleteById(any(), any(), any())
        } returns Mono.empty()

        val variables =
            buildVariables().apply {
                this.jurisdiction = "MD"
            }
        mockProcessingOrderService(variables)
        val processInstance = startFilingProcessInstance(variables)

        executeJobsUntilRPA(processInstance)
        camundaTestHelpers.executeTimer(processInstance, "filing-one-day-expired-timer")
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "filing-salesforce-activity")

        assertThat(processInstance).hasPassed("filing-one-day-expired-timer")
        assertThat(processInstance).hasPassed("delete-rpa-job-from-queue-task")
        assertThat(processInstance).hasPassed("rpa-callback-task")

        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = processInstance,
            disposition = Retry.value,
        )
        verifyProcessingOrderStatus()

        emulateRPA(processInstance, RpaCallback.success("123"))
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                variables.processingOrderId!!,
                StateFilingComplete.processingStatusId,
                any(),
                variables.customerId.toString(),
                any(),
                any(),
            )

            if (raOrderItem != null) {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    raOrderItem!!.processingOrder!!.processingOrderId!!,
                    RaFilingComplete.processingStatusId,
                    any(),
                    variables.customerId.toString(),
                    any(),
                    any(),
                )
            }
        }
    }
}
