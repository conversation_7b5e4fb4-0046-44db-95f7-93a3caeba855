package com.legalzoom.fulfillment.products.bizformation.process

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.launchdarkly.sdk.LDUser
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.businessentities.BusinessEntitiesApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.dds.DocumentRequestApi
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.model.answer.Company
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.businessentities.EntityDetailDto
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.businessentities.UpdateEntityResponse
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.dsd.printship.PrintShipJobResponseDto
import com.legalzoom.api.model.notificationsplatform.NotificationResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutFulfillmentSystemByProcessingOrderIdResponse
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailResponse
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.model.questionnaire.GetQuestionnaireInfoResponse
import com.legalzoom.api.model.rpa.AddQueueItemRequest
import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.notificationplatform.notificationplatform.NotificationsServiceApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.fulfillment.common.Constants
import com.legalzoom.fulfillment.common.enumeration.RelationshipType
import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.printandship.entity.VendorCode
import com.legalzoom.fulfillment.printandship.repository.VendorCodeRepository
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintConfig
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintOrderStatuses
import com.legalzoom.fulfillment.printandshipapi.enumeration.RequestType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.AlchemyDocumentType
import com.legalzoom.fulfillment.service.data.AlchemyMessage
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.data.ValidationResult
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.AlchemyMessageHandlerService
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FieldUpdaterService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.LegacyEventHandlerService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrderStatusDescription
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.PrintService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedService
import com.legalzoom.fulfillment.service.service.asknicely.AskNicelyService
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.helper.documents.StoragePlatformClient
import com.legalzoom.fulfillment.service.service.questionnaire.AnswerFieldType
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataPayload
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.testing.Await
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.testing.configuration.FixedClockConfiguration
import com.legalzoom.fulfillment.workflow.bpmn.helpers.BaseProcessTest
import com.legalzoom.fulfillment.workflow.service.AddQueuesItemService
import com.legalzoom.fulfillment.workflow.service.AttachedEinService
import com.legalzoom.fulfillment.workflow.service.InstantFilingService
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.SlackMessageService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.startProcessInstanceByKey
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import io.mockk.verifyOrder
import org.assertj.core.api.Assertions
import org.awaitility.kotlin.untilNotNull
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import org.springframework.web.reactive.function.client.toEntity
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.net.URI
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.UUID

@Import(FixedClockConfiguration::class)
@Deployment(
    resources = [
        "bpmn/post-filing.bpmn",
        "bpmn/document-generation.bpmn",
        "bpmn/ein-filing.bpmn",
        "bpmn/california-soi.bpmn",
        "bpmn/hold-area.bpmn",
        "bpmn/print.bpmn",
        "bpmn/salesforce.bpmn",
    ],
)
class PostFilingProcessTest(
    private val alchemyMessageHandlerService: AlchemyMessageHandlerService,
    private val instantFilingService: InstantFilingService,
) : BaseProcessTest() {
    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    @Value(Constants.ALCHEMY_TOPIC)
    private lateinit var alchemyTopic: String

    @MockkBean
    private lateinit var documentRequestApi: DocumentRequestApi

    @MockkBean
    private lateinit var printService: PrintService

    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var ssorcoFeatureToggleService: ORCOFeatureToggleService

    @MockkBean(relaxed = true)
    @Qualifier("webClient")
    private lateinit var webClient: WebClient

    @MockkBean
    private lateinit var ordersApiService: OrdersApiService

    @MockkBean
    private lateinit var documentService: DocumentService

    @MockkBean
    private lateinit var s3Service: S3Service

    @MockkBean
    private lateinit var addQueuesItemService: AddQueuesItemService

    @MockkBean
    private lateinit var salesforceApiService: SalesforceApiService

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    private lateinit var businessEntitiesApi: BusinessEntitiesApi

    @MockkBean
    private lateinit var questionnaireApi: QuestionnaireApi

    @MockkBean
    private lateinit var fulfillmentEventService: FulfillmentEventService

    @MockkBean
    private lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    private lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    private lateinit var customerApi: CustomerApi

    @MockkBean
    private lateinit var notificationEventService: NotificationEventService

    @MockkBean
    private lateinit var fieldUpdaterService: FieldUpdaterService

    @MockkBean
    private lateinit var activityFeedService: ActivityFeedService

    @MockkBean
    private lateinit var storagePlatformClient: StoragePlatformClient

    @MockkBean
    private lateinit var orderCancellationService: OrderCancellationService

    @MockkBean
    private lateinit var slackMessageService: SlackMessageService

    @MockkBean
    private lateinit var printShipJobsApi: PrintShipJobsApi

    @MockkBean
    private lateinit var vendorCodeRepository: VendorCodeRepository

    @MockkBean
    private lateinit var orderContactsService: OrderContactsService

    @MockkBean
    private lateinit var documentListService: DocumentListService

    @MockkBean
    private lateinit var completedOrderDetailService: CompletedOrderDetailService

    @MockkBean
    private lateinit var attachedEinService: AttachedEinService

    @MockkBean
    private lateinit var ldClient: LDClient

    val clock = Clock.fixed(Instant.parse("2025-01-01T00:00:00Z"), ZoneId.systemDefault())

    @MockkBean
    private lateinit var ordersOrderItemsApiService: OrdersOrderItemsApiService

    @MockkBean
    private lateinit var productsApi: ProductsApi

    @MockkBean(relaxUnitFun = true)
    private lateinit var legacyEventHandlerService: LegacyEventHandlerService

    @MockkBean
    private lateinit var notificationsServiceInternalApi: NotificationsServiceApi

    @MockkBean(relaxUnitFun = true)
    private lateinit var askNicelyService: AskNicelyService

    private fun mockSlackMessageCalls() {
        every {
            slackMessageService.sendSlackMessage(any(), any())
        } returns Unit
    }

    companion object {
        const val TEST_EVIDENCE_FILE_PATH =
            "https://document-ingestion-processed-bucket-dev.s3.us-west-2.amazonaws.com/testFile.png?response-content-disposition=inline&" +
                "X-Amz-Security-Token=IQoJb3JpZ2luX2VjEBcaCXVzLXdlc3QtMSJHMEUCIQDkk8%2Fyg1t9tGWamdcO%2BPmTiRTCK%2FGbpOuhoO%2BrA5bhmgI" +
                "gGBSbecqF%2FpSajHK%2F9u749sE3fLD3P85i8sXQoz4EktEq%2BwIIcBADGgw5NDI5MjU4NDgyOTgiDORYdxdpWgZjZFWfbSrYAs44Uds9pUc2S5CGd" +
                "lPYANpHKzoif%2BhhcyMTc%2FjHGWohM%2Bm8N0TiNWEb8rBS%2BCGgv9wnppQwrhtb24zP9UHbIfb%2FsbfEIoSw%2FLY54bd18jmprcOHeyGSb5Vad" +
                "vnFaZVQcskmD4POqAZC650IyVOmjoVeGrCm3PQInVkwO9DGe4mqEAQbKa22orY9gUnSTLdOOikrVcz3KlcYftodaVTubrL%2FTmx5kbnsdazAeQUJa3M" +
                "xco%2FPFLEAlVbCHhU8RWiKL%2Bv%2Fr%2BZonFuy3aXoEvj6gPJKQGiGLaFwfn2UpewAIKxmMqBYv72Bl7zK2AltBpFthEqWsVzy3DoAtzk8eS8hRZE" +
                "1TjUXQkTDYxQ0%2BrZ506z27s1T7et39anu3GLtqPEu0hgme6pIqp8xy0J8SsdLvCGgZetFyWEwytUc4ZlXqlnD9iFtK7%2BdcZJRRTL7ie8cc0b6L0W" +
                "Xgr8pMNO%2B15AGOrMC13ERBSw7pr5tdLfA%2B8VHJIMJ4%2F68rDde64TC%2Bs66W3H0qhTCYpyYQxZM8RcTDMTTZXQ%2FNkDJUv4cOmGME2xXG7Ybp" +
                "MZ%2BNwJy2Uu57Nv9h23m4viXPMj7pfXhMi3eEWisu4C08PP5bKPDBenkRsZIj%2FTAsAn%2FbsNzx9dv%2BfNmthI76Ihrpjb7VEC5E6aAhHNx9z4p2" +
                "fecpH66XHEzVszIhIGHdZSf7H0Z61iom5K8q8AFAiBCFsApfXLq53xpTpiAtJx6606jaRgIearSFQ6sVqmW3JA0JhUZ%2B6xfCAoYoBGD5Hek5zwUeUv" +
                "wEsj7R%2FgFvXJ3swBZ7u5fOmHM%2FKUPshPGsZYjEK1MY0fbcU5QKYpTf3D1lnj5nfGu20B6kR4E19j04mVbGwPIofusUPjcpdDaPA%3D%3D&X-Amz-" +
                "Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220223T083024Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=" +
                "ASIA5XCWCT3VHHZQ6H56%2F20220223%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=9d8df50c15b18f0ead8d7ca822986db1c13c" +
                "708075e46ebb05e6110545d9f5e1"

        const val PRINT_REQUEST_ID = "1234"
        const val TEST_EIN = "33-3333333"
        const val TRACKING_ID = "SOMETRACKINGID12345789"

        @JvmStatic
        fun llcAndInc() = listOf(ProductType.LLC.processId, ProductType.INC.processId)

        @JvmStatic
        fun holdAreEnabledProductsWithProcessingOrder() =
            listOf(
                Arguments.of(ProductType.INC.processId, 511405966),
                Arguments.of(ProductType.LLC.processId, 511205226),
            )
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
        mockFulfillmentEventService()
        mockCustomerService()
        mockSnsService()
        mockGenerateDocuments()
        mockFeatureToggleService()
        mockWebClient()
        mockS3Service()
        mockAddQueueItem()
        mockSalesforceApi()
        mockQuestionnaireApi()
        mockAnswerApi()
        mockBusinessEntitiesApi()
        mockCompletedOrderDetailApi()
        mockProcessingOrdersApi()
        mockFieldUpdaterService()
        mockActivityFeed()
        mockOrderCancellationService()
        mockSlackMessageCalls()
        mockOrderOrderItemsApi()
        mockAttachedEinService()
        mockPrintShipServices()
        mockGetFilingData()
        mockNotificationPlatform()
        mockLDClient()
    }

    private fun mockGetFilingData() {
        val mockAnswer = mockk<FilingDataPayload>()
        every {
            mockAnswer.company
        } returns Company().apply { isProfessional = false }
        every {
            questionnaireAnswerService.getFilingData(any(), any(), any())
        } returns mockAnswer
    }

    private fun mockVariables(
        processIdValue: Int,
        processingOrderIdValue: Int,
        customerIdValue: String,
        accountId: UUID = UUID.randomUUID(),
    ) {
        val execution: DelegateExecution = mockk()
        every {
            execution.input
        } returns
            variables {
                this.processId = processIdValue
                this.processingOrderId = processingOrderIdValue
                this.customerId = customerIdValue
                this.accountId = accountId
            }
    }

    private fun mockNotificationPlatform() {
        every {
            notificationsServiceInternalApi.createNotification1(any(), any())
        } returns
            Flux.just(
                NotificationResponse().also { response ->
                    response.status = NotificationResponse.StatusEnum.ACCEPTED
                },
            )
    }

    fun mockPrintShipServices() {
        mockCompletedOrderDetailService()
        mockDocumentListService()
        mockOrderContactsService()
        mockDSDPrintShipApi()
        mockVendorCodeRepository()
        mockkStatic(LocalDateTime::class)
        every {
            LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        } returns LocalDateTime.parse("2024-01-01T00:00:00")
        mockProductsApi()
    }

    private fun mockLDClient() {
        every {
            ldClient.boolVariation(any(), any<LDUser>(), true)
        } returns true
        every {
            ldClient.boolVariation(any(), any<LDUser>(), false)
        } returns false
    }

    private fun correlatePrintResponse(
        processInstance: ProcessInstance,
        businessKey: String,
        status: PrintOrderStatuses,
    ) {
        Await.await(Duration.of(30, ChronoUnit.SECONDS)).untilAsserted {
            BpmnAwareTests.assertThat(processInstance).isWaitingAt("waiting-print-response")
        }

        Await.await(Duration.of(30, ChronoUnit.SECONDS)).untilAsserted {
            BpmnAwareTests.assertThat(processInstance)
                .isWaitingFor(com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_STATUS_MESSAGE)
        }

        camundaTestHelpers.runtimeService.createMessageCorrelation(com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_STATUS_MESSAGE)
            .processInstanceBusinessKey(businessKey)
            .setVariables(
                variables {
                    printStatus = status.name
                    evidenceTransactionNumber = TRACKING_ID
                    printErrorMessage =
                        if (status == PrintOrderStatuses.SHIPPED) {
                            null
                        } else {
                            "Printing failed, vendor response: "
                        }
                },
            ).correlate()
    }

    private fun mockProductsApi() {
        val json = ClassPathResource("productsApiPostOptionResponse.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)
    }

    fun mockDSDPrintShipApi() {
        every {
            printShipJobsApi.createJobAsync(any())
        } returns
            Mono.just(
                PrintShipJobResponseDto().also { response ->
                    response.id = UUID.randomUUID().toString()
                },
            )
    }

    fun mockVendorCodeRepository() {
        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.PRINT },
            )
        } answers {
            listOf(
                VendorCode(
                    "viatech",
                    "PLZ-LLCPOD",
                    "LLC Printing",
                    RequestType.PRINT,
                    AnswersEntityType.LLC.processId,
                ),
            )
        }

        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.KIT },
            )
        } returns
            listOf(
                VendorCode("viatech", "KLZ-LCTT-CA", "LLC Founders Kit", RequestType.KIT, AnswersEntityType.LLC.processId),
            )
    }

    fun mockOrderContactsService() {
        val json = ClassPathResource("orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns response.contacts!!
    }

    fun mockDocumentListService() {
        every {
            documentListService.getDefaultDocumentListWithConfig(any(), any(), any(), any(), any())
        } returns
            listOf(
                PrintDocumentInfo(
                    documentId = "1234",
                    documentName = "Articles Filed.pdf",
                    printConfig = PrintConfig.LZDOC1,
                    documentType = "Articles Filed",
                ),
            )
    }

    fun mockCompletedOrderDetailService() {
        val json = ClassPathResource("completedOrderDetailResponse.json", javaClass).file
        val response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)

        every {
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(any(), any())
        } returns response
    }

    fun mockOrderCancellationService() {
        every {
            orderCancellationService.cancelOrder(any(), any())
        } returns Unit
    }

    private fun mockOrderOrderItemsApi() {
        val getOrderItemResponse = GetOrderItemResponse().orderId(1234)

        every {
            ordersOrderItemsApiService.getOrdersOrderItems(
                any(),
                any(),
                any(),
                any(),
            )
        } returns getOrderItemResponse
    }

    private fun mockActivityFeed() {
        every {
            activityFeedService.sendEvent(any<ProcessingOrderStatus>(), any())
        } returns null
    }

    private var capturedSaveFieldKey = mutableListOf<String>()
    private var capturedSaveFieldValue = mutableListOf<String>()

    private fun mockFieldUpdaterService() {
        capturedSaveFieldKey.clear()
        capturedSaveFieldValue.clear()

        every {
            fieldUpdaterService.saveFieldValue(
                any(),
                any(),
                capture(capturedSaveFieldKey),
                capture(capturedSaveFieldValue),
                any(),
            )
        } returns Unit
    }

    private fun mockProcessingOrdersApi(testProcessId: Int = ProductType.LLC.processId) {
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdFulfillmentOrderLogPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(PutFulfillmentSystemByProcessingOrderIdResponse())

        val processingOrderResponse =
            GetProcessingOrderResponse().apply {
                processingOrder =
                    ProcessingOrderDto().apply {
                        processingStatusId =
                            ProcessingOrderStatus.fromProcessIdAndDescription(
                                testProcessId,
                                OrderStatusDescription.NotYetStarted,
                            )?.processingStatusId
                        processId = testProcessId
                    }
            }

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(processingOrderResponse)
    }

    private fun mockGenerateDocuments() {
        every {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                any(),
                any(),
                any(),
            )
        }.answers {
            val response = DocumentsGenerationResponse()
            response.isAllDocRequestSubmitted = true
            Mono.just(response)
        }
    }

    private fun mockFeatureToggleService() {
        every {
            featureToggleService.isAutoRetryEnabled(any(), any())
        } returns true

        every {
            ssorcoFeatureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isAutomatedQCEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false

        every {
            featureToggleService.isSSORCORepeatNotificationsEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isPrintNameplateEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isPostFilingQcEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isDSDDocGenEnabled(any(), any(), any())
        } returns false
    }

    private var capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter =
        mutableListOf<CustomerDocumentType>()

    private fun mockDocumentService(
        success: Boolean,
        processingOrderId: Int,
        orderId: Int,
    ) {
        capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter.clear()
        val updateDocumentMetaDataResponse =
            DocumentResponse().documentId("Test")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")

        every {
            storagePlatformClient.updateDocumentMetaData(any(), any(), any())
        } returns updateDocumentMetaDataResponse

        if (success) {
            every {
                documentService.uploadDocument(
                    any(),
                    capture(capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter),
                    any(),
                    any(),
                    any(),
                )
            } returns
                DocumentResponse().documentId("Test")
                    .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                    .documentVersion("1")
        } else {
            every {
                documentService.uploadDocument(
                    any(),
                    capture(capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter),
                    any(),
                    any(),
                    any(),
                )
            } throws Exception("Unexpected System Error. Document was not stored")
        }
        val testFile = ClassPathResource("documentBoth.json", javaClass).file
        val documents = objectMapper.readValue<List<Document>>(testFile)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns documents
        every {
            documentService.findDocumentsBy(
                processingOrderId.toLong(),
                null,
                null,
                orderId = orderId.toLong(),
                accountId = any(),
            )
        } returns documents

        every {
            documentService.updateDocument(any(), DocumentStatus.Inactive, any(), any(), any(), any(), any())
        } returns
            DocumentResponse().documentId("00154c503df1476f873bd054a52a311f")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE")).documentVersion("1")

        every {
            documentService.updateDocument(any(), DocumentStatus.Active, any(), any(), any(), any(), any())
        } returns
            DocumentResponse().documentId("00154c503df1476f873bd054a52a311f")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE")).documentVersion("1")

        every {
            documentService.getEntityName(any(), any(), any())
        } returns "Test entity name"
    }

    private fun mockWebClient() {
        val testFile = ClassPathResource("test_file.png", javaClass).file
        val resource: Resource = FileSystemResource(testFile)
        val responseEntity = ResponseEntity(resource, HttpStatus.OK)
        every {
            webClient.get().uri(any<URI>()).retrieve().toEntity<Resource>().block()
        } returns responseEntity
    }

    private fun mockOrderService(
        processId: Int,
        processingOrderId: Int,
        childEINProcessingOrderId: Int? = null,
        hasEIN: Boolean = true,
        cancelPrinting: Boolean = false,
        orderResponseJsonFile: String? = null,
    ) {
        val orderJsonFileName =
            if (orderResponseJsonFile?.isNotEmpty() == true) {
                orderResponseJsonFile
            } else if (processId == ProductType.INC.processId) {
                "ca_inc_order_without_soi_response.json"
            } else {
                "ca_llc_order_without_soi_response.json"
            }
        val json = ClassPathResource(orderJsonFileName, javaClass).file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(json)

        orderResponse.order!!.orderItems!!.firstOrNull { it.processingOrder != null && it.processingOrder!!.processId == processId }
            .let {
                it!!.processingOrder!!.processingOrderId = processingOrderId
            }

        if (childEINProcessingOrderId != null) {
            orderResponse.order!!.orderItems!!.firstOrNull {
                it.processingOrder != null && it.processingOrder!!.processId == ProductType.EIN.processId
            }
                .let {
                    it!!.processingOrder!!.processingOrderId = childEINProcessingOrderId
                }
        }

        if (!hasEIN) {
            orderResponse.order!!.orderItems!!.firstOrNull {
                it.processingOrder != null && it.processingOrder!!.processId == ProductType.EIN.processId
            }
                ?.let {
                    it.isCancelled = true
                }
        }

        if (cancelPrinting) {
            orderResponse.order!!.orderItems!!.firstOrNull { it.productConfiguration?.productTypeId?.value == RelationshipType.SHIPPING.Id }
                ?.let {
                    it.isCancelled = true
                }
        }

        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse
    }

    private fun mockAttachedEinService(einProcessingOrderId: Int? = null) {
        val json = ClassPathResource("ca_llc_order_without_soi_response.json", javaClass).file
        val documentResponse = objectMapper.readValue<GetOrderResponse>(json)
        val einOrderItem =
            documentResponse.order?.orderItems?.firstOrNull {
                it.processingOrder?.processId == ProductType.EIN.processId
            }

        if (einProcessingOrderId != null) {
            einOrderItem?.processingOrder?.processingOrderId = einProcessingOrderId
        }

        every {
            attachedEinService.getAttachedEinOrderItem(any())
        } returns einOrderItem

        every {
            attachedEinService.startParallelEinProcess(any(), any())
        } returns ExecutionEntity()

        every {
            attachedEinService.havePreExistingEinInstance(any(), any())
        } returns false
    }

    private fun mockS3Service() {
        val testFile = ClassPathResource("test_file.png", javaClass).file

        every {
            s3Service.getDocument(any())
        } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "test_file.png",
            )
        val docGenDocument1 =
            "s3://dds-document-storage-dev/179923/179923_511062511_Financial_Account_Authorization_Letter.pdf"
        val docGenDocument2 = "s3://dds-document-storage-dev/179923/179923_511062511_SOS_Welcome_Letter.pdf"
        val docGenDocument3 = "s3://dds-document-storage-dev/179923/179923_511062511_Welcome_Packet.pdf"
        val docGenDocument4 =
            "s3://dds-document-storage-dev/179923/179923_511062511_Financial_Account_Authorization_Letter.doc"
        every { s3Service.getDocument(URI(docGenDocument1)) } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "179923_511062511_Financial_Account_Authorization_Letter.pdf",
            )
        every { s3Service.getDocument(URI(docGenDocument2)) } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "179923_511062511_SOS_Welcome_Letter.pdf",
            )
        every { s3Service.getDocument(URI(docGenDocument3)) } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "179923_511062511_Welcome_Packet.pdf",
            )
        every { s3Service.getDocument(URI(docGenDocument4)) } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "179923_511062511_Financial_Account_Authorization_Letter.doc",
            )
    }

    private var capturedAddQueueItemRequest = mutableListOf<AddQueueItemRequest>()

    private fun mockAddQueueItem() {
        capturedAddQueueItemRequest.clear()
        val json = ClassPathResource("rpa_response.json", javaClass).file
        val response = objectMapper.readValue<QueueItemDto>(json)
        every {
            addQueuesItemService.addQueuesItem(any(), any(), any(), capture(capturedAddQueueItemRequest), any())
        } returns response
    }

    private var capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
    private var capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
    private var capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

    private fun mockSalesforceApi() {
        capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
        capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
        capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

        every {
            salesforceApiService.updateCase(capture(capturedSalesForceUpdateCaseRequests))
        } returns SalesforceCaseUpdateResponse("test", "test", "test")

        every {
            salesforceApiService.createCase(capture(capturedSalesForceRequests))
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(capture(capturedLedgerNoteRequests))
        } returns AddLedgerNoteResponse("test", "test", emptyList())
    }

    private fun mockQuestionnaireApi() {
        val json = ClassPathResource("llc_questionnaire.json", javaClass).file
        val response = objectMapper.readValue<GetQuestionnaireInfoResponse>(json)
        every {
            questionnaireApi.questionnaireFieldsQuestionnaireIdGet(any(), any(), any(), any())
        } returns Mono.just(response)
    }

    private fun mockQuestionnaireAnswerService(
        processId: Int,
        manualEINFiling: Boolean = false,
    ) {
        val answersApiJson = ClassPathResource("answers_api_questionnaire_field_group_answers.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJson)

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any {
                it.fieldName == com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.ManualFilingEIN.FieldName
            } == false
        ) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297949
            fieldAnswerDto.fieldName = com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.ManualFilingEIN.FieldName
            fieldAnswerDto.fieldValue = if (manualEINFiling) "Yes" else "No"
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                processId,
                answersApiResponse,
            )
    }

    private fun mockQuestionnaireAnswerServiceWithEINValue(processId: Int) {
        val answersApiJson = ClassPathResource("answers_api_questionnaire_field_group_answers.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJson)

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any {
                it.fieldName == com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.EIN.FieldName
            } == false
        ) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297942
            fieldAnswerDto.fieldName =
                AnswerFieldType.fromFieldTypeAndProcessId(
                    com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.EIN, processId,
                ).fieldName
            fieldAnswerDto.fieldValue = "12-12345678"
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                processId,
                answersApiResponse,
            )
    }

    private fun mockAnswerApi() {
        every {
            questionnaireAnswerService.putPartialUpdate(any(), any())
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())
    }

    private fun mockBusinessEntitiesApi() {
        every {
            businessEntitiesApi.businessEntitiesEntityIdPut(any(), any(), any(), any(), any(), any())
        } returns Mono.just(UpdateEntityResponse())

        every {
            businessEntitiesApi.businessEntitiesProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(GetEntityByEntityIdResponse().entity(EntityDetailDto().entityId(123)))
    }

    private var capturedFulfillmentEvents = mutableListOf<FulfillmentEvent>()
    private var capturedAlchemyEvents = mutableListOf<AlchemyMessage>()

    private fun mockFulfillmentEventService() {
        capturedFulfillmentEvents = mutableListOf()
        capturedAlchemyEvents = mutableListOf()

        every {
            fulfillmentEventService.send(capture(capturedFulfillmentEvents))
        } returns Unit
    }

    private fun mockCompletedOrderDetailApi() {
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(UpdateCompletedOrderDetailResponse().entityName("test"))

        val completedOrderDetailResponseJson =
            ClassPathResource("GetCompletedOrderDetailByProcessingOrderId.json", javaClass).file
        val completedOrderDetailResponse =
            objectMapper.readValue<GetCompleteOrderDetailResponse>(completedOrderDetailResponseJson)

        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(completedOrderDetailResponse)
    }

    private fun mockCustomerService() {
        val customerByIdResponseJson =
            ClassPathResource("customer_api_customer_by_customerId_response.json", javaClass).file
        val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        } returns Mono.just(customerByIdResponse)
    }

    private fun mockSnsService() {
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns UUID.randomUUID().toString()
    }

    fun startPostFilingProcessInstance(
        jurisdictions: String,
        processIdValue: Int,
        processingOrderIdValue: Int,
        customerIdValue: Int,
        orderIdValue: Int,
    ): Pair<String, ProcessInstance> {
        val businessKey = processingOrderIdValue.toString()
        val processInstance =
            camundaTestHelpers.runtimeService.startProcessInstanceByKey(EventPhase.POST_FILING.workflowName, businessKey) {
                customerId = customerIdValue.toString()
                orderId = orderIdValue
                processingOrderId = processingOrderIdValue
                processId = processIdValue
                jurisdiction = jurisdictions
                entityName = "Random-Name LLC"
                isPrintingRequired = true
                // causes the SF timers to be skipped.  previously tried moving clock forward but there are still 5 second
                // intervals between jobs such that that approach wasn't as fast as this
                sfDelayTimer = "PT0S"
                accountId = UUID.randomUUID()
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isStarted }

        return Pair(businessKey, processInstance)
    }

    private fun getPastHoldArea(
        processingOrderId: String,
        processInstance: ProcessInstance,
    ) {
        val evidenceFilePathFakeValue =
            "https://document-ingestion-processed-bucket-dev.s3.us-west-2.amazonaws.com/tmr_test_1_14e891f7-8a40-417e-b9a6-6546e64b8352" +
                ".pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIA5XCWCT3VJRW5QJPP%" +
                "2F20230726%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20230726T110623Z&X-Amz-Expires=172800&X-Amz-Security-Token=I" +
                "QoJb3JpZ2luX2VjEKv%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMiJHMEUCIHflqu4d%2B5YLYVLpBB1yGuSpoX6D%2BX0TFtjnjto" +
                "Dg9h6AiEA3TljQwbqSRBpiOSMEwjJazucuhBDPUUxcMYywLOyKLUqsQMIRBAEGgw5NDI5MjU4NDgyOTgiDFYjddzcgsg2HHjIlSqOA6f6OQAXInPhY" +
                "fvgMJh830%2FlKqGrBU%2BA0THbzeMYGNwlVQdNwV6GW%2Bm8vIg2t1KYVD%2BfirsLTUl%2BkKXBP9M7f3kSXwl2fyMwrk6yC0yN2tgqDE9HuU6rO" +
                "bpuhflcDjjm1%2FYbZ7vcP0LmSfDbzyisAcYEmOBBfFPZEbnXznRgWjgjTtVMHcOYyhepFsg4H%2Bv%2FVo9O7wtGmWiAnvumkrj4dp1zEraYr9Ejh" +
                "OMHOQeTz69wzOlzp8jmaabpQmlnfpCwBjxbnGXKCTuvgvgJYsCojkY0wtumrPtgN4w3lg%2Fq1ldKyx7cm2ZIAdYppn3FhJBvk7qI%2BmPowRTr6je" +
                "PNgJsDU1R4p2u20DWhjPo3dMrrpYMcd%2FqMkiGym17WotAsQbnjn%2Fxqs%2BXh%2BZFGFVqwviBr%2FfmQHPi0ET%2BPPf3briBxqyQxLoNoOBJW" +
                "hqPVmYpTHLE3MzGfxSMitlWBlswJkZ7HEJYHJRDRiy6JIU0zHDBOFxx8lCrNKRKoinIKVYqKL4eKnLrlbqi9otVq%2FL5gb%2FdMK38g6YGOp0BHnr" +
                "w7zt7mcTvMTiSFw8xkAktEXnl%2B7sn8hmCCMWPeSfxUKMRtr3Ll%2BsgDnUlGmp%2F6%2Fm77wobTj1hgm3pdbpkWWs8kRwxjM4wKpzXPlLK8N%2F" +
                "Gu%2FmWl%2BsguyxLfDh0LmdBq0Z6aCf0RFqJqDx4HTMXjyRhtYZqxyA8TMF7GoMj8%2BO3qbOqwws6Wf4xSm6wtnjcFq0SMLxCTBMl1MbslA%3D%3" +
                "D&X-Amz-Signature=f091e3d0413cefc0d263a7130177f3b02da61154cb857663c1f54360ca1e7695&X-Amz-SignedHeaders=host&x-id=G" +
                "etObject"

        val alchemyMessage =
            buildAlchemyMessage(processingOrderId, evidenceFilePathFakeValue, AlchemyDocumentType.ARTICLES_FILED, "12345")
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "hold-area-process")
        camundaTestHelpers.executeHoldAreaProcessByParentProcess(
            parentProcessInstance = processInstance,
            alchemyKafkaMessage = alchemyMessage,
        )
    }

    private fun buildAlchemyMessage(
        processingOrderId: String,
        evidenceFilePathFakeValue: String,
        documentTypeValue: AlchemyDocumentType,
        stateIssuedEntityId: String?,
    ) = AlchemyMessage(
        processingOrderId = processingOrderId.toLong(),
        correlationId = UUID.randomUUID().toString(),
        disposition = null,
        evidenceFilePath = evidenceFilePathFakeValue,
        documentType = documentTypeValue.displayName,
        effectiveDate =
            if (documentTypeValue == AlchemyDocumentType.ARTICLES_FILED) {
                OffsetDateTime.of(
                    LocalDate.now(clock),
                    LocalTime.NOON,
                    ZoneOffset.UTC,
                ).toString()
            } else {
                ""
            },
        evidenceTransactionNumber = stateIssuedEntityId,
        jobId = UUID.randomUUID().toString(),
    )

//    private fun awaitSalesforceCaseForActivity(
//        activityId: String,
//        processInstance: ProcessInstance,
//    ) {
//        when (activityId) {
//            in listOf("salesforce-task-shadowcase") -> {
//                Await.await(Duration.of(30, ChronoUnit.SECONDS))
//                    .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt(activityId) }
//                Await.await(Duration.of(30, ChronoUnit.SECONDS))
//                    .untilNotNull {
//                        BpmnAwareTests.task(
//                            activityId,
//                            processInstance,
//                        )
//                    }
//            }
//
//            in
//            listOf(
//                "print-salesforce-activity",
//                "salesforce-task-rejectioncase",
//                "post-salesforce-activity",
//                "doc-gen-salesforce-activity",
//                "salesforce-publications-activity",
//                "salesforce-exception-activity",
//            ),
//            -> {
//                val salesforceProcess = salesforceHelper.getSalesforceProcessInstance(processInstance)
//                Await.await(Duration.of(30, ChronoUnit.SECONDS))
//                    .untilAsserted { BpmnAwareTests.assertThat(salesforceProcess).isWaitingAt("salesforce-task") }
//                Await.await(Duration.of(30, ChronoUnit.SECONDS))
//                    .untilNotNull {
//                        BpmnAwareTests.task(
//                            "salesforce-task",
//                            salesforceProcess,
//                        )
//                    }
//            }
//
//            else -> throw Exception("activityId $activityId not supported ")
//        }
//    }

//    private fun awaitAndResolveSalesforceCase(
//        processInstance: ProcessInstance,
//        activityId: String,
//        caseDisposition: String,
//        variables: Variables? = null,
//    ) {
//        awaitSalesforceCaseForActivity(activityId, processInstance)
//
//        var otherVariables: Variables? = null
//        if (variables != null) {
//            variables.disposition = caseDisposition
//        } else {
//            otherVariables = variables { disposition = caseDisposition }
//        }
//
//        when (activityId) {
//            in listOf("salesforce-task-shadowcase") -> {
//                BpmnAwareTests.complete(
//                    BpmnAwareTests.task(activityId, processInstance),
//                    variables ?: otherVariables,
//                )
//            }
//
//            in
//            listOf(
//                "print-salesforce-activity",
//                "salesforce-task-rejectioncase",
//                "post-salesforce-activity",
//                "doc-gen-salesforce-activity",
//                "salesforce-publications-activity",
//                "salesforce-exception-activity",
//            ),
//            -> {
//                BpmnAwareTests.complete(
//                    BpmnAwareTests.task(
//                        "salesforce-task",
//                        salesforceHelper.getSalesforceProcessInstance(processInstance),
//                    ),
//                    variables ?: otherVariables,
//                )
//            }
//
//            else -> throw Exception("Unsupported activityId $activityId")
//        }
//    }

    private fun getPastSfSubProcess(
        processInstance: ProcessInstance,
        eventType: EventType = EventType.ARTICLES_FILED,
    ) {
        camundaTestHelpers.completeSalesforceTaskByParentProcess(
            parentProcess = processInstance,
            disposition = "proceed",
            eventType = eventType.name,
        )
    }

    private fun getPastPrintAndShip(
        processInstance: ProcessInstance,
        status: PrintOrderStatuses = PrintOrderStatuses.SHIPPED,
    ) {
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "print")
        val printAndShipProcess =
            camundaTestHelpers.getProcessInstanceByParentAndTemplateKey(
                parentProcessInstance = processInstance,
                processDefinitionKey = "print-process",
            )
        camundaTestHelpers.executeJobsIncludingActivity(
            processInstance = printAndShipProcess,
            activityId = "waiting-print-response",
        )
        camundaTestHelpers.correlateMessage(
            processInstance = printAndShipProcess,
            messageRef = "Message_PrintAndShip_Status_Response",
            variablesToSet =
                variables {
                    printStatus = status.name
                    evidenceTransactionNumber = TRACKING_ID
                    printErrorMessage =
                        if (status == PrintOrderStatuses.SHIPPED) {
                            null
                        } else {
                            "Printing failed, vendor response: "
                        }
                },
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(printAndShipProcess)
    }

    fun getPastDocGen(
        processInstance: ProcessInstance,
        status: String = "Success",
        hasValidationError: Boolean = false,
        documentPaths: MutableList<String> =
            mockDocumentPath(
                camundaTestHelpers
                    .runtimeService
                    .getVariable(processInstance.id, "processId") as Int,
            ),
    ) {
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "post-filing-doc-generation")
        camundaTestHelpers.executeDocGenProcessByParentProcess(
            parentProcessInstance = processInstance,
            messageVariables =
                variables {
                    this.status = status
                    this.validationError = hasValidationError
                    this.documentPaths = documentPaths
                },
        )
    }

    private fun correlateDocGenProcess(
        docGenerationProcessInstance: ProcessInstance?,
        status: String,
        documentPaths: MutableList<String>,
        hasValidationError: Boolean,
    ) {
        camundaTestHelpers.runtimeService.createMessageCorrelation("Message_DOCGEN")
            .processInstanceId(docGenerationProcessInstance?.processInstanceId)
            .setVariables(
                variables {
                    this.status = status
                    validationError = hasValidationError
                    this.documentPaths = documentPaths
                },
            ).correlate()
    }

    /**
     * Helper for running post-filing tests. Represents the common case where post filing runs all the way
     * to Documents ReadyForDownload
     */
    private fun executeJobsUntilUpdateDocumentStatus(
        processInstance: ProcessInstance,
        processIdValue: Int,
        processingOrderId: Int,
    ): () -> Unit {
        val mockedDocumentPaths = mockDocumentPath(processIdValue)
        val execution: DelegateExecution = mockk()
        every {
            execution.input
        } returns
            variables {
                this.jurisdiction = camundaTestHelpers.runtimeService.getVariable(processInstance.id, "jurisdiction").toString()
                this.processId = camundaTestHelpers.runtimeService.getVariable(processInstance.id, "processId") as Int
                this.processingOrderId =
                    camundaTestHelpers.runtimeService.getVariable(processInstance.id, "processingOrderId") as Int
                this.customerId = camundaTestHelpers.runtimeService.getVariable(processInstance.id, "customerId") as String
                this.accountId = camundaTestHelpers.runtimeService.getVariable(processInstance.id, "accountId") as UUID
            }
        if (!(instantFilingService.isInstantFilingState(execution))) {
            getPastHoldArea(processingOrderId.toString(), processInstance)
        }

        // pre-print-data-fetch
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "post-filing-doc-generation")
        camundaTestHelpers.executeDocGenProcessByParentProcess(
            processInstance,
            variables {
                this.status = "Success"
                this.validationError = false
                this.documentPaths = mockedDocumentPaths
            },
        )

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "post-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            processInstance,
            disposition = "proceed",
        )

        verify(exactly = 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processIdValue, "Documents Prepared")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }

        return {
            verify {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    processingOrderId,
                    ProcessingOrderStatus.fromProcessIdAndDescription(processIdValue, "Documents Received From SOS")!!.processingStatusId,
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }

            verify {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    processingOrderId,
                    ProcessingOrderStatus.fromProcessIdAndDescription(processIdValue, "Ready for Download")!!.processingStatusId,
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }

            verify(exactly = 1) {
                documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                    any(),
                    any(),
                    any(),
                    match { it.initiator == "Doc-Gen-Api" },
                )
            }
        }
    }

    data class RpaCallback(val payload: Map<String, Any?>) {
        companion object {
            fun success(
                correlationId: String,
                evidenceTransactionNumber: String,
            ) = RpaCallback(
                payload =
                    mapOf<String, Any?>(
                        "status" to "Success",
                        "evidenceTransactionNumber" to evidenceTransactionNumber,
                        "evidenceFilePath" to "s3://uipath-rpa-storage-dev/Jobs_Data/AR_LLC_CA/Pow_${correlationId}_20231023101193.pdf",
                    ),
            )

            fun semiSuccess(evidenceTransactionNumber: String) =
                RpaCallback(
                    payload =
                        mapOf<String, Any?>(
                            "status" to "Success",
                            "evidenceTransactionNumber" to evidenceTransactionNumber,
                            "documentPaths" to
                                listOf(
                                    "s3://uipath-rpa-storage-dev/Jobs_Data/LLC_CA/" +
                                        "PoW_60839537-63fb-11ec-b4f8-62ed787118e7.pdf",
                                ),
                        ),
                )

            fun failure(
                status: String = "SystemError",
                retriable: Boolean = false,
            ) = RpaCallback(
                payload =
                    mapOf<String, Any?>(
                        "status" to status,
                        "message" to "An application error occurred.",
                        "retriable" to retriable,
                    ),
            )
        }
    }

    fun mockDocumentPath(processId: Int): MutableList<String> {
        var documentPaths: MutableList<String> = mutableListOf()
        if (processId == ProductType.LLC.processId) {
            documentPaths =
                mutableListOf(
                    "s3://dds-document-storage-dev/179923/179923_511062511_Financial_Account_Authorization_Letter.pdf",
                    "s3://dds-document-storage-dev/179923/179923_511062511_SOS_Welcome_Letter.pdf",
                    "s3://dds-document-storage-dev/179923/179923_511062511_Welcome_Packet.pdf",
                    "s3://dds-document-storage-dev/179923/179923_511062511_Financial_Account_Authorization_Letter.doc",
                )
        } else if (processId == ProductType.INC.processId) {
            documentPaths =
                mutableListOf(
                    "s3://dds-document-storage-dev/179923/179923_511062511_Certification_Letter.pdf",
                    "s3://dds-document-storage-dev/179923/179923_511062511_SOS_Welcome_Letter.pdf",
                    "s3://dds-document-storage-dev/179923/179923_511062511_Welcome_Packet.pdf",
                    "s3://dds-document-storage-dev/179923/179923_511062511_Filing_Acknowledgement.pdf",
                )
        }
        return documentPaths
    }

    private fun executeCaliforniaSOI(
        postFilingProcess: ProcessInstance,
        variables: Variables,
    ) {
        camundaTestHelpers.executeJobsIncludingActivity(postFilingProcess, "california-soi-process")
        val soiProcess =
            camundaTestHelpers.getProcessInstanceByParentAndTemplateKey(
                postFilingProcess,
                "california-soi-process",
            )
        camundaTestHelpers.executeJobsIncludingActivity(soiProcess, "rpa-bot-process")
        camundaTestHelpers.executeRPABotJobsAndSimulateCallbackByParentProcess(soiProcess, variables)
        camundaTestHelpers.executeJobsUntilProcessCompleted(soiProcess)
        BpmnAwareTests.assertThat(soiProcess)
            .variables()
            .containsAllEntriesOf(variables { "childProcessId" to 80 })
    }

    fun startCaliforniaSOIInstance(): Pair<String, ProcessInstance> {
        val businessKey = UniqueId.nextInt().toString()

        val processInstance =
            camundaTestHelpers.runtimeService.startProcessInstanceByKey(
                EventPhase.INITIAL_REPORT.workflowName,
                businessKey,
            ) {
                customerId = "51746703"
                orderId = 79324335
                processingOrderId = 568291567
                processId = 2
                jurisdiction = "CA"
                entityName = "Random-Name LLC"
                expediteSpeed = "Expedite"
                childProcessId = 80
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isStarted }

        return Pair(businessKey, processInstance)
    }

    @ParameterizedTest
    @MethodSource("holdAreEnabledProductsWithProcessingOrder")
    fun testPostFilingProcess(processId: Int) {
        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = false)
        mockQuestionnaireAnswerService(processId)
        mockDocumentService(success = true, processingOrderId, processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CA",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )
        mockVariables(processId, processingOrderId, orderIdValue.toString())

        val runVerifications =
            this.executeJobsUntilUpdateDocumentStatus(
                processInstance,
                processId,
                processingOrderId,
            )

        getPastPrintAndShip(processInstance)
        runVerifications()

        verify {
            printShipJobsApi.createJobAsync(
                match {
                    it.referenceIds.processingOrderId == processingOrderId.toString()
                },
            )
        }

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Sent to Customer")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        // INC
        "1,511405966,511405982",
        // LLC
        "2,511205226,511205234",
    )
    fun testInstantFilingProcess(
        processId: Int,
        processingOrderId: Int,
        childEINProcessingOrderId: Int,
    ) {
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = true)
        mockQuestionnaireAnswerService(processId)
        mockDocumentService(success = true, processingOrderId, processId)
        mockProcessingOrdersApi(processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CO",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        getPastDocGen(
            processInstance = processInstance,
            documentPaths = mockDocumentPath(processId),
        )

        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "post-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = processInstance,
            disposition = FulfillmentDisposition.Proceed.value,
        )

        getPastPrintAndShip(processInstance)

        // there's a listener after the print-ship process
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Ready for Download")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }

        verify {
            printShipJobsApi.createJobAsync(
                match {
                    it.referenceIds.processingOrderId == processingOrderId.toString()
                },
            )
        }

        verify {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Sent to Customer")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }

        verify(exactly = capturedFulfillmentEvents.count()) {
            fulfillmentEventService.send(any())
        }
    }

    @ParameterizedTest
    @MethodSource("holdAreEnabledProductsWithProcessingOrder")
    fun testFailureInUpdateDocsListenerIsHandledAppropriately(processId: Int) {
        clearAllMocks()
        setup()

        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = false)
        mockQuestionnaireAnswerService(processId)
        mockDocumentService(success = true, processingOrderId, processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CA",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )
        mockVariables(processId, processingOrderId, orderIdValue.toString())

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        }.answers {
            val children = camundaTestHelpers.runtimeService.getActivityInstance(processInstance.id).childActivityInstances
            if (children.any { it.activityId == "update-document-status" }) {
                throw WebClientResponseException(504, "statusText", null, null, null)
            } else {
                val customerByIdResponseJson =
                    ClassPathResource("customer_api_customer_by_customerId_response.json", javaClass).file
                val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)
                Mono.just(customerByIdResponse)
            }
        }

        executeJobsUntilUpdateDocumentStatus(
            processInstance,
            processId,
            processingOrderId,
        )

        // Should throw the exception
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "update-document-status")

        // Go all the way back to salesforce exception and complete the case
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "salesforce-exception-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(processInstance, "proceed")

        Assertions.assertThat(capturedSalesForceRequests.count()).isEqualTo(3)

        val salesForceCaseRequest = capturedSalesForceRequests.last()
        Assertions.assertThat(salesForceCaseRequest.exceptions.count()).isEqualTo(1)
        Assertions.assertThat(salesForceCaseRequest.exceptions.first().eventPhase).isEqualTo("POST_FILING")
        Assertions.assertThat(salesForceCaseRequest.exceptions.first().type)
            .isEqualTo(SalesforceExceptionType.Exception)
        // Assert that the correlation id for each salesforce case is different. ensures it creates new case everytime.
        Assertions.assertThat(capturedSalesForceRequests.first().correlationId)
            .isNotEqualTo(capturedSalesForceRequests.last().correlationId)

        val fulfillmentEvent = capturedFulfillmentEvents.last()
        Assertions.assertThat((fulfillmentEvent.data as ValidationResult).errors!!.first().message)
            .contains("escalate to Engineering")

        // next time we get to update-document-status allow the API calls to succeed
        mockCustomerService()
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "post-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(processInstance, "proceed")
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "pre-print-data-fetch")
    }

    @ParameterizedTest
    @MethodSource("holdAreEnabledProductsWithProcessingOrder")
    fun testPostFilingProcessWithPrintingCancelled(processId: Int) {
        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        val json =
            if (processId == ProductType.INC.processId) {
                "ca_inc_order_response.json"
            } else {
                "ca_llc_order_response.json"
            }

        mockOrderService(
            processId,
            processingOrderId,
            childEINProcessingOrderId,
            hasEIN = false,
            cancelPrinting = true,
            orderResponseJsonFile = json,
        )

        mockQuestionnaireAnswerService(processId)
        mockDocumentService(success = true, processingOrderId, processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CO",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        executeJobsUntilUpdateDocumentStatus(processInstance, processId, processingOrderId)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        BpmnAwareTests.assertThat(processInstance).hasNotPassed("print")

        verify(exactly = 0) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }

        verify(exactly = 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Ready for Download")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            printShipJobsApi.createJobAsync(
                match {
                    it.referenceIds.processingOrderId == processingOrderId.toString()
                },
            )
        }

        verify(exactly = capturedFulfillmentEvents.count()) {
            fulfillmentEventService.send(any())
        }
    }

    @ParameterizedTest
    @MethodSource("holdAreEnabledProductsWithProcessingOrder")
    fun testNonCaliforniaOrderDoesNotTriggerSOIBpmn(processId: Int) {
        // the purpose of this test is to prove that if the order is not for California then we don't spawn the SOI bpmn
        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = false)
        mockQuestionnaireAnswerServiceWithEINValue(processId)
        mockDocumentService(success = true, processingOrderId, processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                // this alone should preclude the SOI from running
                "NJ",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        getPastDocGen(processInstance)

        BpmnAwareTests.assertThat(processInstance).hasNotPassed("california-soi-process")
    }

    @ParameterizedTest
    @MethodSource("holdAreEnabledProductsWithProcessingOrder")
    fun testCaliforniaOrderWithoutSOIChildItemDoesNotTriggerSoiBpmn(processId: Int) {
        // the purpose of this test is to prove that if the order is for California but doesn't have the SOI child
        // order item, we don't visit the CA SOI bpmn

        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = false)
        // this will make us skip ein - not really related to the test but keeps the test simpler
        mockQuestionnaireAnswerServiceWithEINValue(processId)
        mockDocumentService(success = true, processingOrderId, processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CA",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        getPastHoldArea(processingOrderId.toString(), processInstance)

        // prove we get to doc gen (which is after where SOI would have happened)...
        camundaTestHelpers.executeJobsUntilWaitingForActivity(processInstance, "post-filing-doc-generation")

        // ...and prove that we didn't actually go into SOI bpmn
        BpmnAwareTests.assertThat(processInstance).hasNotPassed("california-soi-process")
    }

    @Test
    fun testCAOrderWithSOILineItemRunsSOIBpmn() {
        // the purpose of this test is to prove that a California LLC order with an SOI line item will trigger the
        // child process to obtain the SOI
        val processId: Int = ProductType.LLC.processId
        val processingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(
            processId,
            processingOrderId,
            hasEIN = false,
            orderResponseJsonFile = "orders_api_ca_llc_with_soi.json",
        )

        // this will make us skip ein - not really related to the test but keeps the test simpler
        mockQuestionnaireAnswerServiceWithEINValue(processId)
        mockDocumentService(success = true, processingOrderId, processId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CA",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        getPastHoldArea(processingOrderId.toString(), processInstance)
        executeCaliforniaSOI(processInstance, soiBotSuccessVars("1234"))

        // prove that the name of the bot was correct
        val queueRequest = capturedAddQueueItemRequest.last()
        Assertions.assertThat("AR_LLC_CA").isEqualTo(queueRequest.itemData.name)
        BpmnAwareTests.assertThat(processInstance).hasPassed("california-soi-process")
    }

    private fun awaitThenSimulateSoiRpaResponse(
        processInstance: ProcessInstance,
        variables: Variables,
    ) {
        val rpaProcessInstance =
            Await.await(Duration.of(1, ChronoUnit.MINUTES))
                .untilNotNull {
                    camundaTestHelpers.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(com.legalzoom.fulfillment.domain.Constants.RPA_BOT_PROCESS)
                        .superProcessInstanceId(processInstance.id)
                        .active().list().singleOrNull()
                }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(rpaProcessInstance).isWaitingAt("rpa-task") }

        camundaTestHelpers.runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceBusinessKey(processInstance.businessKey)
            .setVariables(variables)
            .correlate()
    }

    private fun soiBotSuccessVars(evidenceTransactionNumberVal: String? = null) =
        variables {
            status = "Success"
            evidenceTransactionNumber = evidenceTransactionNumberVal
            evidenceFilePath =
                "s3://rpa-storage-prod/Jobs_Data/EIN_FEDERAL/d581f243-0800-11ef-977f-226c103580c3/Proof_Of_Work/123.pdf"
        }

    @Test
    fun testParallelEINProcessIsStarted() {
        val processId = ProductType.LLC.processId

        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = true)
        mockQuestionnaireAnswerService(processId)
        mockDocumentService(success = true, processingOrderId, processId)
        mockAttachedEinService(einProcessingOrderId = childEINProcessingOrderId)

        val (_, processInstance) =
            startPostFilingProcessInstance(
                "CA",
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        getPastHoldArea(processingOrderId.toString(), processInstance)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "parallel-ein-filing-process")

        verify(exactly = 1) {
            attachedEinService.startParallelEinProcess(any(), childEINProcessingOrderId.toString())
        }
    }

    @Test
    fun testPostFilingWithPublicationsEnabled() {
        val processingOrderId: Int = UniqueId.nextInt()
        val childEINProcessingOrderId: Int = UniqueId.nextInt()
        val customerIdValue = UniqueId.nextInt()
        val orderIdValue = UniqueId.nextInt()
        val processId = ProductType.LLC.processId
        val jurisdiction = "KY"

        mockOrderService(processId, processingOrderId, childEINProcessingOrderId, hasEIN = false)
        mockQuestionnaireAnswerService(processId)
        mockDocumentService(success = true, processingOrderId, processId)
        mockVariables(processId, processingOrderId, orderIdValue.toString())
        val execution: DelegateExecution = mockk()
        every {
            execution.input
        } returns
            variables {
                this.jurisdiction = jurisdiction
                this.processId = processId
                this.processingOrderId = processingOrderId
                this.customerId = customerId
            }

        val (_, processInstance) =
            startPostFilingProcessInstance(
                jurisdiction,
                processId,
                processingOrderId,
                customerIdValue,
                orderIdValue,
            )

        getPastDocGen(processInstance)
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "salesforce-publications-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = processInstance,
            disposition = "proceed",
            eventType = EventPhase.PUBLICATION.name,
        )
        camundaTestHelpers.executeJobsIncludingActivity(processInstance, "post-salesforce-activity")
        camundaTestHelpers.executeSalesforceJobsAndCompleteTaskByParentProcess(
            parentProcess = processInstance,
            disposition = "proceed",
            eventType = EventType.ARTICLES_FILED.name,
        )
        getPastPrintAndShip(processInstance)
        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)

        verifyOrder {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Prepared")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Ready for Download")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Sent to Customer")!!.processingStatusId,
                any(),
                any(),
                any(),
                any(),
            )
        }

        verify(exactly = 1) {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                any(),
                any(),
                match { it.initiator == "Doc-Gen-Api" },
            )
        }

        verify {
            printShipJobsApi.createJobAsync(
                match {
                    it.referenceIds.processingOrderId == processingOrderId.toString()
                },
            )
        }
    }
}
