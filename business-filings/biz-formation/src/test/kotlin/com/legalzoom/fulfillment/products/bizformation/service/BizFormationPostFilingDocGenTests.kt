package com.legalzoom.fulfillment.products.bizformation.service

import com.launchdarkly.sdk.LDUser
import com.launchdarkly.sdk.UserAttribute
import com.legalzoom.api.model.answer.AnswerSource
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.products.bizformation.helpers.ProductStateCommerceCombo
import com.legalzoom.fulfillment.products.bizformation.helpers.createBizFormationTestVariablesForProductStateCombo
import com.legalzoom.fulfillment.products.bizformation.mappers.getProcessIdNotNull
import com.legalzoom.fulfillment.service.data.UnifiedCommerceIds
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.workflow.variable.Variables
import io.mockk.every
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono

class BizFormationPostFilingDocGenTests : BaseBizFormationServiceTest() {
    private fun mockDSDPostFilingFlag(
        enabled: Boolean,
        variables: Variables,
    ) {
        every {
            launchDarkly.boolVariation(
                "biz-formation-post-filing-dsd-doc-gen",
                match { user: LDUser ->
                    user.getAttribute(UserAttribute.forName("processId")).intValue() == variables.processId
                },
                false,
            )
        } returns enabled
    }

    @Test
    fun `only generates dsd post filing docs when feature flag is on for LLC`() {
        val variables =
            createBizFormationTestVariablesForProductStateCombo(
                ProductStateCommerceCombo(
                    product = ProductType.LLC,
                    jurisdiction = State.ALASKA,
                    unifiedCommerceIds = UnifiedCommerceIds(cp1OrderId = 1),
                ),
            )

        every {
            answerApi.answersUserOrderIdSourceGet(
                variables.getProcessIdNotNull(),
                AnswerSource.NUMBER_0,
                null,
                null,
                null,
                "1.0",
                variables.customerId,
                true,
            )
        } returns
            Mono.just(
                GetQuestionnaireAnswerResponse().also { response ->
                    response.questionnaireFieldGroupAnswers =
                        QuestionnaireAnswerDto().also { answers ->
                            answers.fieldAnswers =
                                listOf(
                                    FieldAnswerDto().also { fieldAnswer ->
                                        fieldAnswer.fieldName = "LLC_name"
                                        fieldAnswer.fieldValue = "companyName"
                                    },
                                )
                        }
                },
            )

        mockDSDPostFilingFlag(enabled = false, variables = variables)
        Assertions.assertThat(bizFormationService.determinePostFilingDocGen(variables)).isNull()

        mockDSDPostFilingFlag(enabled = true, variables = variables)
        Assertions.assertThat(bizFormationService.determinePostFilingDocGen(variables)).isNotNull()
    }
}
