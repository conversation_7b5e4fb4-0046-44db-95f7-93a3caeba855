package com.legalzoom.fulfillment.printandshipapi.enumeration

enum class PrintConfig(
    val printConfigCode: String,
    val description: String,
) {
    // Standard doc printing
    LZDOC1("LZDOC1", "White paper, no staple, no 3-hole punch"),
    LZDOC2("LZDOC2", "White paper, no staple,  3-hole punch"),
    LZDOC3("LZDOC3", "White paper, staple in upper-left corner, no 3-hole punch"),
    LZDOC4("LZDOC4", "White paper,staple in upper-left corner, 3-hole punch"),
    LZDOC5("LZDOC5", "Ivory paper, no staple, no 3-hole punch"),
    LZDOC6("LZDOC6", "Ivory paper, no staple,  3-hole punch"),
    LZDOC7("LZDOC7", "Ivory paper, staple in upper-left corner, no 3-hole punch"),
    LZDOC8("LZDOC8", "Ivory paper, staple in upper-left corner, 3-hole punch"),
    LZDOC9("LZDOC9", "Royal Sundance paper, no staple, no 3-hole punch"),
    LZDOC10("LZDOC10", "Ivory paper, staple in upper-left corner, no 3-hole punch"),
    LZDOC11("LZDOC11", "Ivory paper,staple in upper-left corner, 3-hole punch"),
    LZDOC12("LZDOC12", "White paper, Duplex-both sided (Book Style) Print"),
    LZDOC13("LZDOC13", "Royal Sundance paper, staple in upper-left corner, no 3-hole punch"),
    LZDOC14("LZDOC14", "White paper, no staple, no 3-hole punch, color"),

    // Pre-printed LZ logo on paper
    LZLETTER1("LZLETTER1", "Letterhead first page, subsequent pages white paper, no staple, no 3-hole punch"),

    // Nameplate printing
    LZCARD1("LZCARD1", "Card Printer - color"),

    // Colored doc printing
    P_LLCPODCLR("P-LLCPODCLR", "colored doc for LLC"),
}
