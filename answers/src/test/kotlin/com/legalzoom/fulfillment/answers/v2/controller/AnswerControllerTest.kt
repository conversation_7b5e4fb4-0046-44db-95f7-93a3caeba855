package com.legalzoom.fulfillment.answers.v2.controller

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.legalzoom.fulfillment.answers.v2.service.AnswerServiceWrapper
import com.legalzoom.fulfillment.answersapi.Constants.CONTEXT_PATH2
import com.legalzoom.fulfillment.answersapi.model.AnswerIdType
import com.legalzoom.fulfillment.answersapi.model.AnswerIdType.ProcessingOrderId
import com.legalzoom.fulfillment.answersapi.model.dtos.SchemaDto
import com.legalzoom.fulfillment.answersapi.model.dtos.v2.AnswerDto
import com.legalzoom.fulfillment.answersapi.model.dtos.v2.AnswerReferenceDto
import com.legalzoom.fulfillment.answersapi.model.dtos.v2.SchemaInfoDto
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.slot
import io.mockk.verify
import io.vavr.control.Either
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.web.reactive.server.WebTestClient
import java.util.UUID

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EmbeddedKafka
class AnswerControllerTest(
    private val webTestClient: WebTestClient,
) {
    @MockkBean
    private lateinit var answerService: AnswerServiceWrapper

    companion object {
        private const val SCHEMA_ID = "18ee9bd0-f0ca-408d-99c7-7b87c51315a6"
        const val ID = "123456"
        const val NAME = "inc-details"
        private const val VERSION_TAG = "1"
        const val VALID_JSON = "{\"data\":\"something\"}"
        const val EXPECTED_SCHEMA =
            "{\"id\":\"18ee9bd0-f0ca-408d-99c7-7b87c51315a6\",\"flow\":\"inc-details\",\"version\":\"1\"," +
                "\"schema\":{\"data\":\"something\"}}"
        val idType = ProcessingOrderId
        val schema: JsonNode = ObjectMapper().readTree(VALID_JSON)
        val expectedSchemaDto = SchemaDto(SCHEMA_ID, NAME, VERSION_TAG, VALID_JSON)
        private val expectedSchemaInfoDto = SchemaInfoDto(NAME, VERSION_TAG)
        private val expectedReferences = listOf(AnswerReferenceDto(ProcessingOrderId, "123456"))
        val expectedAnswerDto = AnswerDto("null", VALID_JSON, null, null, null, expectedSchemaInfoDto, false, expectedReferences)
        val expectedAnswerWithVersionDto =
            AnswerDto("null", VALID_JSON, 1, null, null, expectedSchemaInfoDto, false, expectedReferences)
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
    }

    @Test
    fun testGetSchema() {
        val uriVariables =
            mapOf(
                "schemaName" to NAME,
            )
        every {
            answerService.getSchema(any(), any())
        } returns expectedSchemaDto

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers/schema?schemaName={schemaName}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
            .expectBody(String::class.java).isEqualTo(EXPECTED_SCHEMA)
    }

    @Test
    fun testSaveSchema() {
        val uriVariables =
            mapOf(
                "schemaName" to NAME,
            )

        val requestParams =
            mapOf(
                "file" to schema,
            )

        every {
            answerService.putSchema(any(), any())
        } returns expectedSchemaDto

        every {
            answerService.getSchema(any(), any())
        } returns expectedSchemaDto

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers/schema?schemaName={schemaName}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestParams)
            .exchange()
            .expectStatus().isOk
            .expectBody(String::class.java).isEqualTo(EXPECTED_SCHEMA)
    }

    @Test
    fun `testing save answer with invalid answer id type`() {
        val uriVariables =
            mapOf(
                "idType" to "unknownType",
                "id" to ID,
                "skipValidation" to true,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        val idTypes = slot<List<AnswerIdType>>()

        every {
            answerService.saveAnswer(any(), any(), capture(idTypes), any())
        } returns Either.right(expectedAnswerDto)

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers?idType={idType}&id={id}&skipValidation={skipValidation}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()

        assertEquals(AnswerIdType.Unknown, idTypes.captured.first())
    }

    @Test
    fun testSaveAnswersWithoutValidation() {
        val uriVariables =
            mapOf(
                "idType" to idType,
                "id" to ID,
                "skipValidation" to true,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        every {
            answerService.saveAnswer(any(), any(), any(), any())
        } returns Either.right(expectedAnswerDto)

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers?idType={idType}&id={id}&skipValidation={skipValidation}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    fun testSaveAnswersWithValidation() {
        val uriVariables =
            mapOf(
                "idType" to idType,
                "id" to ID,
                "skipValidation" to false,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        every {
            answerService.saveAnswer(any(), any(), any(), any())
        } returns Either.right(expectedAnswerDto)

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers?idType={idType}&id={id}&skipValidation={skipValidation}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    fun testUpdateAnswers() {
        val uriVariables =
            mapOf(
                "answerId" to UUID.randomUUID(),
                "skipValidation" to false,
                "payload" to VALID_JSON,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        every {
            answerService.updateAnswer(any(), any(), any())
        } returns Either.right(expectedAnswerDto)

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}?skipValidation={skipValidation}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    fun testUpdateAnswersFailure() {
        val uriVariables =
            mapOf(
                "answerId" to ID,
                "skipValidation" to false,
                "payload" to VALID_JSON,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        every {
            answerService.updateAnswer(any(), any(), any())
        } returns Either.right(expectedAnswerDto)

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}?skipValidation={skipValidation}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()
            .expectStatus().isBadRequest
    }

    @Test
    fun `testing add reference with invalid answer id type`() {
        val uriVariables =
            mapOf(
                "answerId" to UUID.randomUUID(),
                "idType" to "unknownType",
                "id" to ID,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        val idType = slot<AnswerIdType>()

        every {
            answerService.addReference(any(), any(), capture(idType))
        } returns expectedAnswerDto

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}/references?idType={idType}&id={id}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()

        assertEquals(AnswerIdType.Unknown, idType.captured)
    }

    @Test
    fun testAddReference() {
        val uriVariables =
            mapOf(
                "answerId" to UUID.randomUUID(),
                "idType" to idType,
                "id" to ID,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        every {
            answerService.addReference(any(), any(), any())
        } returns expectedAnswerDto

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}/references?idType={idType}&id={id}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    fun testAddReferenceFailure() {
        val uriVariables =
            mapOf(
                "answerId" to ID,
                "idType" to idType,
                "id" to ID,
            )
        val requestBody =
            mapOf(
                "payload" to VALID_JSON,
            )
        every {
            answerService.addReference(any(), any(), any())
        } returns expectedAnswerDto

        webTestClient.post()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}/references?idType={idType}&id={id}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .bodyValue(requestBody)
            .exchange()
            .expectStatus().isBadRequest
    }

    @Test
    fun `testing search answer with invalid answer id type`() {
        val uriVariables =
            mapOf(
                "idType" to "unknownType",
                "id" to ID,
                "onlyLatest" to true,
            )
        val idTypes = slot<List<AnswerIdType>>()

        every {
            answerService.searchAnswer(any(), capture(idTypes), any())
        } returns listOf(expectedAnswerDto)

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers?idType={idType}&id={id}&onlyLatest={onlyLatest}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk

        assertEquals(AnswerIdType.Unknown, idTypes.captured.first())
    }

    @Test
    fun testSearchAnswer() {
        val uriVariables =
            mapOf(
                "idType" to idType,
                "id" to ID,
                "onlyLatest" to true,
            )
        every {
            answerService.searchAnswer(any(), any(), any())
        } returns listOf(expectedAnswerDto)

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers?idType={idType}&id={id}&onlyLatest={onlyLatest}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    fun `search answer mapped`() {
        val uriVariables =
            mapOf(
                "idType" to idType,
                "id" to ID,
                "idType2" to AnswerIdType.Flow,
                "id2" to "inc-details",
                "onlyLatest" to true,
                "transformer" to "transformer-test",
            )

        every {
            answerService.searchAnswerTransformed(any(), any(), any(), any())
        } returns VALID_JSON

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers?idType={idType}&id={id}&idType={idType2}&id={id2}&onlyLatest={onlyLatest}&" +
                    "transformer={transformer}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk

        verify(exactly = 1) { answerService.searchAnswerTransformed(any(), any(), any(), any()) }
    }

    @Test
    fun testGetAnswer() {
        val uriVariables =
            mapOf(
                "answerId" to UUID.randomUUID(),
                "onlyLatest" to true,
            )
        every {
            answerService.getAnswer(any(), any(), any())
        } returns listOf(expectedAnswerDto)

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}?onlyLatest={onlyLatest}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    @Disabled("became flaky with no apparent reason")
    fun testGetAnswerWithVersion() {
        val uriVariables =
            mapOf(
                "answerId" to UUID.randomUUID(),
                "onlyLatest" to false,
                "version" to 1,
            )
        every {
            answerService.getAnswer(any(), any(), any())
        } returns listOf(expectedAnswerWithVersionDto)

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}?onlyLatest={onlyLatest}&version={version}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
    }

    @Test
    fun testGetAnswerFailure() {
        val uriVariables =
            mapOf(
                "answerId" to ID,
                "onlyLatest" to true,
            )
        every {
            answerService.getAnswer(any(), any(), any())
        } returns listOf(expectedAnswerDto)

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}?onlyLatest={onlyLatest}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isBadRequest
    }

    @Test
    fun `get answer mapped`() {
        val uriVariables =
            mapOf(
                "answerId" to UUID.randomUUID(),
                "onlyLatest" to true,
                "transformer" to "transformer-test",
            )

        every {
            answerService.getAnswerTransformed(any(), any(), any(), any())
        } returns VALID_JSON

        webTestClient.get()
            .uri(
                "$CONTEXT_PATH2/answers/{answerId}?onlyLatest={onlyLatest}&transformer={transformer}",
                uriVariables,
            )
            .accept(APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk

        verify(exactly = 1) { answerService.getAnswerTransformed(any(), any(), any(), any()) }
    }

    @Test
    fun testPutAnonymizedFields() {
        val uriVariables =
            mapOf(
                "idType" to idType,
                "id" to ID,
            )
        every {
            answerService.putAnonymizedFields(any(), any())
        } returns listOf(expectedAnswerDto)

        webTestClient.put()
            .uri(
                "$CONTEXT_PATH2/answers/anonymize?id={id}&idType={idType}",
                uriVariables,
            )
            .exchange()
            .expectStatus().isOk
            .expectBody()
    }
}
