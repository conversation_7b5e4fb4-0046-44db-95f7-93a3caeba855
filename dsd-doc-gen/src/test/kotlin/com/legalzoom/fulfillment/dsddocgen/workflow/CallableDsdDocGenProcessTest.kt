package com.legalzoom.fulfillment.dsddocgen.workflow

import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.dsd.docgen.DocGenJobsApi
import com.legalzoom.api.model.dsd.docgen.DocGenJobReferenceIdsDto
import com.legalzoom.api.model.dsd.docgen.DocGenJobRequestDto
import com.legalzoom.api.model.dsd.docgen.DocGenJobResponseDto
import com.legalzoom.api.model.dsd.docgen.DocGenJobStorageDto
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.bpmn.helpers.BaseProcessTest
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.camunda.bpm.engine.test.Deployment
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono

@Deployment(resources = ["bpmn/callable-dsd-doc-gen-process.bpmn"])
class CallableDsdDocGenProcessTest : BaseProcessTest() {
    @MockkBean
    private lateinit var docGenJobsApi: DocGenJobsApi

    @MockkBean
    private lateinit var ldClient: LDClient

    @Test
    fun `callable dsd doc loops through sends and waits for requests`() {
        val jobIds = listOf(UniqueId.nextIdString(), UniqueId.nextIdString())
        val processingOrderIdValue = UniqueId.nextIdString()
        val requests =
            listOf(
                DocGenJobRequestDto().apply {
                    accountId = UniqueId.nextUUIDString()
                    referenceIds =
                        DocGenJobReferenceIdsDto().apply {
                            processingOrderId = processingOrderIdValue
                        }
                    storage = DocGenJobStorageDto()
                    templateId = UniqueId.nextIdString()
                },
                DocGenJobRequestDto().apply {
                    accountId = UniqueId.nextUUIDString()
                    referenceIds =
                        DocGenJobReferenceIdsDto().apply {
                            processingOrderId = processingOrderIdValue
                        }
                    storage = DocGenJobStorageDto()
                    templateId = UniqueId.nextIdString()
                },
            )

        every {
            docGenJobsApi.generateDoc(any())
        } returnsMany
            jobIds.map { jobId ->
                Mono.just(
                    DocGenJobResponseDto().also { response ->
                        response.id = jobId
                    },
                )
            }

        every {
            docGenJobsApi.findById(any())
        } returnsMany
            jobIds.map { jobId ->
                Mono.just(
                    DocGenJobResponseDto().also { response ->
                        response.id = jobId
                        response.status = "SUCCESS"
                    },
                )
            }

        val processInstance =
            camundaTestHelpers.startProcessInstanceAndWaitForStart(
                processDefinitionKey = "callable-dsd-doc-gen-process",
                businessKey = processingOrderIdValue,
                variables =
                    mutableMapOf(
                        "requests" to requests,
                    ),
            )

        repeat(jobIds.size) { i ->
            camundaTestHelpers.executeJobsIncludingActivity(processInstance, "callable-dsd-doc-gen-wait-for-completion-receive-task")
            camundaTestHelpers.correlateMessage(
                messageRef = "Message_DSD_DocGenJob_Complete",
                processInstance = processInstance,
                variablesToSet =
                    mapOf(
                        "dsdDocGenJobId" to jobIds[i],
                    ),
            )
            camundaTestHelpers.executeJobsIncludingActivity(processInstance, "callable-dsd-doc-gen-verify-success")
        }

        camundaTestHelpers.executeJobsUntilProcessCompleted(processInstance)
    }
}
