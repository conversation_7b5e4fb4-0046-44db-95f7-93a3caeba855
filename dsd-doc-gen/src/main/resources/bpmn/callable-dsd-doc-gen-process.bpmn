<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1x8d8fd" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.35.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.23.0">
  <bpmn:process id="callable-dsd-doc-gen-process" name="callable-dsd-doc-gen-process" isExecutable="true">
    <bpmn:startEvent id="callable-dsd-doc-gen-start" name="callable-dsd-doc-gen-start" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:outgoing>Flow_0d2by3o</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:subProcess id="callable-dsd-doc-gen-loop" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:incoming>Flow_0d2by3o</bpmn:incoming>
      <bpmn:outgoing>Flow_1f8ek8j</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:collection="requests" camunda:elementVariable="request" />
      <bpmn:startEvent id="callable-dsd-doc-gen-loop-start" name="callable-dsd-doc-gen-loop-start" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:outgoing>Flow_1xubp04</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:receiveTask id="callable-dsd-doc-gen-wait-for-completion-receive-task" name="wait for completion" camunda:asyncBefore="true" camunda:asyncAfter="true" messageRef="Message_3jtfk2v">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="dsdDocGenJobId" value="${dsdDocGenJobId}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0g64v3e</bpmn:incoming>
        <bpmn:outgoing>Flow_0yhaa8l</bpmn:outgoing>
      </bpmn:receiveTask>
      <bpmn:serviceTask id="callable-dsd-doc-gen-verify-success" name="verify success" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${verifyDsdDocGenOutcomeDelegate}">
        <bpmn:incoming>Flow_0yhaa8l</bpmn:incoming>
        <bpmn:outgoing>Flow_15dqqml</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="callable-dsd-doc-gen-queue-job" name="start dsd doc-gen job for this request" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${callableStartDsdDocGenJobDelegate}">
        <bpmn:incoming>Flow_1xubp04</bpmn:incoming>
        <bpmn:outgoing>Flow_0g64v3e</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1xubp04" sourceRef="callable-dsd-doc-gen-loop-start" targetRef="callable-dsd-doc-gen-queue-job" />
      <bpmn:sequenceFlow id="Flow_0g64v3e" sourceRef="callable-dsd-doc-gen-queue-job" targetRef="callable-dsd-doc-gen-wait-for-completion-receive-task" />
      <bpmn:sequenceFlow id="Flow_0yhaa8l" sourceRef="callable-dsd-doc-gen-wait-for-completion-receive-task" targetRef="callable-dsd-doc-gen-verify-success" />
      <bpmn:endEvent id="callable-dsd-doc-gen-loop-end" name="callable-dsd-doc-gen-loop-end" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_15dqqml</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_15dqqml" sourceRef="callable-dsd-doc-gen-verify-success" targetRef="callable-dsd-doc-gen-loop-end" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0d2by3o" sourceRef="callable-dsd-doc-gen-start" targetRef="callable-dsd-doc-gen-loop" />
    <bpmn:endEvent id="callable-dsd-doc-gen-end" name="callable-dsd-doc-gen-end" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:incoming>Flow_1f8ek8j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1f8ek8j" sourceRef="callable-dsd-doc-gen-loop" targetRef="callable-dsd-doc-gen-end" />
    <bpmn:textAnnotation id="TextAnnotation_147o3em">
      <bpmn:text>this is a loop - we do it for each template</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1v4kt49" associationDirection="None" sourceRef="callable-dsd-doc-gen-loop" targetRef="TextAnnotation_147o3em" />
  </bpmn:process>
  <bpmn:message id="Message_3jtfk2v" name="Message_DSD_DocGenJob_Complete" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="callable-dsd-doc-gen-process">
      <bpmndi:BPMNShape id="Event_1fnalw0_di" bpmnElement="callable-dsd-doc-gen-end">
        <dc:Bounds x="962" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="938" y="305" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="callable-dsd-doc-gen-start">
        <dc:Bounds x="158" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="134" y="305" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0tknnmr_di" bpmnElement="callable-dsd-doc-gen-loop" isExpanded="true">
        <dc:Bounds x="263" y="180" width="637" height="203" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ncbd0r_di" bpmnElement="callable-dsd-doc-gen-loop-start">
        <dc:Bounds x="303" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="279" y="305" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1m9phjv_di" bpmnElement="callable-dsd-doc-gen-wait-for-completion-receive-task">
        <dc:Bounds x="523" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10oc1eo_di" bpmnElement="callable-dsd-doc-gen-verify-success">
        <dc:Bounds x="673" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0l2schq_di" bpmnElement="callable-dsd-doc-gen-queue-job">
        <dc:Bounds x="383" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_04ur1h3_di" bpmnElement="callable-dsd-doc-gen-loop-end">
        <dc:Bounds x="814" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="790" y="305" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1xubp04_di" bpmnElement="Flow_1xubp04">
        <di:waypoint x="339" y="280" />
        <di:waypoint x="383" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g64v3e_di" bpmnElement="Flow_0g64v3e">
        <di:waypoint x="483" y="280" />
        <di:waypoint x="523" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yhaa8l_di" bpmnElement="Flow_0yhaa8l">
        <di:waypoint x="623" y="280" />
        <di:waypoint x="673" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15dqqml_di" bpmnElement="Flow_15dqqml">
        <di:waypoint x="773" y="280" />
        <di:waypoint x="814" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_147o3em_di" bpmnElement="TextAnnotation_147o3em">
        <dc:Bounds x="233" y="63" width="170" height="56" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0d2by3o_di" bpmnElement="Flow_0d2by3o">
        <di:waypoint x="194" y="280" />
        <di:waypoint x="263" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1v4kt49_di" bpmnElement="Association_1v4kt49">
        <di:waypoint x="417" y="180" />
        <di:waypoint x="337" y="119" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f8ek8j_di" bpmnElement="Flow_1f8ek8j">
        <di:waypoint x="900" y="280" />
        <di:waypoint x="962" y="280" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
