--liquibase formatted sql

--changeset tchadalavada:create-pause-rule-table
CREATE TABLE pause_rule (
                            id UUID PRIMARY KEY,
                            created_by <PERSON><PERSON><PERSON><PERSON>(255),
                            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                            modified_by <PERSON><PERSON><PERSON><PERSON>(255),
                            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                            description VARCHAR(255),
                            enabled BOOLEAN NOT NULL,
                            filing_phase VARCHAR(255) NOT NULL,
                            is_expedited BOOLEAN,
                            pause_hours INTEGER NOT NULL,
                            product INTEGER,
                            ra_type VARCHAR(255),
                            state VARCHAR(255)
);
--changeset-end
