package com.legalzoom.fulfillment.domain.model

import com.fasterxml.jackson.annotation.JsonProperty
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.Table

@Entity
@Table(name = "PauseRule")
class PauseRule(
    @JsonProperty("product")
    @Column(name = "product")
    var product: Int? = null,
    @JsonProperty("state")
    @Column(name = "state")
    var state: String? = null,
    @JsonProperty("is_expedited")
    @Column(name = "is_expedited")
    var isExpedited: Boolean? = null,
    @JsonProperty("ra_type")
    @Column(name = "ra_type")
    var raType: String? = null,
    @JsonProperty("filing_phase")
    @Column(name = "filing_phase", nullable = false)
    var filingPhase: String,
    @JsonProperty("pause_hours")
    @Column(name = "pause_hours", nullable = false)
    var pauseHours: Int,
    @JsonProperty("enabled")
    @Column(name = "enabled", nullable = false)
    var enabled: Boolean = true,
    @JsonProperty("description")
    @Column(name = "description")
    var description: String? = null,
) : AbstractEntity()
