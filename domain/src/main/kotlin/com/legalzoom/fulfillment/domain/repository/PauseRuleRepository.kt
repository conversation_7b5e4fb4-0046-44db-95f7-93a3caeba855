package com.legalzoom.fulfillment.domain.repository

import com.legalzoom.fulfillment.domain.model.PauseRule
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.util.UUID

interface PauseRuleRepository : JpaRepository<PauseRule, UUID> {
    @Query(
        """
    SELECT p FROM PauseRule p
    WHERE p.enabled = true
      AND p.filingPhase = :filingPhase
      AND (p.product = :product OR p.product IS NULL)
      AND (p.state = :state OR p.state IS NULL)
      AND (p.isExpedited = :isExpedited OR p.isExpedited IS NULL)
      AND (p.raType = :raType OR p.raType IS NULL)
    ORDER BY
      CASE WHEN p.product IS NULL THEN 1 ELSE 0 END,
      CASE WHEN p.state IS NULL THEN 1 ELSE 0 END,
      CASE WHEN p.isExpedited IS NULL THEN 1 ELSE 0 END,
      CASE WHEN p.raType IS NULL THEN 1 ELSE 0 END
    """,
    )
    fun findMatchingRules(
        @Param("product") product: Int?,
        @Param("state") state: String?,
        @Param("isExpedited") isExpedited: Boolean?,
        @Param("raType") raType: String?,
        @Param("filingPhase") filingPhase: String,
    ): List<PauseRule>
}
