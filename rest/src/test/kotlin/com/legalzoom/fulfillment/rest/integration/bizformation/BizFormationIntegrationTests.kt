package com.legalzoom.fulfillment.rest.integration.bizformation

import com.fasterxml.jackson.databind.ObjectMapper
import com.launchdarkly.sdk.LDUser
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.businessentities.BusinessEntitiesApi
import com.legalzoom.api.businessentities.v2.BusinessEntitiesVApi
import com.legalzoom.api.businessprofile.namecheck.NamecheckModuleApi
import com.legalzoom.api.commerce.OrderApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.dds.DocumentRequestApi
import com.legalzoom.api.documentAutomation.DocumentAutomationApi
import com.legalzoom.api.dsd.docgen.DocGenJobsApi
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.dsd.printship.model.PrintJobStatusEventKafkaDto
import com.legalzoom.api.fulfillment.RpaApi
import com.legalzoom.api.model.answer.AnswerSource
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.businessentities.v2.CreateEntityInformationResponse
import com.legalzoom.api.model.commerce.OrderItemDto
import com.legalzoom.api.model.notificationsplatform.NotificationResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailResponse
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.model.storageplatform.SearchResponse
import com.legalzoom.api.notificationplatform.notificationplatform.NotificationsServiceApi
import com.legalzoom.api.order.OrdersApi
import com.legalzoom.api.order.OrdersOrderItemsApi
import com.legalzoom.api.ordercontacts.OrdersContactsApi
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.api.questionnaire.QuestionnaireApi
import com.legalzoom.api.rpa.FoldersApi
import com.legalzoom.api.rpa.QueuesApi
import com.legalzoom.api.storageplatform.DocumentStorageApi
import com.legalzoom.api.workorders.workorders.WorkOrderApi
import com.legalzoom.fulfillment.common.Constants.DSD_PRINT_SHIP_JOB_STATUS_TOPIC
import com.legalzoom.fulfillment.domain.Constants.BIZ_FORMATION_PROCESS
import com.legalzoom.fulfillment.domain.Constants.CALIFORNIA_SOI_PROCESS
import com.legalzoom.fulfillment.domain.Constants.FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.POST_FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.PRE_FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.PRINT_PROCESS
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.rest.BaseIntegrationTest
import com.legalzoom.fulfillment.rest.helpers.UnifiedCommerceHelper.generateCommerceIdsForSystem
import com.legalzoom.fulfillment.salesforce.SalesforceService
import com.legalzoom.fulfillment.service.DocumentIngestionService
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.data.documents.StatusManagementSnsRequest
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.DocumentVisibility
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.AccountService
import com.legalzoom.fulfillment.service.service.DocumentAutomationService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.LegacyEventHandlerApi
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedService
import com.legalzoom.fulfillment.service.service.asknicely.AskNicelyService
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.helper.documents.StoragePlatformClient
import com.legalzoom.fulfillment.testing.Await.await
import com.legalzoom.fulfillment.testing.UniqueId
import com.ninjasquad.springmockk.MockkBean
import com.ninjasquad.springmockk.MockkClear
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifyOrder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.RequestEntity
import org.springframework.http.ResponseEntity
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.security.util.InMemoryResource
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.net.URI
import java.util.Optional
import java.util.UUID

class BizFormationIntegrationTests : BaseIntegrationTest() {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @MockkBean(clear = MockkClear.NONE)
    lateinit var launchDarklyClient: LDClient

    @MockkBean(clear = MockkClear.NONE)
    lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var customerApi: CustomerApi

    // To avoid mocking multiple services, just assume it's possible to get account id by processing order id.
    @MockkBean(clear = MockkClear.NONE)
    lateinit var accountService: AccountService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var answerApi: AnswerApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var nameCheckModuleApi: NamecheckModuleApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var rpaApi: RpaApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var workOrdersApi: WorkOrderApi

    @MockkBean(clear = MockkClear.NONE, name = "documentStorageApiDefault")
    lateinit var documentStorageApi: DocumentStorageApi

    // Mocking one layer above the api client as the storage api client ways that are difficult to mock (e.g. upload)
    @MockkBean(clear = MockkClear.NONE)
    lateinit var storagePlatformClient: StoragePlatformClient

    @MockkBean(clear = MockkClear.NONE)
    lateinit var uiPathFoldersApi: FoldersApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var uiPathQueuesApi: QueuesApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var s3Service: S3Service

    @MockkBean(clear = MockkClear.NONE)
    lateinit var docGenJobsApi: DocGenJobsApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var legacyEventHandlerApi: LegacyEventHandlerApi

    @MockkBean(clear = MockkClear.NONE, name = "notificationsServiceInternalApi")
    lateinit var notificationsServiceInternalApi: NotificationsServiceApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var notificationEventService: NotificationEventService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var salesforceService: SalesforceService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var questionnaireApi: QuestionnaireApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var businessEntitiesApi: BusinessEntitiesApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var businessEntitiesV2Api: BusinessEntitiesVApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var documentAutomationApi: DocumentAutomationApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var ordersApi: OrdersApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var ordersOrderItemsApi: OrdersOrderItemsApi

    /**
     * Only used to "packSuiteCase" and failures are ignored, so just making a relaxed mock and not bothering further.
     */
    @MockkBean(clear = MockkClear.NONE, relaxUnitFun = true)
    lateinit var documentAutomationService: DocumentAutomationService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var documentRequestApi: DocumentRequestApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var cp2OrderApi: OrderApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var documentIngestionService: DocumentIngestionService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var fulfillmentEventService: FulfillmentEventService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var activityFeedService: ActivityFeedService

    @MockkBean(clear = MockkClear.NONE, relaxUnitFun = true)
    lateinit var askNicelyService: AskNicelyService

    @MockkBean(clear = MockkClear.NONE)
    lateinit var orderContactsApi: OrdersContactsApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var productsApi: ProductsApi

    @MockkBean(clear = MockkClear.NONE)
    lateinit var printShipJobsApi: PrintShipJobsApi

    @Value(DSD_PRINT_SHIP_JOB_STATUS_TOPIC)
    lateinit var dsdPrintShipKafkaTopic: String

    @Autowired
    lateinit var dsdPrintShipKafkaTemplate: KafkaTemplate<String, PrintJobStatusEventKafkaDto>

    private fun mockCP1SpecificCalls(testCase: BizFormationIntegrationTestCase) {
        // TODO: This is called from OrderStatusService & OrderStatusDelegate which already has a processing order id from the variables, why does this need to happen?
        every {
            ordersApi.coreOrdersOrderIdGet(
                testCase.commerceIds.cp1OrderId!!,
                null,
                null,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.cp1GetOrdersResponse)

        every {
            ordersOrderItemsApi.coreOrdersOrderItemsProcessingOrdersProcessingOrderIdGet(
                testCase.processingOrderId,
                any(),
                any(),
                any(),
            )
        } returns
            Mono.just(
                GetOrderItemResponse().also { response ->
                    response.orderId = testCase.commerceIds.cp1OrderId
                    response.orderItem =
                        com.legalzoom.api.model.order.OrderItemDto().also { orderItem ->
                            orderItem.isCancelled = false
                        }
                },
            )

        every {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                testCase.customerId.toString(),
                any(),
                match { request -> request.processingOrderId == testCase.processingOrderId },
            )
        } returns Mono.just(testCase.ddsDocGenResponse)

        every {
            s3Service.getDocument(URI(testCase.successfulDDSDocGenDocument))
        } returns
            ResourceWithType(
                filename = "dds-doc-gen-filename.pdf",
                contentType = MediaType.APPLICATION_PDF,
                resource = InMemoryResource("some dds data"),
            )
    }

    private fun mockCP2SpecificCalls(testCase: BizFormationIntegrationTestCase) {
        every {
            cp2OrderApi.getOrderItemById(
                match { it.toString() == testCase.commerceIds.cp2OrderItemId },
                any(),
            )
        } returns
            Mono.just(
                OrderItemDto().also {
                    it.orderId = testCase.orderId
                },
            )
    }

    private fun mockFilingCalls(testCase: BizFormationIntegrationTestCase) {
        if (testCase.filingMethod == BizFormationTestFilingMethod.MANUAL) {
            every {
                salesforceService.createCase(
                    match { request ->
                        // TODO: it.orderNumber == testCase.orderId &&
                        request.customerId == testCase.customerId.toString() &&
                            request.processId == testCase.product.processId.toString() &&
                            request.processingNumber == testCase.processingOrderId.toString() &&
                            request.exceptions.any { exception ->
                                exception.eventType == "MANUAL_FILING" &&
                                    exception.eventPhase == "MANUAL_FILING"
                            }
                    },
                )
            } returns testCase.salesforceManualFileCaseCreatedResponse
        } else {
            every {
                uiPathFoldersApi.foldersGet(
                    null,
                    "FullyQualifiedName eq '${testCase.uiPathFolderName}'",
                    null,
                    null,
                    null,
                    null,
                    null,
                )
            } returns Mono.just(testCase.uiPathFoldersResponse)
            every {
                uiPathQueuesApi.queuesAddQueueItem(
                    null,
                    null,
                    BizFormationIntegrationTestCase.UI_PATH_FOLDER_ID,
                    any(),
                )
            } returns Mono.just(testCase.rpaQueueItemResponse)

            // All documents uploaded by RPA bot should be downloadable from S3
            (testCase.rpaProofOfWorkDocuments + testCase.successRPAEvidenceFilePath).forEach {
                every {
                    s3Service.getDocument(URI(it))
                } returns
                    ResourceWithType(
                        filename = "filename.pdf",
                        contentType = MediaType.APPLICATION_PDF,
                        resource = InMemoryResource("some data"),
                    )
            }

            // No SoSFilingException documents in storage
            every {
                storagePlatformClient.findAllDocumentsBy(
                    processingOrderId = testCase.processingOrderId.toLong(),
                    customerId = testCase.customerId.toLong(),
                    customerDocumentType = match { it.docTemplateId == testCase.sosFilingExceptionDocTemplateId },
                    documentStatus = DocumentStatus.Active,
                    documentVisibility = DocumentVisibility.All,
                    accountId = testCase.accountId.toString(),
                )
            } returns SearchResponse().also { it.payload = emptyList() }
        }

        if (testCase.waitsForMailFromSOS) {
            // Allow for creating a shadow holding area salesforce case while also waiting for Alchemy response
            every {
                salesforceService.createCase(
                    match { request ->
                        // TODO: it.orderNumber == testCase.orderId &&
                        request.customerId == testCase.customerId.toString() &&
                            request.processId == testCase.product.processId.toString() &&
                            request.processingNumber == testCase.processingOrderId.toString() &&
                            request.exceptions.any { exception ->
                                exception.eventType == "SOS_WAITING" &&
                                    exception.eventPhase == "POST_FILING"
                            }
                    },
                )
            } returns testCase.salesforceDelayedFileCaseCreatedResponse

            every {
                salesforceService.updateCase(
                    match { request -> request.caseId == testCase.salesforceDelayedFileCaseId },
                )
            } returns testCase.salesforceDelayedFileCaseClosedResponse

            // TODO: Need to handle CP2 orders with hold area callbacks from alchemy

            every {
                salesforceService.updateCase(
                    match { request -> request.caseId == testCase.salesforceDelayedFileCaseId },
                )
            } returns testCase.salesforceDelayedFileCaseClosedResponse

            // download articles filed document from alchemy
            every {
                documentIngestionService.getDocumentFromSharedLinkUrl(testCase.successfulAlchemyArticlesFiled)
            } returns ResponseEntity.of(Optional.of(InMemoryResource("alchemy articles filed daya")))

            // update storage platform data for articles filed from alchemy
            every {
                storagePlatformClient.updateDocumentMetaData(
                    documentId = any(),
                    documentStatus = DocumentStatus.Active,
                    referenceData = match { it["jobId"] == testCase.successfulAlchemyCallback.jobId },
                )
            } returns null
        }

        if (testCase.shouldExecuteCaliforniaSOI) {
            every {
                s3Service.getDocument(URI(testCase.successfulSOIEvidenceFilPath))
            } returns
                ResourceWithType(
                    filename = "ca-soi.pdf",
                    contentType = MediaType.APPLICATION_PDF,
                    resource = InMemoryResource("some soi data"),
                )
        }
    }

    private fun mockCommonCalls(testCase: BizFormationIntegrationTestCase) {
        every {
            fulfillmentEventService.send(any())
        } answers {
            logger.info("Publishing fulfillment event {}", it.invocation.args.first())
        }
        /**
         * Mocks that are the same for all concurrent tests.
         */
        every {
            launchDarklyClient.boolVariation(any(), any<LDUser>(), false)
        } returns false
        every {
            launchDarklyClient.boolVariation(any(), any<LDUser>(), true)
        } returns true
        every {
            storagePlatformClient.uploadDocument(
                documentAsTemporaryFile = any(),
                ownerType = any(),
                customerDocumentType = any(),
                documentStatus = any(),
                orderContext = any(),
                documentPath = any(),
                availableForImmediateCustomerDownload = any(),
            )
        } returns
            DocumentResponse().also { response ->
                response.documentId = UUID.randomUUID().toString()
                response.documentStatus = DocumentResponse.DocumentStatusEnum.ACTIVE
            }
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns UniqueId.nextUUIDString()
        every {
            customerApi.customersCustomerIdGet(
                testCase.customerId,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.customerDetails)
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                testCase.processingOrderId,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.processingOrderDetails)
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                testCase.processingOrderId,
                any(),
                "1",
                testCase.customerId.toString(),
                true,
                any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())
        if (testCase.includeAttachedRegisteredAgent) {
            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    testCase.attachedRegisteredAgentProcessingOrderId,
                    any(),
                    "1",
                    testCase.customerId.toString(),
                    true,
                    any(),
                )
            } returns Mono.just(PutProcessingOrderResponse())
        }
        if (testCase.includeAttachedInitialReports) {
            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    testCase.attachedInitialReportsProcessingOrderId,
                    any(),
                    "1",
                    testCase.customerId.toString(),
                    true,
                    any(),
                )
            } returns Mono.just(PutProcessingOrderResponse())
        }
        if (testCase.includeAttachedEIN) {
            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                    testCase.attachedEINProcessingOrderId,
                    any(),
                    "1",
                    testCase.customerId.toString(),
                    true,
                    any(),
                )
            } returns Mono.just(PutProcessingOrderResponse())
        }
        every {
            accountService.getAccountIdFromCustomerId(
                testCase.customerId.toString(),
                testCase.processingOrderId.toString(),
                testCase.product.processId,
            )
        } returns testCase.accountId
        every {
            answerApi.answersProcessingOrdersProcessingOrderIdProcessesProcessIdFilingDataGet(
                testCase.processingOrderId,
                testCase.product.processId,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.answersFilingDataResponse)
        every {
            answerApi.answersProcessingOrdersProcessingOrderIdProcessesProcessIdEntityDataGet(
                testCase.processingOrderId,
                testCase.product.processId,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.answersEntityDataResponse)
        every {
            nameCheckModuleApi.findCompanyNameAvailability(
                any(),
                testCase.namecheckEntityType,
                testCase.jurisdiction.abbreviation,
                any(),
                any(),
                "exact",
            )
        } returns Mono.just(testCase.nameCheckAvailabilityResponse)
        // fetching articles fled
        every {
            storagePlatformClient.findAllDocumentsBy(
                processingOrderId = testCase.processingOrderId.toLong(),
                customerId = testCase.customerId.toLong(),
                // Articles Field
                customerDocumentType = match { it.docTemplateId == testCase.articlesFiledDocTemplateId },
                documentStatus = DocumentStatus.Active,
                documentVisibility = DocumentVisibility.All,
                accountId = testCase.accountId.toString(),
            )
        } returns SearchResponse().also { it.payload = emptyList() }
        // TODO: fetching everything during post filing to update documents available, need to return documents
        // the docs are determined by CustomerDocumentType.csv
        every {
            storagePlatformClient.findAllDocumentsBy(
                testCase.processingOrderId.toLong(),
                testCase.customerId.toLong(),
                null,
                DocumentStatus.Active,
                DocumentVisibility.All,
                null,
                testCase.accountId.toString(),
            )
        } returns testCase.articlesFiledStorageSearchResponse
        every {
            storagePlatformClient.updateDocument(any(), any(), testCase.accountId.toString())
        } returns
            DocumentResponse().also { response ->
                response.documentId = "documentid" // TODO: return document id from request
                response.documentStatus = DocumentResponse.DocumentStatusEnum.ACTIVE
            }
        every {
            documentStorageApi.getDocumentsWithHttpInfo(
                any(),
                any(),
                testCase.accountId.toString(),
                "documentSubtype=${testCase.articlesFiledSubDocType}&documentType=Fulfillment&" +
                    "processingOrderId=${testCase.processingOrderId}&documentStatus=Active OR " +
                    "Inactive&ownerId=${testCase.customerId}&productType=${testCase.product.name}",
            )
        } returnsMany
            listOf(
                // first call returns no articles filed
                Mono.just(ResponseEntity.of(Optional.of(SearchResponse().also { it.payload = emptyList() }))),
            )

        every {
            notificationsServiceInternalApi.createNotification1(any(), null)
        } returns
            Flux.just(
                NotificationResponse().also { response ->
                    response.status = NotificationResponse.StatusEnum.ACCEPTED
                },
            )
        every {
            legacyEventHandlerApi.postToEventHandler(
                processingStatusId = any(),
                processingOrderId = testCase.processingOrderId.toString(),
            )
        } just runs
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                testCase.processingOrderId,
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(UpdateCompletedOrderDetailResponse())
        every {
            questionnaireApi.questionnaireFieldsQuestionnaireIdGet(
                testCase.product.questionnaireId,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.questionnaireInfoResponse)
        every {
            // TODO: Assert the contents of this request
            answerApi.answersAnswerBankPartialUpdatePut(
                any(),
                any(),
                any(),
                match { it.questionnaireFieldGroupAnswers.processingOrderId == testCase.processingOrderId },
            )
        } returns Mono.just(SaveQuestionnaireAnswerResponse())
        every {
            answerApi.answersUserOrderIdSourceGet(
                testCase.processingOrderId,
                AnswerSource.NUMBER_0,
                null,
                null,
                null,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.questionnaireAnswersResponse)
        every {
            salesforceService.addLedgerNote(match { it.processingNumber == testCase.processingOrderId.toString() })
        } returns testCase.salesforceLedgerNoteResponse
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                testCase.processingOrderId,
                any(),
                any(),
                any(),
            )
        } returns Mono.just(testCase.completedOrderDetailResponse)

        every {
            businessEntitiesApi.businessEntitiesProcessingOrdersProcessingOrderIdGet(
                testCase.processingOrderId,
                any(),
                any(),
                any(),
            )
        } returnsMany
            listOf(
                // First request fails with 404 to trigger code to create a new business entity
                Mono.error(
                    WebClientResponseException.create(
                        HttpStatus.NOT_FOUND.value(),
                        HttpStatus.NOT_FOUND.reasonPhrase,
                        HttpHeaders.EMPTY,
                        "Simulating not found business entity".toByteArray(),
                        null,
                    ),
                ),
                Mono.just(testCase.getBusinessEntityResponse),
            )
        every {
            businessEntitiesV2Api.businessEntitiesPost(
                any(),
                any(),
                any(),
                match { request ->
                    request.entity!!.processingOrderId == testCase.processingOrderId
                },
            )
        } returns Mono.just(CreateEntityInformationResponse())
        every {
            businessEntitiesApi.businessEntitiesEntityIdPut(
                testCase.businessEntityId,
                false,
                any(),
                any(),
                any(),
                match { it.request!!.stateEntityNumber == testCase.stateEntityNumber },
            )
        } returns Mono.just(testCase.updateBusinessEntityResponse)
        every {
            fulfillmentEventService.send(any())
        } answers {
            logger.info("Publishing fulfillment event {}", it.invocation.args.first())
        }
        every {
            activityFeedService.sendEvent(
                orderStatus = any(),
                activityFeedVariables = any(),
            )
        } returns SendResult(null, null)

        if (testCase.includeAttachedPrintAndShipFoundersKit) {
            if (testCase.commerceIds.isForSystem(CommerceSystem.CP1)) {
                every {
                    orderContactsApi.coreOrdersOrderIdContactsGet(
                        testCase.commerceIds.cp1OrderId,
                        any(),
                        testCase.customerId.toString(),
                        any(),
                    )
                } returns Mono.just(testCase.cp1OrderContactResponse)
                every {
                    productsApi.coreProductsProductIdPostOptionGet(
                        testCase.cp1ProductConfigurationId,
                        any(),
                        testCase.customerId.toString(),
                        any(),
                    )
                } returns Mono.just(testCase.cp1ProductsPostOptionResponse)
            } else {
                TODO("Get shipping information from cp2")
            }
            every {
                printShipJobsApi.createJobAsync(
                    match {
                        it.accountId == testCase.accountId.toString()
                    },
                )
            } returns Mono.just(testCase.dsdPrintShipResponse)
        }
    }

    @ParameterizedTest(name = "happy path {index} {argumentsWithNames}")
    @MethodSource("happyPathTestCases")
    @Execution(ExecutionMode.CONCURRENT)
    fun `biz formation happy path works`(testCase: BizFormationIntegrationTestCase) {
        logger.info("Test case: {}", testCase)

        /**
         * Mocks that change based on which commerce system placed the order.
         */
        if (testCase.commerceIds.isForSystem(CommerceSystem.CP1)) {
            mockCP1SpecificCalls(testCase)
        } else {
            mockCP2SpecificCalls(testCase)
        }

        /**
         * Mocks related to instant, delayed, and manual filing scenarios.
         */
        mockFilingCalls(testCase)

        /**
         * Mocks that are the same regardless of commerce system.
         */
        mockCommonCalls(testCase)

        /**
         * Start the biz-formation process.
         */
        val request =
            RequestEntity
                .post("$localBaseUrl/fulfillment/v1/filings/formation")
                .body(testCase.formationOrderRequest)
        val response = testRestTemplate.exchange(request, Any::class.java)
        assertThat(response.statusCode).isEqualTo(HttpStatus.OK)

        /**
         * Start & finish pre-filing process.
         */
        camundaTestHelpers.executeJobsIncludingActivity(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = BIZ_FORMATION_PROCESS,
            // Pre-filing sub-process activity
            activityId = "Activity_08ccv1q",
        )
        camundaTestHelpers.executeJobsUntilProcessCompleted(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = PRE_FILING_PROCESS,
        )

        /**
         * Start filing process
         */
        camundaTestHelpers.executeJobsIncludingActivity(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = BIZ_FORMATION_PROCESS,
            // Filing sub-process activity
            activityId = "Activity_1ct87nd",
        )

        /**
         * Scenarios that require pre-filing documents wait on doc gen before filing.
         */
        if (testCase.requiresPreFilingDocuments) {
            camundaTestHelpers.executeJobsUntilWaitingForActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = FILING_PROCESS,
                activityId = "documents-generated-task",
            )
            camundaTestHelpers.correlateMessage(
                messageRef = "Message_DOCGEN",
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = FILING_PROCESS,
                variablesToSet = testCase.successfulDDSDocGenCallback,
            )
        }

        if (testCase.filingMethod == BizFormationTestFilingMethod.MANUAL) {
            /**
             * For manual filings, wait for salesforce user task.
             */
            camundaTestHelpers.executeJobsIncludingActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = FILING_PROCESS,
                activityId = "manual-filing-task",
            )
            camundaTestHelpers.completeTask(
                taskDefinitionKey = "manual-filing-task",
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = FILING_PROCESS,
                variablesToSet = testCase.salesforceSuccessfulManualFileTaskCompleteVariables,
            )
        } else {
            /**
             * For automated filing, wait for RPA and simulate successful callback
             */
            camundaTestHelpers.executeJobsUntilWaitingForActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = FILING_PROCESS,
                activityId = "rpa-task",
            )
            camundaTestHelpers.correlateMessage(
                messageRef = "Message_RPA",
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = FILING_PROCESS,
                variablesToSet = testCase.successfulRPACallback.payload,
            )
        }

        camundaTestHelpers.executeJobsUntilProcessCompleted(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = FILING_PROCESS,
        )

        /**
         * Start post-filing process
         */
        camundaTestHelpers.executeJobsIncludingActivity(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = BIZ_FORMATION_PROCESS,
            // Post-filing subprocess activity
            activityId = "Activity_0hi3ity",
        )

        /**
         * Non-instant filings wait for Alchemy.
         */
        if (testCase.waitsForMailFromSOS) {
            camundaTestHelpers.executeJobsIncludingActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = POST_FILING_PROCESS,
                activityId = "hold-area-process",
            )

            camundaTestHelpers.executeHoldAreaProcessByBusinessKey(
                businessKey = testCase.processingOrderId.toString(),
                alchemyKafkaMessage = testCase.successfulAlchemyCallback,
            )
        }

        if (testCase.shouldExecuteCaliforniaSOI) {
            camundaTestHelpers.executeJobsIncludingActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = POST_FILING_PROCESS,
                activityId = "california-soi-process",
            )
            val soiProcess =
                camundaTestHelpers.getProcessInstanceByBusinessKeyAndTemplateKey(
                    businessKey = testCase.processingOrderId.toString(),
                    processDefinitionKey = CALIFORNIA_SOI_PROCESS,
                )
            camundaTestHelpers.executeJobsIncludingActivity(soiProcess, "rpa-bot-process")
            camundaTestHelpers.executeRPABotJobsAndSimulateCallbackByParentProcess(
                parentProcess = soiProcess,
                callbackVariables = testCase.successfulSOIBotResponse,
            )
            camundaTestHelpers.executeJobsUntilProcessCompleted(soiProcess)
        }

        /**
         * CP1 Orders currently still use the old doc gen process so simulate the finished job for post-filing.
         * TODO: As we onboard templates to DSD Doc Gen & CP2 we should have CP1 orders use DSD Doc Gen too.
         */
        if (testCase.commerceIds.isForSystem(CommerceSystem.CP1)) {
            camundaTestHelpers.executeJobsIncludingActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = POST_FILING_PROCESS,
                activityId = "post-filing-doc-generation",
            )
            camundaTestHelpers.executeDocGenProcessByBusinessKey(
                businessKey = testCase.processingOrderId.toString(),
                messageVariables = testCase.successfulDDSDocGenCallback,
            )
        }

        if (testCase.includeAttachedPrintAndShipFoundersKit) {
            camundaTestHelpers.executeJobsIncludingActivity(
                businessKey = testCase.processingOrderId.toString(),
                processDefinitionKey = POST_FILING_PROCESS,
                activityId = "print",
            )
            val printProcess =
                camundaTestHelpers.getProcessInstanceByBusinessKeyAndTemplateKey(
                    businessKey = testCase.processingOrderId.toString(),
                    processDefinitionKey = PRINT_PROCESS,
                )
            camundaTestHelpers.executeJobsIncludingActivity(
                processInstance = printProcess,
                activityId = "waiting-print-response",
            )
            dsdPrintShipKafkaTemplate.send(dsdPrintShipKafkaTopic, testCase.dsdPrintShipKafkaResponse)
            camundaTestHelpers.awaitMessageCorrelated(
                messageRef = "Message_PrintAndShip_Status_Response",
                processInstance = printProcess,
            )
            camundaTestHelpers.executeJobsUntilProcessCompleted(printProcess)
        }

        camundaTestHelpers.executeJobsUntilProcessCompleted(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = POST_FILING_PROCESS,
        )

        /**
         * Wait for all the process instances to finish and ensure no validation errors or incidents.
         */
        camundaTestHelpers.executeJobsUntilProcessCompleted(
            businessKey = testCase.processingOrderId.toString(),
            processDefinitionKey = BIZ_FORMATION_PROCESS,
        )
        camundaTestHelpers.assertProcessInstancesFinishedWithoutIncidentsOrErrors(
            businessKey = processingOrdersApi.toString(),
            errorKeys = setOf("validationErrors", "validationError", "errorCode", "errorMessage"),
        )

        /**
         * Verify side effects of the process.
         */
        verify(exactly = 1) {
            businessEntitiesV2Api.businessEntitiesPost(
                any(),
                any(),
                any(),
                match(testCase.expectedCreatedBusinessEntity),
            )
        }
        if (testCase.expectedDSDDocGenJobRequests.isNotEmpty()) {
            testCase.expectedDSDDocGenJobRequests.forEach { expectedDocGenRequest ->
                verifyOrder {
                    docGenJobsApi.generateDoc(expectedDocGenRequest)
                }
            }
        }
        // The last status is updated via an execution listener that runs async, so need to await the last status.
        await().untilAsserted {
            verifyOrder {
                testCase.expectedProcessingOrderStatusUpdates.forEach { processingOrderStatusId ->
                    processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                        testCase.processingOrderId,
                        processingOrderStatusId,
                        any(),
                        testCase.customerId.toString(),
                        any(),
                        any(),
                    )
                }
            }
        }

        if (testCase.expectedAttachedProcessingOrderStatusUpdates.isNotEmpty()) {
            verifyOrder {
                testCase.expectedAttachedProcessingOrderStatusUpdates.forEach { (processingOrderId, processingOrderStatusId) ->
                    processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                        processingOrderId,
                        processingOrderStatusId,
                        any(),
                        testCase.customerId.toString(),
                        any(),
                        any(),
                    )
                }
            }
        }

        verifyOrder {
            testCase.expectedNotificationPlatformRequestMatchers.forEach { matcher ->
                notificationsServiceInternalApi.createNotification1(match(matcher), any())
            }
        }
        if (testCase.expectedBusinessEntityUpdates.isEmpty()) {
            verify(exactly = 0) {
                businessEntitiesApi.businessEntitiesEntityIdPut(
                    testCase.businessEntityId,
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }
        } else {
            verifyOrder {
                testCase.expectedBusinessEntityUpdates.forEach { matcher ->
                    businessEntitiesApi.businessEntitiesEntityIdPut(
                        testCase.businessEntityId,
                        any(),
                        any(),
                        any(),
                        any(),
                        match(matcher),
                    )
                }
            }
        }
        verifyOrder {
            testCase.expectedCompletedOrderDetailUpdates.forEach { matcher ->
                completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                    testCase.processingOrderId,
                    match(matcher),
                    any(),
                    any(),
                    any(),
                )
            }
        }
        verifyOrder {
            testCase.expectedAnswersUpdates.forEach { matcher ->
                answerApi.answersAnswerBankPartialUpdatePut(
                    any(),
                    any(),
                    any(),
                    match(matcher),
                )
            }
        }
        if (testCase.expectedDDSRequests.isEmpty()) {
            verify(exactly = 0) {
                documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                    any(),
                    testCase.customerId.toString(),
                    any(),
                    any(),
                )
            }
        } else {
            verifyOrder {
                testCase.expectedDDSRequests.forEach { matcher ->
                    documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                        any(),
                        testCase.customerId.toString(),
                        any(),
                        match(matcher),
                    )
                }
            }
        }

        verifyOrder {
            testCase.expectedFulfillmentEvents.forEach { matcher ->
                fulfillmentEventService.send(match(matcher))
            }
        }

        verify(exactly = 1) {
            askNicelyService.sendSurvey(testCase.expectedNPSSurvey)
        }

        /**
         * Below here is deprecated functionality that should no longer happen.
         */
        verify(exactly = 0) {
            notificationEventService.publishToTopic(
                topicArnVal = any(),
                messageBody =
                    match { body ->
                        val snsRequest = objectMapper.readValue(body, StatusManagementSnsRequest::class.java)
                        snsRequest.referenceData.order!!.processingOrderId == testCase.processingOrderId
                    },
                correlationId = any(),
            )
        }

        if (testCase.expectedActivityFeedEvents.isNotEmpty()) {
            verifyOrder {
                testCase.expectedActivityFeedEvents.forEach { (status, matcher) ->
                    activityFeedService.sendEvent(
                        orderStatus = status,
                        activityFeedVariables = match(matcher),
                    )
                }
            }
        }

        if (testCase.expectedLegacyEventHandlerProcessingOrderStatusIds.isNotEmpty()) {
            verifyOrder {
                testCase.expectedLegacyEventHandlerProcessingOrderStatusIds.forEach { processingOrderStatusId ->
                    legacyEventHandlerApi.postToEventHandler(
                        processingStatusId = processingOrderStatusId.toString(),
                        processingOrderId = testCase.processingOrderId.toString(),
                    )
                }
            }
        }
    }

    companion object {
        // TODO: , CommerceSystem.CP2
        private val currentlySupportedCommerceSystems = listOf(CommerceSystem.CP1)

        /**
         * TODO: more tests cases
         * TODO: Determine and add other important cases.
         *  - Professional LLC
         *  - attached Registered Agent
         *  - attached Operating Agreement
         */
        @JvmStatic
        fun happyPathTestCases(): List<Arguments> {
            return currentlySupportedCommerceSystems.map { commerceSystem ->
                listOf(
                    // Standalone LLC
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.COLORADO,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = false,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = false,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.MINNESOTA,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = false,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = false,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.CALIFORNIA,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = false,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.HAWAII,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = false,
                        requiresPreFilingDocuments = false,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.FLORIDA,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = false,
                        requiresPreFilingDocuments = false,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.TEXAS,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = false,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.DELAWARE,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = true,
                    ),
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.WEST_VIRGINIA,
                        filingMethod = BizFormationTestFilingMethod.MANUAL,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = true,
                    ),
                    // LLC With attached products
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.LLC,
                        jurisdiction = State.CALIFORNIA,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = false,
                        includeAttachedRegisteredAgent = true,
                        includeAttachedInitialReports = true,
                        includeAttachedEIN = true,
                        includeAttachedPrintAndShipFoundersKit = true,
                        shouldExecuteCaliforniaSOI = true,
                    ),
                    // INC Standalone
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.INC,
                        jurisdiction = State.CALIFORNIA,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = false,
                    ),
                    // NP Standalone
                    BizFormationIntegrationTestCase(
                        commerceIds = generateCommerceIdsForSystem(commerceSystem),
                        product = ProductType.NP,
                        jurisdiction = State.CALIFORNIA,
                        filingMethod = BizFormationTestFilingMethod.RPA,
                        waitsForMailFromSOS = true,
                        hasStateIssuedId = true,
                        requiresPreFilingDocuments = true,
                    ),
                )
            }
                .flatten()
//                .filter { it.product == ProductType.LLC && it.includeAttachedRegisteredAgent }
                .map { Arguments.of(it) }
        }
    }
}
