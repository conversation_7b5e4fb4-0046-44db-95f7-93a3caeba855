package com.legalzoom.fulfillment.rest.service

import com.legalzoom.fulfillment.domain.model.PauseRule
import com.legalzoom.fulfillment.domain.repository.PauseRuleRepository
import com.legalzoom.fulfillment.rest.model.PauseRuleRequest
import com.legalzoom.fulfillment.rest.model.toEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.Optional
import java.util.UUID

class PauseRuleServiceTest {
    private lateinit var pauseRuleRepository: PauseRuleRepository
    private lateinit var pauseRuleService: PauseRuleService

    @BeforeEach
    fun setup() {
        pauseRuleRepository = mockk()
        pauseRuleService = PauseRuleService(pauseRuleRepository)
    }

    @Test
    fun `find returns all rules converted to response`() {
        val entity =
            PauseRule(
                product = 2,
                state = "CA",
                isExpedited = true,
                raType = "LZ",
                filingPhase = "pre-filing",
                pauseHours = 5,
                enabled = true,
                description = "test desc",
            ).apply { id = UUID.randomUUID() }

        every { pauseRuleRepository.findAll() } returns listOf(entity)

        val result = pauseRuleService.find(null, null, null, null, null)

        assertEquals(1, result.size)
        assertEquals("test desc", result[0].description)
        verify { pauseRuleRepository.findAll() }
    }

    @Test
    fun `save converts request to entity and saves`() {
        val request =
            PauseRuleRequest(
                product = 2,
                state = "CA",
                isExpedited = true,
                raType = "LZ",
                filingPhase = "post-filing",
                pauseHours = 4,
                enabled = true,
                description = "save test",
            )
        val entity = request.toEntity().apply { id = UUID.randomUUID() }

        every { pauseRuleRepository.save(any()) } returns entity

        val response = pauseRuleService.save(request)

        assertEquals("save test", response.description)
        assertEquals(2, response.product)
        verify { pauseRuleRepository.save(any()) }
    }

    @Test
    fun `update modifies existing entity and saves`() {
        val id = UUID.randomUUID()
        val existingEntity =
            PauseRule(
                product = 2,
                state = "CA",
                isExpedited = true,
                raType = "LZ",
                filingPhase = "pre-filing",
                pauseHours = 3,
                enabled = true,
                description = "old desc",
            ).apply { this.id = id }

        val request =
            PauseRuleRequest(
                product = 2,
                state = "NY",
                isExpedited = false,
                raType = "LZ",
                filingPhase = "post-filing",
                pauseHours = 6,
                enabled = false,
                description = "updated desc",
            )

        every { pauseRuleRepository.findById(id) } returns Optional.of(existingEntity)
        every { pauseRuleRepository.save(existingEntity) } returns existingEntity

        val response = pauseRuleService.update(id.toString(), request)

        assertEquals("updated desc", response.description)
        assertEquals(2, response.product)
        verify { pauseRuleRepository.findById(id) }
        verify { pauseRuleRepository.save(existingEntity) }
    }

    @Test
    fun `softDelete disables the rule and saves`() {
        val id = UUID.randomUUID()
        val entity =
            PauseRule(
                product = 2,
                state = "CA",
                isExpedited = true,
                raType = "LZ",
                filingPhase = "pre-filing",
                pauseHours = 2,
                enabled = true,
                description = "soft delete",
            ).apply { this.id = id }

        every { pauseRuleRepository.findById(id) } returns Optional.of(entity)
        every { pauseRuleRepository.save(entity) } returns entity

        pauseRuleService.softDelete(id.toString())

        assertFalse(entity.enabled)
        verify { pauseRuleRepository.findById(id) }
        verify { pauseRuleRepository.save(entity) }
    }

    @Test
    fun `findMatchingRule with request returns first matching rule`() {
        val request =
            PauseRuleRequest(
                product = 2,
                state = "CA",
                isExpedited = true,
                raType = "LZ",
                filingPhase = "pre-filing",
                pauseHours = 4,
                enabled = true,
                description = "match request",
            )
        val entity = request.toEntity().apply { id = UUID.randomUUID() }

        every {
            pauseRuleRepository.findMatchingRules(
                product = request.product,
                state = request.state,
                isExpedited = request.isExpedited,
                raType = request.raType,
                filingPhase = request.filingPhase,
            )
        } returns listOf(entity)

        val result = pauseRuleService.findMatchingRule(request)

        assertNotNull(result)
        assertEquals("match request", result!!.description)
    }

    @Test
    fun `findMatchingRule with parameters returns first matching rule`() {
        val product = 2
        val state = "CA"
        val isExpedited = true
        val raType = "LZ"
        val filingPhase = "post-filing"

        val entity =
            PauseRule(
                product = product,
                state = state,
                isExpedited = isExpedited,
                raType = raType,
                filingPhase = filingPhase,
                pauseHours = 4,
                enabled = true,
                description = "match params",
            ).apply { id = UUID.randomUUID() }

        every {
            pauseRuleRepository.findMatchingRules(
                product = product,
                state = state,
                isExpedited = isExpedited,
                raType = raType,
                filingPhase = filingPhase,
            )
        } returns listOf(entity)

        val result = pauseRuleService.findMatchingRule(product, state, isExpedited, raType, filingPhase)

        assertNotNull(result)
        assertEquals("match params", result!!.description)
    }
}
