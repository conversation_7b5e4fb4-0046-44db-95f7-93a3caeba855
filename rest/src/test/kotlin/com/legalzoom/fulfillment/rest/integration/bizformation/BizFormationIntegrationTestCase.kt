package com.legalzoom.fulfillment.rest.integration.bizformation

import com.fasterxml.jackson.databind.ObjectMapper
import com.legalzoom.api.dsd.printship.model.PrintJobStatusEventKafkaDto
import com.legalzoom.api.model.answer.EntityMappedDataResponse
import com.legalzoom.api.model.answer.FilingMappedDataResponse
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.SaveAnswerBankRequest
import com.legalzoom.api.model.businessentities.EntityDetailDto
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.businessentities.UpdateEntityRequest
import com.legalzoom.api.model.businessentities.UpdateEntityResponse
import com.legalzoom.api.model.businessentities.v2.CreateEntityInformationRequest
import com.legalzoom.api.model.businessprofile.namecheck.CompanyNameAvailabilityResponseGet
import com.legalzoom.api.model.customer.ContactDto
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.customer.CustomerInfoDto
import com.legalzoom.api.model.dds.DocumentRequest
import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.dds.SCJobSubmitResultDto
import com.legalzoom.api.model.dsd.docgen.DocGenJobRequestDto
import com.legalzoom.api.model.dsd.printship.PrintShipJobReferenceIdsDto
import com.legalzoom.api.model.dsd.printship.PrintShipJobResponseDto
import com.legalzoom.api.model.notificationsplatform.Account
import com.legalzoom.api.model.notificationsplatform.NotificationRequest
import com.legalzoom.api.model.notificationsplatform.TargetChannel
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderDto
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.order.ProductConfigurationDto
import com.legalzoom.api.model.order.RelationshipType
import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.CompletedOrderDetailDto
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailRequest
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.model.product.PostOptionDto
import com.legalzoom.api.model.questionnaire.GetQuestionnaireInfoResponse
import com.legalzoom.api.model.rpa.FolderDto
import com.legalzoom.api.model.rpa.ODataValueOfIEnumerableOfFolderDto
import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.storageplatform.SearchResponse
import com.legalzoom.api.model.storageplatform.SearchResult
import com.legalzoom.api.service.dto.SurveyTriggerRequest
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.rest.helpers.FakeRpaCallback
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.service.data.AlchemyDocumentType
import com.legalzoom.fulfillment.service.data.AlchemyMessage
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.data.UnifiedCommerceIds
import com.legalzoom.fulfillment.service.data.activityFeed.ActivityFeedVariables
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.data.formation.FormationOrder
import com.legalzoom.fulfillment.workflow.service.DocumentGenerationService
import org.springframework.core.io.ClassPathResource
import java.time.Clock
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.UUID

/**
 * The different types of filing for biz formation that determine the path through the process.
 * This changes the mock setups, callbacks, and mock verifications.
 */
enum class BizFormationTestFilingMethod {
    RPA,
    MANUAL,
}

/**
 * This test case class should contain:
 * - All the information necessary to mock service responses during the biz formation process
 * - The requests necessary to simulate starting the process and simulating any webhooks/callbacks/events
 * - The definitive assertions that denote a business formation updates the correct systems
 */
class BizFormationIntegrationTestCase(
    val commerceIds: UnifiedCommerceIds,
    val product: ProductType,
    val jurisdiction: State,
    val filingMethod: BizFormationTestFilingMethod,
    val waitsForMailFromSOS: Boolean,
    val hasStateIssuedId: Boolean,
    val requiresPreFilingDocuments: Boolean,
    val shouldExecuteCaliforniaSOI: Boolean = false,
    val includeAttachedRegisteredAgent: Boolean = false,
    val includeAttachedInitialReports: Boolean = false,
    val includeAttachedEIN: Boolean = false,
    val includeAttachedPrintAndShipFoundersKit: Boolean = false,
) {
    val customerId: Int = UniqueId.nextInt()
    val processingOrderId: Int = UniqueId.nextInt()
    val accountId: UUID = UniqueId.nextUUID()
    val rpaQueueItemId: Long = UniqueId.nextLong()
    val businessEntityId: Int = UniqueId.nextInt()
    val stateEntityNumber: String = UniqueId.nextIdString()
    val orderId =
        if (commerceIds.isForSystem(CommerceSystem.CP2)) {
            UniqueId.nextCP2OrderId()
        } else {
            commerceIds.cp1OrderId!!.toString()
        }
    val attachedRegisteredAgentProcessingOrderId = UniqueId.nextInt()
    val attachedInitialReportsProcessingOrderId = UniqueId.nextInt()
    val attachedEINProcessingOrderId = UniqueId.nextInt()

    val questionnaireInfoResponse =
        objectMapperForReadingFixtureFiles.readValue(
            ClassPathResource("${product.questionnaireId}_questionnaire.json", javaClass).file,
            GetQuestionnaireInfoResponse::class.java,
        )
    val questionnaireAnswersResponse =
        objectMapperForReadingFixtureFiles.readValue(
            ClassPathResource("${product.processId}_answers.json", javaClass).file,
            GetQuestionnaireAnswerResponse::class.java,
        ).also {
            it.questionnaireFieldGroupAnswers?.also { answers ->
                answers.userOrderId = processingOrderId
                answers.fieldAnswers?.forEach { fieldAnswer ->
                    // Replace jurisdiction in fixture with test jurisdiction
                    // TODO: Replace county and zip codes as well?
                    if (fieldAnswer.fieldValue == "Colorado") {
                        fieldAnswer.fieldValue = jurisdiction.displayName
                    }
                }
            }
        }
    val answersFilingDataResponse =
        objectMapperForReadingFixtureFiles.readValue(
            ClassPathResource("${product.processId}_answers_filing.json", javaClass).file,
            FilingMappedDataResponse::class.java,
        ).also { response ->
            response.company?.entityNumber = stateEntityNumber
            response.company?.registeredStateCode = jurisdiction.abbreviation
            response.company?.registeredStateName = jurisdiction.displayName
            // TODO: Update county?
            // response.company?.registeredCountyName = jurisdiction.displayName
        }
    val answersEntityDataResponse =
        objectMapperForReadingFixtureFiles.readValue(
            ClassPathResource("${product.processId}_answers_entity.json", javaClass).file,
            EntityMappedDataResponse::class.java,
        ).also { response ->
            response.entityDataResponse!!.formationState = jurisdiction.id
        }

    val companyEffectiveDate = LocalDate.now(Clock.systemUTC())

    val customerDetails =
        CustomerDetailResponse().also { response ->
            response.customerInfo =
                CustomerInfoDto().also { customer ->
                    customer.customerId = customerId
                }
            response.contactInfo =
                ContactDto().also { contact ->
                    contact.firstName = "firstname"
                    contact.lastName = "lastname"
                    contact.email1 = "<EMAIL>"
                }
        }

    val processingOrderDetails =
        GetProcessingOrderResponse().also { response ->
            response.processingOrder =
                ProcessingOrderDto().also { processingOrderDto ->
                    processingOrderDto.customerId = customerId
                    processingOrderDto.processingOrderId = processingOrderId
                    processingOrderDto.processId = product.processId
                    processingOrderDto.processingStatusId =
                        when (product) {
                            ProductType.LLC -> ProcessingOrderStatus.NotStarted.processingStatusId
                            ProductType.INC -> ProcessingOrderStatus.IncPreFilingValidationStarted.processingStatusId
                            ProductType.NP -> ProcessingOrderStatus.NonProfitValidationStarted.processingStatusId
                            else -> unsupportedProduct()
                        }
                    processingOrderDto.questionnaireId = product.questionnaireId
                }
        }
    val namecheckEntityType =
        when (product) {
            ProductType.LLC -> "llc"
            ProductType.INC -> "corp"
            ProductType.NP -> "npc"
            else -> unsupportedProduct()
        }
    val nameCheckAvailabilityResponse =
        CompanyNameAvailabilityResponseGet().also {
            it.status = "available"
        }
    val articlesFiledDocTemplateId: Int =
        when (product) {
            ProductType.LLC -> 455
            ProductType.INC -> 439
            ProductType.NP -> 486
            else -> unsupportedProduct()
        }
    val articlesFiledSubDocType: String = "Articles Filed"

    val sosFilingExceptionDocTemplateId: Int =
        when (product) {
            ProductType.LLC -> 1445
            ProductType.INC -> 1461
            ProductType.NP -> 0
            else -> unsupportedProduct()
        }

    val articlesFiledStorageSearchResponse =
        SearchResponse().also { response ->
            response.payload =
                listOf(
                    SearchResult().also { doc ->
                        doc.documentId = "articles_filed_doc_id"
                        doc.documentType = SearchResult.DocumentTypeEnum.FULFILLMENT
                        doc.reference =
                            mapOf(
                                "processingOrderId" to this.processingOrderId.toString(),
                                "productType" to this.product.name,
                                "documentSubtype" to this.articlesFiledSubDocType,
                            )
                        doc.documentStatus = SearchResult.DocumentStatusEnum.ACTIVE
                        doc.history = emptyList()
                        doc.createdDate = OffsetDateTime.now()
                        doc.documentPath = "/LegalZoom Files"
                        doc.documentName = "articles filed.pdf"
                        doc.ownerId = customerId.toString()
                        doc.documentVersion = 1
                    },
                )
        }

    val rpaQueueItemResponse =
        QueueItemDto().also {
            it.id = rpaQueueItemId
        }

    // NOTE: Configured in uipath config properties
    val uiPathFolderName =
        when {
            this.product == ProductType.LLC && this.jurisdiction == State.NEW_YORK -> "LegalZoom/StaticIP"
            this.product == ProductType.INC && this.jurisdiction == State.NEW_YORK -> "LegalZoom/StaticIP"
            else -> "LegalZoom"
        }

    val uiPathFoldersResponse =
        ODataValueOfIEnumerableOfFolderDto().also {
            it.value =
                listOf(
                    FolderDto().also { folderDto ->
                        folderDto.id = UI_PATH_FOLDER_ID
                    },
                )
        }

    val salesforceLedgerNoteResponse =
        AddLedgerNoteResponse(
            sfOrderId = orderId,
            sfContactId = "sfContactId",
            casesAndNotes = emptyList(),
        )

    val salesforceDelayedFileCaseId = UniqueId.nextUUIDString()

    val salesforceDelayedFileCaseCreatedResponse =
        SalesforceCaseResponse(
            recordId = this.salesforceDelayedFileCaseId,
            message = "holding area case created",
        )

    val salesforceDelayedFileCaseClosedResponse =
        SalesforceCaseUpdateResponse(
            recordId = this.salesforceDelayedFileCaseId,
            message = "holding area case closed",
            error = null,
        )

    val salesforceManualFileCaseId = UniqueId.nextUUIDString()

    val salesforceManualFileCaseCreatedResponse =
        SalesforceCaseResponse(
            recordId = this.salesforceManualFileCaseId,
            message = "manual file case created",
        )

    val salesforceSuccessfulManualFileTaskCompleteVariables =
        mapOf(
            "disposition" to FulfillmentDisposition.Proceed.value,
        )

    val completedOrderDetailResponse =
        GetCompleteOrderDetailResponse().also { response ->
            response.completedOrderDetail =
                CompletedOrderDetailDto().also { detail ->
                    // This is a special date/time that signifies the effective date needs to be updated.
                    detail.effectiveDate = OffsetDateTime.parse("1970-09-05T00:00Z")
                    if (this.includeAttachedPrintAndShipFoundersKit) {
                        // Standard Shipping
                        detail.shipMethodId = 6
                    }
                }
        }

    val getBusinessEntityResponse =
        GetEntityByEntityIdResponse().also { response ->
            response.entity =
                EntityDetailDto().also { detail ->
                    detail.entityId = businessEntityId
                    detail.processingOrderId = processingOrderId
                    // NOTE: Does not return stateEntityNumber so the process will update it
                }
        }

    val updateBusinessEntityResponse =
        UpdateEntityResponse().also { response ->
            response.entityId = businessEntityId
            response.success = true
        }

    // Just using a made up value for product configuration id
    val cp1ProductConfigurationId = 999
    val cp1GetOrderItemResponse =
        GetOrderItemResponse().also { response ->
            response.orderId = this.commerceIds.cp1OrderId
            response.orderItem =
                OrderItemDto().also { orderItem ->
                    orderItem.orderItemId = UniqueId.nextInt()
                    orderItem.parentOrderItemId = orderItem.orderItemId
                    orderItem.isCancelled = false
                    orderItem.processingOrder =
                        com.legalzoom.api.model.order.ProcessingOrderDto().also { processingOrder ->
                            processingOrder.processingOrderId = this.processingOrderId
                            processingOrder.processId = product.processId
                            // TODO: Why does this need to be true?
                            processingOrder.complete = true
                        }
                    orderItem.productConfiguration =
                        ProductConfigurationDto().also { productConfiguration ->
                            productConfiguration.productConfigurationId = cp1ProductConfigurationId
                            productConfiguration.productTypeId = RelationshipType.NUMBER_2 // PACKAGE
                        }
                }
        }

    val cp1GetOrdersResponse =
        GetOrderResponse().also { response ->
            response.order =
                OrderDto().also { order ->
                    order.orderId = commerceIds.cp1OrderId
                    order.isCancelled = false
                    order.orderItems =
                        listOfNotNull(
                            this.cp1GetOrderItemResponse.orderItem,
                            if (this.includeAttachedRegisteredAgent) {
                                createAttachedCP1OrderItem(
                                    ProductType.RegisteredAgentService,
                                    attachedRegisteredAgentProcessingOrderId,
                                )
                            } else {
                                null
                            },
                            if (this.includeAttachedInitialReports) {
                                createAttachedCP1OrderItem(
                                    ProductType.InitialReports,
                                    attachedInitialReportsProcessingOrderId,
                                )
                            } else {
                                null
                            },
                            if (this.includeAttachedEIN) {
                                createAttachedCP1OrderItem(ProductType.EIN, attachedEINProcessingOrderId)
                            } else {
                                null
                            },
                            if (this.includeAttachedPrintAndShipFoundersKit) {
                                OrderItemDto().also { orderItem ->
                                    orderItem.isCancelled = false
                                    orderItem.parentOrderItemId = this.cp1GetOrderItemResponse.orderItem!!.orderItemId
                                    // Standard Shipping
                                    orderItem.shipMethodId = 6
                                    orderItem.productConfiguration =
                                        ProductConfigurationDto().also { pc ->
                                            // SHIPPING
                                            pc.productTypeId = RelationshipType.NUMBER_7
                                        }
                                }
                            } else {
                                null
                            },
                        )
                    order.dateCreated = OffsetDateTime.now()
                }
        }

    val cp1OrderContactResponse =
        GetOrderContactsResponse().also { response ->
            response.contacts =
                listOf(
                    ContactInfoDto().also { contactInfo ->
                        contactInfo.contactType = ContactType.Shipping
                        contactInfo.country = "USA"
                        contactInfo.firstName = "shippingfirstName"
                        contactInfo.lastName = "shippinglastName"
                        contactInfo.addressLine1 = "123 shipping address"
                        contactInfo.stateId = this.jurisdiction.id
                        contactInfo.state = this.jurisdiction.abbreviation
                        contactInfo.city = "city"
                        contactInfo.zipCode = "12345"
                        contactInfo.mobilePhone = "************"
                    },
                )
        }

    val cp1ProductsPostOptionResponse =
        GetPostOptionResponse().also { response ->
            response.postOption =
                PostOptionDto().also { postOption ->
                    postOption.isKitIncluded = true
                    postOption.cardinality = 3
                }
        }

    private fun createAttachedCP1OrderItem(
        productType: ProductType,
        processingOrderId: Int,
    ): OrderItemDto {
        return OrderItemDto().also { orderItem ->
            orderItem.isCancelled = false
            orderItem.stateId = this.jurisdiction.id
            orderItem.processingOrder =
                com.legalzoom.api.model.order.ProcessingOrderDto().also { processingOrder ->
                    processingOrder.processingOrderId = processingOrderId
                    processingOrder.processId = productType.processId
                }
        }
    }

    val ddsDocGenResponse =
        DocumentsGenerationResponse().also { response ->
            response.isAllDocRequestSubmitted = true
            response.jobSubmissionResults =
                listOf(
                    SCJobSubmitResultDto().also { result ->
                        result.jobId = UniqueId.nextInt()
                    },
                )
        }

    /*****************************************************
     * Requests used to start and push along the process.
     *****************************************************/
    val formationOrderRequest =
        FormationOrder(
            customerId = customerId.toLong(),
            orderId = commerceIds.cp1OrderId?.toLong() ?: 0,
            processingOrderId = processingOrderId.toLong(),
            processId = product.processId.toLong(),
            cp2OrderItemId = commerceIds.cp2OrderItemId,
            state = jurisdiction.abbreviation,
        )

    // Simulate a successful callback from UI Path RPA Bot
    val successRPAEvidenceFilePath: String =
        "s3://uipath-rpa-storage-dev/Jobs_Data/BOT_NAME/PoW_${UUID.randomUUID()}.pdf"

    val rpaProofOfWorkDocuments =
        listOf("s3://uipath-rpa-storage-dev/Jobs_Data/INC_CA/PoW_60839537-63fb-11ec-b4f8-62ed787118e7.pdf")

    val successfulRPACallback =
        if (this.waitsForMailFromSOS) {
            FakeRpaCallback.successWithoutArticlesFiled(stateEntityNumber, rpaProofOfWorkDocuments)
        } else {
            FakeRpaCallback.successWithArticlesFiled(
                stateEntityNumber,
                successRPAEvidenceFilePath,
                rpaProofOfWorkDocuments,
            )
        }

    val successfulDDSDocGenDocument =
        "s3://dds-document-storage-dev/181121/181121_${processingOrderId}_PreFiling_Articles.pdf"

    val successfulDDSDocGenCallback =
        mapOf(
            "status" to "success",
            "documentPaths" to listOf(successfulDDSDocGenDocument),
        )

    val successfulAlchemyArticlesFiled =
        "https://document-ingestion-processed-bucket.invalid/processing-order/${this.processingOrderId}"

    val successfulAlchemyCallback =
        AlchemyMessage(
            processingOrderId = this.processingOrderId.toLong(),
            correlationId = UUID.randomUUID().toString(),
            disposition = null,
            evidenceFilePath = successfulAlchemyArticlesFiled,
            documentType = AlchemyDocumentType.ARTICLES_FILED.displayName,
            effectiveDate =
                companyEffectiveDate.format(
                    DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                ),
            evidenceTransactionNumber = this.stateEntityNumber,
            jobId = UUID.randomUUID().toString(),
        )

    val successfulSOIEvidenceFilPath = "s3://rpa-storage-prod/SOI-${this.processingOrderId}.pdf"
    val successfulSOIBotResponse =
        mapOf<String, Any?>(
            "status" to "Success",
            "evidenceTransactionNumber" to "123-${this.processingOrderId}",
            "evidenceFilePath" to successfulSOIEvidenceFilPath,
        )

    val dsdPrintShipRequestId = UniqueId.nextUUIDString()
    val dsdPrintShipKafkaResponse =
        PrintJobStatusEventKafkaDto(
            id = dsdPrintShipRequestId,
            accountId = this.accountId.toString(),
            status = "SHIPPED",
            error = null,
            trackingNumber = UniqueId.nextIdString(),
            referenceIds =
                PrintShipJobReferenceIdsDto().also { referenceIds ->
                    referenceIds.processingOrderId = this.processingOrderId.toString()
                },
            shipMethod = null,
        )
    val dsdPrintShipResponse =
        PrintShipJobResponseDto().also { response ->
            response.id = dsdPrintShipRequestId
            response.accountId = this.accountId.toString()
            response.referenceIds =
                PrintShipJobReferenceIdsDto().also { reference ->
                    reference.processingOrderId = this.processingOrderId.toString()
                    reference.cp1OrderId = this.commerceIds.cp1OrderId?.toString()
                    reference.cp2OrderItemId = this.commerceIds.cp2OrderItemId
                }
        }

    /***************************************************************************
     * Primary asserted calls to services that represent a successful formation.
     * TODO: The only stand-alone instant filing doc that should be generated is "welcome letter"
     * TODO: Setup test fixtures for product/state combos
     **************************************************************************/
    val expectedDSDDocGenJobRequests = listOf<DocGenJobRequestDto>()

    val expectedProcessingOrderStatusUpdates =
        when (product) {
            ProductType.LLC -> listOf(75, 76, 80, 83, 82, 1456)
            ProductType.INC -> listOf(1, 2, 6, 70, 69, 1474)
            ProductType.NP -> listOf(303, 482, 304, 307, 1036, 1586)
            else -> unsupportedProduct()
        }

    val expectedAttachedProcessingOrderStatusUpdates =
        listOfNotNull<Pair<Int, Int>>(
            if (this.includeAttachedRegisteredAgent) {
                this.attachedRegisteredAgentProcessingOrderId to 1028
            } else {
                null
            },
            if (this.includeAttachedInitialReports) {
                this.attachedInitialReportsProcessingOrderId to 1023
            } else {
                null
            },
        )

    /**
     * NOTE: Currently only still used by the shared hold area bpmn, turned off everywhere else.
     */
    val expectedLegacyEventHandlerProcessingOrderStatusIds =
        if (this.waitsForMailFromSOS) {
            when (product) {
                ProductType.LLC -> listOf(83)
                ProductType.INC -> listOf(70)
                ProductType.NP -> listOf(307)
                else -> unsupportedProduct()
            }
        } else {
            emptyList()
        }

    val expectedNPSSurvey =
        SurveyTriggerRequest(
            email = "<EMAIL>",
            name = "firstname lastname",
            addContact = null,
            triggerEmail = false,
            delayMinutes = 4320,
            segment = null,
            team = null,
            department = null,
            customProperties =
                mapOf(
                    "firstname_c" to "firstname",
                    "orderid_c" to this.orderId,
                    "Department" to
                        when (this.product) {
                            ProductType.LLC -> "LZ LLC pNPS"
                            ProductType.INC -> "LZ Inc pNPS"
                            ProductType.NP -> "LZ NP pNPS"
                            else -> unsupportedProduct()
                        },
                ),
            thenDeactivate = null,
        )

    private fun createNotificationPlatformMatcher(
        type: String,
        channel: TargetChannel.ChannelTypeEnum,
        templateData: Map<String, Any?>,
    ): (NotificationRequest) -> Boolean {
        return { request ->
            request.notificationType == type &&
                request.account.accountId == this.customerId.toString() &&
                request.account.accountType == Account.AccountTypeEnum.PERSONAL &&
                request.account.contactType == Account.ContactTypeEnum.PRIMARY &&
                request.topic!!.processingOrderId == this.processingOrderId.toString() &&
                request.topic!!.orderId == this.orderId &&
                request.targetChannels[0]!!.channelType == channel &&
                request.targetChannels[0]!!.dynamicTemplateData == templateData
        }
    }

    val expectedNotificationPlatformRequestMatchers: List<(NotificationRequest) -> Boolean> =
        listOfNotNull(
            createNotificationPlatformMatcher(
                type = "fulfillment_statusupdate_biz-formation-sent-to-sos_20250509",
                channel = TargetChannel.ChannelTypeEnum.EMAIL,
                templateData =
                    mapOf(
                        "firstName" to this.customerDetails.contactInfo!!.firstName,
                        "accountId" to this.accountId.toString(),
                    ),
            ),
            createNotificationPlatformMatcher(
                type = "fulfillment_statusupdate_order-status-sent-to-sos-sms_20240209",
                channel = TargetChannel.ChannelTypeEnum.SMS,
                templateData =
                    mapOf(
                        "product" to this.product.productName,
                        "orderId" to this.orderId,
                        "date" to LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd")),
                    ),
            ),
            // TODO: Test the notification with an attached EIN
            createNotificationPlatformMatcher(
                type =
                    if (this.includeAttachedEIN) {
                        "fulfillment_statusupdate_biz-formation-ready-for-download-ein_20250509"
                    } else {
                        "fulfillment_statusupdate_biz-formation-ready-for-download-no-ein_20250509"
                    },
                channel = TargetChannel.ChannelTypeEnum.EMAIL,
                templateData =
                    mapOf(
                        "firstName" to this.customerDetails.contactInfo!!.firstName,
                        "accountId" to this.accountId.toString(),
                    ),
            ),
            createNotificationPlatformMatcher(
                type = "fulfillment_statusupdate_order-status-order-complete-sms_20240209",
                channel = TargetChannel.ChannelTypeEnum.SMS,
                templateData = emptyMap(),
            ),
            // Print & Ship emails are sent via the OrderStatusNotificationService because it is a shared bpmn.
            if (this.includeAttachedPrintAndShipFoundersKit) {
                createNotificationPlatformMatcher(
                    type = "fulfillment_statusupdate_order-status-order-shipped-sms_20240801",
                    channel = TargetChannel.ChannelTypeEnum.SMS,
                    templateData =
                        mapOf(
                            "firstName" to this.customerDetails.contactInfo!!.firstName,
                            "entityName" to "Some Company L.L.C.",
                            "product" to this.product.productName,
                            "orderId" to this.orderId,
                            "date" to LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd")),
                            "jurisdiction" to this.jurisdiction.abbreviation,
                            "paygovTrackingId" to "",
                            "agencyTrackingId" to "",
                            "submissionDate1023EZ" to "",
                            "document1023EZId" to "",
                            "document3500AId" to "",
                            "accountId" to this.accountId.toString(),
                        ),
                )
            } else {
                null
            },
            if (this.includeAttachedPrintAndShipFoundersKit) {
                createNotificationPlatformMatcher(
                    type = "fulfillment_statusupdate_order-status-order-shipped-email_20240801",
                    channel = TargetChannel.ChannelTypeEnum.EMAIL,
                    templateData =
                        mapOf(
                            "firstName" to this.customerDetails.contactInfo!!.firstName,
                            "entityName" to "Some Company L.L.C.",
                            "product" to this.product.productName,
                            "orderId" to this.orderId,
                            "date" to LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd")),
                            "jurisdiction" to this.jurisdiction.abbreviation,
                            "paygovTrackingId" to "",
                            "agencyTrackingId" to "",
                            "submissionDate1023EZ" to "",
                            "document1023EZId" to "",
                            "document3500AId" to "",
                            "accountId" to this.accountId.toString(),
                        ),
                )
            } else {
                null
            },
        )

    val expectedCreatedBusinessEntity: (CreateEntityInformationRequest) -> Boolean = { request ->
        request.entity!!.let { entity ->
            entity.processingOrderId == this.processingOrderId &&
                entity.formationState == this.jurisdiction.abbreviation &&
                entity.commerceSystem == this.commerceIds.system.name
        }
    }

    /**
     * NOTES:
     * - Effective date/time is only updated in COD, not in Business Entities.
     * - Some states don't issue an entity id, and thus we do not update business entities.
     */
    val expectedBusinessEntityUpdates: List<(UpdateEntityRequest) -> Boolean> =
        when {
            // entity number is updated by alchemy
            this.waitsForMailFromSOS -> emptyList()
            // entity number updated by fulfillment for instant file states that support state entity numbers
            this.filingMethod == BizFormationTestFilingMethod.RPA && this.hasStateIssuedId ->
                listOf(
                    { updateRequest -> updateRequest.request!!.stateEntityNumber == this.stateEntityNumber },
                )

            else -> emptyList()
        }

    val expectedCompletedOrderDetailUpdates: List<(UpdateCompletedOrderDetailRequest) -> Boolean> =
        listOf(
            { updateRequest -> updateRequest.entityName == answersFilingDataResponse.company!!.nameWithDesignator },
            { updateRequest ->
                updateRequest.effectiveDate ==
                    OffsetDateTime.of(
                        companyEffectiveDate,
                        LocalTime.NOON,
                        ZoneOffset.UTC,
                    )
            },
        )

    // NOTE: Only used for CP1 orders, CP2 orders use DSD Doc Gen
    // TODO: Assertions for document uploads from DDS Requests
    val expectedDDSRequests: List<(DocumentRequest) -> Boolean> =
        if (commerceIds.isForSystem(CommerceSystem.CP1)) {
            listOf(
//            TODO: Some product/states might initiate pre-filing docs?
//            { ddsRequest ->
//                ddsRequest.orderId == commerceIds.cp1OrderId &&
//                    ddsRequest.processingOrderId == this.processingOrderId &&
//                    ddsRequest.initiator == DocumentGenerationService.DocGenApiInitiator.PRE_FILING_DOC_TEMPLATE.value &&
//                    ddsRequest.answerSource == com.legalzoom.api.model.dds.AnswerSource.NUMBER_0 &&
//                    ddsRequest.revisionType == com.legalzoom.api.model.dds.RevisionType.NUMBER_0
//            },
                { ddsRequest ->
                    ddsRequest.orderId == commerceIds.cp1OrderId &&
                        ddsRequest.processingOrderId == this.processingOrderId &&
                        ddsRequest.initiator == DocumentGenerationService.DocGenApiInitiator.POST_FILING_DOC_TEMPLATE.value &&
                        ddsRequest.answerSource == com.legalzoom.api.model.dds.AnswerSource.NUMBER_0 &&
                        ddsRequest.revisionType == com.legalzoom.api.model.dds.RevisionType.NUMBER_0
                },
            )
        } else {
            emptyList()
        }

    // TODO: The field ids/names will be different for INC/NP?
    val expectedAnswersUpdates: List<(SaveAnswerBankRequest) -> Boolean> =
        when (product) {
            ProductType.LLC ->
                listOf(
                    { answersUpdate ->
                        val field = answersUpdate.questionnaireFieldGroupAnswers.fieldAnswers[0]
                        answersUpdate.questionnaireFieldGroupAnswers.processingOrderId == this.processingOrderId &&
                            field.fieldId == 8243 &&
                            field.fieldName == "LLC_name" &&
                            field.fieldValue == answersFilingDataResponse.company!!.nameWithDesignator
                    },
                    { answersUpdate ->
                        val field = answersUpdate.questionnaireFieldGroupAnswers.fieldAnswers[0]
                        answersUpdate.questionnaireFieldGroupAnswers.processingOrderId == this.processingOrderId &&
                            field.fieldId == 297939 &&
                            field.fieldName == "Entity_Effective_Date_hidden" &&
                            field.fieldValue ==
                            companyEffectiveDate.format(
                                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                            )
                    },
                )

            ProductType.INC ->
                listOf(
                    { answersUpdate ->
                        val field = answersUpdate.questionnaireFieldGroupAnswers.fieldAnswers[0]
                        answersUpdate.questionnaireFieldGroupAnswers.processingOrderId == this.processingOrderId &&
                            field.fieldId == 8303 &&
                            field.fieldName == "Corporate_Name" &&
                            field.fieldValue == answersFilingDataResponse.company!!.nameWithDesignator
                    },
                    { answersUpdate ->
                        val field = answersUpdate.questionnaireFieldGroupAnswers.fieldAnswers[0]
                        answersUpdate.questionnaireFieldGroupAnswers.processingOrderId == this.processingOrderId &&
                            field.fieldId == 299679 &&
                            field.fieldName == "entity_effective_date_st" &&
                            field.fieldValue ==
                            companyEffectiveDate.format(
                                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                            )
                    },
                )

            ProductType.NP ->
                listOf(
                    { answersUpdate ->
                        val field = answersUpdate.questionnaireFieldGroupAnswers.fieldAnswers[0]
                        answersUpdate.questionnaireFieldGroupAnswers.processingOrderId == this.processingOrderId &&
                            field.fieldId == 10001 &&
                            field.fieldName == "Name_of_non_profit" &&
                            field.fieldValue == answersFilingDataResponse.company!!.nameWithDesignator
                    },
                    { answersUpdate ->
                        val field = answersUpdate.questionnaireFieldGroupAnswers.fieldAnswers[0]
                        answersUpdate.questionnaireFieldGroupAnswers.processingOrderId == this.processingOrderId &&
                            field.fieldId == 300107 &&
                            field.fieldName == "formation_date" &&
                            field.fieldValue ==
                            companyEffectiveDate.format(
                                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                            )
                    },
                )

            else -> unsupportedProduct()
        }

    val expectedFulfillmentEvents: List<(FulfillmentEvent) -> Boolean> =
        run {
            val events =
                mutableListOf(
                    EventPhase.PRE_FILING to EventType.START,
                    EventPhase.PRE_FILING to EventType.VALIDATION_COMPLETE,
                    EventPhase.PRE_FILING to EventType.END,
                    EventPhase.FILING to EventType.START,
                )

            when (this.filingMethod) {
                BizFormationTestFilingMethod.RPA -> {
                    if (!this.waitsForMailFromSOS) {
                        events.addAll(
                            listOf(
                                // save document upload fields updates effective date
                                EventPhase.FILING to EventType.FIELD_UPDATED,
                            ),
                        )

                        if (this.hasStateIssuedId) {
                            // save state entity number
                            events.add(EventPhase.FILING to EventType.FIELD_UPDATED)
                        }

                        events.addAll(
                            listOf(
                                EventPhase.FILING to EventType.RECEIVED_FROM_SOS,
                            ),
                        )
                    } else {
                        // NOTE: SENT_TO_SOS fulfillment event is not sent for instant file states
                        events.add(EventPhase.FILING to EventType.SENT_TO_SOS)
                    }
                }
                BizFormationTestFilingMethod.MANUAL -> {
                    events.add(EventPhase.FILING to EventType.SENT_TO_SOS)
                }
            }

            events.addAll(
                listOf(
                    EventPhase.FILING to EventType.END,
                    EventPhase.POST_FILING to EventType.START,
                ),
            )

            if (this.waitsForMailFromSOS) {
                events.addAll(
                    listOf(
                        EventPhase.HOLD_AREA to EventType.START,
                        EventPhase.HOLD_AREA to EventType.RECEIVED_FROM_SOS,
                        EventPhase.HOLD_AREA to EventType.FIELD_UPDATED,
                        EventPhase.HOLD_AREA to EventType.END,
                    ),
                )
            }

            events.addAll(
                listOf(
                    EventPhase.DOC_GENERATION to EventType.START,
                    EventPhase.DOC_GENERATION to EventType.END,
                    EventPhase.POST_FILING to EventType.DOCUMENTS_GENERATED,
                    EventPhase.POST_FILING to EventType.END,
                ),
            )
            events
        }
            .map { (eventPhase, eventType) ->
                { event: FulfillmentEvent ->
                    event.customerId == this.customerId.toString() &&
                        event.processId == this.product.processId.toString() &&
                        event.orderId == this.orderId &&
                        event.processingOrderId == this.processingOrderId.toString() &&
                        event.eventType == eventType &&
                        event.eventPhase == eventPhase &&
                        event.jurisdiction == this.jurisdiction.abbreviation
                }
            }

    private fun matchesCommonActivityFeedVariables(activityFeedVariables: ActivityFeedVariables): Boolean {
        return activityFeedVariables.accountId == this.accountId &&
            activityFeedVariables.customerId == this.customerId.toString() &&
            activityFeedVariables.processId == this.product.processId &&
            activityFeedVariables.productName == this.product.productName &&
            activityFeedVariables.entityType == this.product.name &&
            activityFeedVariables.jurisdiction == this.jurisdiction.abbreviation &&
            // TODO: This is an Int and won't work with CP2!
            activityFeedVariables.orderId == this.orderId.toInt() &&
            activityFeedVariables.processingOrderId == this.processingOrderId
    }

    /**
     * NOTE: Currently still only used in the shared hold area bpmn and nowhere else.
     */
    val expectedActivityFeedEvents =
        if (this.waitsForMailFromSOS) {
            when (product) {
                ProductType.LLC ->
                    listOfNotNull<Pair<ProcessingOrderStatus, (ActivityFeedVariables) -> Boolean>>(
                        ProcessingOrderStatus.DocumentsReceivedFromState to {
                            matchesCommonActivityFeedVariables(it) &&
                                it.evidenceTransactionNumber == this.stateEntityNumber
                        },
                    )

                ProductType.INC ->
                    listOfNotNull<Pair<ProcessingOrderStatus, (ActivityFeedVariables) -> Boolean>>(
                        ProcessingOrderStatus.IncDocumentsReceivedFromState to {
                            matchesCommonActivityFeedVariables(it) &&
                                it.evidenceTransactionNumber == this.stateEntityNumber
                        },
                    )

                ProductType.NP ->
                    listOfNotNull<Pair<ProcessingOrderStatus, (ActivityFeedVariables) -> Boolean>>(
                        ProcessingOrderStatus.NonProfitDocumentsReceivedFromState to {
                            matchesCommonActivityFeedVariables(it) &&
                                it.evidenceTransactionNumber == this.stateEntityNumber
                        },
                    )

                else -> unsupportedProduct()
            }
        } else {
            emptyList()
        }

    // TODO: Assertions for manual filing cases (Salesforce requests)

    private fun unsupportedProduct(): Nothing {
        throw Exception("Product ${this.product.name} not supported by this test suite.")
    }

    override fun toString(): String {
        return "product=${this.product}, jurisdiction=${this.jurisdiction}, processingOrderId=${this.processingOrderId}"
    }

    companion object {
        const val UI_PATH_FOLDER_ID = 1L
        private val objectMapperForReadingFixtureFiles = ObjectMapper().findAndRegisterModules()
    }
}
