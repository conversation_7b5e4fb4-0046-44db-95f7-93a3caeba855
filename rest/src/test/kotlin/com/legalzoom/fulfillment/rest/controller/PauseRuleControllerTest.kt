package com.legalzoom.fulfillment.rest.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.launchdarkly.sdk.server.LDClient
import com.legalzoom.fulfillment.RestApplication
import com.legalzoom.fulfillment.rest.model.PauseRuleRequest
import com.legalzoom.fulfillment.rest.model.PauseRuleResponse
import com.legalzoom.fulfillment.rest.service.PauseRuleService
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.web.reactive.server.WebTestClient
import java.time.Instant
import java.util.UUID

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = [RestApplication::class])
@EmbeddedKafka
class PauseRuleControllerTest {
    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Autowired
    lateinit var webTestClient: WebTestClient

    @MockkBean
    lateinit var pauseRuleService: PauseRuleService

    @MockkBean
    lateinit var launchDarklyClient: LDClient

    companion object {
        private val RULE_ID = UUID.fromString("1f9f5c2e-73e6-4d4d-b01f-bf3d8cf31234")
        private const val PRODUCT = 101
        private const val STATE = "CA"
        private const val RA_TYPE = "STANDARD"
        private const val FILING_PHASE = "PHASE_1"
        private const val PAUSE_HOURS = 48
        private const val DESCRIPTION = "Test pause rule"
        private const val CREATED_BY = "<EMAIL>"
        private const val MODIFIED_BY = "<EMAIL>"
        private val CREATED_DATE = Instant.parse("2023-05-01T12:00:00Z")
        private val MODIFIED_DATE = Instant.parse("2023-05-02T15:30:00Z")

        private val REQUEST =
            PauseRuleRequest(
                product = PRODUCT,
                state = STATE,
                isExpedited = true,
                raType = RA_TYPE,
                filingPhase = FILING_PHASE,
                pauseHours = PAUSE_HOURS,
                enabled = true,
                description = DESCRIPTION,
            )

        private val RESPONSE =
            PauseRuleResponse(
                id = RULE_ID,
                product = PRODUCT,
                state = STATE,
                isExpedited = true,
                raType = RA_TYPE,
                filingPhase = FILING_PHASE,
                pauseHours = PAUSE_HOURS,
                enabled = true,
                description = DESCRIPTION,
                createdBy = CREATED_BY,
                createdDate = CREATED_DATE,
                modifiedBy = MODIFIED_BY,
                modifiedDate = MODIFIED_DATE,
            )
    }

    @Test
    fun testFindPauseRules() {
        every {
            pauseRuleService.find(PRODUCT, STATE, true, RA_TYPE, FILING_PHASE)
        } returns listOf(RESPONSE)

        webTestClient.get()
            .uri(
                "/api/rules?product={product}&state={state}&isExpedited=true&raType={raType}&filingPhase={filingPhase}",
                PRODUCT,
                STATE,
                RA_TYPE,
                FILING_PHASE,
            )
            .exchange()
            .expectStatus().isOk
            .expectBody()
            .jsonPath("$.length()").isEqualTo(1)
            .jsonPath("$[0].id").isEqualTo(RULE_ID.toString())
            .jsonPath("$[0].pause_hours").isEqualTo(PAUSE_HOURS)
            .jsonPath("$[0].enabled").isEqualTo(true)
            .jsonPath("$[0].description").isEqualTo(DESCRIPTION)
    }

    @Test
    fun testMatchPauseRuleFound() {
        every {
            pauseRuleService.findMatchingRule(PRODUCT, STATE, true, RA_TYPE, FILING_PHASE)
        } returns RESPONSE

        webTestClient.get()
            .uri(
                "/api/rules/match?product={product}&state={state}&isExpedited=true&raType={raType}&filingPhase={filingPhase}",
                PRODUCT,
                STATE,
                RA_TYPE,
                FILING_PHASE,
            )
            .exchange()
            .expectStatus().isOk
            .expectBody()
            .jsonPath("$.id").isEqualTo(RULE_ID.toString())
            .jsonPath("$.pause_hours").isEqualTo(PAUSE_HOURS)
            .jsonPath("$.description").isEqualTo(DESCRIPTION)
    }

    @Test
    fun testMatchPauseRuleNotFound() {
        every {
            pauseRuleService.findMatchingRule(PRODUCT, STATE, true, RA_TYPE, FILING_PHASE)
        } returns null

        webTestClient.get()
            .uri(
                "/api/rules/match?product={product}&state={state}&isExpedited=true&raType={raType}&filingPhase={filingPhase}",
                PRODUCT,
                STATE,
                RA_TYPE,
                FILING_PHASE,
            )
            .exchange()
            .expectStatus().isNotFound
            .expectBody()
            .jsonPath("$.error").isEqualTo("Not Found")
            .jsonPath("$.status").isEqualTo(404)
            .jsonPath("$.path").isEqualTo("/api/rules/match")
    }

    @Test
    fun testSavePauseRule() {
        every { pauseRuleService.save(REQUEST) } returns RESPONSE

        webTestClient.post()
            .uri("/api/rules")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(objectMapper.writeValueAsString(REQUEST))
            .exchange()
            .expectStatus().isOk
            .expectBody()
            .jsonPath("$.id").isEqualTo(RULE_ID.toString())
            .jsonPath("$.pause_hours").isEqualTo(PAUSE_HOURS)
            .jsonPath("$.enabled").isEqualTo(true)

        verify { pauseRuleService.save(REQUEST) }
    }

    @Test
    fun testUpdatePauseRule() {
        every { pauseRuleService.update(RULE_ID.toString(), REQUEST) } returns RESPONSE

        webTestClient.put()
            .uri("/api/rules/$RULE_ID")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(objectMapper.writeValueAsString(REQUEST))
            .exchange()
            .expectStatus().isOk
            .expectBody()
            .jsonPath("$.id").isEqualTo(RULE_ID.toString())
            .jsonPath("$.modified_by").isEqualTo(MODIFIED_BY)
    }

    @Test
    fun testDeletePauseRule() {
        every { pauseRuleService.softDelete(RULE_ID.toString()) } returns Unit

        webTestClient.delete()
            .uri("/api/rules/$RULE_ID")
            .exchange()
            .expectStatus().isOk

        verify { pauseRuleService.softDelete(RULE_ID.toString()) }
    }
}
