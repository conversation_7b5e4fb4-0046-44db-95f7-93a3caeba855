package com.legalzoom.fulfillment.rest.model

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant
import java.util.UUID

data class PauseRuleResponse(
    @JsonProperty("id")
    val id: UUID,
    @JsonProperty("product")
    val product: Int?,
    @JsonProperty("state")
    val state: String?,
    @JsonProperty("is_expedited")
    val isExpedited: Boolean?,
    @JsonProperty("ra_type")
    val raType: String?,
    @JsonProperty("filing_phase")
    val filingPhase: String,
    @JsonProperty("pause_hours")
    val pauseHours: Int,
    @JsonProperty("enabled")
    val enabled: <PERSON><PERSON><PERSON>,
    @JsonProperty("description")
    val description: String?,
    @JsonProperty("created_by")
    val createdBy: String?,
    @JsonProperty("created_date")
    val createdDate: Instant?,
    @JsonProperty("modified_by")
    val modifiedBy: String?,
    @JsonProperty("modified_date")
    val modifiedDate: Instant?,
)
