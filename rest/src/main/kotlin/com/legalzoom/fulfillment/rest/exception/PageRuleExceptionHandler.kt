package com.legalzoom.fulfillment.rest.exception

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

@ControllerAdvice
class PageRuleExceptionHandler {
    private val logger = LoggerFactory.getLogger(PageRuleExceptionHandler::class.java)

    @ExceptionHandler(PageRuleException::class)
    fun handlePageRuleException(e: PageRuleException): ResponseEntity<PageRuleExceptionResponse> {
        logger.warn("PageRuleException caught: ${e.message}", e)

        val response =
            PageRuleExceptionResponse(
                message = e.message ?: "No matching pause rule found",
            )

        return ResponseEntity(response, HttpStatus.NOT_FOUND)
    }
}
