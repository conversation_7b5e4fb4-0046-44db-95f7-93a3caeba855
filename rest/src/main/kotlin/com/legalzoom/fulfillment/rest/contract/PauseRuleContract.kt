package com.legalzoom.fulfillment.rest.contract

import com.legalzoom.fulfillment.rest.model.PauseRuleRequest
import com.legalzoom.fulfillment.rest.model.PauseRuleResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.MediaType.APPLICATION_JSON_VALUE
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam

@Tag(name = "PauseRule", description = "Pause Rule Management")
interface PauseRuleContract {
    @Operation(summary = "Retrieve all pause rules", description = "Get all the pause rules.")
    @GetMapping
    fun find(
        @RequestParam(required = false) product: Int?,
        @RequestParam(required = false) state: String?,
        @RequestParam(required = false) isExpedited: Boolean?,
        @RequestParam(required = false) raType: String?,
        @RequestParam(required = false) filingPhase: String?,
    ): List<PauseRuleResponse>

    @Operation(summary = "Create a new pause rule", description = "Create a new rule for pausing workflows.")
    @PostMapping
    fun save(
        @RequestBody pauseRuleRequest: PauseRuleRequest,
    ): PauseRuleResponse

    @Operation(summary = "Update an existing pause rule", description = "Update an existing pause rule.")
    @PutMapping("/{id}")
    fun update(
        @PathVariable id: String,
        @RequestBody pauseRuleRequest: PauseRuleRequest,
    ): PauseRuleResponse

    @Operation(summary = "Delete a pause rule", description = "Soft delete an existing pause rule.")
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String,
    )

    @Operation(
        summary = "Find matching pause rule",
        description = "Find a matching pause rule based on parameters. Returns 404 if no match found.",
    )
    @ApiResponse(
        responseCode = "200",
        description = "Matching pause rule found",
        content = [Content(mediaType = APPLICATION_JSON_VALUE, schema = Schema(implementation = PauseRuleResponse::class))],
    )
    @ApiResponse(
        responseCode = "404",
        description = "No matching pause rule found",
        content = [Content(mediaType = APPLICATION_JSON_VALUE)],
    )
    @GetMapping("/match")
    fun match(
        @RequestParam(required = false) product: Int?,
        @RequestParam(required = false) state: String?,
        @RequestParam(required = false) isExpedited: Boolean?,
        @RequestParam(required = false) raType: String?,
        @RequestParam(required = false) filingPhase: String?,
    ): PauseRuleResponse
}
