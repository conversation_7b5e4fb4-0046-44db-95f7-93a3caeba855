package com.legalzoom.fulfillment.rest.model

import com.legalzoom.fulfillment.domain.model.PauseRule

fun PauseRule.toResponse(): PauseRuleResponse =
    PauseRuleResponse(
        id = id!!,
        product = product,
        state = state,
        isExpedited = isExpedited,
        raType = raType,
        filingPhase = filingPhase,
        pauseHours = pauseHours,
        enabled = enabled,
        description = description,
        createdBy = createdBy,
        createdDate = createdDate,
        modifiedBy = modifiedBy,
        modifiedDate = modifiedDate,
    )

fun PauseRuleRequest.toEntity(): PauseRule =
    PauseRule(
        product = product,
        state = state,
        isExpedited = isExpedited,
        raType = raType,
        filingPhase = filingPhase,
        pauseHours = pauseHours,
        enabled = enabled,
        description = description,
    )
