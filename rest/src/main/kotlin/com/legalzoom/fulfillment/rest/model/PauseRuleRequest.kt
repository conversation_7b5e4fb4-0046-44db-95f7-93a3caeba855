package com.legalzoom.fulfillment.rest.model

import com.fasterxml.jackson.annotation.JsonProperty

data class PauseRuleRequest(
    @JsonProperty("product")
    val product: Int?,
    @JsonProperty("state")
    val state: String?,
    @JsonProperty("is_expedited")
    val isExpedited: Boolean?,
    @JsonProperty("ra_type")
    val raType: String?,
    @JsonProperty("filing_phase")
    val filingPhase: String,
    @JsonProperty("pause_hours")
    val pauseHours: Int,
    @JsonProperty("enabled")
    val enabled: Boolean = true,
    @JsonProperty("description")
    val description: String? = null,
)
