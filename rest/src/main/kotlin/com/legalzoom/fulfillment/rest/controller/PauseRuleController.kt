package com.legalzoom.fulfillment.rest.controller

import com.legalzoom.fulfillment.rest.contract.PauseRuleContract
import com.legalzoom.fulfillment.rest.exception.PageRuleException
import com.legalzoom.fulfillment.rest.model.PauseRuleRequest
import com.legalzoom.fulfillment.rest.model.PauseRuleResponse
import com.legalzoom.fulfillment.rest.service.PauseRuleService
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/rules")
class PauseRuleController(private val pauseRuleService: PauseRuleService) : PauseRuleContract {
    @GetMapping
    override fun find(
        @RequestParam(required = false) product: Int?,
        @RequestParam(required = false) state: String?,
        @RequestParam(required = false) isExpedited: Boolean?,
        @RequestParam(required = false) raType: String?,
        @RequestParam(required = false) filingPhase: String?,
    ): List<PauseRuleResponse> {
        return pauseRuleService.find(product, state, isExpedited, raType, filingPhase)
    }

    @PostMapping
    override fun save(
        @RequestBody pauseRuleRequest: PauseRuleRequest,
    ): PauseRuleResponse {
        return pauseRuleService.save(pauseRuleRequest)
    }

    @PutMapping("/{id}")
    override fun update(
        @PathVariable id: String,
        @RequestBody pauseRuleRequest: PauseRuleRequest,
    ): PauseRuleResponse {
        return pauseRuleService.update(id, pauseRuleRequest)
    }

    @DeleteMapping("/{id}")
    override fun delete(
        @PathVariable id: String,
    ) {
        pauseRuleService.softDelete(id)
    }

    @GetMapping("/match")
    override fun match(
        @RequestParam(required = false) product: Int?,
        @RequestParam(required = false) state: String?,
        @RequestParam(required = false) isExpedited: Boolean?,
        @RequestParam(required = false) raType: String?,
        @RequestParam(required = false) filingPhase: String?,
    ): PauseRuleResponse {
        return pauseRuleService.findMatchingRule(product, state, isExpedited, raType, filingPhase)
            ?: throw PageRuleException("No matching pause rule found for the given parameters")
    }
}
