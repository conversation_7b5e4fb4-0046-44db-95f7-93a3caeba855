package com.legalzoom.fulfillment.rest.exception

import com.legalzoom.fulfillment.answersapi.exception.NotFoundException
import com.legalzoom.fulfillment.answersapi.exception.TooManyMatchesException
import com.legalzoom.fulfillment.common.exception.MissingRequiredFieldException
import com.legalzoom.fulfillment.common.exception.ResourceNotFoundException
import com.legalzoom.fulfillment.service.exception.DocumentPlatformException
import com.legalzoom.fulfillment.service.exception.InvalidContentTypeException
import com.legalzoom.fulfillment.service.exception.InvalidOperationException
import org.slf4j.LoggerFactory
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.AccessDeniedException
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.client.HttpClientErrorException
import javax.persistence.EntityNotFoundException
import javax.persistence.NoResultException
import javax.validation.ConstraintViolationException

@ControllerAdvice
class ExceptionHandlerControllerAdvice {
    private val logger = LoggerFactory.getLogger(javaClass)

    @ExceptionHandler(
        ConstraintViolationException::class,
        HttpClientErrorException.BadRequest::class,
        MissingServletRequestParameterException::class,
        MissingRequiredFieldException::class,
        IllegalArgumentException::class,
        InvalidOperationException::class,
        InvalidContentTypeException::class,
        TooManyMatchesException::class,
    )
    fun constraintViolationException(e: Exception): ResponseEntity<ErrorResponse> {
        val message =
            when (e.message) {
                "", " ", null -> "Bad request"
                else -> {
                    e.message
                }
            }
        return generateErrorResponse(HttpStatus.BAD_REQUEST, message!!, e)
    }

    @ExceptionHandler(
        MethodArgumentNotValidException::class,
        ParameterArgumentNotValidException::class,
    )
    fun methodArgumentNotValidException(ex: Exception): ResponseEntity<ErrorResponse> {
        val errors: MutableMap<String, String> = HashMap()
        if (ex is MethodArgumentNotValidException) {
            ex.bindingResult.allErrors.forEach { error ->
                val fieldName = (error as FieldError).field
                val errorMessage: String? = error.getDefaultMessage()
                if (errorMessage != null) {
                    errors[fieldName] = errorMessage
                }
            }
            return generateErrorResponse(HttpStatus.BAD_REQUEST, errors.toString(), ex)
        } else {
            return generateErrorResponse(HttpStatus.BAD_REQUEST, "Bad request", ex)
        }
    }

    /*
        @ExceptionHandler(AuthorizationException::class)
        fun unauthorizedException(e: Exception): ResponseEntity<ErrorResponse> {
            return generateErrorResponse(HttpStatus.FORBIDDEN, "You are not authorized to do this operation", e)
        }
     */

    @ExceptionHandler(AccessDeniedException::class)
    fun forbiddenException(e: Exception): ResponseEntity<ErrorResponse> {
        return generateErrorResponse(HttpStatus.UNAUTHORIZED, "You are not allowed to do this operation", e)
    }

    @ExceptionHandler(
        EntityNotFoundException::class,
        ResourceNotFoundException::class,
        NoSuchElementException::class,
        NoResultException::class,
        EmptyResultDataAccessException::class,
        IndexOutOfBoundsException::class,
        KotlinNullPointerException::class,
        NotFoundException::class,
    )
    fun notFoundException(e: Exception): ResponseEntity<ErrorResponse> {
        return generateErrorResponse(HttpStatus.NOT_FOUND, e.message ?: "Resource not found", e)
    }

    @ExceptionHandler(
        DocumentPlatformException::class,
        Exception::class,
    )
    fun internalServerErrorException(e: Exception): ResponseEntity<ErrorResponse> {
        // Let it bubble up to the correct handler as we want PageRuleException to be handled differently
        if (e is PageRuleException) throw e
        return generateErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, e.message ?: "Resource not found", e)
    }

    private fun generateErrorResponse(
        status: HttpStatus,
        message: String,
        e: Exception,
    ): ResponseEntity<ErrorResponse> {
        e.let { logger.warn(e.stackTraceToString()) }
        return ResponseEntity(ErrorResponse(status, message), status)
    }
}
