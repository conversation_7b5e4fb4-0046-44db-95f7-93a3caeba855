package com.legalzoom.fulfillment.rest.service

import com.legalzoom.fulfillment.domain.repository.PauseRuleRepository
import com.legalzoom.fulfillment.rest.model.PauseRuleRequest
import com.legalzoom.fulfillment.rest.model.PauseRuleResponse
import com.legalzoom.fulfillment.rest.model.toEntity
import com.legalzoom.fulfillment.rest.model.toResponse
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class PauseRuleService(private val pauseRuleRepository: PauseRuleRepository) {
    fun find(
        product: Int?,
        state: String?,
        isExpedited: Boolean?,
        raType: String?,
        filingPhase: String?,
    ): List<PauseRuleResponse> {
        val rules = pauseRuleRepository.findAll()
        return rules.map { it.toResponse() }
    }

    fun save(pauseRuleRequest: PauseRuleRequest): PauseRuleResponse {
        val pauseRule = pauseRuleRequest.toEntity()
        val savedRule = pauseRuleRepository.save(pauseRule)
        return savedRule.toResponse()
    }

    @Transactional
    fun update(
        id: String,
        pauseRuleRequest: PauseRuleRequest,
    ): PauseRuleResponse {
        val existingRule =
            pauseRuleRepository.findById(UUID.fromString(id))
                .orElseThrow { IllegalArgumentException("Pause rule with ID $id not found") }

        existingRule.product = pauseRuleRequest.product
        existingRule.state = pauseRuleRequest.state
        existingRule.isExpedited = pauseRuleRequest.isExpedited
        existingRule.raType = pauseRuleRequest.raType
        existingRule.filingPhase = pauseRuleRequest.filingPhase
        existingRule.pauseHours = pauseRuleRequest.pauseHours
        existingRule.enabled = pauseRuleRequest.enabled
        existingRule.description = pauseRuleRequest.description

        return pauseRuleRepository.save(existingRule).toResponse()
    }

    @Transactional
    fun softDelete(id: String) {
        val rule =
            pauseRuleRepository.findById(UUID.fromString(id))
                .orElseThrow { IllegalArgumentException("Rule not found") }
        rule.enabled = false
        pauseRuleRepository.save(rule)
    }

    fun findMatchingRule(request: PauseRuleRequest): PauseRuleResponse? {
        val matches =
            pauseRuleRepository.findMatchingRules(
                product = request.product,
                state = request.state,
                isExpedited = request.isExpedited,
                raType = request.raType,
                filingPhase = request.filingPhase,
            )
        return matches.firstOrNull()?.toResponse()
    }

    fun findMatchingRule(
        product: Int?,
        state: String?,
        isExpedited: Boolean?,
        raType: String?,
        filingPhase: String?,
    ): PauseRuleResponse? {
        val matches =
            pauseRuleRepository.findMatchingRules(
                product = product,
                state = state,
                isExpedited = isExpedited,
                raType = raType,
                filingPhase = filingPhase!!,
            )
        return matches.firstOrNull()?.toResponse()
    }
}
