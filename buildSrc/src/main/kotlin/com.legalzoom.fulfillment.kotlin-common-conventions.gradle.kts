import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent.FAILED
import org.gradle.api.tasks.testing.logging.TestLogEvent.PASSED
import org.gradle.api.tasks.testing.logging.TestLogEvent.SKIPPED
import org.gradle.jvm.toolchain.JvmVendorSpec.AMAZON
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.plugin.getKotlinPluginVersion
import org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES

plugins {
    `jacoco`
    `jvm-test-suite`
    `project-report`
    id("org.gradle.test-retry")
    id("org.jlleitschuh.gradle.ktlint")
    id("org.springframework.boot")
    kotlin("jvm")
    kotlin("kapt")
    kotlin("plugin.jpa")
    kotlin("plugin.spring")
}

group = "com.legalzoom.fulfillment"
version = "0.0.1-SNAPSHOT"

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
        vendor.set(AMAZON)
    }
}

configurations {
    all {
        exclude(group = "com.sun.xml.bind")
        exclude(group = "com.vaadin.external.google", module = "android-json")
        exclude(group = "commons-logging")
        exclude(group = "javax.activation")
        exclude(group = "javax.annotation")
        exclude(group = "javax.mail")
        exclude(group = "javax.persistence")
        exclude(group = "javax.servlet")
        exclude(group = "javax.validation")
        exclude(group = "javax.xml.bind")
        exclude(group = "org.aspectj", module = "aspectjrt")
        exclude(group = "org.jboss.spec.javax.transaction")
    }
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven { url = uri("https://artifactory.legalzoom.com/artifactory/public/") }
    maven { url = uri("https://artifactory.legalzoom.com/artifactory/releases/") }
}

dependencies {
    val camundaVersion = "7.18.2-ee"
    implementation("ch.qos.logback.contrib:logback-jackson")
    implementation("ch.qos.logback.contrib:logback-json-classic")
    implementation("com.auth0:java-jwt")
    implementation("com.brsanthu:migbase64")
    implementation("com.docusign:docusign-esign-java")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-csv")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.google.guava:guava")
    implementation("com.launchdarkly:launchdarkly-java-server-sdk")
    implementation("com.legalzoom:api-spring-starter")
    implementation("com.networknt:json-schema-validator")
    implementation("com.openhtmltopdf:openhtmltopdf-pdfbox")
    implementation("com.openhtmltopdf:openhtmltopdf-slf4j")
    implementation("com.slack.api:slack-api-client")
    implementation("io.github.resilience4j:resilience4j-kotlin")
    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations")
    implementation("io.opentelemetry:opentelemetry-api")
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation("io.swagger.core.v3:swagger-annotations")
    implementation("org.apache.commons:commons-lang3")
    implementation("org.apache.kafka:kafka-streams")
    implementation("org.apache.oltu.oauth2:org.apache.oltu.oauth2.client")
    implementation("org.glassfish.jersey.media:jersey-media-multipart")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j")
    implementation("org.jsoup:jsoup")
    implementation("org.opensearch.client:opensearch-rest-high-level-client")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-integration")
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-web-services")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-websocket")
    implementation("org.springframework.cloud:spring-cloud-starter-bootstrap")
    implementation("org.springframework.cloud:spring-cloud-starter-circuitbreaker-reactor-resilience4j")
    implementation("org.springframework.cloud:spring-cloud-starter-vault-config")
    implementation("org.springframework.data:spring-data-commons")
    implementation("org.springframework.integration:spring-integration-kafka")
    implementation("org.springframework.kafka:spring-kafka")
    implementation("org.springframework.security:spring-security-core")
    implementation("software.amazon.awssdk:s3")
    implementation("software.amazon.awssdk:ses")
    implementation("software.amazon.awssdk:sso")
    implementation("software.amazon.awssdk:sts")
    implementation("software.amazon.awssdk:sns")
    implementation("software.amazon.jdbc:aws-advanced-jdbc-wrapper:2.3.2")
    implementation(enforcedPlatform("com.google.guava:guava-bom:31.1-jre"))
    implementation(enforcedPlatform("org.jetbrains.kotlin:kotlin-bom:${getKotlinPluginVersion()}"))
    implementation(enforcedPlatform("org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.7.3"))
    implementation(enforcedPlatform(BOM_COORDINATES))
    implementation(platform("com.azure.spring:spring-cloud-azure-dependencies:4.4.1"))
    implementation(platform("io.opentelemetry:opentelemetry-bom:1.28.0"))
    implementation(platform("org.camunda.bpm:camunda-bom:$camundaVersion"))
    implementation(platform("org.springframework.cloud:spring-cloud-dependencies:2021.0.5"))
    implementation(platform("org.testcontainers:testcontainers-bom:1.17.6"))
    implementation(platform("software.amazon.awssdk:bom:2.18.29"))
    kapt("org.hibernate.validator:hibernate-validator-annotation-processor")
    kapt("org.springframework.boot:spring-boot-configuration-processor")
    kapt(enforcedPlatform(BOM_COORDINATES))
    runtimeOnly(enforcedPlatform(BOM_COORDINATES))
    testImplementation("com.ninja-squad:springmockk")
    testImplementation("com.squareup.okhttp3:mockwebserver")
    testImplementation("io.projectreactor:reactor-test")
    testImplementation("org.awaitility:awaitility-kotlin")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.integration:spring-integration-test")
    testImplementation("org.springframework.kafka:spring-kafka-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    constraints {
        val logbackVersion = "0.1.5"
        val openhtmlVersion = "1.0.10"
        val pdfboxVersion = "2.0.27"
        val springdocVersion = "1.6.14"
        implementation("ch.qos.logback.contrib:logback-jackson:$logbackVersion")
        implementation("ch.qos.logback.contrib:logback-json-classic:$logbackVersion")
        implementation("com.auth0:java-jwt:4.2.1")
        implementation("com.brsanthu:migbase64:2.2")
        implementation("com.docusign:docusign-esign-java:3.21.0")
        implementation("com.launchdarkly:launchdarkly-java-server-sdk:6.0.0")
        implementation("com.legalzoom:api-spring-starter:1.11.102")
        implementation("com.networknt:json-schema-validator:1.0.74")
        implementation("com.ninja-squad:springmockk:3.1.2")
        implementation("com.openhtmltopdf:openhtmltopdf-core:$openhtmlVersion")
        implementation("com.openhtmltopdf:openhtmltopdf-pdfbox:$openhtmlVersion")
        implementation("com.openhtmltopdf:openhtmltopdf-slf4j:$openhtmlVersion")
        implementation("com.slack.api:slack-api-client:1.27.2")
        implementation("com.vladmihalcea:hibernate-types-55:2.20.0")
        implementation("commons-io:commons-io:2.11.0")
        implementation("io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:1.20.2")
        implementation("io.swagger.core.v3:swagger-annotations:2.2.7")
        implementation("org.apache.oltu.oauth2:org.apache.oltu.oauth2.client:1.0.2")
        implementation("org.apache.pdfbox:fontbox:$pdfboxVersion")
        implementation("org.apache.pdfbox:pdfbox:$pdfboxVersion")
        implementation("org.apache.pdfbox:xmpbox:$pdfboxVersion")
        implementation("org.camunda.bpm:camunda-engine:$camundaVersion")
        implementation("org.camunda.bpm:camunda-engine-rest-openapi:$camundaVersion")
        implementation("org.jsoup:jsoup:1.15.3")
        implementation("org.opensearch.client:opensearch-rest-high-level-client:2.4.0")
        implementation("org.springdoc:springdoc-openapi-kotlin:$springdocVersion")
        implementation("org.springdoc:springdoc-openapi-security:$springdocVersion")
        implementation("org.springdoc:springdoc-openapi-ui:$springdocVersion")
        implementation("org.webjars.npm:htmx.org:1.8.5")
        implementation("org.webjars:swagger-ui:4.15.5")
    }
}

testing {
    suites {
        val test by getting(JvmTestSuite::class) {
            useJUnitJupiter()

            targets {
                all {
                    testTask.configure {
                        jvmArgs("--add-opens", "java.base/java.time=ALL-UNNAMED")
                        environment("TESTCONTAINERS_REUSE_ENABLE", true)
                        retry {
                            maxRetries.set(3)
                            maxFailures.set(10)
                            filter {
                                includeAnnotationClasses.add("org.springframework.boot.test.context.SpringBootTest")
                            }
                        }
                    }
                }
            }
        }
    }
}

kotlin {
    compilerOptions {
        freeCompilerArgs.addAll("-Xjsr305=strict")
        jvmTarget.set(JvmTarget.JVM_17)
    }
}

tasks {
    withType<DependencyReportTask>().configureEach {
        outputs.upToDateWhen { false }
        outputFile = file("dependencies.txt")
    }

    withType<JacocoReport>().configureEach {
        enabled = true
        dependsOn(tasks.test) // tests are required to run before generating the report
        reports {
            xml.required = true
            csv.required = false
            html.required = false
        }
    }

    check {
        dependsOn(jacocoTestReport) // running check task (which is part of build) runs test coverage report
    }

    withType<Test>().configureEach {
        maxHeapSize = "3g"
        systemProperty("spring.cloud.kubernetes.enabled", false)
        systemProperty("spring.cloud.loadbalancer.enabled", false)
        systemProperty("spring.cloud.vault.enabled", false)
        testLogging {
            events(FAILED, PASSED, SKIPPED)
            showExceptions = true
            showCauses = true
            showStackTraces = true
            showStandardStreams = true
            exceptionFormat = TestExceptionFormat.FULL
        }
        // See https://github.com/gradle/kotlin-dsl/issues/836
        addTestListener(object : TestListener {
            override fun beforeSuite(suite: TestDescriptor) {}
            override fun beforeTest(testDescriptor: TestDescriptor) {}
            override fun afterTest(testDescriptor: TestDescriptor, result: TestResult) {}

            override fun afterSuite(suite: TestDescriptor, result: TestResult) {
                if (suite.parent == null) { // root suite
                    logger.lifecycle("----")
                    logger.lifecycle("Test result: ${result.resultType}")
                    logger.lifecycle("Test summary: ${result.testCount} tests, ${result.successfulTestCount} succeeded, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped")
                }
            }
        })
    }
}
