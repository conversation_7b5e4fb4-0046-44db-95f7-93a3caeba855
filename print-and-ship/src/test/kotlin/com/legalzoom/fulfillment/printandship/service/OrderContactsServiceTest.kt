package com.legalzoom.fulfillment.printandship.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.ordercontacts.OrdersContactsApi
import com.legalzoom.fulfillment.service.service.OrderContactsService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import reactor.core.publisher.Mono

@ExtendWith(MockKExtension::class)
class OrderContactsServiceTest {
    @MockK
    private lateinit var orderContactsApi: OrdersContactsApi

    @InjectMockKs
    private lateinit var orderContactsService: OrderContactsService

    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    companion object {
        const val PROCESSING_ORDER_ID = 513427530
        const val ORDER_ID = 315196
        const val CUSTOMER_ID = "LZTestCustomer"
    }

    private fun mockOrderContactsApi() {
        val resource = ClassPathResource("../orderContactsResponse.json", javaClass).file
        val response: GetOrderContactsResponse = objectMapper.readValue<GetOrderContactsResponse>(resource)

        every {
            orderContactsApi.coreOrdersOrderIdContactsGet(
                ORDER_ID,
                "1.0",
                CUSTOMER_ID,
                true,
            )
        } returns Mono.just(response)
    }

    @BeforeEach
    fun setup() {
        mockOrderContactsApi()
    }

    @Test
    fun `should return a list of contacts`() {
        val contacts = orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        assert(
            contacts.any {
                it.contactType == ContactType.Primary
            },
        )
        assert(
            contacts.any {
                it.contactType == ContactType.Shipping
            },
        )
    }
}
