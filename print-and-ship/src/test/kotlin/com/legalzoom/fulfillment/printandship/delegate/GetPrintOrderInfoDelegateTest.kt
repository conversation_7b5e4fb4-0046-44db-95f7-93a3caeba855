package com.legalzoom.fulfillment.printandship.delegate

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailHistoryResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.printandship.service.BusinessEntityService
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.Constants
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintConfig
import com.legalzoom.fulfillment.printandshipapi.model.ShippingInformationDto
import com.legalzoom.fulfillment.service.data.ValidationResult
import com.legalzoom.fulfillment.service.enumeration.Cp1CustomerShipMethod
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.LivingTrust
import com.legalzoom.fulfillment.service.enumeration.ProductType.PowerOfAttorney
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.document.getEPTrustName
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.GetAnswersPayload
import com.legalzoom.fulfillment.workflow.delegate.printandship.GetPrintOrderInfoDelegate
import com.legalzoom.fulfillment.workflow.exception.PrintMaintenanceException
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions
import org.assertj.core.api.AssertionsForClassTypes.assertThat
import org.camunda.bpm.engine.HistoryService
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.delegate.BpmnError
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.LoggerFactory
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import reactor.core.publisher.Mono
import java.time.Clock

@ExtendWith(MockKExtension::class)
class GetPrintOrderInfoDelegateTest() {
    @MockK
    private lateinit var execution: DelegateExecution

    @MockK
    private lateinit var historyService: HistoryService

    @MockK
    private lateinit var runtimeService: RuntimeService

    @MockK
    private lateinit var documentListService: DocumentListService

    @MockK
    private lateinit var completedOrderDetailService: CompletedOrderDetailService

    @MockK
    private lateinit var orderContactsService: OrderContactsService

    @MockK
    private lateinit var ordersApiService: OrdersApiService

    @MockK
    private lateinit var ordersOrderItemsApi: OrdersOrderItemsApiService

    @MockK
    private lateinit var businessEntityService: BusinessEntityService

    @MockK
    private lateinit var processingOrderService: ProcessingOrderService

    @MockK
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockK
    private lateinit var featureToggleService: FeatureToggleService

    @MockK
    private lateinit var productsApi: ProductsApi

    @MockK
    private lateinit var clock: Clock

    @MockK
    private lateinit var fulfillmentEventService: FulfillmentEventService

    @InjectMockKs
    private lateinit var delegate: GetPrintOrderInfoDelegate

    private val logger = LoggerFactory.getLogger(javaClass)

    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    companion object {
        const val PROCESSING_ORDER_ID = *********
        const val REQUEST_ID = 54321
        const val CUSTOMER_ID = "121591"
        const val TWO_DAY_SHIPPING_METHOD_ID = 8
        const val PROCESS_ID = 2
        const val ORDER_ID = 70881571
        const val PACKAGE_CONFIGURATION_ID = 7820
        const val ENTITY_NAME = "Test Company LLC"
        const val CARDINALITY = 3
        const val EXECUTION_ID = "123-abc"
        private val JURISDICTION = State.fromId(1)!!.abbreviation
        private val printDocumentList =
            listOf(
                PrintDocumentInfo("documentId1", "doc1.pdf", PrintConfig.LZDOC1, "Articles Filed"),
                PrintDocumentInfo("documentId2", "doc2.pdf", PrintConfig.LZDOC2, "Articles Filed"),
            )
        private val printDocumentIdList =
            listOf(
                PrintDocumentInfo("documentId1", null, null, null),
                PrintDocumentInfo("documentId2", null, null, null),
            )
        private val date = LocalDateTime.parse("2024-01-01T12:01:00")

        @JvmStatic
        fun parameters() =
            listOf(
                arguments(
                    LivingTrust.processId,
                    false,
                ),
                arguments(
                    LivingTrust.processId,
                    true,
                ),
                arguments(
                    PowerOfAttorney.processId,
                    true,
                ),
            )
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
        mockDocumentListService()
        mockBusinessEntityService()
        mockCompletedOrderDetailService()
        mockProcessingOrderService()
        mockOrdersApiService()
        mockOrderContactsService()
        mockQuestionnaireAnswerService()
        mockFeatureToggleService()
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles")) } returns date
        every {
            clock.instant()
        } returns date.toDate().toInstant()
        every {
            fulfillmentEventService.send(any())
        } returns Unit

        every {
            execution.id
        } returns EXECUTION_ID
        mockProductsApi()
    }

    private fun mockProductsApi() {
        val json = ClassPathResource("../productsApiPostOptionResponse.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                "1.0",
                CUSTOMER_ID,
                true,
            )
        } returns Mono.just(response)
    }

    private fun mockFeatureToggleService() {
        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false
    }

    private fun mockQuestionnaireAnswerService() {
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), match { it == AnswerSource.AnswerBank })
        } returns
            mockk<GetAnswersPayload>().also {
                every { it.entityName } returns ENTITY_NAME
            }

        every {
            questionnaireAnswerService.getAnswerVersionForUserOrderId(
                any(),
                any(),
                match { it == AnswerSource.AnswerBank },
            )
        } returns
            mockk<GetQuestionnaireAnswerResponse>().also {
                every { it.questionnaireFieldGroupAnswers?.fieldAnswers } returns
                    listOf(
                        mockk {
                            every { fieldName } returns "grantor_first"
                            every { fieldValue } returns "Grantor Full Name"
                        },
                    )
            }
    }

    private fun mockOrderContactsService() {
        val json = ClassPathResource("../orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns response.contacts!!
    }

    private fun mockDocumentListService(documentInfoList: List<PrintDocumentInfo> = printDocumentList) {
        every {
            documentListService.getDefaultDocumentListWithConfig(any(), "viatech", any(), CUSTOMER_ID)
        } returns documentInfoList

        every {
            documentListService.getPrintDocumentInfoByIds(
                match {
                    it.any { documentInfo -> documentInfo.documentId == "documentId1" } &&
                        it.any { documentInfo -> documentInfo.documentId == "documentId2" }
                },
                any(),
                any(),
                match { it == PROCESSING_ORDER_ID },
                any(),
            )
        } returns documentInfoList
    }

    private fun mockOrdersApiService() {
        val json = ClassPathResource("../ordersApiResponse.json", javaClass).file

        val response = objectMapper.readValue<GetOrderResponse>(json)

        every {
            ordersApiService.getOrders(ORDER_ID, null, null, "1.0", CUSTOMER_ID, true)
        } returns response
    }

    private fun mockShippingInformationService() {
        val json = ClassPathResource("../orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns response.contacts!!
    }

    private fun mockBusinessEntityService() {
        val json = ClassPathResource("../businessEntityResponse.json", javaClass).file
        val response = objectMapper.readValue<GetEntityByEntityIdResponse>(json)
        every {
            businessEntityService.getBusinessEntityResponseByProcessingOrderId(any())
        } returns response
    }

    private fun mockCompletedOrderDetailService(download: Boolean = true) {
        val json = ClassPathResource("../completedOrderDetailResponse.json", javaClass).file
        var response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)
        if (!download) {
            response =
                response.apply {
                    this.completedOrderDetail!!.shipMethodId = Cp1CustomerShipMethod.DOWNLOAD.Id
                    this.completedOrderDetail!!.shipMethod = com.legalzoom.api.model.processingorder.ShipMethod.DOWNLOAD
                }
        }
        every {
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(any(), any())
        } returns response
    }

    fun mockProcessingOrderService(
        processId: Int = 2,
        isRevised: Boolean = false,
    ) {
        var revisionCount = 0
        if (processId in setOf(3, 6, 9, 11)) {
            if (isRevised) {
                revisionCount = 2
            }
        }
        every {
            processingOrderService.getProcessingOrder(any())
        } returns
            ProcessingOrderDto().revisionCount(revisionCount)
                .customerId(CUSTOMER_ID.toInt())
    }

    @Test
    fun `should load all null variables`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockProcessingOrderService(PROCESS_ID, false)

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        Assertions.assertThat(delegateVariables.captured.shipMethod).isEqualTo("FEDEX 2ND DAY")
        Assertions.assertThat(delegateVariables.captured.printDocumentList).isEqualTo(printDocumentList)
        Assertions.assertThat(delegateVariables.captured.shippingInformation?.country).isEqualTo("USA")
        Assertions.assertThat(delegateVariables.captured.kitRequired).isEqualTo(true)
        Assertions.assertThat(delegateVariables.captured.isPrintingRequired).isEqualTo(true)
        Assertions.assertThat(delegateVariables.captured.jurisdiction).isEqualTo(JURISDICTION)
    }

    @Test
    fun `should modify the printDocumentList if not reprint`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                printDocumentList = printDocumentIdList
                shipMethod = "Unmodified Ship Method"
                kitRequired = false
            }

        mockDocumentListService()
        mockShippingInformationService()
        mockCompletedOrderDetailService()

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.shipMethod == "Unmodified Ship Method")
        assert(delegateVariables.captured.printDocumentList == printDocumentList)
        assert(delegateVariables.captured.shippingInformation!!.specialInstructions == "")
        assert(delegateVariables.captured.kitRequired == false)
    }

    @Test
    fun `should assign printRequestId if it is set to empty string`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = ""
                processId = PROCESS_ID
            }

        mockDocumentListService()
        mockShippingInformationService()
        mockCompletedOrderDetailService()

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(!delegateVariables.captured.printRequestId.isNullOrEmpty())
        // Assert printRequestId is a UUID
        assert(
            delegateVariables.captured.printRequestId!!.matches(
                Regex("[a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"),
            ),
        )
    }

    @Test
    fun `should propagate errors on getting info`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockDocumentListService()
        mockOrdersApiService()
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } throws Exception("Error getting shipping info")

        assertThrows<Exception> {
            delegate.execute(execution)
        }
    }

    @Test
    fun `throws exception on null contact info`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockDocumentListService()
        mockOrdersApiService()
        var json = ClassPathResource("../orderContactsResponse.json", javaClass).file
        var response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns
            response.contacts!!.onEach {
                it.city = null
            }

        var variables = slot<Variables>()
        justRun { execution.variables = capture(variables) }

        assertThrows<BpmnError> {
            delegate.execute(execution)
        }
        if (variables.isCaptured) {
            Assertions.assertThat(variables.captured.validationError).isTrue()
            Assertions.assertThat(variables.captured.validationErrors).hasSize(1)
            Assertions.assertThat(
                variables.captured.validationErrors!!.first().message,
            ).isEqualTo("Error with customer shipping address. Missing required fields: city.")
        } else {
            Assertions.fail("Variables not captured")
        }

        // Test for missing shipping only
        json = ClassPathResource("../orderContactsResponse.json", javaClass).file
        response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns response.contacts!!.filter { it.contactType != ContactType.Shipping }

        variables = slot<Variables>()
        justRun { execution.variables = capture(variables) }

        assertThrows<BpmnError> {
            delegate.execute(execution)
        }
        if (variables.isCaptured) {
            Assertions.assertThat(variables.captured.validationError).isTrue()
            Assertions.assertThat(variables.captured.validationErrors).hasSize(2)
            Assertions.assertThat(variables.captured.validationErrors!![1].message)
                .isEqualTo("Missing shipping address for customer.")
        } else {
            Assertions.fail("Expected a missing shipping contact to throw an exception.")
        }
    }

    @Test
    fun `empty document list should throw throw Biz Error`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockDocumentListService(emptyList())
        mockShippingInformationService()

        val variables = slot<Variables>()
        justRun { execution.variables = capture(variables) }

        assertThrows<BpmnError> {
            delegate.execute(execution)
        }

        if (variables.isCaptured) {
            Assertions.assertThat(variables.captured.validationError).isTrue()
            Assertions.assertThat(variables.captured.validationErrors).hasSize(1)
            Assertions.assertThat(variables.captured.validationErrors!!.first().message)
                .isEqualTo("No printable documents found.")
        } else {
            Assertions.fail("Variables not captured")
        }
        verify(exactly = 1) {
            fulfillmentEventService.send(
                match { (it.data as ValidationResult).errors!!.first().message == "No printable documents found." },
            )
        }
    }

    @Test
    fun `should return with printingRequired false without physical shipMethod`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockCompletedOrderDetailService(download = false)

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.isPrintingRequired == false)
        assert(delegateVariables.captured.shipMethod == null)
        assert(delegateVariables.captured.printDocumentList == null)
        assert(delegateVariables.captured.shippingInformation == null)
    }

    @Test
    fun `US Territory null country order`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockDocumentListService()
        val json = ClassPathResource("../orderContactsResponsePR.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns response.contacts!!
        mockCompletedOrderDetailService()

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.shippingInformation!!.country == "USA")
        assert(delegateVariables.captured.shippingInformation!!.state == "PR")
        assert(delegateVariables.captured.shipMethod == Constants.USPS_SHIPPING)
    }

    private fun countryNameToCountryCode() =
        listOf(
            // Country name, expected country code
            Arguments.of("United States of America", "USA"),
            Arguments.of("United States", "USA"),
            Arguments.of("USA", "USA"),
            Arguments.of("US", "US"),
            Arguments.of("United Kingdom", "GBR"),
            // default
            Arguments.of("", "USA"),
        )

    @ParameterizedTest
    @MethodSource("countryNameToCountryCode")
    fun `converts country name to ISO3166-1_a-2 country code`(
        testCountryName: String,
        expectedCountryCode: String,
    ) {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                jurisdiction = JURISDICTION
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        val json = ClassPathResource("../orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns
            response.contacts!!.onEach {
                it.country = testCountryName
            }

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.shippingInformation!!.country == expectedCountryCode)
    }

    @Test
    fun `should gen new requestId, address and clear doclist on retry`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                disposition = FulfillmentDisposition.Retry.value
                shippingInformation =
                    ShippingInformationDto(
                        "Name",
                        "Firm",
                        "Address1",
                        "Address2",
                        "City",
                        "State",
                        "Zip",
                        "Country",
                        "Contact",
                        "Special",
                    )
                printRequestId = "Existing Request ID"
                printDocumentList =
                    listOf(
                        PrintDocumentInfo(
                            "oldId",
                            "oldDoc.pdf",
                            PrintConfig.LZDOC1,
                            "Articles Filed",
                        ),
                    )
            }

        mockDocumentListService()
        mockShippingInformationService()
        mockCompletedOrderDetailService()
        mockOrdersApiService()

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.printRequestId != "Existing Request ID")
        assert(delegateVariables.captured.printDocumentList == printDocumentList)
        assert(delegateVariables.captured.shippingInformation!!.addressLine1 == "1542 11th St")
    }

    @Test
    fun `always entityName fetched from answer bank`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                entityName = "From workflow"
            }

        mockDocumentListService()
        mockShippingInformationService()
        mockCompletedOrderDetailService()
        mockOrdersApiService()

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        delegateVariables.clear()

        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                entityName = null
            }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(
                match { it == PROCESSING_ORDER_ID },
                any(),
                AnswerSource.AnswerBank,
            )
        } returns
            mockk<GetAnswersPayload>().also {
                every { it.entityName } returns "From questionnaire"
            }
        delegate.execute(execution)

        assert(delegateVariables.captured.entityName == "From questionnaire")
        assert(delegateVariables.captured.shippingInformation?.firm == "From questionnaire")

        delegateVariables.clear()

        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                entityName = null
            }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(match { it == PROCESSING_ORDER_ID }, any())
        } throws NullPointerException("Not in fieldAnswer")

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(
                match { it == PROCESSING_ORDER_ID },
                any(),
                match { it == AnswerSource.AnswerBank },
            )
        } returns
            mockk<GetAnswersPayload>().also {
                every { it.entityName } returns "From AnswerBank"
            }

        delegate.execute(execution)

        assert(delegateVariables.captured.entityName == "From AnswerBank")
        assert(delegateVariables.captured.shippingInformation?.firm == "From AnswerBank")
        delegateVariables.clear()

        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                entityName = null
            }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(match { it == PROCESSING_ORDER_ID }, any())
        } throws NullPointerException("Not in FieldAnswer")

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(
                match { it == PROCESSING_ORDER_ID },
                any(),
                match { it == AnswerSource.AnswerBank },
            )
        } throws NullPointerException("Not in AnswerBank")

        val json = ClassPathResource("../completedOrderDetailResponse.json", javaClass).file
        val response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)

        every {
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(PROCESSING_ORDER_ID, CUSTOMER_ID)
        } returns response.apply { this.completedOrderDetail!!.entityName = "From COD" }

        delegate.execute(execution)
        assert(delegateVariables.captured.entityName == "From COD")
        assert(delegateVariables.captured.shippingInformation?.firm == "From COD")
    }

    @Test
    fun `sets maintenanceDelayTimer to PT1H if printHoldAreaEnabled is true`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns true

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.maintenanceDelayTimer == "PT1H")
    }

    private fun testDates(): List<Pair<String, String>> {
        return mutableListOf(
            // Start date, expected Date (next Sunday of 3rd weekend).
            // Monday first day of month
            Pair("2024-01-19T21:01:00", "2024-01-21T21:00:00"),
            // Friday
            Pair("2024-03-15T21:01:00", "2024-03-17T21:00:00"),
            // Saturday
            Pair("2024-06-14T21:01:00", "2024-06-16T21:00:00"),
            // Sunday
            Pair("2024-09-13T21:01:00", "2024-09-15T21:00:00"),
        )
    }

    @ParameterizedTest
    @MethodSource("testDates")
    fun `sets maintenanceDelayTimer to the next Monday morning if within 3rd weekend window`(dates: Pair<String, String>) {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles")) } returns LocalDateTime.parse(dates.first)
    }

    @ParameterizedTest
    @MethodSource("testDates")
    fun `REPRINT throws PrintMaintenanceDelayException if within 3rd weekend window`(dates: Pair<String, String>) {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                action = "REPRINT"
            }

        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles")) } returns LocalDateTime.parse(dates.first)

        assertThrows<PrintMaintenanceException> {
            delegate.execute(execution)
        }
    }

    @Test
    fun `REPRINT throws PrintMaintenanceDelayException if hold orders is true`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                action = "REPRINT"
            }

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns true

        assertThrows<PrintMaintenanceException> {
            delegate.execute(execution)
        }
    }

    @Test
    fun `checks for kit in postOption isKitRequired`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        val json = ClassPathResource("../productsApiPostOptionResponseNoKit.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)

        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                "1.0",
                CUSTOMER_ID,
                true,
            )
        } returns Mono.just(response)

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.kitRequired == true)
    }

    @ParameterizedTest
    @MethodSource("parameters")
    fun `checks for kit in postOption isKitRequired for LT`(
        proId: Int,
        isRevised: Boolean,
    ) {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = proId
            }

        mockProcessingOrderService(proId, isRevised)

        val json = ClassPathResource("../productsApiLtPostOptionResponseKit.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)

        val historyJson: java.io.File
        val historyResponse: GetCompleteOrderDetailHistoryResponse

        if (isRevised) {
            historyJson = ClassPathResource("../completedOrderDetailHistoryResponse.json", javaClass).file
            historyResponse = objectMapper.readValue<GetCompleteOrderDetailHistoryResponse>(historyJson)
        } else {
            historyJson = ClassPathResource("../completedOrderDetailHistoryKitResponse.json", javaClass).file
            historyResponse = objectMapper.readValue<GetCompleteOrderDetailHistoryResponse>(historyJson)
        }

        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                "1.0",
                CUSTOMER_ID,
                true,
            )
        } returns Mono.just(response)

        every {
            completedOrderDetailService.getCompletedOrderDetailHistoryByProcessingOrderId(any(), any())
        } returns historyResponse

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        if (proId == LivingTrust.processId && isRevised) {
            assert(delegateVariables.captured.kitRequired == false)
        }

        if (proId == LivingTrust.processId && !isRevised) {
            assert(delegateVariables.captured.kitRequired == true)
        }

        assertThat(delegateVariables.captured.shippingInformation!!.firm).isEqualTo("")
    }

    private fun trustNameParams() =
        listOf(
            arguments(
                "JJK Family Trust",
                "TRUST_NAME",
                ProductType.LivingTrust.processId,
            ),
            arguments(
                "The Grantor Full Name Living Trust",
                "GRANTOR",
                ProductType.LivingTrust.processId,
            ),
            arguments(
                "The Grantor Full Name and CoGrantor Full Name Living Trust",
                "CO_GRANTOR",
                ProductType.LivingTrust.processId,
            ),
            arguments(
                "The Custom Trust Name For Trust Bundle",
                "TRUST_NAME",
                ProductType.TrustBundle.processId,
            ),
        )

    @ParameterizedTest
    @MethodSource("trustNameParams")
    fun `verify living trust kit name`(
        expectedTrustName: String,
        type: String,
        processId: Int,
    ) {
        val answerVersion = mockk<GetQuestionnaireAnswerResponse>()

        if (processId == LivingTrust.processId) {
            answerVersion.also {
                every { it.questionnaireFieldGroupAnswers?.fieldAnswers } returns
                    listOf(
                        mockk {
                            every { fieldName } returns "Grantor_FullName"
                            every { fieldValue } returns "Grantor Full Name"
                        },
                        mockk {
                            every { fieldName } returns "trust_name"
                            every { fieldValue } returns if (type == "TRUST_NAME") "JJK Family Trust" else ""
                        },
                        mockk {
                            every { fieldName } returns "CoGrantor_FullName"
                            every { fieldValue } returns if (type == "CO_GRANTOR") "CoGrantor Full Name" else ""
                        },
                    )
            }
        } else {
            answerVersion.also {
                every { it.questionnaireFieldGroupAnswers?.fieldAnswers } returns
                    listOf(
                        mockk {
                            every { fieldName } returns "Self_FullName"
                            every { fieldValue } returns "Grantor Full Name"
                        },
                        mockk {
                            every { fieldName } returns "Custom_Trust_Name"
                            every { fieldValue } returns if (type == "TRUST_NAME") "The Custom Trust Name For Trust Bundle" else ""
                        },
                        mockk {
                            every { fieldName } returns "CoGrantor_FullName"
                            every { fieldValue } returns if (type == "CO_GRANTOR") "CoGrantor Full Name" else ""
                        },
                    )
            }
        }

        val epKitName = getEPTrustName(processId, answerVersion)
        assert(epKitName == expectedTrustName)
    }

    @Test
    fun `override armed forces ship method`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
                shipMethod = "USPS - Priority"
            }

        val json = ClassPathResource("../orderContactsResponseAFA.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns response.contacts!!

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.shipMethod == "USPS - Priority")
        assert(delegateVariables.captured.shippingInformation!!.country == "CAN")
        assert(delegateVariables.captured.shippingInformation!!.state == "AA")
        assert(delegateVariables.captured.jurisdiction!! == JURISDICTION)
    }

    @Test
    fun `revised EP product uses root productConfigurationId`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = 3
            }

        mockProcessingOrderService(3, true)

        every {
            ordersOrderItemsApi.getOrderItemByOrderItemId(match { it == 999 }, any(), any(), any())
        } returns
            mockk<GetOrderItemResponse>().also {
                every {
                    it.orderItem!!.productConfiguration!!.productConfigurationId!!
                } returns 100
            }

        val getOrdersJSON = ClassPathResource("../ordersApiResponse.json", javaClass).file
        val getOrderResponse = objectMapper.readValue<GetOrderResponse>(getOrdersJSON)
        getOrderResponse.order!!.orderItems!![0].parentOrderItemId = 999
        every {
            ordersApiService.getOrders(ORDER_ID, null, null, "1.0", CUSTOMER_ID, true)
        } returns getOrderResponse

        val json = ClassPathResource("../productsApiLtPostOptionResponseKit.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                match { it == 100 },
                "1.0",
                CUSTOMER_ID,
                true,
            )
        } returns Mono.just(response)

        val historyJson = ClassPathResource("../completedOrderDetailHistoryKitResponse.json", javaClass).file
        val historyResponse = objectMapper.readValue<GetCompleteOrderDetailHistoryResponse>(historyJson)
        every {
            completedOrderDetailService.getCompletedOrderDetailHistoryByProcessingOrderId(any(), any())
        } returns historyResponse

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)
    }

    @Test
    fun `download LW order with card prints`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = ProductType.LivingWill.processId
                hasLivingWillCard = true
            }

        mockCompletedOrderDetailService(download = false)
        val historyJson = ClassPathResource("../completedOrderDetailHistoryResponse.json", javaClass).file
        val historyResponse = objectMapper.readValue<GetCompleteOrderDetailHistoryResponse>(historyJson)
        every {
            completedOrderDetailService.getCompletedOrderDetailHistoryByProcessingOrderId(any(), any())
        } returns historyResponse

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        assert(delegateVariables.captured.isPrintingRequired == true)
        assert(delegateVariables.captured.shipMethod != null)
    }

    @Test
    fun `PO Box uses USPS`() {
        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = PROCESS_ID
            }

        val json = ClassPathResource("../orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        val shippingIndex = response.contacts!!.indexOfFirst { it.contactType == ContactType.Shipping }
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns
            response.contacts!!.also {
                it[shippingIndex].addressLine1 = "PO Box 123"
            }

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }
        delegate.execute(execution)

        Assertions.assertThat(delegateVariables.captured.shipMethod).isEqualTo("FEDEX 2ND DAY")

        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns
            response.contacts!!.also {
                it[shippingIndex].addressLine1 = "123 Main St"
                it[shippingIndex].addressLine2 = "PO Box 123"
            }

        delegate.execute(execution)
        Assertions.assertThat(delegateVariables.captured.shipMethod).isEqualTo("FEDEX 2ND DAY")
    }

    @Test
    fun `EP2 Bundles use passed in shipping address`() {
        val initalAddress =
            ShippingInformationDto(
                "Name",
                "Firm",
                "Address1",
                "Address2",
                "City",
                "CA",
                "90000",
                "USA",
                "1234567890",
                "Special",
            )

        every {
            execution.input
        } returns
            variables {
                processingOrderId = PROCESSING_ORDER_ID
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                printRequestId = "1234"
                processId = ProductType.WillBundle.processId
                shippingInformation = initalAddress
            }

        val historyJson = ClassPathResource("../completedOrderDetailHistoryKitResponse.json", javaClass).file
        val historyResponse = objectMapper.readValue<GetCompleteOrderDetailHistoryResponse>(historyJson)
        every {
            completedOrderDetailService.getCompletedOrderDetailHistoryByProcessingOrderId(any(), any())
        } returns historyResponse

        val delegateVariables = slot<Variables>()
        justRun {
            execution.variables = capture(delegateVariables)
        }

        delegate.execute(execution)

        assert(delegateVariables.captured.shippingInformation == initalAddress)
    }
}
