package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.extensions.findOrderItemByProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.Constants.RETRY_ANSWERS_API
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.github.resilience4j.retry.annotation.Retry
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ManualWorkflowFormationOrderDataFetchService(
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val ordersApiService: OrdersApiService,
    private val orderItemsApiService: OrdersOrderItemsApiService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Retry(name = RETRY_ANSWERS_API)
    fun fetchData(
        execution: DelegateExecution,
        processingOrderId: Int,
        customerId: String?,
    ) {
        logger.info("Fetching answers for processing order $processingOrderId and customer $customerId")

        val answers =
            questionnaireAnswerService.getAnswersByUserOrderId(processingOrderId, customerId, AnswerSource.AnswerBank)

        val input = execution.input

        var orderId = input.orderId

        if (orderId == null || orderId <= 0) {
            val orderItemsResponse =
                orderItemsApiService.getOrdersOrderItems(
                    processingOrderId,
                    xLzApiVersion = "1.0",
                    xLzCustomerid = null,
                    xLzAuthorize = null,
                )

            orderId = orderItemsResponse.orderId!!
        }

        val orderResponse =
            ordersApiService.getOrders(
                orderId,
                productIdType = null,
                showOrderItemTree = null,
                xLzApiVersion = "1.0",
                xLzCustomerid = null,
                xLzAuthorize = null,
            )

        val einOrderItem = orderResponse.order?.findOrderItemByProductType(ProductType.EIN.processId)

        val entityName = answers.entityName
        val entityType = answers.entityType?.let { ProductType.fromName(it)?.name }
        val countyName = answers.businessCounty
        val registrantType = answers.registrantType

        if (entityName.isNullOrEmpty()) {
            logger.warn("Unable to retrieve entity name from answers.")
        }

        if (answers.entityType.isNullOrEmpty()) {
            logger.warn(
                "Unable to retrieve entity type from answers. Calculated entityType:$entityType Answer entityType:${answers.entityType}",
            )
        }

        val expediteSpeed =
            if (einOrderItem != null) {
                getExpediteSpeed(processingOrderId, input.processId, customerId)
            } else {
                null
            }

        val isRAOrderItemExist =
            orderResponse.order?.orderItems?.any {
                it.isCancelled == false && it.processingOrder?.processId == ProductType.RegisteredAgentService.processId
            }

        execution.output {
            this.entityName = entityName
            this.entityType = entityType ?: answers.entityType
            this.einFilingEnabled = einOrderItem != null
            this.countyName = countyName
            this.expediteSpeed = expediteSpeed
            this.iSLZRA = isRAOrderItemExist
            this.registrantType = registrantType
        }
    }

    private fun getExpediteSpeed(
        processingOrderId: Int?,
        processId: Int?,
        customerId: String?,
    ): String? {
        return try {
            questionnaireAnswerService.getFilingData(
                processingOrderId,
                processId,
                customerId,
            ).expediteSpeed
        } catch (ex: Exception) {
            logger.warn("Unable to retrieve expediteSpeed for processingOrderId $processingOrderId. Defaulting it to null.")
            null
        }
    }
}
