package com.legalzoom.fulfillment.workflow.listener

import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.ExecutionListener
import org.springframework.stereotype.Component

// The reason we made this a listener isntead of a delegate is to minimize "technical noise"
// in the bpmn.  The purpose here is to take the output of the DMN decision, as since the output
// is multicolumn, it comes out as a map.  And we want to extract the values from the map and
// assign them to discrete camudna variables.

@Component
class EinUnrollDmnOutputVariables : ExecutionListener {

    @WithSpan("EinUnrollDmnOutputVariables")
    override fun notify(execution: DelegateExecution) {
        val outputMap: Map<String, Any?>? = execution.input.einBotFailureDmnOutput

        // verify preconditions

        if( outputMap == null ) {
            throw IllegalStateException("The 'einBotFailureDmnOutput' input variable is null.")
        }

        if( outputMap["action"] == null ) {
            throw IllegalStateException("Unexpected: the 'action' key is missing in the 'einBotFailureDmnOutput' input variable.")
        }

        execution.output {
            action = outputMap["action"]!!.toString()
            shouldDelay = outputMap["shouldDelay"] as? Boolean
        }
    }
}
