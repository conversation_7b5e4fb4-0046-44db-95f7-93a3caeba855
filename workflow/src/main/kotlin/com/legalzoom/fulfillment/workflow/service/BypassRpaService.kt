package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.service.Constants.BYPASS_RPA_BASED_ON_RA
import com.legalzoom.fulfillment.service.Constants.BYPASS_RPA_FILING_STATES
import com.legalzoom.fulfillment.service.Constants.BYPASS_RPA_INC_PREFERRED_STOCK_FILING_STATES
import com.legalzoom.fulfillment.service.enumeration.AgentType
import com.legalzoom.fulfillment.service.enumeration.BusinessType
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.INC
import com.legalzoom.fulfillment.service.enumeration.ProviderType
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.service.FastForwardService.Companion.NGF_PRODUCTS
import com.legalzoom.fulfillment.workflow.service.helper.StateAttributeHelper.Companion.stateHasAttribute
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class BypassRpaService(
    private val featureToggleService: FeatureToggleService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    fun isBypassRpa(execution: DelegateExecution): Boolean {
        return isBypassRpaByVariables(execution.input)
    }

    fun isBypassRpaByVariables(input: Variables): Boolean {
        val jurisdiction = input.jurisdiction ?: ""
        val processId = input.processId
        val productType = ProductType.fromProcessIdNullable(processId)

        if ((processId in NGF_PRODUCTS) && (input.company?.isProfessional == true)) {
            logger.info("Bypassing RPA because professional $productType isn't currently supported.")
            return true
        }

        if (stateHasAttribute(input, BYPASS_RPA_FILING_STATES)) {
            logger.info("Bypassing RPA because $productType $jurisdiction isn't currently supported")
            return true
        }

        if (productType == INC) {
            val company = input.company
            val isPreferredStock = company?.companyEquityInfo?.preferredStock == true
            val companyAgent = company?.agent
            val agentType: AgentType? = companyAgent?.agentType?.let { AgentType.fromLabel(it) }
            val provider: ProviderType? = companyAgent?.provider?.let { ProviderType.fromLabel(it) }

            if (agentType == null) {
                logger.warn(
                    "Missing or invalid agent type for INC order ( processingOrderId ${input.processingOrderId} , " +
                        "agentType: ${companyAgent?.agentType} )",
                )
            }

            if (provider == null) {
                logger.warn(
                    "Missing or invalid agent provider for INC order ( processingOrderId ${input.processingOrderId} , " +
                        "provider: ${companyAgent?.provider} )",
                )
            }

            if (agentType != null && provider != null && shouldBypassRpaBasedOnRa(agentType, provider, jurisdiction)) {
                logger.info("Bypassing RPA because RA type of $agentType $provider isn't currently supported.")
                return true
            }

            if (isPreferredStock && BYPASS_RPA_INC_PREFERRED_STOCK_FILING_STATES.contains(jurisdiction)) {
                logger.info("Bypassing RPA because preferred stock for $jurisdiction isn't currently supported.")
                return true
            }
        }
        return false
    }

    fun shouldBypassRpaBasedOnRa(
        agentType: AgentType,
        providerType: ProviderType,
        jurisdiction: String,
    ): Boolean {
        if (!BYPASS_RPA_BASED_ON_RA.containsKey(agentType to providerType)) {
            logger.warn("Bypassing RPA because RA type of $agentType $providerType isn't defined in BYPASS_RPA_BASED_ON_RA")
            true
        }

        return BYPASS_RPA_BASED_ON_RA[agentType to providerType]?.contains(jurisdiction) == true
    }

    fun isBypassEinRpa(execution: DelegateExecution): Boolean {
        val input = execution.input

        val processId =
            if (input.isAttachedOrder == true) {
                input.parentProcessId!!
            } else {
                val businessTypeFieldName =
                    questionnaireAnswerService.getAnswersByUserOrderId(
                        input.processingOrderId!!,
                        input.customerId,
                        AnswerSource.AnswerBank,
                    ).businessType
                BusinessType.fromFieldTypeNullable(businessTypeFieldName)?.processId
            }

        return if (processId == null) {
            true
        } else {
            featureToggleService.isRpaEINBypassEnabled(input.customerId, null, processId)
        }
    }
}
