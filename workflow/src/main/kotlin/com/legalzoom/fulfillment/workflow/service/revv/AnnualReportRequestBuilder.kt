package com.legalzoom.fulfillment.workflow.service.revv

import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.fulfillment.answersapi.model.States
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentField
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvMappingFields
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.variable.Variables

class AnnualReportRequestBuilder(
    private val input: Variables,
    private val ordersContactsApiService: OrdersContactsApiService,
    private val answerApi: AnswerApi,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) : RevvFieldsRequestBuilder(ordersContactsApiService, answerApi, questionnaireAnswerService) {
    override fun getTemplates(): MutableList<RevvDocumentType> {
        return mutableListOf(RevvDocumentType.ANNUAL_REPORTS_FINAL_LETTER)
    }

    override fun getCustomerDocumentType(): MutableMap<RevvDocumentType, CustomerDocumentType> {
        return mutableMapOf(
            RevvDocumentType.ANNUAL_REPORTS_FINAL_LETTER to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "Final Letter",
                ),
        )
    }

    override fun getMappingFields(templates: MutableList<RevvDocumentType>?): MutableList<RevvDocumentField> {
        var revDocumentMappingFields = getFinalLetterDocumentFields(input)

        val questionnaireAnswerResponse = getAnswerInfo(input)

        var stateOfFormation = getFieldAnswerValue(questionnaireAnswerResponse, "State_of_formation")

        // For standalone order it should have data in Answer-bank
        // Following logic is needed if there is bundled AR Order with LLC/INC...
        if (stateOfFormation.isEmpty()) {
            val answers = getAnswers(input.parentProcessingOrderId!!, input.customerId!!)

            stateOfFormation = States.fromAbbreviation(answers.jurisdiction!!)?.displayName!!
        }

        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.FORMATION_STATE.value,
                stateOfFormation,
            ),
        )
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.PRODUCT_NAME.value,
                ProductType.AnnualReports.productName,
            ),
        )
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.REGISTRATION_TYPE.value,
                "periodic report",
            ),
        )
        return revDocumentMappingFields
    }
}
