package com.legalzoom.fulfillment.workflow.delegate

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.rpa.ODataValueOfIEnumerableOfQueueItemDto
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.ABANDONED
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.DELETED
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.FAILED
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.IN_PROGRESS
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.NEW
import com.legalzoom.api.model.rpa.QueueItemDto.StatusEnum.SUCCESSFUL
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.service.QueueItemsService
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.data.RobotOutputData
import com.legalzoom.fulfillment.workflow.delegate.helper.RpaHelper
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class GetQueueItemDelegate(
    private val objectMapper: ObjectMapper,
    private val queueItemsService: QueueItemsService,
    private val rpaHelper: RpaHelper,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan("Get_Queue_Item_RPA")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        val input = execution.input

        if (input.rpaQueueItemId == null) {
            "Unexpected System Error. RPA Queue Item Id was not stored".let {
                logger.errorEvent("rpa.failed")
                throw(Exception(it))
            }
        }

        val result =
            queueItemsService.queueItemsGet(
                null,
                null,
                null,
                "Id eq ${input.rpaQueueItemId}",
                null,
                null,
                10,
                null,
                null,
                null,
                rpaHelper.getRpaProcessId(input),
            ).blockSingle()

        logger.info(result.toString())

        when (result.value?.first()?.status) {
            SUCCESSFUL -> handleSuccess(result, execution)
            DELETED, ABANDONED, FAILED -> handleFailure(result, execution)
            NEW, IN_PROGRESS -> {
                execution.output {
                    status = result.value?.first()?.status?.value
                }

                logger.info("RPA Queue Item status is ${result.value?.first()?.status} for ProcessingOrder ${input.processingOrderId} ")
            }
            else -> {
                "Unexpected System Error. RPA Queue Item Issue ${result.value?.first()?.status}".let {
                    logger.errorEvent("rpa.failed")
                    throw(Exception(it))
                }
            }
        }
    }

    private fun handleSuccess(
        result: ODataValueOfIEnumerableOfQueueItemDto,
        execution: DelegateExecution,
    ) {
        val outputData =
            result.value?.first()?.outputData?.let {
                objectMapper.readValue<RobotOutputData>(it)
            }

        execution.output {
            status = outputData?.dynamicProperties?.status ?: result.value?.first()?.status?.value
            evidenceFilePath = outputData?.dynamicProperties?.evidenceFilePath
            evidenceTransactionNumber = outputData?.dynamicProperties?.evidenceTransactionNumber
            evidenceTransactionMessage = outputData?.dynamicProperties?.evidenceTransactionMessage
        }
    }

    private fun handleFailure(
        result: ODataValueOfIEnumerableOfQueueItemDto,
        execution: DelegateExecution,
    ) {
        val processingException = result.value?.first()?.processingException

        execution.output {
            status = processingException?.type?.value ?: result.value?.first()?.status?.value
            evidenceFilePath = processingException?.details
            message = processingException?.reason
        }
    }
}
