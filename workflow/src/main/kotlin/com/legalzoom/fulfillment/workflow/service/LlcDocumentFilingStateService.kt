package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.domain.enumeration.State.DELAWARE
import com.legalzoom.fulfillment.domain.enumeration.State.IOWA
import com.legalzoom.fulfillment.domain.enumeration.State.MAINE
import com.legalzoom.fulfillment.domain.enumeration.State.NEBRASKA
import com.legalzoom.fulfillment.domain.enumeration.State.NEW_MEXICO
import com.legalzoom.fulfillment.domain.enumeration.State.UTAH
import com.legalzoom.fulfillment.domain.enumeration.State.WEST_VIRGINIA
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.LLC
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
class LlcDocumentFilingStateService : DocumentFilingStateService {
    private val productType = LLC

    override fun getProductType(): ProductType = productType

    override fun documentRequired(execution: DelegateExecution): Boolean {
        return documentRequiredByVariables(execution.input)
    }

    override fun documentRequiredByVariables(variables: Variables): Boolean {
        return when (variables.jurisdiction) {
            DELAWARE.abbreviation -> {
                true
            }

            NEW_MEXICO.abbreviation -> {
                true
            }

            NEBRASKA.abbreviation -> {
                true
            }

            UTAH.abbreviation -> {
                true
            }

            IOWA.abbreviation -> {
                true
            }

            MAINE.abbreviation -> {
                true
            }

            WEST_VIRGINIA.abbreviation -> {
                true
            }

            else -> false
        }
    }
}
