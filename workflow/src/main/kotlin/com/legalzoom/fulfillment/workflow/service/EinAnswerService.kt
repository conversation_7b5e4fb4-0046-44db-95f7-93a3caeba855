package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.variable.input
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
class EinAnswerService(
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) {
    fun isEinFieldPopulated(execution: DelegateExecution): Boolean {
        val input = execution.input
        val answersResponse =
            questionnaireAnswerService.getAnswersByUserOrderId(
                if (input.isAttachedOrder == true) input.parentProcessingOrderId!! else input.processingOrderId!!,
                input.customerId,
                AnswerSource.AnswerBank,
            )
        return !answersResponse.ein.isNullOrBlank()
    }

    fun getEinField(execution: DelegateExecution): String {
        val input = execution.input
        val answersResponse =
            questionnaireAnswerService.getAnswersByUserOrderId(
                if (input.isAttachedOrder == true) input.parentProcessingOrderId!! else input.processingOrderId!!,
                input.customerId,
                AnswerSource.AnswerBank,
            )
        return answersResponse.ein!!
    }

    fun getEinFieldNullable(execution: DelegateExecution): String? {
        val input = execution.input
        val answersResponse =
            questionnaireAnswerService.getAnswersByUserOrderId(
                if (input.isAttachedOrder == true) input.parentProcessingOrderId!! else input.processingOrderId!!,
                input.customerId,
                AnswerSource.AnswerBank,
            )
        // If you're adding EIN support for a new parent product make sure "{ParentProduct}EIN" exists in AnswerFieldType enum
        return answersResponse.ein
    }
}
