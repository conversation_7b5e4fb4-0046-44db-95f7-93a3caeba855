package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.domain.enumeration.State.ARKANSAS
import com.legalzoom.fulfillment.domain.enumeration.State.CALIFORNIA
import com.legalzoom.fulfillment.domain.enumeration.State.COLORADO
import com.legalzoom.fulfillment.domain.enumeration.State.DELAWARE
import com.legalzoom.fulfillment.domain.enumeration.State.DISTRICT_OF_COLUMBIA
import com.legalzoom.fulfillment.domain.enumeration.State.FLORIDA
import com.legalzoom.fulfillment.domain.enumeration.State.GEORGIA
import com.legalzoom.fulfillment.domain.enumeration.State.HAWAII
import com.legalzoom.fulfillment.domain.enumeration.State.IDAHO
import com.legalzoom.fulfillment.domain.enumeration.State.INDIANA
import com.legalzoom.fulfillment.domain.enumeration.State.KANSAS
import com.legalzoom.fulfillment.domain.enumeration.State.KENTUCKY
import com.legalzoom.fulfillment.domain.enumeration.State.LOUISIANA
import com.legalzoom.fulfillment.domain.enumeration.State.MAINE
import com.legalzoom.fulfillment.domain.enumeration.State.MARYLAND
import com.legalzoom.fulfillment.domain.enumeration.State.MICHIGAN
import com.legalzoom.fulfillment.domain.enumeration.State.MINNESOTA
import com.legalzoom.fulfillment.domain.enumeration.State.MISSOURI
import com.legalzoom.fulfillment.domain.enumeration.State.NEBRASKA
import com.legalzoom.fulfillment.domain.enumeration.State.NEW_HAMPSHIRE
import com.legalzoom.fulfillment.domain.enumeration.State.NEW_MEXICO
import com.legalzoom.fulfillment.domain.enumeration.State.NEW_YORK
import com.legalzoom.fulfillment.domain.enumeration.State.NORTH_CAROLINA
import com.legalzoom.fulfillment.domain.enumeration.State.NORTH_DAKOTA
import com.legalzoom.fulfillment.domain.enumeration.State.OHIO
import com.legalzoom.fulfillment.domain.enumeration.State.OKLAHOMA
import com.legalzoom.fulfillment.domain.enumeration.State.OREGON
import com.legalzoom.fulfillment.domain.enumeration.State.RHODE_ISLAND
import com.legalzoom.fulfillment.domain.enumeration.State.SOUTH_CAROLINA
import com.legalzoom.fulfillment.domain.enumeration.State.TEXAS
import com.legalzoom.fulfillment.domain.enumeration.State.UTAH
import com.legalzoom.fulfillment.domain.enumeration.State.VIRGINIA
import com.legalzoom.fulfillment.domain.enumeration.State.WEST_VIRGINIA
import com.legalzoom.fulfillment.domain.enumeration.State.WISCONSIN
import com.legalzoom.fulfillment.domain.enumeration.State.WYOMING
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.INC
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
class IncDocumentFilingStateService : DocumentFilingStateService {
    private val productType = INC

    override fun getProductType(): ProductType = productType

    override fun documentRequired(execution: DelegateExecution): Boolean {
        return documentRequiredByVariables(execution.input)
    }

    override fun documentRequiredByVariables(variables: Variables): Boolean {
        return when (variables.jurisdiction) {
            ARKANSAS.abbreviation -> {
                true
            }

            CALIFORNIA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            COLORADO.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            DELAWARE.abbreviation -> {
                true
            }

            DISTRICT_OF_COLUMBIA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            GEORGIA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            FLORIDA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            IDAHO.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            INDIANA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            KANSAS.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            KENTUCKY.abbreviation -> {
                true
            }

            LOUISIANA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            MAINE.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            MICHIGAN.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            MISSOURI.abbreviation -> {
                true
            }

            MINNESOTA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            MARYLAND.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            NEW_HAMPSHIRE.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            NORTH_CAROLINA.abbreviation -> {
                true
            }

            HAWAII.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            NEBRASKA.abbreviation -> {
                true
            }

            NEW_MEXICO.abbreviation -> {
                true
            }

            OHIO.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            NEW_YORK.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            NORTH_DAKOTA.abbreviation -> {
                true
            }

            OKLAHOMA.abbreviation -> {
                true
            }

            OREGON.abbreviation -> {
                true
            }

            RHODE_ISLAND.abbreviation -> {
                true
            }

            SOUTH_CAROLINA.abbreviation -> {
                true
            }

            TEXAS.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            UTAH.abbreviation -> {
                true
            }

            VIRGINIA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            WISCONSIN.abbreviation -> {
                true
            }

            WEST_VIRGINIA.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            WYOMING.abbreviation -> {
                variables.company?.companyEquityInfo?.preferredStock == true
            }

            else -> false
        }
    }
}
