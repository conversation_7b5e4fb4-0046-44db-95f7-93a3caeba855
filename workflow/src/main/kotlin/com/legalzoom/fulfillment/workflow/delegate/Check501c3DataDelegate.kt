package com.legalzoom.fulfillment.workflow.delegate

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.GetAnswersPayload
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.delegate.helper.LedgerNoteHelperService
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.apache.kafka.common.requests.DeleteAclsResponse
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class Check501c3DataDelegate(
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val ledgerNoteHelperService: LedgerNoteHelperService,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan("Check501c3DataDelegate")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        val input = execution.input
        val answersResponse =
            questionnaireAnswerService.getAnswersByUserOrderId(
                input.processingOrderId!!,
                input.customerId,
                AnswerSource.AnswerBank,
            )

        if (!is501c3FieldsPopulated(answersResponse)) {
            val newValidationErrors = if (input.validationErrors != null) input.validationErrors else mutableListOf()
            newValidationErrors?.add(
                ValidationError(
                    "501c3 tracking IDs and/or submission date details were not added to Proofer.",
                    data =
                        mapOf(
                            "Detail" to "Please enter and save the tracking IDs and/or submission date details, " +
                                "then select \"Skip-I Have Manually Obtained 501c3\"",
                        ),
                ),
            )

            ledgerNoteHelperService.createLedger(
                input,
                "note",
                "501c3 tracking IDs and/or submission date details were not added to Proofer.",
            )
            execution.output {
                validationError = true
                validationErrors = newValidationErrors
            }

            logger.event(
                "501c3 tracking IDs and/or submission date details were not added to Proofer.",
                mapOf(
                    "processingOrderId" to input.processingOrderId,
                    "parentProcessId" to input.parentProcessId,
                    "orderId" to input.orderId,
                    "processId" to input.processId,
                    "parentProcessingOrderId" to input.parentProcessingOrderId,
                ),
            )
        } else {
            execution.output {
                // These need to be set in here so that they will be present in ActivityFeedVariables and thus sent the email notification
                paygovTrackingId = answersResponse.paygovTrackingId
                agencyTrackingId = answersResponse.agencyTrackingId
                submissionDate1023EZ = answersResponse.submissionDate1023EZ
            }
        }
    }

    fun is501c3FieldsPopulated(answersResponse: GetAnswersPayload): Boolean {
        return if (answersResponse.paygovTrackingId.isNullOrEmpty()) {
            DeleteAclsResponse.log.info("Failed 501c3 Validation. paygovTrackingId is null or empty")
            false
        } else if (answersResponse.agencyTrackingId.isNullOrEmpty()) {
            DeleteAclsResponse.log.info("Failed 501c3 Validation. agencyTrackingId is empty")
            false
        } else if (answersResponse.submissionDate1023EZ.isNullOrEmpty()) {
            DeleteAclsResponse.log.info("Failed 501c3 Validation. submissionDate1023EZ is empty")
            false
        } else {
            true
        }
    }
}
