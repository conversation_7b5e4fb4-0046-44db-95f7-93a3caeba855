package com.legalzoom.fulfillment.workflow.service.revv

import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentField
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvMappingFields
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.variable.Variables

class AmendmentRequestBuilder(
    private val input: Variables,
    private val ordersContactsApiService: OrdersContactsApiService,
    private val answerApi: AnswerApi,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) : RevvFieldsRequestBuilder(ordersContactsApiService, answerApi, questionnaireAnswerService) {
    override fun getTemplates(): MutableList<RevvDocumentType> {
        val templates =
            mutableListOf(
                RevvDocumentType.AMENDMENT_FINAL_LETTER,
            )
        val questionnaireAnswerResponse = getAnswerInfo(input)
        val stateOfFormation = getFieldAnswerValue(questionnaireAnswerResponse, "state_of_formation")

        if (State.fromNameOrAbbre(stateOfFormation) == State.TENNESSEE) {
            templates.add(RevvDocumentType.REGISTER_OF_DEEDS_ATTACHMENT)
        }
        return templates
    }

    override fun getCustomerDocumentType(): MutableMap<RevvDocumentType, CustomerDocumentType> {
        return mutableMapOf(
            RevvDocumentType.AMENDMENT_FINAL_LETTER to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "FinalLetter",
                ),
            RevvDocumentType.REGISTER_OF_DEEDS_ATTACHMENT to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "Register of Deeds Attachment",
                ),
        )
    }

    override fun getMappingFields(templates: MutableList<RevvDocumentType>?): MutableList<RevvDocumentField> {
        var revDocumentMappingFields = getFinalLetterDocumentFields(input)

        val questionnaireAnswerResponse = getAnswerInfo(input)

        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.FORMATION_STATE.value,
                getFieldAnswerValue(questionnaireAnswerResponse, "state_of_formation"),
            ),
        )
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.PRODUCT_NAME.value,
                ProductType.Amendment.productName,
            ),
        )

        return revDocumentMappingFields
    }
}
