package com.legalzoom.fulfillment.workflow.delegate

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.EIN
import com.legalzoom.fulfillment.service.service.FieldUpdaterService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.EinFilingNotStarted
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedService
import com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.EINUnavailableReason
import com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.ManualFilingEIN
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.Constants
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.service.BypassRpaService
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.helper.FulfillmentEventServiceHelper
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import com.legalzoom.fulfillment.workflow.variable.toActivityFeedVariables
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.RepositoryService
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Clock

@Component
class EinStatusVerificationDelegate(
    private val ordersApiService: OrdersApiService,
    private val salesforceApiService: SalesforceApiService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val fieldUpdaterService: FieldUpdaterService,
    private val activityFeedServiceImpl: ActivityFeedService,
    private val orderCancellationService: OrderCancellationService,
    private val repositoryService: RepositoryService,
    private val fulfillmentEventService: FulfillmentEventService,
    private val rpaBypassRpaService: BypassRpaService,
    private val clock: Clock,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan("EinStatusVerification")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        val input = execution.input

        val orderResponse = ordersApiService.getOrders(input.orderId, null, null, null, null, null)

        var einOrderItem =
            orderResponse.order?.orderItems?.singleOrNull {
                it.processingOrder?.processId == EIN.processId
            }

        // Order cancellation check
        if (einOrderItem?.isCancelled == true) {
            orderCancellationService.cancelOrder(execution, input.processingOrderId!!)
            return
        }

        var isEinFilingEnabled = false
        if (einOrderItem != null) {
            val answersResponse =
                questionnaireAnswerService.getAnswersByUserOrderId(
                    if (input.isAttachedOrder == true) input.parentProcessingOrderId!! else input.processingOrderId!!,
                    input.customerId,
                    AnswerSource.AnswerBank,
                )

            val manualEINFilingFieldValue = answersResponse.manualFilingEIN
            val isEINObtained = !answersResponse.ein.isNullOrBlank()

            if (manualEINFilingFieldValue.isNullOrEmpty()) {
                updateFieldAnswer(
                    execution,
                    ManualFilingEIN.toString(),
                    "No",
                )
            } else if (manualEINFilingFieldValue == "Yes" && !isEINObtained) {
                updateFieldAnswer(
                    execution,
                    EINUnavailableReason.toString(),
                    "Incomplete - Missing Info - Amendment Needed - Deliver Final Package",
                )

                addProblemLedgerNote(input)
            }

            isEinFilingEnabled = manualEINFilingFieldValue != "Yes" && !isEINObtained

            if (isEINObtained) {
                logger.event(
                    "ein.filing.ein.obtained",
                    mapOf(
                        "processingOrderId" to input.processingOrderId,
                        "parentProcessingOrderId" to input.parentProcessingOrderId,
                        "isEINObtained" to isEINObtained,
                        "message" to "EIN is obtained for the order ${input.processingOrderId}",
                        "isAttachedOrder" to input.isAttachedOrder,
                    ),
                )
            } else {
                logger.event(
                    "ein.filing.enabled.value",
                    mapOf(
                        "processingOrderId" to input.processingOrderId,
                        "parentProcessingOrderId" to input.parentProcessingOrderId,
                        "isEinFilingEnabled" to isEinFilingEnabled,
                        "isAttachedOrder" to input.isAttachedOrder,
                    ),
                )
            }
        } else {
            isEinFilingEnabled = false
            logger.event(
                "ein.filing.disabled",
                mapOf(
                    "processingOrderId" to input.processingOrderId,
                    "parentProcessingOrderId" to input.parentProcessingOrderId,
                    "message" to "EIN OrderItem either cancelled or Not found. Skipping EIN Obtainment",
                    "isAttachedOrder" to input.isAttachedOrder,
                ),
            )
        }

        val bypassRpa = rpaBypassRpaService.isBypassEinRpa(execution)
        execution.output {
            einFilingEnabled = isEinFilingEnabled
            validationError = bypassRpa
            validationErrors =
                if (bypassRpa) {
                    mutableListOf(
                        ValidationError(
                            "Manual EIN obtainment required",
                            data =
                                mapOf(
                                    "Detail" to "This order requires manual EIN obtainment. " +
                                        "Please manually obtain the EIN and then select \"Skip-I Have Manually Obtained EIN\"",
                                ),
                        ),
                    )
                } else {
                    mutableListOf()
                }
        }

        if (einOrderItem != null) {
            activityFeedServiceImpl.sendEvent(EinFilingNotStarted, execution.toActivityFeedVariables())
        }
    }

    private fun addProblemLedgerNote(input: Variables) {
        try {
            salesforceApiService.addLedgerNote(
                AddLedgerNoteRequest(
                    objectType = "case",
                    customerOrderNumber = input.orderId.toString(),
                    processingNumber = input.processingOrderId.toString(),
                    description = "EIN was not obtained due to customer request and potential changes to the customer's order.",
                    createdBy = Constants.SALESFORCE_USER,
                ),
            )
        } catch (e: Exception) {
            logger.error("Adding Ledger Note Failed: ${e.message}", e)
        }
    }

    private fun updateFieldAnswer(
        execution: DelegateExecution,
        fieldNameKey: String,
        fieldNameValue: String,
    ) {
        val input = execution.input
        val product =
            ProductType.fromProcessId(if (input.isAttachedOrder == true) input.parentProcessId!! else input.processId!!)
        fieldUpdaterService.saveFieldValue(
            product.questionnaireId,
            if (input.isAttachedOrder == true) input.parentProcessingOrderId!! else input.processingOrderId!!,
            fieldNameKey,
            fieldNameValue,
            product.processId,
        )

        // Generate event notifying a field has been updated
        val key = repositoryService.getProcessDefinition(execution.processDefinitionId).key
        val eventPhase = EventPhase.fromWorkflowName(key)
        val fulfillmentEvent =
            FulfillmentEventServiceHelper(clock).buildFulfillmentEvent(
                execution = execution,
                eventType = EventType.FIELD_UPDATED,
                eventPhase = eventPhase,
                data =
                    mapOf(
                        "processId" to input.processId,
                        "processingOrderId" to input.processingOrderId.toString(),
                        "customerId" to input.customerId,
                        "fieldName" to fieldNameKey,
                    ),
            )
        fulfillmentEventService.send(fulfillmentEvent)
    }
}
