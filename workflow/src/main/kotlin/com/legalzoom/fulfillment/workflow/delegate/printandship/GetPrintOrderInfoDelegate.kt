package com.legalzoom.fulfillment.workflow.delegate.printandship

import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.api.model.processingorder.CompletedOrderDetailDto
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.printandship.entity.ShippingInformation
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.Constants
import com.legalzoom.fulfillment.printandshipapi.Constants.ARMED_FORCES_STATES
import com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_PROBLEM_ERROR
import com.legalzoom.fulfillment.printandshipapi.Constants.US_TERRITORIES
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.CountryCode
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintOrderStatuses
import com.legalzoom.fulfillment.service.Constants.EP_PRODUCTS
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.ValidationResult
import com.legalzoom.fulfillment.service.enumeration.Cp1CustomerShipMethod
import com.legalzoom.fulfillment.service.enumeration.Cp1CustomerShipMethod.STANDARD_SHIPPING
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.PrintAndShipShippingMethod
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.exception.PrintException
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.LivingTrustOrderPrintedAndShipped
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.TrustBundleOrderPrintedAndShipped
import com.legalzoom.fulfillment.service.service.document.getEPTrustName
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.exception.PrintMaintenanceException
import com.legalzoom.fulfillment.workflow.extensions.addValidationError
import com.legalzoom.fulfillment.workflow.service.helper.FulfillmentEventServiceHelper
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import org.camunda.bpm.engine.delegate.BpmnError
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Clock
import java.util.UUID

@Component
class GetPrintOrderInfoDelegate(
    private val documentListService: DocumentListService,
    private val completedOrderDetailService: CompletedOrderDetailService,
    private val ordersApiService: OrdersApiService,
    private val orderContactsService: OrderContactsService,
    private val ordersOrderItemsApi: OrdersOrderItemsApiService,
    private val processingOrderService: ProcessingOrderService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val featureToggleService: FeatureToggleService,
    private val productsApi: ProductsApi,
    private val clock: Clock,
    private val fulfillmentEventService: FulfillmentEventService,
) : JavaDelegate {
    override fun execute(execution: DelegateExecution?) {
        val processingOrderId = execution!!.input.processingOrderId!!
        val processId = execution.input.processId!!
        var shippingInfo = execution.input.shippingInformation
        var shipMethod = execution.input.shipMethod
        val orderId = execution.input.orderId!!
        var printDocumentList = execution.input.printDocumentList
        var printRequestId = execution.input.printRequestId
        var jurisdiction = execution.input.jurisdiction
        var kitRequired = execution.input.kitRequired
        var customerId = execution.input.customerId
        val isEpProduct = processId in EP_PRODUCTS
        val accountId = execution.input.accountId?.toString()
        var isAlreadyPrinted = false
        var trustName = execution.input.trustName
        val attachedWillCard = execution.input.hasLivingWillCard ?: false

        if (
            execution.input.disposition == FulfillmentDisposition.Retry.value ||
            execution.input.printRequestId.isNullOrEmpty()
        ) {
            // Reset variables for retry, or if printRequestId is not set (Reprint passes in printRequestId with the request since we generate on spawning the print-process)
            printRequestId = UUID.randomUUID().toString()
            printDocumentList = null

            // EP2 bundles use a specific shipping address since the customer can request multiple shipments/addresses
            if (!isEpProduct) shippingInfo = null
        }

        /** API Calls to get order details **/
        val processingOrderDetails = processingOrderService.getProcessingOrder(processingOrderId)
        if (customerId == null) {
            customerId = processingOrderDetails.customerId.toString()
        }

        val completedOrderDetails =
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(
                processingOrderId,
                customerId,
            ).completedOrderDetail!!

        val contacts = orderContactsService.getOrderContactsByOrderId(orderId, customerId)

        val orderItems = ordersApiService.getOrders(orderId, null, null, "1.0", customerId, true).order!!.orderItems!!
        var productConfigurationId =
            orderItems.first { it.processingOrder?.processingOrderId == processingOrderId }.productConfiguration!!.productConfigurationId!!
        val parentOrderItemId =
            orderItems.first { it.processingOrder?.processingOrderId == processingOrderId }.orderItemId!!
        val rootOrderItemId =
            orderItems.first { it.processingOrder?.processingOrderId == processingOrderId }.parentOrderItemId!!

        if (isEpProduct) {
            // Check for EP products if they have been printed before to determine if a kit is required
            val completedOrderDetailsHistory =
                completedOrderDetailService.getCompletedOrderDetailHistoryByProcessingOrderId(
                    processingOrderId,
                    customerId,
                )
            isAlreadyPrinted = completedOrderDetailsHistory.completedOrderDetailHistory?.any {
                it.processingStatusId == LivingTrustOrderPrintedAndShipped.processingStatusId ||
                    it.processingStatusId == TrustBundleOrderPrintedAndShipped.processingStatusId
            }
                ?: false

            // If the EP product is a revision, we need to get the root order item ID to determine if a kit is required
            val isRevised = processingOrderDetails.revisionCount?.let { it > 0 } ?: false
            if (isRevised && rootOrderItemId > 0) {
                val rootOrderItemIdDetails =
                    ordersOrderItemsApi.getOrderItemByOrderItemId(rootOrderItemId, "1.0", customerId, true).orderItem!!
                productConfigurationId = rootOrderItemIdDetails.productConfiguration!!.productConfigurationId!!
            }

            // Trust Name for EP products instead of Entity Name
            trustName =
                getEPTrustName(
                    processId,
                    questionnaireAnswerService.getAnswerVersionForUserOrderId(processingOrderId, customerId, AnswerSource.AnswerBank),
                )
        }

        val postOption =
            productsApi.coreProductsProductIdPostOptionGet(
                productConfigurationId,
                "1.0",
                customerId,
                true,
            )
                .blockSingle()
                .postOption!!

        /** Setting Variables from Order Details **/

        var shipMethodId = completedOrderDetails.shipMethodId ?: Cp1CustomerShipMethod.UNSET.Id

        if (
            processId == ProductType.LivingWill.processId &&
            shipMethodId !in Cp1CustomerShipMethod.PHYSICAL_SHIP_METHODS && attachedWillCard
        ) {
            shipMethodId = STANDARD_SHIPPING.Id
        }

        if (shipMethod == null && shipMethodId !in Cp1CustomerShipMethod.PHYSICAL_SHIP_METHODS) {
            logger.warnEvent(
                "printAndShip.getOrderInfo.ejectDownloadOrder",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "shipMethodId" to shipMethodId,
                    "shipMethodDescription" to Cp1CustomerShipMethod.fromId(shipMethodId)?.Description,
                ),
            )
            execution.output { isPrintingRequired = false }
            return
        }

        if (jurisdiction.isNullOrEmpty() && !isEpProduct) {
            jurisdiction = State.fromId(completedOrderDetails.stateId!!)!!.abbreviation
        }

        if (execution.input.action != "REPRINT" || execution.input.kitRequired == null) {
            kitRequired = kitRequired ?: (
                postOption.isKitIncluded == true ||
                    orderItems.any {
                        it.parentOrderItemId == parentOrderItemId &&
                            it.productConfiguration!!.productComponent!!.name!!.contains(" kit", ignoreCase = true)
                    }
            )
        }

        // EP only sends kit for first print, not for reprints
        if ((processId == ProductType.LivingTrust.processId || processId == ProductType.TrustBundle.processId) && isAlreadyPrinted) {
            kitRequired = false
        }

        val effectiveDate =
            completedOrderDetails.effectiveDate!!.format(
                java.time.format.DateTimeFormatter.ofPattern("MM/dd/yyyy"),
            )

        val entityName: String? = getEntityName(processingOrderId, customerId, printRequestId!!, completedOrderDetails)

        /** Business Errors actionable by SF case **/
        try {
            printDocumentList =
                getDocumentList(processingOrderId, processId, customerId, printRequestId, printDocumentList, accountId)

            val firm = if (isEpProduct) "" else entityName ?: ""
            if (shippingInfo == null) {
                shippingInfo =
                    validateShippingInfoFromResponse(
                        contacts.firstOrNull { it.contactType == ContactType.Shipping },
                        firm,
                        processingOrderId,
                    ).toDto()
            } else {
                shippingInfo.firm = firm
            }
        } catch (e: PrintException) {
            // Send to salesforce case to be fixed
            logger.warnEvent(
                "printAndShip.getOrderInfo.actionableError",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "printRequestId" to printRequestId,
                    "orderId" to orderId,
                    "error" to e.message,
                ),
            )
            val validationError =
                ValidationError(
                    e.message!!,
                    mapOf(
                        "Details" to e.message!!,
                        "processingOrderId" to processingOrderId.toString(),
                        "orderId" to orderId,
                        "printStatus" to PrintOrderStatuses.PROBLEM_ORDER.name,
                        "printRequestId" to printRequestId,
                    ),
                )
            execution.addValidationError(validationError)
            val fulfillmentEvent =
                FulfillmentEventServiceHelper(clock).buildFulfillmentEvent(
                    execution = execution,
                    eventType = EventType.VALIDATION_COMPLETE,
                    eventPhase = EventPhase.PRINT_SHIP,
                    data =
                        ValidationResult(
                            passed = false,
                            errors = listOf(validationError),
                            message = e.message!!,
                        ),
                )
            fulfillmentEventService.send(fulfillmentEvent)
            throw BpmnError(PRINT_PROBLEM_ERROR, e.message)
        }

        if (shipMethod.isNullOrEmpty()) {
            shipMethod =
                if (
                    shippingInfo.state in (US_TERRITORIES + ARMED_FORCES_STATES).map { it.abbreviation } ||
                    shippingInfo.isPOBoxAddress
                ) {
                    Constants.USPS_SHIPPING
                } else {
                    PrintAndShipShippingMethod.getForProduct(
                        product = ProductType.fromProcessId(processId),
                        postOption = postOption,
                        customerShipMethod = Cp1CustomerShipMethod.fromIdNotNull(shipMethodId),
                    ).shipVia
                }
        }

        /** Hold orders based on feature flag OR weekend maintenance @ Viatech **/
        val shouldHoldOrder = featureToggleService.shouldPrintShipHoldOrders(customerId, jurisdiction, processId)
        // 1hr re-check on LD flag, if it's still enabled, we'll hold the order.
        // Otherwise, if false, we check if it is a maintenance weekend for automated holding
        val maintenanceDelay = if (shouldHoldOrder) "PT1H" else setDelayTimerForMaintenance()
        if (maintenanceDelay != null) {
            logger.event(
                "printAndShip.getOrderInfo.maintenanceDelay",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "printRequestId" to printRequestId,
                    "orderId" to orderId,
                    "maintenanceDelay" to maintenanceDelay,
                ),
            )
        }
        if (maintenanceDelay != null && execution.input.action == "REPRINT" &&
            execution.input.disposition != FulfillmentDisposition.Retry.value
        ) {
            // Reject API call
            throw PrintMaintenanceException("Print orders are being held for maintenance. Please try again later.")
        }

        logger.event(
            "printAndShip.getOrderInfo.success",
            mapOf(
                "processingOrderId" to processingOrderId,
                "processId" to processId,
                "orderId" to orderId,
                "printDocumentList" to printDocumentList,
                "shippingInformation" to shippingInfo.redact(),
                "shipMethod" to shipMethod,
                "jurisdiction" to jurisdiction,
                "kitRequired" to kitRequired,
                "maintenanceDelay" to maintenanceDelay,
                "isEp" to isEpProduct,
            ),
        )

        execution.output {
            this.printDocumentList = printDocumentList
            this.shippingInformation = shippingInfo
            this.shipMethod = shipMethod
            this.entityName = entityName
            this.effectiveDate = effectiveDate
            this.jurisdiction = jurisdiction
            this.processId = processId
            this.kitRequired = kitRequired
            this.isPrintingRequired = true
            this.printRequestId = printRequestId
            this.maintenanceDelayTimer = maintenanceDelay
            this.action = if (execution.input.action == "REPRINT") "REPRINT" else "PRINT"
            this.trustName = trustName
        }
    }

    @Throws(PrintException::class)
    private fun getDocumentList(
        processingOrderId: Int,
        processId: Int,
        customerId: String,
        printRequestId: String,
        documentList: List<PrintDocumentInfo>?,
        accountId: String? = null,
    ): List<PrintDocumentInfo> {
        val returnList: List<PrintDocumentInfo>
        logger.event(
            "printAndShip.getOrderInfo.documentList.start",
            mapOf(
                "processingOrderId" to processingOrderId,
                "printRequestId" to printRequestId,
                "processId" to processId,
                "printDocumentList" to documentList,
            ),
        )
        if (documentList.isNullOrEmpty()) {
            returnList =
                documentListService.getDefaultDocumentListWithConfig(
                    processId,
                    LOCATION_CODE,
                    processingOrderId,
                    customerId,
                    accountId,
                )
            if (returnList.isEmpty()) {
                logger.errorEvent(
                    "printAndShip.getOrderInfo.documentList.emptyError",
                    mapOf(
                        "processId" to processId,
                        "locationCode" to LOCATION_CODE,
                        "processingOrderId" to processingOrderId,
                        "printRequestId" to printRequestId,
                    ),
                )
                throw PrintException("No printable documents found.")
            }
        } else { // Document IDs to print were passed in for selective reprint
            returnList =
                documentListService.getPrintDocumentInfoByIds(
                    documentList,
                    processId,
                    LOCATION_CODE,
                    processingOrderId,
                    customerId,
                    accountId,
                )
        }

        returnList.forEach { printDocumentInfo ->
            printDocumentInfo.takeIf { it.documentName == null || it.printConfig == null }?.apply {
                throw Exception("Error retrieving info for $this")
            }
        }
        return returnList
    }

    @Throws(PrintException::class)
    fun validateShippingInfoFromResponse(
        contact: ContactInfoDto?,
        entityName: String?,
        processingOrderId: Int,
    ): ShippingInformation {
        if (contact == null) {
            logger.errorEvent(
                "printAndShip.getOrderInfo.missingShippingContact",
                mapOf(
                    "processingOrderId" to processingOrderId,
                ),
            )
            throw PrintException("Missing shipping address for customer.")
        }

        val countryCode =
            contact.country?.let {
                try {
                    CountryCode.fromLocaleOrName(it)
                } catch (e: Exception) {
                    logger.errorEvent(
                        "printAndShip.getOrderInfo.countryCodeError",
                        mapOf(
                            "country" to contact.country,
                            "processingOrderId" to processingOrderId,
                            "error" to e.message,
                        ),
                    )
                    throw PrintException("Country not supported. 3-letter country code for ${contact.country}.")
                }
            } ?: CountryCode.UNITED_STATES_OF_AMERICA.countryCode

        try {
            return ShippingInformation(
                name = contact.firstName!! + " " + contact.lastName!!,
                firm = entityName,
                addressLine1 = contact.addressLine1!!,
                addressLine2 = contact.addressLine2 ?: "",
                city = contact.city!!,
                state = State.fromNameOrAbbre(contact.state!!)!!.abbreviation,
                zipCode = contact.zipCode!!,
                country = countryCode.ifEmpty { CountryCode.UNITED_STATES_OF_AMERICA.countryCode },
                contactNumber =
                    contact.homePhone ?: contact.mobilePhone
                        ?: contact.workPhone!!,
                // Fallback to other phone numbers
                // TODO: How should we set this for default?
                specialInstructions = "",
            )
        } catch (e: NullPointerException) {
            logger.errorEvent(
                "printAndShip.getOrderInfo.shippingInfoError",
                mapOf(
                    "processingOrderId" to processingOrderId,
                ),
            )

            val requiredAddressFields = listOf("firstName", "lastName", "addressLine1", "city", "state", "zipCode", "contactNumber")

            val missingFields: String =
                requiredAddressFields.filter { field ->
                    when (field) {
                        "firstName" -> contact.firstName.isNullOrEmpty()
                        "lastName" -> contact.lastName.isNullOrEmpty()
                        "addressLine1" -> contact.addressLine1.isNullOrEmpty()
                        "city" -> contact.city.isNullOrEmpty()
                        "state" -> contact.state.isNullOrEmpty()
                        "zipCode" -> contact.zipCode.isNullOrEmpty()
                        "contactNumber" ->
                            contact.homePhone.isNullOrEmpty() && contact.mobilePhone.isNullOrEmpty() &&
                                contact.workPhone.isNullOrEmpty()
                        else -> false
                    }
                }.joinToString(", ")

            throw PrintException(
                "Error with customer shipping address. " +
                    "Missing required fields: $missingFields.",
            )
        }
    }

    private fun getEntityName(
        processingOrderId: Int,
        customerId: String,
        printRequestId: String,
        completedOrderDetails: CompletedOrderDetailDto,
    ): String? {
        try {
            val entityNameFieldAnswerOrModelOne =
                questionnaireAnswerService.getAnswersByUserOrderId(
                    processingOrderId,
                    customerId,
                    AnswerSource.AnswerBank,
                ).entityName!!
            if (entityNameFieldAnswerOrModelOne.trim().isEmpty()) throw IllegalArgumentException("Entity name is empty")
            return entityNameFieldAnswerOrModelOne
        } catch (e: Exception) {
            logger.warnEvent(
                "printAndShip.getOrderInfo.questionnaireFieldAnswerNoEntityName",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "customerId" to customerId,
                    "error" to e,
                    "printRequestId" to printRequestId,
                    "fallbackCODEntityName" to completedOrderDetails.entityName,
                ),
            )
        }

        try {
            val entityNameAnswerBank =
                questionnaireAnswerService.getAnswersByUserOrderId(
                    processingOrderId,
                    customerId,
                    AnswerSource.AnswerBank,
                ).entityName!!
            if (entityNameAnswerBank.trim().isEmpty()) throw IllegalArgumentException("Entity name is empty")
            return entityNameAnswerBank
        } catch (e: Exception) {
            logger.warnEvent(
                "printAndShip.getOrderInfo.questionnaireAnswerBankNoEntityName",
                mapOf(
                    "processingOrderId" to processingOrderId,
                    "customerId" to customerId,
                    "error" to e,
                    "printRequestId" to printRequestId,
                    "fallbackCODEntityName" to completedOrderDetails.entityName,
                ),
            )
        }

        return if (!completedOrderDetails.entityName.isNullOrEmpty()) completedOrderDetails.entityName else "N/A"
    }

    fun setDelayTimerForMaintenance(): String? {
        val currentDate = LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        val thirdFridayAt9pm = currentDate.withDayOfMonth(1).withDayOfWeek(5).plusWeeks(2).withTime(21, 0, 0, 0)
        val nextSundayAt9pm = thirdFridayAt9pm.plusDays(2).withTime(21, 0, 0, 0)

        return if (currentDate.isAfter(thirdFridayAt9pm) && currentDate.isBefore(nextSundayAt9pm)) {
            nextSundayAt9pm.toString()
        } else {
            null
        }
    }

    companion object {
        const val LOCATION_CODE = "viatech"
        private val logger: Logger = LoggerFactory.getLogger(GetPrintOrderInfoDelegate::class.java)
    }
}
