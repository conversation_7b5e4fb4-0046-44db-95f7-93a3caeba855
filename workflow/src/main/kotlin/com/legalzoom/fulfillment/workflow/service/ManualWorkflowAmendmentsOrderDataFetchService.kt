package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.Constants.RETRY_ANSWERS_API
import com.legalzoom.fulfillment.workflow.variable.output
import io.github.resilience4j.retry.annotation.Retry
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ManualWorkflowAmendmentsOrderDataFetchService(
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Retry(name = RETRY_ANSWERS_API)
    fun fetchData(
        execution: DelegateExecution,
        processingOrderId: Int,
        customerId: String?,
    ) {
        logger.info("Fetching answers for processing order $processingOrderId and customer $customerId")
        val answers = questionnaireAnswerService.getAnswersByUserOrderId(processingOrderId, customerId, AnswerSource.AnswerBank)

        val entityType = answers.entityType?.let { ProductType.fromName(it) }

        val entityName = answers.entityName

        entityType ?: logger.warn("Unable to retrieve entity type from answers.")

        if (entityName.isNullOrEmpty()) {
            logger.warn("Unable to retrieve entity name from answers.")
        }

        val needNameCheck: Boolean = (answers.nameChange != null || answers.otherChange != null)

        execution.output {
            this.entityType = entityType?.name ?: answers.entityType
            this.entityName = entityName
            this.needNameCheck = needNameCheck
        }
    }
}
