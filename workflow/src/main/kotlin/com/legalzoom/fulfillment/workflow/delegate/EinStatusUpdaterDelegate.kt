package com.legalzoom.fulfillment.workflow.delegate

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import com.legalzoom.fulfillment.workflow.variable.toActivityFeedVariables
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class EinStatusUpdaterDelegate(
    private val ordersApiService: OrdersApiService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val processingOrderService: ProcessingOrderService,
    private val activityFeedServiceImpl: ActivityFeedService,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan("EinStatusUpdater")
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        val input = execution.input

        val orderResponse = ordersApiService.getOrders(input.orderId, null, null, null, null, null)

        val einOrderItem =
            orderResponse.order?.orderItems?.firstOrNull {
                it.processingOrder?.processId == ProductType.EIN.processId && it.isCancelled == false
            }

        var skipDocGenPrint = false

        if (einOrderItem != null) {
            logger.event(
                "ein.status.update.start",
                mapOf(
                    "orderId" to input.orderId,
                    "processingOrderId" to input.processingOrderId,
                    "parentProcessingOrderId" to input.parentProcessingOrderId,
                    "isAttachedOrder" to input.isAttachedOrder,
                ),
            )

            val answersResponse =
                questionnaireAnswerService.getAnswersByUserOrderId(
                    if (input.isAttachedOrder == true) input.parentProcessingOrderId!! else input.processingOrderId!!,
                    input.customerId,
                    AnswerSource.AnswerBank,
                )

            if (answersResponse.manualFilingEIN == "Yes") {
                val einProcessingStatus =
                    if (answersResponse.ein.isNullOrEmpty()) {
                        ProcessingOrderStatus.EinPreliminaryProblem
                    } else {
                        ProcessingOrderStatus.EinFilingComplete
                    }

                logger.event(
                    "ein.status.update",
                    mapOf(
                        "orderId" to input.orderId,
                        "processingOrderId" to input.processingOrderId,
                        "parentProcessingOrderId" to input.parentProcessingOrderId,
                        "isAttachedOrder" to input.isAttachedOrder,
                        "processingStatus" to einProcessingStatus,
                    ),
                )
                processingOrderService.updateProcessingOrderStatus(
                    input.processingOrderId!!,
                    input.customerId!!,
                    einProcessingStatus,
                    execution.toActivityFeedVariables(),
                )

                activityFeedServiceImpl.sendEvent(
                    einProcessingStatus,
                    execution.toActivityFeedVariables(),
                )
            }
        } else {
            skipDocGenPrint = true
            logger.event(
                "ein.status.no.update",
                mapOf(
                    "orderId" to input.orderId,
                    "processingOrderId" to input.processingOrderId,
                    "parentProcessingOrderId" to input.parentProcessingOrderId,
                    "isAttachedOrder" to input.isAttachedOrder,
                    "message" to "There was no EIN order item in the order.",
                ),
            )
        }

        execution.output {
            einFilingEnabled = false
            skipDocGenAndPrint = skipDocGenPrint
        }
    }
}
