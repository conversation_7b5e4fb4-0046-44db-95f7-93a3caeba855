package com.legalzoom.fulfillment.workflow.service.revv

import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.businessentities.BusinessEntitiesApi
import com.legalzoom.api.model.answer.AnswerSource
import com.legalzoom.api.model.answer.GroupAnswerDto
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.ordercontacts.ContactInfoDto
import com.legalzoom.api.model.ordercontacts.ContactType
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.revv.NPBusinessActivities
import com.legalzoom.fulfillment.service.data.revv.RevvAddress
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentField
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvMappingFields
import com.legalzoom.fulfillment.service.data.revv.SS4MappingFields
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.helper.DateFormatHelper
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.GetAnswersPayload
import com.legalzoom.fulfillment.workflow.variable.Variables
import org.springframework.http.HttpStatus
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

abstract class RevvFieldsRequestBuilder(
    private val ordersContactsApiService: OrdersContactsApiService,
    private val answerApi: AnswerApi,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private var businessEntitiesApi: BusinessEntitiesApi? = null,
) {
    abstract fun getTemplates(): MutableList<RevvDocumentType>

    abstract fun getCustomerDocumentType(): MutableMap<RevvDocumentType, CustomerDocumentType>

    abstract fun getMappingFields(templates: MutableList<RevvDocumentType>? = null): MutableList<RevvDocumentField>

    fun getContactInfo(
        orderId: Int?,
        customerId: String,
    ): ContactInfoDto {
        val orderContacts =
            ordersContactsApiService.getOrdersContacts(
                orderId,
                customerId,
            )
        var contactInfo = orderContacts?.contacts?.find { it.contactType == ContactType.Shipping }
        contactInfo = contactInfo ?: orderContacts?.contacts?.find { it.contactType == ContactType.Primary }
        return contactInfo!!
    }

    open fun getSS4Mapping(
        revDocumentMappingFields: MutableList<RevvDocumentField>,
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        obtainment: String,
    ): MutableList<RevvDocumentField> {
        return mutableListOf()
    }

    open fun getForm3500AMapping(
        revDocumentMappingFields: MutableList<RevvDocumentField>,
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
    ): MutableList<RevvDocumentField> {
        return mutableListOf()
    }

    fun getAnswerInfo(
        input: Variables,
        processingOrderId: Int? = null,
    ): QuestionnaireAnswerDto {
        // Once ModelOne Implementation started/Mapper been created we need to change this method
        val answers =
            answerApi.answersUserOrderIdSourceGet(
                processingOrderId ?: input.processingOrderId!!,
                AnswerSource.NUMBER_0,
                null,
                null,
                null,
                "1.0",
                input.customerId.toString(),
                true,
            )?.block()
        return answers?.questionnaireFieldGroupAnswers!!
    }

    fun getFieldAnswerValue(
        answers: QuestionnaireAnswerDto,
        fieldName: String,
    ): String {
        return answers.fieldAnswers?.firstOrNull {
            it.fieldName == fieldName
        }?.fieldValue ?: ""
    }

    fun getGroupAnswerValue(
        answers: QuestionnaireAnswerDto,
        fieldName: String,
    ): String {
        return answers.groupAnswers?.firstOrNull {
            it.fieldName == fieldName
        }?.fieldValue ?: ""
    }

    fun getGroupAnswerByValue(
        answers: QuestionnaireAnswerDto,
        value: String,
    ): List<GroupAnswerDto>? {
        return answers.groupAnswers?.filter {
            it.fieldValue.equals(value, true)
        }
    }

    fun getFinalLetterDocumentFields(input: Variables): MutableList<RevvDocumentField> {
        val contactInfo = getContactInfo(input.orderId, input.customerId.toString())
        val addressLine1 = contactInfo?.addressLine1?.takeIf { it.isNotEmpty() }?.let { "$it\n" } ?: ""
        val addressLine2 = contactInfo?.addressLine2?.takeIf { it.isNotEmpty() }?.let { "$it\n" } ?: ""
        val (streetLine, city, state, zip) =
            RevvAddress(
                addressLine1 + addressLine2,
                contactInfo?.city.orEmpty(),
                contactInfo?.state.orEmpty(),
                contactInfo?.zipCode.orEmpty(),
            )

        return mutableListOf(
            RevvDocumentField(
                RevvMappingFields.DATE.value,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")).toString(),
            ),
            RevvDocumentField(RevvMappingFields.COMPANY_NAME.value, input.entityName.toString()),
            RevvDocumentField(RevvMappingFields.ORDER_ID.value, input.orderId.toString()),
            RevvDocumentField(RevvMappingFields.SHIPPING_CONTACT_FIRST_NAME.value, contactInfo?.firstName ?: ""),
            RevvDocumentField(RevvMappingFields.SHIPPING_CONTACT_LAST_NAME.value, contactInfo?.lastName ?: ""),
            RevvDocumentField(RevvMappingFields.SHIPPING_CONTACT_ADDRESS.value, "$streetLine$city, $state, $zip"),
        )
    }

    fun addDesigneeDetails(revDocumentMappingFields: MutableList<RevvDocumentField>) {
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.DESIGNEES_NAME.value,
                "Erik Treutlein",
            ),
        )
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.DESIGNEE_ADDRESS.value,
                "11501 Domain Dr, Ste 200, Austin, TX 78758",
            ),
        )
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.DESIGNEES_TELEPHONE_NUMBER.value,
                "(*************",
            ),
        )
        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.DESIGNEES_FAX_NUMBER.value,
                "(*************",
            ),
        )
    }

    fun addRevvDocumentField(
        revDocumentMappingFields: MutableList<RevvDocumentField>,
        fieldName: String,
        fieldAnswerKey: String,
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        fixedValue: String? = null,
    ) {
        revDocumentMappingFields.add(
            RevvDocumentField(
                fieldName,
                fixedValue ?: getFieldAnswerValue(questionnaireAnswerResponse, fieldAnswerKey),
            ),
        )
    }

    fun getAnswers(
        processingOrderId: Int,
        customerId: String,
    ): GetAnswersPayload {
        val answers =
            questionnaireAnswerService.getAnswersByUserOrderId(
                processingOrderId,
                customerId,
                com.legalzoom.fulfillment.answersapi.model.AnswerSource.AnswerBank,
            )
        return answers
    }

    fun getBusinessEntityResponseByProcessingOrderId(
        processingOrderId: Int,
        customerId: String,
    ): GetEntityByEntityIdResponse {
        var response = GetEntityByEntityIdResponse()
        try {
            response =
                businessEntitiesApi?.businessEntitiesProcessingOrdersProcessingOrderIdGet(
                    processingOrderId,
                    "1.0",
                    customerId,
                    null,
                )?.blockSingle()!!
        } catch (e: WebClientResponseException) {
            if (e.statusCode != HttpStatus.NOT_FOUND) {
                throw e
            }
        }
        return response
    }

    fun getEinNumber(
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        businessEntity: GetEntityByEntityIdResponse,
        einNumberKey: String,
    ): String {
        var einNumber = getFieldAnswerValue(questionnaireAnswerResponse, einNumberKey)
        return einNumber.ifEmpty { businessEntity?.entity?.ein ?: "" }
    }

    fun getFormationDate(
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        businessEntity: GetEntityByEntityIdResponse,
        formationDateKey: String,
    ): String {
        var formationDate = getFieldAnswerValue(questionnaireAnswerResponse, formationDateKey)
        formationDate =
            if (formationDate.isNotEmpty()) {
                val convertedToDate = DateFormatHelper.getDate(formationDate, mutableListOf("yyyy-MM-dd"))
                if (convertedToDate != null) SimpleDateFormat("MM/dd/yyyy").format(convertedToDate) else formationDate
            } else {
                businessEntity?.entity?.formationDate?.format(
                    DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                ) ?: ""
            }
        // if it's a default date then do not consider
        return if (formationDate == SS4MappingFields.DEFAULT_FORMATION_DATE.value) "" else formationDate
    }

    fun getEinNumberAndFormationDate(
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        businessEntity: GetEntityByEntityIdResponse,
        einNumberKey: String,
        formationDateKey: String,
    ): Pair<String, String> {
        return Pair(
            getEinNumber(questionnaireAnswerResponse, businessEntity, einNumberKey),
            getFormationDate(questionnaireAnswerResponse, businessEntity, formationDateKey),
        )
    }

    fun getStateEntityId(
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        businessEntity: GetEntityByEntityIdResponse,
        stateIdKey: String,
    ): String {
        val stateEntityId = getFieldAnswerValue(questionnaireAnswerResponse, stateIdKey)
        return stateEntityId.ifEmpty { businessEntity.entity?.stateEntityNumber ?: "" }
    }

    fun dateWagesPaid(
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        answerKey: String,
    ): String {
        var firstDateWages: String? = getFieldAnswerValue(questionnaireAnswerResponse, answerKey)

        firstDateWages = DateFormatHelper.getDate(firstDateWages, mutableListOf("yyyy-MM-dd"), "MM/dd/yyyy")

        return if (firstDateWages.isNullOrEmpty()) SS4MappingFields.NOT_APPLICABLE.value else firstDateWages
    }

    fun getPurposeCategory(purposeCategory: String): String {
        return when (purposeCategory) {
            in NPBusinessActivities.getCharitableCategories() -> {
                "Charitable"
            }
            else -> {
                purposeCategory
            }
        }
    }
}
