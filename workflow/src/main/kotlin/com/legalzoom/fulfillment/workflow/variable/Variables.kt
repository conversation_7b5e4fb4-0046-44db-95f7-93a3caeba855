package com.legalzoom.fulfillment.workflow.variable

import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.dsd.docgen.DocGenJobRequestDto
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.model.ShippingInformationDto
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.service.data.UnifiedCommerceIds
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.activityFeed.ActivityFeedVariables
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.workflow.data.DMNCompany
import com.legalzoom.fulfillment.workflow.data.OrderExpirationData
import org.camunda.bpm.engine.DecisionService
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.delegate.VariableScope
import org.camunda.bpm.engine.runtime.ProcessInstance
import java.util.UUID

/**
 * **NOTE**: When adding additional strongly typed variables to this class - they should be written to account for
 * backward compatibility. If the variable classes are updated/deleted/renamed/moved without consideration for
 * backward compatibiltiy issues may arise with existing running processes in Camunda
 */
class Variables(mutableMap: MutableMap<String, Any?> = mutableMapOf()) : MutableMap<String, Any?> by mutableMap {
    private val variableMap = mutableMap.withDefault { null }

    var accountId: UUID? by variableMap
    var alreadyFiled: Boolean? by variableMap
    var conditionRecheckTimer: String? by variableMap
    var customerId: String? by variableMap
    var blocked: Boolean? by variableMap
    var blockingProcessIds: MutableList<Int>? by variableMap
    var entityName: String? by variableMap
    var orderId: Int? by variableMap
    var processId: Int? by variableMap
    var product: String? by variableMap
    var processingOrderId: Int? by variableMap
    var jurisdiction: String? by variableMap
    var processingStatusId: String? by variableMap
    var correlationId: String? by variableMap
    var validationError: Boolean? by variableMap
    var validationErrors: MutableList<ValidationError>? by variableMap
    var documentResults: MutableMap<String, Any?>? by variableMap
    var status: String? by variableMap
    var message: String? by variableMap
    var evidenceFilePath: String? by variableMap
    var evidenceTransactionNumber: String? by variableMap
    var evidenceTransactionMessage: String? by variableMap
    var disposition: String? by variableMap
    var errorMessage: String? by variableMap
    var errorCode: String? by variableMap
    var humanTaskCreationEnabled: Boolean? by variableMap
    var postFilingEnabled: Boolean? by variableMap // TODO: This can be deleted once the bpmn is updated
    var documentType: String? by variableMap
    var effectiveDate: String? by variableMap
    var jobId: String? by variableMap // JobID from Alchemy Holding Area
    var childProcessId: Int? by variableMap
    var childProcessingOrderId: Int? by variableMap
    var documentsGenerationResponse: DocumentsGenerationResponse? by variableMap
    var documentPaths: MutableList<String>? by variableMap
    var fieldName: String? by variableMap
    var fieldValue: String? by variableMap
    var preFilingQcEnabled: Boolean? by variableMap
    var employerIdentificationNumber: String? by variableMap
    var einFilingEnabled: Boolean? by variableMap
    var fulfillmentSystem: String? by variableMap
    var hasLivingWillCard: Boolean? by variableMap
    var grantorName: String? by variableMap
    var coGrantorName: String? by variableMap
    var isPrintingRequired: Boolean? by variableMap
    var entityId: String? by variableMap
    var documentId: String? by variableMap
    var rpaQueueItemId: Long? by variableMap

    // ein
    var einBotFailureDmnOutput: Map<String, Any?>? by variableMap
    var shouldDelay: Boolean? by variableMap

    // should have one of three values: [null,"system","business"]
    var botExceptionType: String? by variableMap
    var data: Map<String, Any?>? by variableMap
    var printStatus: String? by variableMap
    var printRequestId: String? by variableMap
    var expediteSpeed: String? by variableMap
    var company: DMNCompany? by variableMap
    var retriable: Boolean? by variableMap
    var retryCount: Int? by variableMap
    var orderIncludesEIN: Boolean? by variableMap
    var orderIncludesInitialReports: Boolean? by variableMap
    var orderIncludesOperatingAgreement: Boolean? by variableMap
    var orderIncludesPatentSearch: Boolean? by variableMap
    var orderIncludesPatentIllustration: Boolean? by variableMap
    var crmId: String? by variableMap
    var userTaskCompletionInitiator: String? by variableMap // var flag used to denote order coming from NGF
    var caseCompleted: Boolean? by variableMap
    var eventType: String? by variableMap
    var automatedQCApproval: Boolean? by variableMap
    var automatedQCMatchesManual: Boolean? by variableMap
    var sfCorrelationId: String? by variableMap
    var sfDelayTimer: String? by variableMap
    var alreadyAppliedSsorcoCheat: Boolean? by variableMap
    var printResponseDueDuration: String? by variableMap
    var sfCreatedSelfServeOrco: Boolean? by variableMap
    var sfCreatedCase: Boolean? by variableMap
    var shouldFileCaliforniaSOI: Boolean? by variableMap
    var documentFilingState: Boolean? by variableMap
    var isFaxFilingState: Boolean? by variableMap
    var muteOrcoNotifications: Boolean? by variableMap
    var workOrderId: UUID? by variableMap
    var workOrderItemId: UUID? by variableMap
    var orderStatusEmailNotificationsEnabled: Boolean? by variableMap
    var byPassSmartCommDocGen: Boolean? by variableMap
    var parentProcessingOrderId: Int? by variableMap
    var parentProcessId: Int? by variableMap
    var isAttachedOrder: Boolean? by variableMap
    var parentEventPhase: String? by variableMap
    var skipDocGenAndPrint: Boolean? by variableMap
    var requestedDocumentsCSV: String? by variableMap
    var setDocStatusToActive: Boolean? by variableMap
    var stateEntityNumber: String? by variableMap
    var triggeredTimerName: String? by variableMap
    var sfCaseCorrelationId: String? by variableMap
    var fraudCheckPerformed: Boolean? by variableMap

    // Manual Filing Process
    var action: String? by variableMap
    var manualWorkflowStep: String? by variableMap
    var manualWorkflowStatus: String? by variableMap
    var manualDocGenEnabled: Boolean? by variableMap
    var salesforceEventPhase: String? by variableMap
    var entityType: String? by variableMap
    var manualPostFilingQcEnabled: Boolean? by variableMap
    var docTemplateHint: String? by variableMap
    var preStepOrderStatuses: List<String>? by variableMap
    var caseType: String? by variableMap
    var convertEntityTo: String? by variableMap
    var supressVariableClearAfterSFCaseClosure: String? by variableMap // populated with a disposition string
    var manualSosFilingRequired: Boolean? by variableMap
    var notificationTemplate: String? by variableMap
    var needNameCheck: Boolean? by variableMap
    var countyName: String? by variableMap
    var isPublicationNeeded: Boolean? by variableMap
    var iSLZRA: Boolean? by variableMap
    var alchemyDocUploadedBy: String? by variableMap
    var registrantType: String? by variableMap
    var ss4GeneratedForCustomer: Boolean? by variableMap

    // ALTM Filing Process
    var altmPostFilingQcEnabled: Boolean? by variableMap
    var altmVariables: MutableMap<String, Any?>? by variableMap

    // EP 2.0
    val epIsQCEligible: Boolean by variableMap

    // Print & Ship
    var printErrorMessage: String? by variableMap
    var printDocumentList: List<PrintDocumentInfo>? by variableMap
    var shippingInformation: ShippingInformationDto? by variableMap
    var shipMethod: String? by variableMap
    var kitRequired: Boolean? by variableMap
    var maintenanceDelayTimer: String? by variableMap
    var hasLogoSearch: Boolean? by variableMap
    var trustName: String? by variableMap

    // 501c3
    var paygovTrackingId: String? by variableMap
    var agencyTrackingId: String? by variableMap
    var submissionDate1023EZ: String? by variableMap
    var document1023EZId: String? by variableMap
    var document3500AId: String? by variableMap

    // Order Expiry
    var orderExpiryEnabled: Boolean? by variableMap
    var isOrderEligibleForExpiry: Boolean? by variableMap
    var hasExpiryNotification: Boolean? by variableMap
    var shouldExpireOrder: Boolean? by variableMap
    var expiryEligibleOrders: List<OrderExpirationData> by variableMap
    var expiringOrderInfo: OrderExpirationData by variableMap
    var orderMigrated: Boolean? by variableMap
    var cp2OrderItemId: String? by variableMap
    var cp2OrderId: String? by variableMap

    // Biz-Formation DSD DocGen
    var preFilingDSDDocGenJobId: String? by variableMap
    var postFilingDSDDocGenRequests: List<DocGenJobRequestDto>? by variableMap
    var preFilingDSDDocGenRequests: List<DocGenJobRequestDto>? by variableMap

    /**
     * Represents both primary identifiers for the CP1 and CP2 commerce systems.
     */
    val commerceIds get() = UnifiedCommerceIds(cp1OrderId = this.orderId, cp2OrderItemId = this.cp2OrderItemId)

    /**
     * For cases when a string order id is needed from either commerce system, use this.
     * For example, Salesforce cases need this.
     */
    val unifiedOrderId: String get() =
        if (this.commerceIds.isForSystem(CommerceSystem.CP1)) {
            commerceIds.cp1OrderId!!.toString()
        } else {
            this.cp2OrderId!!
        }

    fun isForState(state: State): Boolean {
        return this.jurisdiction == state.abbreviation
    }

    fun isForProduct(product: ProductType): Boolean {
        return this.processId == product.processId
    }

    fun addValidationError(validationError: ValidationError) {
        val newValidationErrors = this.validationErrors ?: mutableListOf()
        newValidationErrors.add(validationError)

        this.validationErrors = newValidationErrors
        this.validationError = true
    }

    override fun equals(other: Any?): Boolean {
        if (other == null || other !is Variables) {
            return false
        }

        return variableMap == other.variableMap
    }

    override fun hashCode(): Int {
        return variableMap.hashCode()
    }
}

fun variables(variables: Variables.() -> Unit) = Variables().apply(variables)

fun RuntimeService.startProcessInstanceByKey(
    processDefinitionKey: String,
    variables: Variables.() -> Unit,
): ProcessInstance = this.startProcessInstanceByKey(processDefinitionKey, variables(variables))

fun RuntimeService.startProcessInstanceByKey(
    processDefinitionKey: String,
    businessKey: String,
    variables: Variables.() -> Unit,
): ProcessInstance = this.startProcessInstanceByKey(processDefinitionKey, businessKey, variables(variables))

val VariableScope.input: Variables
    get() = Variables(this.variables)

val VariableScope.inputLocal: Variables
    get() = Variables(this.variablesLocal)

fun VariableScope.output(variables: Variables.() -> Unit) {
    this.variables = variables(variables)
}

fun VariableScope.outputLocal(variables: Variables.() -> Unit) {
    this.variablesLocal = variables(variables)
}

fun DecisionService.evaluateDecisionTableByKey(
    decisionDefinitionKey: String,
    variables: Variables.() -> Unit,
): List<Variables> =
    this.evaluateDecisionTableByKey(decisionDefinitionKey, variables(variables))
        .resultList.map { Variables(it) }

fun VariableScope.toActivityFeedVariables(): ActivityFeedVariables {
    val vars = input
    return ActivityFeedVariables(
        accountId = vars.accountId,
        customerId = vars.customerId,
        orderStatusEmailNotificationsEnabled = vars.orderStatusEmailNotificationsEnabled ?: false,
        processId = vars.processId,
        productName = vars.processId?.let { ProductType.fromProcessIdNullable(it)?.productName } ?: "LLC",
        entityName = vars.entityName ?: "",
        entityType =
            vars.entityType ?: (
                vars.processId?.let { AnswersEntityType.fromProcessIdNullable(it)?.name }
                    ?: "LLC"
            ),
        jurisdiction = vars.jurisdiction,
        evidenceTransactionNumber = vars.evidenceTransactionNumber,
        employerIdentificationNumber = vars.employerIdentificationNumber,
        orderId = vars.orderId,
        processingOrderId = vars.processingOrderId,
        childProcessingOrderId = vars.childProcessingOrderId,
        paygovTrackingId = vars.paygovTrackingId,
        agencyTrackingId = vars.agencyTrackingId,
        submissionDate1023EZ = vars.submissionDate1023EZ,
        document1023EZId = vars.document1023EZId,
        document3500AId = vars.document3500AId,
    )
}
