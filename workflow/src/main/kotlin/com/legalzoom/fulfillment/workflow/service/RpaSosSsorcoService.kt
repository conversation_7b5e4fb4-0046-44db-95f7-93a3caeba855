package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants.EIN_SS4_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.Constants.SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.Constants.SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.SosSsorcoType.ADDRESS
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.SosSsorcoType.EIN_SS4
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.SosSsorcoType.NAME
import com.legalzoom.fulfillment.workflow.variable.Variables
import org.springframework.stereotype.Service

@Service
class RpaSosSsorcoService(
    val featureToggleService: ORCOFeatureToggleService,
) {
    companion object {
        const val INVALID_NAME_PREFIX =
            "Company Name Error: The business name you selected conflicts with an existing business. " +
                "Please provide a new name to get your order back on track."
        const val INVALID_ADDRESS_PREFIX = "Company Address Error"
        const val REF_101_PREFIX = "We are unable to provide you with an EIN. - Reference Number 101"
        const val REF_105_PREFIX = "We are unable to provide you with an EIN. - Reference Number 105"
        const val REF_109_PREFIX = "Technical Difficulties - Reference Number 109"
        const val REF_110_PREFIX = "Technical Difficulties - Reference Number 110"
        const val REF_111_PREFIX = "Technical Difficulties - Reference Number 111"
        const val REF_114_PREFIX = "We are unable to provide you with an EIN. - Reference Number 114"
        const val INVALID_BUSINESS_LOCATION_PREFIX = "Order validation errors: Business Location Address line1 must not exceed"
        const val EIN_APPLICATION_ERROR_PREFIX =
            "An application error occurred. - " +
                "EIN Application was not submitted. Order may not be retried."
        const val AUTHENTICATION_ERROR_PREFIX =
            "Authenticate Error(s) has occurred: The information you have entered does not match IRS records. - " +
                "EIN Application was not submitted. Order can be retried."

        const val FORCE_NAME_ERROR = "SSORCO-Filing-Name"
        const val FORCE_ADDRESS_ERROR = "SSORCO-Filing-Address"
        const val FORCE_REF_ERROR = "SSORCO-Reference"
    }

    enum class SosSsorcoType(val prefix: List<String>, val cheatCode: String, val catId: String) {
        NAME(listOf(INVALID_NAME_PREFIX), FORCE_NAME_ERROR, SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID),
        ADDRESS(listOf(INVALID_ADDRESS_PREFIX), FORCE_ADDRESS_ERROR, SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID),
        EIN_SS4(
            listOf(
                REF_101_PREFIX,
                REF_105_PREFIX,
                REF_109_PREFIX,
                REF_110_PREFIX,
                REF_111_PREFIX,
                REF_114_PREFIX,
                INVALID_BUSINESS_LOCATION_PREFIX,
                EIN_APPLICATION_ERROR_PREFIX,
                AUTHENTICATION_ERROR_PREFIX,
            ),
            FORCE_REF_ERROR,
            EIN_SS4_ORCO_CAT_ID,
        ),
        ;

        fun matches(
            errorMessage: String?,
            entityName: String?,
        ) = this.prefix.any { errorMessage?.startsWith(it) == true } || entityName?.contains(this.cheatCode) == true
    }

    private fun getSsorcoType(
        errorMessage: String? = null,
        input: Variables,
    ): SosSsorcoType? {
        if (!featureToggleService.isSsOrcoEnabled(input.customerId, input.jurisdiction, input.processId)) {
            return null
        }

        if (NAME.matches(errorMessage, input.entityName) &&
            featureToggleService.isSsSosNameOrcoEnabled(input.customerId, input.jurisdiction, input.processId)
        ) {
            return NAME
        }

        if (ADDRESS.matches(errorMessage, input.entityName) &&
            featureToggleService.isSsSosAddressOrcoEnabled(input.customerId, input.jurisdiction, input.processId)
        ) {
            return ADDRESS
        }

        if (EIN_SS4.matches(errorMessage, input.entityName) &&
            featureToggleService.isSS4SSOrcoEnabled(input.customerId, input.jurisdiction, input.processId)
        ) {
            return EIN_SS4
        }

        return null
    }

    fun createValidationError(
        errorMessage: String,
        input: Variables,
    ): ValidationError {
        val ssOrcoType = getSsorcoType(errorMessage, input)

        return ValidationError(
            errorMessage,
            // "Detail" will be the error shown in salesforce
            data = mapOf("Detail" to errorMessage),
            isSelfServe = ssOrcoType != null,
            orcoReasonCategoryId = ssOrcoType?.catId,
        )
    }

    fun checkCheatCodes(input: Variables) {
        if (input.alreadyAppliedSsorcoCheat != true) {
            getSsorcoType(input = input)?.let {
                input.alreadyAppliedSsorcoCheat = true
                val validationError = createValidationError(it.prefix[0], input)
                input.addValidationError(validationError)
            }
        }
    }
}
