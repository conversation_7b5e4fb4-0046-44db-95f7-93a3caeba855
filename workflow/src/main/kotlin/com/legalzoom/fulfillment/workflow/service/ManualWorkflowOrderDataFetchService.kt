package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.extensions.packageOrderItem
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.Constants.RETRY_ANSWERS_API
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.github.resilience4j.retry.annotation.Retry
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ManualWorkflowOrderDataFetchService(
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val ordersApiService: OrdersApiService,
    private val orderItemsApiService: OrdersOrderItemsApiService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Retry(name = RETRY_ANSWERS_API)
    fun fetchData(
        execution: DelegateExecution,
        processingOrderId: Int?,
        customerId: String?,
    ) {
        logger.info("Fetching answers for processing order $processingOrderId and customer $customerId")

        val input = execution.input

        var orderId = input.orderId

        if (orderId == null || orderId <= 0) {
            val orderItemsResponse =
                orderItemsApiService.getOrdersOrderItems(
                    processingOrderId,
                    xLzApiVersion = "1.0",
                    xLzCustomerid = null,
                    xLzAuthorize = null,
                )

            orderId = orderItemsResponse.orderId!!
        }

        val orderResponse =
            ordersApiService.getOrders(
                orderId,
                productIdType = null,
                showOrderItemTree = null,
                xLzApiVersion = "1.0",
                xLzCustomerid = null,
                xLzAuthorize = null,
            )

        val packageOrderItem = orderResponse.order?.packageOrderItem()

        // The processingOrderId may belong to child orderItem, for example, Child Annual report
        // in that case we would need parent processingOrderId to fetch correct answers
        val processingOrderIdToFetchAnswers =
            if (processingOrderId == packageOrderItem!!.processingOrder!!.processingOrderId!!) {
                processingOrderId
            } else {
                packageOrderItem.processingOrder!!.processingOrderId!!
            }

        val answers =
            questionnaireAnswerService.getAnswersByUserOrderId(
                processingOrderIdToFetchAnswers,
                customerId,
                AnswerSource.AnswerBank,
            )

        answers.entityType ?: logger.warnEvent(
            "answers.missing.entity.type",
            mapOf(
                "message" to "Unable to retrieve entity type from answers.",
                "processingOrderId" to processingOrderId,
                "processingOrderIdToFetchAnswers" to processingOrderIdToFetchAnswers,
                "processId" to input.processId!!,
                "parentProcessId" to packageOrderItem.processingOrder!!.processId!!,
                "customerId" to input.customerId,
            ),
        )

        val entityType =
            answers.entityType?.let { AnswersEntityType.fromLabel(it) }
                ?: answers.entityType?.let { ProductType.fromName(it) }
                ?: null

        val entityName = answers.entityName

        entityType ?: logger.warnEvent(
            "ngx.missing.entity.type",
            mapOf(
                "message" to "Unable to retrieve entity type from answers.",
                "entityType" to answers.entityType,
                "processingOrderId" to processingOrderId,
                "processingOrderIdToFetchAnswers" to processingOrderIdToFetchAnswers,
                "processId" to input.processId!!,
                "parentProcessId" to packageOrderItem.processingOrder!!.processId!!,
                "customerId" to input.customerId,
            ),
        )

        entityName ?: logger.warnEvent(
            "ngx.answers.missing.entity.name",
            mapOf(
                "message" to "Unable to retrieve entity name from answers.",
                "processingOrderId" to processingOrderId,
                "processingOrderIdToFetchAnswers" to processingOrderIdToFetchAnswers,
                "processId" to input.processId!!,
                "parentProcessId" to packageOrderItem.processingOrder!!.processId!!,
                "customerId" to input.customerId,
            ),
        )

        execution.output {
            this.entityType = entityType?.name
            this.entityName = entityName
        }
    }
}
