package com.legalzoom.fulfillment.workflow.delegate.printandship

import com.fasterxml.jackson.databind.ObjectMapper
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.model.dsd.printship.DSDErrorResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.printandship.data.offsetDateTimeDeserializerModule
import com.legalzoom.fulfillment.printandship.data.toNoPIIJsonString
import com.legalzoom.fulfillment.printandship.repository.VendorCodeRepository
import com.legalzoom.fulfillment.printandshipapi.enumeration.RequestType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.Constants
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.camunda.bpm.engine.impl.util.ClockUtil
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.util.Calendar
import java.util.Date

@Component
class ViatechDelegate(
    private val vendorCodeRepository: VendorCodeRepository,
    private val salesforceApiService: SalesforceApiService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val printShipJobsApi: PrintShipJobsApi,
    private val objectMapper: ObjectMapper,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    companion object {
        const val DEFAULT_EXPECTED_DAYS_TO_DELIVER = 3
        const val WEEKEND_DAYS_TO_DELIVER = 6
    }

    @WithSpan("ViatechDelegate")
    @Transactional
    override fun execute(execution: DelegateExecution) {
        // TODO: After? switching to only DSD print, update this so it only maps i/o from execution input to
        //  ViatechDelegateService and decouple the input: Variables (Service cannot access this type)
        val input = execution.input
        val printVendorCode =
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                "viatech",
                input.processId!!,
                RequestType.PRINT,
            ).first().vendorCode

        val kitVendorCode = determineKitVendorCode(input)

        val printRequestId = createDsdPrintJob(input, printVendorCode, kitVendorCode)
        val dueInNumDays = getDueInNumDays()
        val camundaDueDateCycleFormat = "P${dueInNumDays}D"

        execution.output {
            this.printResponseDueDuration = camundaDueDateCycleFormat
            this.printRequestId = printRequestId
        }

        try {
            salesforceApiService.addLedgerNote(
                AddLedgerNoteRequest(
                    objectType = "note",
                    customerOrderNumber = input.orderId.toString(),
                    processingNumber = input.processingOrderId.toString(),
                    description =
                        "Print request sent successfully",
                    createdBy = Constants.SALESFORCE_USER,
                ),
            )
        } catch (e: Exception) {
            logger.error("Adding Ledger Note Failed: ${e.message}", e)
        }
    }

    private fun determineKitVendorCode(input: Variables): String? {
        return if (input.kitRequired == true) {
            val kitProcessId =
                if (input.processId == ProductType.Conversion.processId) {
                    val convertedEntityType =
                        input.convertEntityTo
                            ?: questionnaireAnswerService.getAnswersByUserOrderId(
                                input.processingOrderId!!, input.customerId, AnswerSource.AnswerBank,
                            ).convertEntityTo
                    ProductType.fromName(convertedEntityType)!!.processId
                } else {
                    input.processId
                }

            val vendorCodes =
                vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                    "viatech",
                    kitProcessId!!,
                    RequestType.KIT,
                )

            if (vendorCodes.isEmpty()) {
                logger.errorEvent(
                    "printAndShip.viatechDelegate.noKitVendorCode",
                    mapOf(
                        "processingOrderId" to input.processingOrderId,
                        "processId" to input.processId,
                        "printRequestId" to input.printRequestId,
                    ),
                )
                throw Exception("Could not find a kit vendor code for processId ${input.processId}")
            }

            vendorCodes.first().vendorCode
        } else {
            null
        }
    }

    // TODO: After switch to DSD, extract this to a separate class and remove deps from delegate
    private fun createDsdPrintJob(
        input: Variables,
        printVendorCode: String,
        kitVendorCode: String?,
    ): String {
        val request = input.toPrintAndShipRequest(printVendorCode = printVendorCode, kitVendorCode = kitVendorCode)
        logger.event(
            "printAndShip.viatechDelegate.createDsdPrintJob",
            mapOf(
                "processingOrderId" to input.processingOrderId,
                "jobRequest" to request.toNoPIIJsonString(),
            ),
        )

        try {
            val dsdPrintJobResponse = printShipJobsApi.createJobAsync(request).blockSingle()
            logger.event(
                "printAndShip.viatechDelegate.createDsdPrintJob.success",
                mapOf(
                    "processingOrderId" to input.processingOrderId,
                    "jobId" to dsdPrintJobResponse.id,
                    "jobRequest" to request.toNoPIIJsonString(),
                ),
            )

            return dsdPrintJobResponse.id
        } catch (wcre: WebClientResponseException) {
            val dsdErrorResponse =
                objectMapper.registerModules(offsetDateTimeDeserializerModule())
                    .readValue(wcre.responseBodyAsString, DSDErrorResponse::class.java)

            logger.errorEvent(
                "printAndShip.viatechDelegate.createDsdPrintJob.failure",
                mapOf(
                    "processingOrderId" to input.processingOrderId,
                    "jobRequest" to request.toNoPIIJsonString(),
                    "error" to dsdErrorResponse.message,
                ),
            )
            throw Exception("Failed to create print job: ${dsdErrorResponse.message}", wcre)
        }
    }

    private fun getDueInNumDays(): Int {
        val currentDate: Date = ClockUtil.getCurrentTime()
        val deliveryCalendarDate: Calendar = Calendar.getInstance()
        deliveryCalendarDate.time = currentDate
        deliveryCalendarDate.add(Calendar.DAY_OF_MONTH, DEFAULT_EXPECTED_DAYS_TO_DELIVER)

        return when (deliveryCalendarDate.get(Calendar.DAY_OF_WEEK)) {
            Calendar.MONDAY, Calendar.SATURDAY, Calendar.SUNDAY -> WEEKEND_DAYS_TO_DELIVER
            else -> DEFAULT_EXPECTED_DAYS_TO_DELIVER
        }
    }
}
