package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.workflow.variable.Variables
import org.camunda.bpm.engine.delegate.DelegateExecution

interface DocumentFilingStateService {
    fun getProductType(): ProductType

    fun documentRequired(execution: DelegateExecution): Boolean

    fun documentRequiredByVariables(variables: Variables): <PERSON>olean
}
