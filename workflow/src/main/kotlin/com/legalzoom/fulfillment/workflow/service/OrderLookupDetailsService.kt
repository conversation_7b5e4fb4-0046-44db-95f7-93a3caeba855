package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.extensions.packageOrderItem
import com.legalzoom.fulfillment.common.logging.warnEvent
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class OrderLookupDetailsService(
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val ordersApiService: OrdersApiService,
    private val orderItemsApiService: OrdersOrderItemsApiService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    fun getConvertEntityTo(
        processingOrderId: Int,
        processId: Int,
        customerId: String,
    ): String? {
        var convertEntityTo: String? = null
        when (processId) {
            ProductType.Conversion.processId -> {
                val answers =
                    questionnaireAnswerService.getAnswersByUserOrderId(
                        processingOrderId,
                        customerId,
                        AnswerSource.AnswerBank,
                    )

                val entityType = answers.entityType?.let { ProductType.fromName(it) }

                val convertEntityToObj = answers.convertEntityTo?.let { ProductType.fromName(it) }
                convertEntityTo = convertEntityToObj?.name
                val entityName = answers.entityName

                entityType ?: logger.warn("Unable to retrieve entity type from answers.")

                if (entityName.isNullOrEmpty()) {
                    logger.warn("Unable to retrieve entity name from answers.")
                }

                convertEntityToObj ?: logger.warn("Unable to retrieve convert to entity name from answers.")
            }
        }

        return convertEntityTo
    }

    fun getEntityType(
        processingOrderId: Int,
        processId: Int,
        customerId: String,
    ): String? {
        var orderId: Int? = 0
        var entityType: String? = null
        when (processId) {
            ProductType.AnnualReports.processId,
            ProductType.InitialReports.processId,
            ProductType.CertificateofGoodStanding.processId,
            ProductType.CertifiedCopies.processId,
            ProductType.CorporateDissolution.processId,
            ProductType.ForeignQualification.processId,
            ProductType.RegisteredAgentService.processId,
            ProductType.OperatingAgreement.processId,
            ProductType.ByLawsAndResolutions.processId,
            ProductType.BOIR.processId,
            -> {
                val orderItemsResponse =
                    orderItemsApiService.getOrdersOrderItems(
                        processingOrderId,
                        xLzApiVersion = "1.0",
                        xLzCustomerid = null,
                        xLzAuthorize = null,
                    )

                orderId = orderItemsResponse.orderId!!
                val orderResponse =
                    ordersApiService.getOrders(
                        orderId,
                        productIdType = null,
                        showOrderItemTree = null,
                        xLzApiVersion = "1.0",
                        xLzCustomerid = null,
                        xLzAuthorize = null,
                    )

                val packageOrderItem = orderResponse.order?.packageOrderItem()

                // The processingOrderId may belong to child orderItem, for example, Child Annual report
                // in that case we would need parent processingOrderId to fetch correct answers
                val processingOrderIdToFetchAnswers =
                    if (processingOrderId == packageOrderItem!!.processingOrder!!.processingOrderId!!) {
                        processingOrderId
                    } else {
                        packageOrderItem.processingOrder!!.processingOrderId!!
                    }
                val answers =
                    questionnaireAnswerService.getAnswersByUserOrderId(processingOrderIdToFetchAnswers, customerId, AnswerSource.AnswerBank)
                val entityTypeObj =
                    if (processingOrderId == packageOrderItem.processingOrder!!.processingOrderId) {
                        answers.entityType?.let { AnswersEntityType.fromLabel(it) }
                            ?: answers.entityType?.let { ProductType.fromName(it) }
                    } else {
                        ProductType.fromProcessId(packageOrderItem.processingOrder!!.processId!!)
                    }
                entityType = entityTypeObj?.name
                entityType ?: logger.warnEvent(
                    "ngx.answers.missing.entity.type",
                    mapOf(
                        "message" to "Unable to retrieve entity type from answers.",
                        "processingOrderId" to processingOrderId,
                        "processingOrderIdToFetchAnswers" to processingOrderIdToFetchAnswers,
                        "processId" to packageOrderItem.processingOrder!!.processId!!,
                    ),
                )
            }
            ProductType.Amendment.processId -> {
                val answers =
                    questionnaireAnswerService.getAnswersByUserOrderId(processingOrderId, customerId, AnswerSource.AnswerBank)

                entityType = answers.entityType?.let { ProductType.fromName(it) }?.name
            }

            ProductType.Conversion.processId -> {
                val answers =
                    questionnaireAnswerService.getAnswersByUserOrderId(processingOrderId, customerId, AnswerSource.AnswerBank)

                entityType = answers.entityType?.let { ProductType.fromName(it) }?.name
            }
            ProductType.LLP.processId,
            ProductType.LP.processId,
            ProductType.DBA.processId,
            -> {
                val answers =
                    questionnaireAnswerService.getAnswersByUserOrderId(processingOrderId, customerId, AnswerSource.AnswerBank)
                entityType = answers.entityType
            }
        }
        return entityType
    }
}
