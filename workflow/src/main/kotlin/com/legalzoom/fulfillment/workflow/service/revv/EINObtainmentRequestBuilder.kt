package com.legalzoom.fulfillment.workflow.service.revv

import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.revv.NPBusinessActivities
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentField
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvMappingFields
import com.legalzoom.fulfillment.service.data.revv.SS4BusinessActivities
import com.legalzoom.fulfillment.service.data.revv.SS4EntityType
import com.legalzoom.fulfillment.service.data.revv.SS4MappingFields
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.helper.DateFormatHelper
import com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.States
import com.legalzoom.fulfillment.workflow.variable.Variables

class EINObtainmentRequestBuilder(
    private val input: Variables,
    private val ordersContactsApiService: OrdersContactsApiService,
    private val answerApi: AnswerApi,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val featureToggleService: FeatureToggleService,
    private val revvDocumentGenerationService: RevvDocumentGenerationService,
) : RevvFieldsRequestBuilder(ordersContactsApiService, answerApi, questionnaireAnswerService) {
    override fun getTemplates(): MutableList<RevvDocumentType> {
        var templates = mutableListOf<RevvDocumentType>()

        if (isSS4DocGenEnabled()) {
            val answers = getAnswers(input.parentProcessingOrderId!!, input.customerId!!)
            if (answers.ein.isNullOrBlank()) {
                templates.add(RevvDocumentType.SS4_PREPARED)
                input.setDocStatusToActive = true
                return templates
            } else {
                templates.add(RevvDocumentType.SS4_OBTAINED)
            }
        }
        templates.add(RevvDocumentType.EIN_FINAL_LETTER)

        return templates
    }

    private fun isSS4DocGenEnabled() =
        featureToggleService.isRevvSS4DocGenEnabled(
            input.customerId,
            input.jurisdiction,
            processId = input.parentProcessId,
        )

    override fun getCustomerDocumentType(): MutableMap<RevvDocumentType, CustomerDocumentType> {
        return mutableMapOf(
            RevvDocumentType.EIN_FINAL_LETTER to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "Final Letter",
                ),
            RevvDocumentType.SS4_PREPARED to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "SS4 Prepared",
                ),
            RevvDocumentType.SS4_OBTAINED to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "SS4 Obtained",
                ),
        )
    }

    override fun getMappingFields(templates: MutableList<RevvDocumentType>?): MutableList<RevvDocumentField> {
        var revDocumentMappingFields: MutableList<RevvDocumentField> = mutableListOf()

        // SS4 Mapping for Document Requested
        if (templates?.any {
                it in
                    mutableListOf(RevvDocumentType.SS4_OBTAINED, RevvDocumentType.SS4_PREPARED)
            }!!
        ) {
            val requestBuilder =
                revvDocumentGenerationService.getRevvRequestBuilder(input, input.parentProcessId)
                    ?: throw NotImplementedError(
                        "EINObtainmentRequestBuilder: Product Not configured for the revv docgen, " +
                            "Rev template or mapping not available, Please escalate to Engineering",
                    )
            requestBuilder.getSS4Mapping(
                revDocumentMappingFields,
                getAnswerInfo(input, input.parentProcessingOrderId),
                if (templates.contains(
                        RevvDocumentType.SS4_OBTAINED,
                    )
                ) {
                    SS4MappingFields.EIN_OBTAINED.value
                } else {
                    SS4MappingFields.OBTAINMENT_REQUIRED.value
                },
            )
        }

        // Final Letter Mapping Fields
        if (templates.contains(RevvDocumentType.EIN_FINAL_LETTER)) {
            revDocumentMappingFields.addAll(getFinalLetterDocumentFields(input))

            val answers = getAnswers(input.parentProcessingOrderId!!, input.customerId!!)

            revDocumentMappingFields.add(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.EIN.productName,
                ),
            )
            revDocumentMappingFields.add(
                RevvDocumentField(
                    RevvMappingFields.EIN_NUMBER.value,
                    answers.ein ?: "",
                ),
            )
        }

        return revDocumentMappingFields
    }

    override fun getSS4Mapping(
        revDocumentMappingFields: MutableList<RevvDocumentField>,
        questionnaireAnswerResponse: QuestionnaireAnswerDto,
        obtainment: String,
    ): MutableList<RevvDocumentField> {
        val einNumber = getFieldAnswerValue(questionnaireAnswerResponse, "EIN_Number_hidden")
        val formationDate =
            DateFormatHelper.getDate(
                getFieldAnswerValue(questionnaireAnswerResponse, "formation_date"),
                mutableListOf("yyyy-MM-dd"),
                "MM/dd/yyyy",
            )

        val businessState =
            States.fromAbbreviation(
                getFieldAnswerValue(questionnaireAnswerResponse, "sState"),
            )?.displayName ?: ""

        val mailingAddress1 = getFieldAnswerValue(questionnaireAnswerResponse, "street_address_1")
        val mailingAddress2 = getFieldAnswerValue(questionnaireAnswerResponse, "street_address_2")
        val mailingAddress =
            if (mailingAddress2.isNotEmpty()) "$mailingAddress1, $mailingAddress2" else mailingAddress1

        val mailingCityStateZip =
            getFieldAnswerValue(questionnaireAnswerResponse, "City") + ", " +
                businessState + " " +
                getFieldAnswerValue(questionnaireAnswerResponse, "sZip")

        val businessCounty =
            mutableListOf(
                getFieldAnswerValue(questionnaireAnswerResponse, "sCounty"),
                getFieldAnswerValue(questionnaireAnswerResponse, "business_county"),
            ).firstOrNull { it.isNotEmpty() } ?: ""

        val businessCountyAndState =
            (if (businessCounty.isNotEmpty() && businessState.isNotEmpty()) "$businessCounty, " else businessCounty) + businessState

        val businessTypePartner = getFieldAnswerValue(questionnaireAnswerResponse, FieldType.BusinessTypeDBAPartner.FieldName) == "1"
        val businessTypeNP = getFieldAnswerValue(questionnaireAnswerResponse, FieldType.BusinessTypeNP.FieldName) == "1"
        val businessTypeSCorp = getFieldAnswerValue(questionnaireAnswerResponse, FieldType.BusinessTypeSCORP.FieldName) == "1"
        val businessTypeCCorp = getFieldAnswerValue(questionnaireAnswerResponse, FieldType.BusinessTypeCCORP.FieldName) == "1"
        val businessTypeSoleProp = getFieldAnswerValue(questionnaireAnswerResponse, FieldType.BusinessTypeDBASoleProp.FieldName) == "1"

        val isCorporation = businessTypeSCorp || businessTypeCCorp
        val isLLC = getFieldAnswerValue(questionnaireAnswerResponse, FieldType.BusinessTypeLLC.FieldName) == "1"

        val primaryBusinessActivity = getFieldAnswerValue(questionnaireAnswerResponse, "Primary_Business_Activity")
        val principalBusinessActivity = getFieldAnswerValue(questionnaireAnswerResponse, "Principal_business_activity")
        val primaryBusinessActivityNp = getFieldAnswerValue(questionnaireAnswerResponse, "primary_business_activity_np")

        val businessActivitySpecify =
            if (!principalBusinessActivity.isNullOrEmpty() && (
                    primaryBusinessActivity.isNullOrEmpty() ||
                        primaryBusinessActivity.equals(
                            SS4BusinessActivities.OTHER.value,
                            true,
                        ) ||
                        primaryBusinessActivityNp.isNullOrEmpty() ||
                        primaryBusinessActivityNp.equals(
                            SS4BusinessActivities.OTHER.value,
                            true,
                        )
                )
            ) {
                principalBusinessActivity
            } else {
                primaryBusinessActivity.ifEmpty { primaryBusinessActivityNp }
            }
        val serviceProvided =
            mutableListOf(
                getFieldAnswerValue(questionnaireAnswerResponse, "other_other_activity_detailed"),
                getFieldAnswerValue(questionnaireAnswerResponse, "Principal_Activity_Detailed"),
            ).firstOrNull { it.isNotEmpty() } ?: businessActivitySpecify

        val applicantName = getFieldAnswerValue(questionnaireAnswerResponse, "name_of_officer")
        val title = if (businessTypeSoleProp) "Sole proprietor" else getFieldAnswerValue(questionnaireAnswerResponse, "Title_of_officer")

        val applicantNameAndTitle = "$applicantName, $title"

        val liabilityLessThan1k = getFieldAnswerValue(questionnaireAnswerResponse, "liability_less_than_1k")

        val numberOfOwners = getFieldAnswerValue(questionnaireAnswerResponse, "number_members")

        val totalNumberOfOwners = (if (numberOfOwners.isEmpty()) 0 else numberOfOwners.toIntOrNull() ?: 0)

        val businessActivity =
            SS4BusinessActivities.getBusinessActivityContains(businessActivitySpecify)
                ?: SS4BusinessActivities.OTHER.value

        var purposeCategory = getFieldAnswerValue(questionnaireAnswerResponse, "purposeCategory")
        NPBusinessActivities.getSportsCategories().takeIf { purposeCategory in it }?.let {
            purposeCategory = "Sports"
        }

        var nonProfitSpecify = getPurposeCategory(purposeCategory)

        val entityName = if (businessTypeSoleProp) applicantName else getFieldAnswerValue(questionnaireAnswerResponse, "name_of_customer")
        val ssn = getFieldAnswerValue(questionnaireAnswerResponse, "SSN_of_officer")
        val tradeName =
            if (businessTypeSoleProp) {
                getFieldAnswerValue(questionnaireAnswerResponse, "name_of_customer")
            } else {
                getFieldAnswerValue(questionnaireAnswerResponse, "Trade_name_name")
            }

        val fields =
            listOf(
                Triple(RevvMappingFields.EIN_NUMBER.value, "", einNumber),
                Triple(RevvMappingFields.ENTITY_NAME.value, "name_of_customer", entityName),
                Triple(RevvMappingFields.TRADE_NAME.value, "Trade_name_name", tradeName),
                Triple(RevvMappingFields.MAILING_ADDRESS.value, "", mailingAddress),
                Triple(RevvMappingFields.MAILING_CITY_STATE_ZIP.value, "", mailingCityStateZip),
                Triple(RevvMappingFields.COUNTY_AND_STATE.value, "", businessCountyAndState),
                Triple(RevvMappingFields.NAME_OF_RESPONSIBLE_PARTY.value, "name_of_officer", applicantName),
                Triple(RevvMappingFields.SSN_ITIN_OR_EIN.value, "SSN_of_officer", ssn),
                Triple(RevvMappingFields.NUMBER_OF_LLC_MEMBERS.value, "number_members", null),
                Triple(
                    RevvMappingFields.IS_LLC.value,
                    "",
                    if (isLLC) SS4MappingFields.YES.value else SS4MappingFields.NO.value,
                ),
                Triple(
                    RevvMappingFields.IS_LLC_IN_US.value,
                    "",
                    if (isLLC) SS4MappingFields.YES.value else SS4MappingFields.OFF.value,
                ),
                Triple(
                    RevvMappingFields.TYPE_OF_ENTITY.value,
                    "",
                    if (businessTypeSoleProp) {
                        SS4EntityType.SOLE_PROPRIETOR.value
                    } else if (isCorporation) {
                        SS4EntityType.CORPORATION.value
                    } else if (businessTypePartner || (isLLC && totalNumberOfOwners > 1)) {
                        SS4EntityType.PARTNERSHIP.value
                    } else if (businessTypeNP) {
                        SS4EntityType.OTHER_NONPROFIT_ORGANIZATION.value
                    } else {
                        SS4EntityType.OTHER.value
                    },
                ),
                Triple(
                    RevvMappingFields.CORPORATION_SPECIFY.value,
                    "",
                    if (isCorporation) {
                        if (businessTypeSCorp) {
                            SS4MappingFields.FORM_1120S.value
                        } else {
                            SS4MappingFields.FORM_1120.value
                        }
                    } else {
                        ""
                    },
                ),
                Triple(
                    RevvMappingFields.OTHER_NONPROFIT_ORGANIZATION_SPECIFY.value,
                    "purposeCategory",
                    if (businessTypeNP) nonProfitSpecify else "",
                ),
                Triple(RevvMappingFields.SOLE_PROPRIETOR_SPECIFY.value, "", if (businessTypeSoleProp) ssn else ""),
                Triple(RevvMappingFields.FOREIGN_STATE.value, "", if (isCorporation) businessState else ""),
                Triple(RevvMappingFields.REASON_FOR_APPLYING.value, "", SS4MappingFields.STARTED_NEW_BUSINESS.value),
                Triple(
                    RevvMappingFields.OTHER_ENTITY_SPECIFY.value,
                    "",
                    if (isLLC && totalNumberOfOwners == 1) {
                        SS4MappingFields.DISREGARDED_ENTITY_SINGLE_MEMBER_LLC.value
                    } else {
                        ""
                    },
                ),
                Triple(
                    RevvMappingFields.STARTED_NEW_BUSINESS_SPECIFY.value,
                    "",
                    if (businessTypeNP) {
                        purposeCategory.ifEmpty { "Charitable" }
                    } else {
                        businessActivitySpecify
                    },
                ),
                Triple(RevvMappingFields.DATE_BUSINESS_STARTED.value, "formation_date", formationDate),
                Triple(RevvMappingFields.MONTH_OF_ACCOUNTING_YEAR.value, "fiscal_year_end_month", null),
                Triple(
                    RevvMappingFields.AGRICULTURAL_EMPLOYEE.value,
                    "",
                    getFieldAnswerValue(questionnaireAnswerResponse, "Agricultural_employees").ifEmpty { "0" },
                ),
                Triple(
                    RevvMappingFields.HOUSEHOLD_EMPLOYEE.value,
                    "",
                    getFieldAnswerValue(questionnaireAnswerResponse, "Household_employees").ifEmpty { "0" },
                ),
                Triple(
                    RevvMappingFields.OTHER_EMPLOYEE.value,
                    "",
                    getFieldAnswerValue(questionnaireAnswerResponse, "Nonagricultural_employees").ifEmpty { "0" },
                ),
                Triple(
                    RevvMappingFields.EMPLOYMENT_TAX_LIABILITY.value,
                    "",
                    if (liabilityLessThan1k.equals(SS4MappingFields.YES.value, true) ||
                        liabilityLessThan1k.equals(SS4MappingFields.TRUE.value, true)
                    ) {
                        SS4MappingFields.YES.value
                    } else {
                        SS4MappingFields.OFF.value
                    },
                ),
                Triple(
                    RevvMappingFields.FIRST_DATE_WAGES.value,
                    "",
                    dateWagesPaid(questionnaireAnswerResponse, "Date_Wages_Paid"),
                ),
                Triple(RevvMappingFields.BUSINESS_ACTIVITY.value, "", businessActivity),
                Triple(
                    RevvMappingFields.BUSINESS_ACTIVITY_OTHER_SPECIFY.value,
                    "",
                    if (businessTypeNP) {
                        purposeCategory.ifEmpty { "Charitable" }
                    } else {
                        businessActivitySpecify
                    },
                ),
                Triple(RevvMappingFields.SERVICE_PROVIDED.value, "", serviceProvided),
                Triple(RevvMappingFields.IS_EIN_EVER_APPLIED.value, "", SS4MappingFields.NO.value),
                Triple(RevvMappingFields.PREVIOUS_EIN_NUMBER.value, "Previous_EIN_list_number", null),
                Triple(RevvMappingFields.APPLICANT_NAME_AND_TITLE.value, "", applicantNameAndTitle),
                Triple(RevvMappingFields.APPLICANTS_TELEPHONE_NUMBER.value, "phone___of_officer", null),
                Triple(RevvMappingFields.PROCESSING_ORDER_ID.value, "", input.processingOrderId.toString()),
                Triple(
                    RevvMappingFields.DESCRIPTOR.value,
                    "",
                    if (businessTypeSoleProp) "DBA/Trade Name." else "",
                ),
                Triple(
                    RevvMappingFields.OBTAINMENT_REQUIRED.value,
                    "",
                    obtainment,
                ),
            )

        for ((field, answerKey, fixedValue) in fields) {
            addRevvDocumentField(
                revDocumentMappingFields,
                field,
                answerKey,
                questionnaireAnswerResponse,
                fixedValue,
            )
        }

        if (obtainment == SS4MappingFields.OBTAINMENT_REQUIRED.value) {
            addDesigneeDetails(revDocumentMappingFields)
        }

        return revDocumentMappingFields
    }
}
