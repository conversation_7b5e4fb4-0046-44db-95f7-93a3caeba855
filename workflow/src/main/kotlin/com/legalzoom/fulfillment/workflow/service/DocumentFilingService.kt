package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.service.Constants
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
class DocumentFilingService {
    fun isDocumentFilingState(execution: DelegateExecution): Boolean {
        return execution.input.documentFilingState ?: false
    }

    // isFaxFilingState has been deprecated and has been replaced with FaxFilingService.isFaxFilingState
    fun isFaxFilingState(execution: DelegateExecution): Boolean {
        return isFaxFilingStateByVariables(execution.input)
    }

    fun isFaxFilingStateByVariables(variables: Variables): Boolean {
        return Constants.FAX_FILING_STATES.contains(variables.jurisdiction)
    }
}
