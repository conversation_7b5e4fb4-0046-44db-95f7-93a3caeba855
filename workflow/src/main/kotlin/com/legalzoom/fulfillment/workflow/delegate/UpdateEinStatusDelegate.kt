package com.legalzoom.fulfillment.workflow.delegate

import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.service.enumeration.CommerceSystem
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import com.legalzoom.fulfillment.workflow.variable.toActivityFeedVariables
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class UpdateEinStatusDelegate(
    private val ordersApiService: OrdersApiService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
    private val processingOrderService: ProcessingOrderService,
    private val activityFeedServiceImpl: ActivityFeedService,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan("UpdateEinStatus")
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        val input = execution.input
        // Check if the order system is CP2
        if (input.commerceIds.isForSystem(CommerceSystem.CP2)) {
            logger.event(
                "ein.status.no.update",
                mapOf(
                    "processingOrderId" to input.processingOrderId,
                    "message" to "EIN is not applicable for CP2 orders",
                ),
            )
            execution.output {
                childProcessId = null
                childProcessingOrderId = null
                einFilingEnabled = false
            }
            return
        }

        val orderResponse = ordersApiService.getOrders(input.orderId, null, null, null, null, null)

        val einOrderItem =
            orderResponse.order?.orderItems?.firstOrNull {
                it.processingOrder?.processId == ProductType.EIN.processId && it.isCancelled == false
            }

        if (einOrderItem != null) {
            val einProcessingOrderId = einOrderItem.processingOrder?.processingOrderId!!

            logger.event("ein.status.update.start", mapOf("processingOrderId" to input.processingOrderId))

            val answersResponse =
                questionnaireAnswerService.getAnswersByUserOrderId(
                    input.processingOrderId!!,
                    input.customerId,
                    AnswerSource.AnswerBank,
                )

            if (answersResponse.manualFilingEIN == "Yes") {
                val einProcessingStatus =
                    if (answersResponse.ein.isNullOrEmpty()) {
                        ProcessingOrderStatus.EinPreliminaryProblem
                    } else {
                        ProcessingOrderStatus.EinFilingComplete
                    }

                logger.event(
                    "ein.status.update",
                    mapOf(
                        "processingOrderId" to input.processingOrderId,
                        "einProcessingOrderId" to einProcessingOrderId,
                        "processingStatus" to einProcessingStatus,
                    ),
                )
                processingOrderService.updateProcessingOrderStatus(
                    einProcessingOrderId,
                    input.customerId!!,
                    einProcessingStatus,
                    execution.toActivityFeedVariables(),
                )

                activityFeedServiceImpl.sendEvent(
                    einProcessingStatus,
                    execution.toActivityFeedVariables(),
                )
            }
        } else {
            logger.event(
                "ein.status.no.update",
                mapOf(
                    "processingOrderId" to input.processingOrderId,
                    "message" to "EIN might not be added or cancelled for this order",
                ),
            )
        }

        execution.output {
            childProcessId = null
            childProcessingOrderId = null
            einFilingEnabled = false
        }
    }
}
