package com.legalzoom.fulfillment.workflow.service.revv
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentField
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvMappingFields
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.variable.Variables
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class CorporateDissolutionRequestBuilder(
    private val input: Variables,
    private val ordersContactsApiService: OrdersContactsApiService,
    private val answerApi: AnswerApi,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) : RevvFieldsRequestBuilder(ordersContactsApiService, answerApi, questionnaireAnswerService) {
    override fun getTemplates(): MutableList<RevvDocumentType> {
        val templates =
            mutableListOf(
                RevvDocumentType.CORPORATE_DISSOLUTION_FINAL_LETTER,
            )
        val questionnaireAnswerResponse = getAnswerInfo(input)
        val entityType = getFieldAnswerValue(questionnaireAnswerResponse, "entity_type")

        if (entityType == "Limited Liability Company (LLC)") {
            templates.add(RevvDocumentType.CORPORATE_DISSOLUTION_LLC_CONSENT_LETTER)
        } else if (entityType == "Corporation") {
            templates.add(RevvDocumentType.CORPORATE_DISSOLUTION_INC_CONSENT_LETTER)
        }

        val stateOfFormation = getFieldAnswerValue(questionnaireAnswerResponse, "State_of_formation")

        if (State.fromNameOrAbbre(stateOfFormation) == State.TENNESSEE) {
            templates.add(RevvDocumentType.REGISTER_OF_DEEDS_ATTACHMENT)
        }

        return templates
    }

    override fun getCustomerDocumentType(): MutableMap<RevvDocumentType, CustomerDocumentType> {
        return mutableMapOf(
            RevvDocumentType.CORPORATE_DISSOLUTION_FINAL_LETTER to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "FinalLetter",
                ),
            RevvDocumentType.CORPORATE_DISSOLUTION_LLC_CONSENT_LETTER to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "Consent",
                ),
            RevvDocumentType.CORPORATE_DISSOLUTION_INC_CONSENT_LETTER to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "Consent",
                ),
            RevvDocumentType.REGISTER_OF_DEEDS_ATTACHMENT to
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    ProductType.fromProcessId(input.processId!!).productName,
                    "Register of Deeds Attachment",
                ),
        )
    }

    override fun getMappingFields(templates: MutableList<RevvDocumentType>?): MutableList<RevvDocumentField> {
        val revDocumentMappingFields = getFinalLetterDocumentFields(input)
        val questionnaireAnswerResponse = getAnswerInfo(input)

        revDocumentMappingFields.find { it.field == RevvMappingFields.DATE.value }?.value =
            LocalDateTime.now().format(
                DateTimeFormatter.ofPattern("MMMM d, yyyy"),
            ).toString()

        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.FORMATION_STATE.value,
                getFieldAnswerValue(questionnaireAnswerResponse, "State_of_formation"),
            ),
        )

        val entityType = getFieldAnswerValue(questionnaireAnswerResponse, "entity_type")

        revDocumentMappingFields.add(
            RevvDocumentField(
                RevvMappingFields.ENTITY_TYPE.value,
                if (entityType == "Corporation") "Corporation" else "limited liability company",
            ),
        )

        return revDocumentMappingFields
    }
}
