package com.legalzoom.fulfillment.workflow.delegate

import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.rpa.QueueItemsApi
import com.legalzoom.fulfillment.common.logging.errorEvent
import com.legalzoom.fulfillment.common.logging.event
import com.legalzoom.fulfillment.common.mono.blockSingle
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.ALTM_APP_FILING
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.ALTM_FILING
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.APP_501C3
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.EIN_FILING
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.FILING
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.INITIAL_REPORT
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.ALTM_APP_Filing_DraftApp
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.ALTM_AppFilingBotExecutionScreenShotError
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.ALTM_AppPrepBotExecutionScreenShotError
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.ALTM_AppPrepBotExecutionSummaryError
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.ALTM_TeasDraftApp
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.InitialReports_StatementOfInformation
import com.legalzoom.fulfillment.service.data.OrderContext
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus.Active
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus.Inactive
import com.legalzoom.fulfillment.service.enumeration.DocumentType.App501c3FilingException
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ArticlesFiled
import com.legalzoom.fulfillment.service.enumeration.DocumentType.EINFilingException
import com.legalzoom.fulfillment.service.enumeration.DocumentType.EINProofOfWork
import com.legalzoom.fulfillment.service.enumeration.DocumentType.Form1023EZ
import com.legalzoom.fulfillment.service.enumeration.DocumentType.IRSAssignedEIN
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ProofOfWork
import com.legalzoom.fulfillment.service.enumeration.DocumentType.SOIFilingException
import com.legalzoom.fulfillment.service.enumeration.DocumentType.SoSFilingException
import com.legalzoom.fulfillment.service.enumeration.DocumentType.SupportingDocs
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.exception.DocumentRetrievalException
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.workflow.Constants.SALESFORCE_USER
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.extensions.addValidationError
import com.legalzoom.fulfillment.workflow.service.CourierFilingService
import com.legalzoom.fulfillment.workflow.service.InstantFilingService
import com.legalzoom.fulfillment.workflow.service.RpaRetryService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.apache.commons.lang3.exception.ExceptionUtils
import org.camunda.bpm.engine.RepositoryService
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.io.File
import java.net.URI
import java.time.Clock
import java.time.LocalDate

/**
 * Method to execute the callback from the RPA bot.
 * @property documentService - Document/Storage service for uploading files
 * @property s3Service - AWS S3 service to obtain the Proof of Work document
 */
@Component
class RobotCallbackDelegate(
    private val clock: Clock,
    private val documentService: DocumentService,
    private val s3Service: S3Service,
    private val salesforceApiService: SalesforceApiService,
    private val instantFilingService: InstantFilingService,
    private val courierFilingService: CourierFilingService,
    private val repositoryService: RepositoryService,
    private val featureToggleService: FeatureToggleService,
    private val rpaSosSsorcoService: RpaSosSsorcoService,
    private val rpaRetryService: RpaRetryService,
    private val queueItemsApi: QueueItemsApi,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    companion object {
        const val FILING_COMPLETE_PROOF_NOT_STORED =
            "Unexpected System Error. Filing completed but proof was not stored"
        const val FILING_INCOMPLETE = "Unexpected System Error. Please file manually."
    }

    /**
     * This method uploads the Proof of Work document into the Storage system.
     * If status sent from Camunda is a failure, or evidenceFilePath is empty,
     * it will return a ValidationError to the DelegationExecution object & re
     * @param execution - object from Camunda execution that holds all the necessary parameters
     * to be used by Callback Delegate
     */
    @WithSpan("RPA-Callback")
    @BpmnRetryTask
    @Throws(Exception::class)
    override fun execute(execution: DelegateExecution) {
        val input = execution.input
        val key = repositoryService.getProcessDefinition(execution.processDefinitionId).key
        val eventPhase = input.parentEventPhase?.let { EventPhase.fromName(it) } ?: EventPhase.fromWorkflowName(key)

        logger.event(
            "rpa.received",
            mapOf(
                "status" to input.status,
                "message" to input.message,
                "evidenceFilePath" to input.evidenceFilePath,
                "evidenceTransactionNumber" to input.evidenceTransactionNumber,
                "evidenceTransactionMessage" to input.evidenceTransactionMessage,
                "documentPaths" to input.documentPaths,
                "retriable" to input.retriable,
            ),
        )

        if (eventPhase == EIN_FILING && !input.evidenceTransactionNumber.isNullOrBlank()) {
            input.employerIdentificationNumber = input.evidenceTransactionNumber

            execution.output {
                employerIdentificationNumber = input.employerIdentificationNumber
            }
        }

        if (!validateCallback(input, execution)) return

        if (input.status?.lowercase() == "failed") {
            classifyExceptionType(execution)
        }

        input.evidenceFilePath.takeIf { !it.isNullOrBlank() }?.let { file ->
            getDocumentFromS3(execution, file, eventPhase, isPrimaryDocument = true)?.let { result ->
                val customerDocumentType = result.second
                val resourceWithType = result.first
                uploadDocumentToStorage(
                    execution,
                    resourceWithType,
                    customerDocumentType,
                    file,
                    isPrimaryDocument = true,
                )
                // If document is Filing Exception, delegate call is complete
                if (customerDocumentType ==
                    CustomerDocumentType.findCustomerDocTypeFromProductNameNullable(
                        getProductTypeName(execution.input),
                        ProofOfWork.name,
                    )
                ) {
                    updateFilingExceptions(execution)
                }

                // send doc to courier
                if (eventPhase == FILING &&
                    courierFilingService.isCourierFilingState(execution) &&
                    input.status?.lowercase() == "success"
                ) {
                    courierFilingService.sendCourier(
                        input.jurisdiction!!,
                        listOf(resourceWithType),
                        input.entityName,
                        input.processingOrderId.toString(),
                    )
                }
            }
        }
        input.documentPaths?.forEach {
            getDocumentFromS3(
                execution,
                it,
                eventPhase,
                isPrimaryDocument = false,
                isProofDocument = true,
            )?.let { result ->
                val customerDocumentType = result.second
                val resourceWithType = result.first
                uploadDocumentToStorage(
                    execution,
                    resourceWithType,
                    customerDocumentType,
                    it,
                    isPrimaryDocument = false,
                )
                // If document is Filing Exception, delegate call is complete
                if (customerDocumentType ==
                    CustomerDocumentType.findCustomerDocTypeFromProductNameNullable(
                        getProductTypeName(execution.input),
                        ProofOfWork.name,
                    )
                ) {
                    updateFilingExceptions(execution)
                }
            }
        }

        // Add ledger note for RPA evidence transaction number
        // If creating ledger note fails a Salesforce case will not be created
        if (input.status?.lowercase() == "success" &&
            !input.evidenceTransactionNumber.isNullOrEmpty()
        ) {
            try {
                salesforceApiService.addLedgerNote(
                    AddLedgerNoteRequest(
                        objectType = "note",
                        customerOrderNumber = input.unifiedOrderId,
                        processingNumber = input.processingOrderId.toString(),
                        description =
                            "Filing submission summary" +
                                "(${ProductType.fromProcessId(input.childProcessId ?: input.processId ?: 2)}): " +
                                "Transaction Number-${input.evidenceTransactionNumber} ${input.message}",
                        createdBy = SALESFORCE_USER,
                    ),
                )
            } catch (e: Exception) {
                logger.error(e.localizedMessage)
            }
        }
    }

    private fun classifyExceptionType(execution: DelegateExecution) {
        val result =
            queueItemsApi.queueItemsGet(
                null,
                null,
                null,
                "Id eq ${execution.input.rpaQueueItemId}",
                null,
                null,
                10,
                null,
                null,
                null,
            ).blockSingle().value ?: throw NoSuchElementException("Unable to find bot by id ${execution.input.rpaQueueItemId}")

        var job = result.singleOrNull() ?: throw NoSuchElementException("Unable to find bot by id ${execution.input.rpaQueueItemId}")

        if (job.processingExceptionType == null) {
            throw Exception("Violation of assumption: ProcessingExceptionType is null for job ${execution.input.rpaQueueItemId}")
        }

        if (job.processingExceptionType!! == QueueItemDto.ProcessingExceptionTypeEnum.APPLICATION_EXCEPTION) {
            execution.output {
                // note - it's unfortunate that uipath refers to this as an application exception because it's generally
                // referred to as a system exception.  We're going to make the camunda variable reflect "system" to be consistent
                botExceptionType = "system"
            }
        } else if (job.processingExceptionType!! == QueueItemDto.ProcessingExceptionTypeEnum.BUSINESS_EXCEPTION) {
            execution.output {
                botExceptionType = "business"
            }
        } else {
            throw Exception("Unexpected ProcessingExceptionType: ${job.processingExceptionType}")
        }
    }

    /**
     * For upload purposes - Get the appropriate document type and name depending on the context
     * @param execution - execution variables from Camunda
     * @param eventPhase - Phase of the order workflow
     * @param isStatusSuccess - document attr will depend on status given
     * @return
     */
    private fun getDocumentAttributesFromEventPhase(
        execution: DelegateExecution,
        eventPhase: EventPhase,
        isStatusSuccess: Boolean,
        processingOrderId: Long,
        isProofDocument: Boolean = false,
        isPrimaryDocument: Boolean,
        file: String,
    ): Pair<CustomerDocumentType, String?> { // Document Type, Document Name
        return when (eventPhase) {
            FILING -> {
                findDocumentTypeAndNamePairForFiling(execution, isPrimaryDocument, processingOrderId, isStatusSuccess)
            }

            EIN_FILING -> {
                findDocumentTypeAndNamePairForEinFiling(execution, processingOrderId, isProofDocument, isStatusSuccess)
            }

            APP_501C3 -> {
                findDocumentTypeAndNamePairFor501C3Filing(execution, processingOrderId, isProofDocument, isStatusSuccess)
            }

            ALTM_FILING -> {
                findDocumentTypeAndNamePairForAltmAppPrep(processingOrderId, isStatusSuccess, file)
            }
            ALTM_APP_FILING -> {
                findDocumentTypeAndNamePairForAltmAppFiling(processingOrderId, isStatusSuccess)
            }

            INITIAL_REPORT -> {
                findDocumentTypeAndNamePairForCaliforniaSoi(execution.input, processingOrderId, isStatusSuccess)
            }

            else -> {
                Pair(
                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                        getProductTypeName(execution.input),
                        ProofOfWork.name,
                    ),
                    null,
                )
            }
        }
    }

    private fun findDocumentTypeAndNamePairForAltmAppPrep(
        processingOrderId: Long,
        isStatusSuccess: Boolean,
        file: String,
    ) = if (isStatusSuccess) {
        val customerDocumentType = ALTM_TeasDraftApp
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    } else {
        val fileExtension = File(file).extension
        val customerDocumentType =
            if (fileExtension == "pdf") {
                ALTM_AppPrepBotExecutionSummaryError
            } else {
                ALTM_AppPrepBotExecutionScreenShotError
            }
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    }

    private fun findDocumentTypeAndNamePairForAltmAppFiling(
        processingOrderId: Long,
        isStatusSuccess: Boolean,
    ) = if (isStatusSuccess) {
        val customerDocumentType = ALTM_APP_Filing_DraftApp
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    } else {
        val customerDocumentType = ALTM_AppFilingBotExecutionScreenShotError
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    }

    private fun findDocumentTypeAndNamePairForCaliforniaSoi(
        input: Variables,
        processingOrderId: Long,
        isStatusSuccess: Boolean,
    ) = if (isStatusSuccess) {
        val customerDocumentType = InitialReports_StatementOfInformation
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    } else {
        val customerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                getProductTypeName(input),
                SOIFilingException.name,
            )
        Pair(
            customerDocumentType,
            customerDocumentType.fullFilename(processingOrderId),
        )
    }

    private fun findDocumentTypeAndNamePairForEinFiling(
        execution: DelegateExecution,
        processingOrderId: Long,
        isProofDocument: Boolean,
        isStatusSuccess: Boolean,
    ) = if (isStatusSuccess) {
        val customerDocumentType =
            if (isProofDocument) {
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    getProductTypeName(execution.input),
                    EINProofOfWork.name,
                )
            } else {
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    getProductTypeName(execution.input),
                    IRSAssignedEIN.name,
                )
            }
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    } else {
        Pair(
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                getProductTypeName(execution.input),
                EINFilingException.name,
            ),
            null,
        )
    }

    private fun findDocumentTypeAndNamePairFor501C3Filing(
        execution: DelegateExecution,
        processingOrderId: Long,
        isProofDocument: Boolean,
        isStatusSuccess: Boolean,
    ) = if (isStatusSuccess) {
        val customerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                getProductTypeName(execution.input),
                Form1023EZ.name,
            )
        Pair(customerDocumentType, customerDocumentType.fullFilename(processingOrderId))
    } else {
        Pair(
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                getProductTypeName(execution.input),
                App501c3FilingException.name,
            ),
            null,
        )
    }

    private fun findDocumentTypeAndNamePairForFiling(
        execution: DelegateExecution,
        isPrimaryDocument: Boolean,
        processingOrderId: Long,
        isStatusSuccess: Boolean,
    ): Pair<CustomerDocumentType, String?> {
        val productType = getProductTypeName(execution.input)

        val documentTypeName =
            when {
                isStatusSuccess && isPrimaryDocument && isTennesseeStateSupportingDoc(execution) -> {
                    SupportingDocs.displayName
                    // this has to be displayName in customerDocumentType.csv For rest others it does not have any spaces and hence it's working
                }

                isStatusSuccess && isPrimaryDocument && isArticlesFiledDocument(execution) -> {
                    ArticlesFiled.name
                }

                !isStatusSuccess -> {
                    SoSFilingException.name
                }

                else -> {
                    ProofOfWork.name
                }
            }

        val customerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                productType,
                documentTypeName,
            )

        return Pair(
            customerDocumentType,
            if (isStatusSuccess) customerDocumentType.fullFilename(processingOrderId) else null,
        )
    }

    private fun isArticlesFiledDocument(execution: DelegateExecution) =
        instantFilingService.isInstantFilingState(execution) ||
            isSemiInstantStateFilingDocument(
                execution,
            )

    private fun isSemiInstantStateFilingDocument(execution: DelegateExecution) =
        instantFilingService.isSemiInstantFilingState(execution) &&
            !execution.input.evidenceFilePath.isNullOrEmpty()

    private fun isTennesseeStateSupportingDoc(execution: DelegateExecution) =
        instantFilingService.isCourierFilingState(execution) &&
            !execution.input.evidenceFilePath.isNullOrEmpty() && execution.input.jurisdiction == State.TENNESSEE.abbreviation &&
            (execution.input.processId == ProductType.LLC.processId || execution.input.processId == ProductType.INC.processId)

    private fun getDocumentFromS3(
        execution: DelegateExecution,
        file: String,
        eventPhase: EventPhase,
        isPrimaryDocument: Boolean,
        isProofDocument: Boolean = false,
    ): Pair<ResourceWithType, CustomerDocumentType>? {
        val input = execution.input
        val fileUri = URI(file)
        // Check uploaded document on S3
        var resourceWithType =
            try {
                s3Service.getDocument(fileUri)
            } catch (e: DocumentRetrievalException) {
                FILING_COMPLETE_PROOF_NOT_STORED.let {
                    logger.errorEvent(
                        "rpa.callback.document.notfound",
                        mapOf(
                            "filePath" to file,
                            "validationError" to it,
                            "exception" to e.message,
                        ),
                    )
                    execution.addValidationError(ValidationError(it))
                }
                return null
            }

        val (customerDocumentType, updatedFileName) =
            getDocumentAttributesFromEventPhase(
                execution,
                eventPhase,
                input.status == "Success",
                input.processingOrderId!!.toLong(),
                isProofDocument,
                isPrimaryDocument,
                file,
            )

        if (!updatedFileName.isNullOrEmpty()) {
            logger.event(
                "rpa.callback.rename.proof",
                mapOf(
                    "previousName" to resourceWithType.filename,
                    "updatedFileName" to updatedFileName,
                ),
            )
            resourceWithType.filename = updatedFileName
        }
        logger.event(
            "rpa.callback.documentFound",
            mapOf(
                "filePath" to file,
                "contentType" to resourceWithType.contentType,
            ),
        )
        // Upload document
        logger.debug("Document retrieved from S3 with name ${resourceWithType.filename}")
        return Pair(resourceWithType, customerDocumentType)
    }

    private fun uploadDocumentToStorage(
        execution: DelegateExecution,
        resourceWithType: ResourceWithType,
        customerDocumentType: CustomerDocumentType,
        file: String,
        isPrimaryDocument: Boolean,
    ): Any {
        val input = execution.input

        val documentUpdateResponse: DocumentResponse?
        try {
            val orderContext =
                OrderContext(
                    processingOrderId = input.processingOrderId!!.toLong(),
                    customerId = input.customerId!!.toLong(),
                    orderId = input.orderId!!.toLong(),
                    jurisdiction = input.jurisdiction,
                    entityName = input.entityName,
                    processId = input.processId,
                    isFromWorkflow = true,
                    accountId = input.accountId?.toString(),
                )
            documentUpdateResponse =
                documentService.uploadDocument(
                    orderContext,
                    customerDocumentType,
                    resourceWithType,
                    Active,
                    // note - it won't be uploaded as active unless availableForImmediateCustomerDownload is also true
                    // docs like 501c3's 1023ez and EIN's IRSAssignedEin doc come out of the bot and should be
                    // active at that point
                    availableForImmediateCustomerDownload = customerDocumentType.alwaysActive ?: false,
                )
        } catch (e: Exception) {
            FILING_COMPLETE_PROOF_NOT_STORED.let {
                logger.errorEvent(
                    "rpa.documentUpload.failed",
                    mapOf(
                        "message" to e.message,
                        "validationError" to it,
                        "stackTrace" to ExceptionUtils.getStackTrace(e),
                    ),
                )
                throw (Exception(it))
            }
        }
        logger.info(documentUpdateResponse.toString())
        // Check if document successfully uploaded
        if (documentUpdateResponse == null) {
            FILING_COMPLETE_PROOF_NOT_STORED.let {
                logger.errorEvent(
                    "rpa.documentUpload.failed",
                    mapOf(
                        "validationError" to it,
                    ),
                )
                throw (Exception(it))
            }
        }
        logger.event(
            "rpa.documentUpload.success",
            mapOf(
                "filePath" to file,
                "documentResult" to documentUpdateResponse,
            ),
        )

        if (isPrimaryDocument) {
            input.documentType = customerDocumentType.documentType
        }

        if (input.documentResults == null) {
            input.documentResults = mutableMapOf(Pair(customerDocumentType.documentType, documentUpdateResponse))
        } else {
            input.documentResults!![customerDocumentType.documentType] = documentUpdateResponse
        }

        // set effective date for instant filing states
        if (
            (
                instantFilingService.isInstantFilingState(execution) ||
                    instantFilingService.isSemiInstantFilingState(execution) ||
                    instantFilingService.isCourierFilingState(execution)
            ) && input.effectiveDate.isNullOrEmpty()
        ) {
            input.effectiveDate = LocalDate.now(clock).toString()
        }
        execution.variables = input

        return documentUpdateResponse
    }

    private fun updateFilingExceptions(execution: DelegateExecution) {
        val input = execution.input
        // If document is Proof of Work or Articles, hide the Filing Exceptions
        logger.event("rpa.hidingFilingExceptions.start")
        val documents =
            documentService.findDocumentsBy(
                input.processingOrderId!!.toLong(),
                input.customerId!!.toLong(),
                CustomerDocumentType.findCustomerDocTypeFromProductName(
                    getProductTypeName(execution.input),
                    SoSFilingException.name,
                ),
                currentVersionOnly = true,
                accountId = input.accountId?.toString(),
            )
        val activeExceptionDocuments = documents.filter { it.versionHistory.storagePlatformDocumentStatus == Active.name }
        if (activeExceptionDocuments.isEmpty()) {
            logger.event(
                "rpa.hidingFilingExceptions.notApplicable",
                mapOf(
                    "message" to "No past Storage Platform exceptions found. No documents to hide",
                ),
            )
            return
        }

        val documentResponses = mutableListOf<DocumentResponse?>()
        for (document in activeExceptionDocuments) {
            documentResponses.add(
                updateDocumentAsync(
                    document,
                    input.processingOrderId!!.toLong(),
                    input.customerId!!.toLong(),
                    input.accountId?.toString(),
                ),
            )
        }
        when (val numberOfUpdatesPassed = documentResponses.count { (it != null) }) {
            documents.size ->
                logger.event(
                    "rpa.hidingFilingExceptions.success",
                    mapOf(
                        "message" to "Successfully updated ${documents.size} document to status ${Inactive.name}",
                    ),
                )

            0 ->
                throw Exception("All Update operations failed. See logs for additional details")

            else ->
                logger.event(
                    "rpa.hidingFilingExceptions.partialSuccess",
                    mapOf(
                        "message" to "Successfully updated $numberOfUpdatesPassed document to status " +
                            "${Inactive.name}. Update failed for ${documents.size - numberOfUpdatesPassed}",
                    ),
                )
        }
    }

    private fun updateDocumentAsync(
        document: Document,
        processingOrderId: Long,
        customerId: Long,
        accountId: String?,
    ): DocumentResponse? {
        var documentResponse: DocumentResponse? = null
        try {
            documentResponse =
                documentService.updateDocument(
                    document.documentId,
                    Inactive,
                    null,
                    null,
                    processingOrderId,
                    customerId,
                    accountId,
                )
            logger.event(
                "rpa.hidingFilingExceptions.part.success",
                mapOf(
                    "documentId" to document.documentId,
                    "message" to "Successfully updated document to status ${Inactive.name} " +
                        "for documentId ${document.documentId}",
                ),
            )
        } catch (e: Exception) {
            logger.errorEvent(
                "rpa.hidingFilingExceptions.partialSuccess",
                mapOf(
                    "message" to "Unable to update document. documentId ${document.documentId}",
                    "stacktrace" to ExceptionUtils.getStackTrace(e),
                ),
            )
        }
        return documentResponse
    }

    private fun validateCallback(
        input: Variables,
        execution: DelegateExecution,
    ): Boolean {
        // Check Filing Status
        if (input.status != "Success") {
            input.message.let {
                val validationErrorMessage = it ?: FILING_INCOMPLETE
                val count = input.retryCount ?: 0
                logger.event(
                    "rpa.failed",
                    mapOf(
                        "status" to input.status,
                        "message" to "RPA execution failed with message: ${input.message}",
                        "validationError" to validationErrorMessage,
                        "retryCount" to count,
                        "retriable" to input.retriable,
                        "filingState" to input.jurisdiction,
                    ),
                )

                // Check if RPA is retryable
                if (input.retriable == true && count < rpaRetryService.getMaxRetries() &&
                    featureToggleService.isAutoRetryEnabled(
                        input.customerId,
                        input.jurisdiction,
                    )
                ) {
                    execution.output {
                        retryCount = count + 1
                    }
                    return false
                } else {
                    execution.output {
                        retriable = false
                        retryCount = 0
                    }

                    val validationError = rpaSosSsorcoService.createValidationError(validationErrorMessage, input)
                    execution.addValidationError(validationError)
                }
            }
        }
        // Check if Status is RPA Timeout
        if (input.status?.lowercase()?.equals("rpa timeout") == true) return false

        // Check Filepath
        if (input.evidenceFilePath.isNullOrEmpty() &&
            input.documentPaths?.filter { s -> !s.isNullOrEmpty() }
                .isNullOrEmpty()
        ) {
            if (input.processId == ProductType.ALTM.processId && input.status != "Success") {
                logger.event(
                    "rpa.callback.filepath.missing",
                    mapOf(
                        "RPA callback dont have ALTM evidenceFilePath for processingOrderId" to input.processingOrderId,
                    ),
                )
            } else {
                FILING_INCOMPLETE.let {
                    logger.errorEvent(
                        "rpa.callback.filepath.missing",
                        mapOf(
                            "evidenceFilePath" to input.evidenceFilePath,
                            "documentPaths" to input.documentPaths,
                            "validationError" to it,
                        ),
                    )
                    execution.addValidationError(ValidationError(it))
                }
            }
            return false
        }
        return true
    }

    private fun getProductTypeName(input: Variables) =
        ProductType.fromProcessIdNullable(input.processId)?.productName
            ?: throw RuntimeException("Could NOT retrieve product type for processId ${input.processId}")
}
