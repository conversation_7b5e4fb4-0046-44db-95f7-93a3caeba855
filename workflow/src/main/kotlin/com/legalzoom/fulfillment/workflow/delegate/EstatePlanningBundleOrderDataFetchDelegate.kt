package com.legalzoom.fulfillment.workflow.delegate

import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.enumeration.RelationshipType.SHIPPING
import com.legalzoom.fulfillment.common.extensions.orderItemByProcessingOrderId
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.service.enumeration.Cp1CustomerShipMethod.DOWNLOAD
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.Constants
import com.legalzoom.fulfillment.workflow.aspect.BpmnRetryTask
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.output
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.delegate.JavaDelegate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class EstatePlanningBundleOrderDataFetchDelegate(
    private val ordersApiService: OrdersApiService,
    private val salesforceApiService: SalesforceApiService,
    private val featureToggleService: FeatureToggleService,
    private val orderCancellationService: OrderCancellationService,
    private val questionnaireAnswerService: QuestionnaireAnswerService,
) : JavaDelegate {
    private val logger = LoggerFactory.getLogger(javaClass)

    @WithSpan("estatePlanningBundleOrderDataFetchDelegate")
    @Throws(Exception::class)
    @BpmnRetryTask
    override fun execute(execution: DelegateExecution) {
        val input = execution.input

        val orderResponse = ordersApiService.getOrders(input.orderId, null, null, null, null, null)

        val mainOrderItem =
            orderResponse.order?.orderItemByProcessingOrderId(input.processingOrderId!!)

        // Order cancellation check
        if (orderResponse.order?.isCancelled != false || mainOrderItem == null) {
            orderCancellationService.cancelOrder(execution, input.processingOrderId!!)
            return
        }

        val shippingOrderItem =
            orderResponse.order?.orderItems?.firstOrNull {
                it.parentOrderItemId == mainOrderItem.orderItemId && it.isCancelled == false &&
                    it.productConfiguration?.productTypeId?.value == SHIPPING.Id
            }

        var printingRequired = (shippingOrderItem?.shipMethodId ?: DOWNLOAD.Id) != DOWNLOAD.Id

        val isPrintingRequiredInput = input.isPrintingRequired

        val ledgerMessage =
            if (isPrintingRequiredInput != null && isPrintingRequiredInput && isPrintingRequiredInput != printingRequired) {
                "Printing is cancelled."
            } else {
                ""
            }

        if (ledgerMessage.isNotEmpty()) {
            addLedgerNote(ledgerMessage, input.orderId.toString(), input.processingOrderId.toString())
        }

        // For living will order with card, print and ship should be done, even if there is no shipping item
        var attachedWillCard = false
        var grantorFullNameField = ""
        var coGrantorFullNameField = ""

        if (input.processId == ProductType.LivingWill.processId) {
            var questionnaireResponse =
                questionnaireAnswerService.getAnswerVersionForUserOrderId(
                    input.processingOrderId!!,
                    input.customerId,
                    AnswerSource.AnswerBank,
                )
            var lwCard =
                questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                    it.fieldName == "living_will_POA_card"
                }?.fieldValue ?: ""
            lwCard = lwCard.trim().lowercase()

            if (lwCard == "1" || lwCard == "yes" || lwCard == "true") {
                attachedWillCard = true
                printingRequired = true
            }
        }

        if (input.processId == ProductType.LivingTrust.processId || input.processId == ProductType.TrustBundle.processId) {
            var questionnaireResponse =
                questionnaireAnswerService.getAnswerVersionForUserOrderId(
                    input.processingOrderId!!,
                    input.customerId,
                    AnswerSource.AnswerBank,
                )
            if (input.processId == ProductType.TrustBundle.processId) {
                grantorFullNameField = getFullName("Grantor", questionnaireResponse)
                coGrantorFullNameField = getFullName("CoGrantor", questionnaireResponse)
            } else {
                grantorFullNameField = questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                    it.fieldName == "Grantor_FullName"
                }?.fieldValue ?: ""
                coGrantorFullNameField = questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                    it.fieldName == "CoGrantor_FullName"
                }?.fieldValue ?: ""
            }
        }

        // For EP 2.0 bundle, print and ship will be triggered seperately
        if (ProductType.fromProcessIdNullable(input.processId) in Constants.EP_2_PRODUCTS) {
            printingRequired = false
        }

        execution.output {
            hasLivingWillCard = attachedWillCard
            isPrintingRequired = printingRequired
            docTemplateHint = "EP_BUNDLE_DOC_TEMPLATE"
            grantorName = grantorFullNameField
            coGrantorName = coGrantorFullNameField
        }
    }

    fun addLedgerNote(
        ledgerMessage: String,
        orderId: String,
        processingOrderId: String,
    ) {
        try {
            salesforceApiService.addLedgerNote(
                AddLedgerNoteRequest(
                    objectType = "note",
                    customerOrderNumber = orderId,
                    processingNumber = processingOrderId,
                    description = ledgerMessage,
                    createdBy = Constants.SALESFORCE_USER,
                ),
            )
        } catch (e: Exception) {
            logger.error("Adding Ledger Note Failed: ${e.message}", e)
        }
    }

    fun getFullName(
        fieldPrefix: String,
        questionnaireResponse: GetQuestionnaireAnswerResponse,
    ): String {
        val firstName =
            questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                it.fieldName == "${fieldPrefix}_FirstName"
            }?.fieldValue ?: ""
        val middleName =
            questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                it.fieldName == "${fieldPrefix}_MiddleName"
            }?.fieldValue ?: ""
        val lastName =
            questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                it.fieldName == "${fieldPrefix}_LastName"
            }?.fieldValue ?: ""
        val suffix =
            questionnaireResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.find {
                it.fieldName == "${fieldPrefix}_Suffix"
            }?.fieldValue ?: ""

        return listOf(firstName, middleName, lastName, suffix)
            .filter { it.isNotBlank() }
            .joinToString(" ")
    }
}
