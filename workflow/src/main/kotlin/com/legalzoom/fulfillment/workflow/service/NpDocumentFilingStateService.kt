package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.domain.enumeration.State.CALIFORNIA
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.NP
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
class NpDocumentFilingStateService : DocumentFilingStateService {
    private val productType = NP

    override fun getProductType(): ProductType = productType

    override fun documentRequired(execution: DelegateExecution): Bo<PERSON>an {
        return documentRequiredByVariables(execution.input)
    }

    override fun documentRequiredByVariables(variables: Variables): <PERSON><PERSON><PERSON> {
        return when (variables.jurisdiction) {
            CALIFORNIA.abbreviation -> {
                true
            }
            else -> false
        }
    }
}
