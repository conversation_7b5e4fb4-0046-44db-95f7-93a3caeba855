<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1r3imem" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.34.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.22.0">
  <bpmn:process id="annual-reports" name="Annual Reports Process" isExecutable="true">
    <bpmn:startEvent id="start" camunda:asyncAfter="true">
      <bpmn:outgoing>ar-start-flow</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="ar-start-flow" sourceRef="start" targetRef="gateway-filing-method">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${annualReportsStatusListener}" event="take">
          <camunda:field name="processingStatus">
            <camunda:string>AnnualReportsPreliminaryValidationComplete</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="gateway-filing-method" name="filing&#10;method" default="manual-filing-start">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${annualReportsDetermineFilingMethod}" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>ar-start-flow</bpmn:incoming>
      <bpmn:outgoing>auto-filing-start</bpmn:outgoing>
      <bpmn:outgoing>manual-filing-start</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="auto-filing-start" name="bot" sourceRef="gateway-filing-method" targetRef="bot-filing-subprocess">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('filingMethodOrdinal') == 1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="manual-filing-start" name="manual" sourceRef="gateway-filing-method" targetRef="manual-filing" />
    <bpmn:subProcess id="bot-filing-subprocess" name="File with bot" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:incoming>auto-filing-start</bpmn:incoming>
      <bpmn:outgoing>Flow_1og2tlr</bpmn:outgoing>
      <bpmn:startEvent id="bot-filing-subprocess-start-event" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:outgoing>Flow_1iiqrvn</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1iiqrvn" sourceRef="bot-filing-subprocess-start-event" targetRef="gateway-before-queue-bot-job" />
      <bpmn:endEvent id="bot-filing-subprocess-end-event" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_18sarqe</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0xswi0o" sourceRef="queue-bot-job" targetRef="await-bot-job" />
      <bpmn:serviceTask id="queue-bot-job" name="queue bot job" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${annualReportsQueueBotJob}">
        <bpmn:incoming>Flow_1pwl3ce</bpmn:incoming>
        <bpmn:outgoing>Flow_0xswi0o</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:receiveTask id="await-bot-job" name="await result" camunda:asyncBefore="true" camunda:asyncAfter="true" messageRef="Message_0jp6hqa">
        <bpmn:incoming>Flow_0xswi0o</bpmn:incoming>
        <bpmn:outgoing>Flow_1smfr7g</bpmn:outgoing>
      </bpmn:receiveTask>
      <bpmn:sequenceFlow id="Flow_1smfr7g" sourceRef="await-bot-job" targetRef="gateway-retry-bot-job" />
      <bpmn:exclusiveGateway id="gateway-retry-bot-job" name="retry?" camunda:asyncBefore="true" camunda:asyncAfter="true" default="Flow_0mwbpl1">
        <bpmn:extensionElements>
          <camunda:executionListener delegateExpression="${annualReportsBotRetryLogic}" event="start" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1smfr7g</bpmn:incoming>
        <bpmn:outgoing>Flow_18sarqe</bpmn:outgoing>
        <bpmn:outgoing>Flow_0mwbpl1</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_18sarqe" name="no" sourceRef="gateway-retry-bot-job" targetRef="bot-filing-subprocess-end-event">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('retryBot') == false}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:exclusiveGateway id="gateway-before-queue-bot-job" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_1iiqrvn</bpmn:incoming>
        <bpmn:incoming>Flow_0mwbpl1</bpmn:incoming>
        <bpmn:outgoing>Flow_1pwl3ce</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1pwl3ce" sourceRef="gateway-before-queue-bot-job" targetRef="queue-bot-job" />
      <bpmn:sequenceFlow id="Flow_0mwbpl1" name="should retry" sourceRef="gateway-retry-bot-job" targetRef="gateway-before-queue-bot-job" />
      <bpmn:textAnnotation id="TextAnnotation_0pzz1en">
        <bpmn:text>note that at this point could be success or failure</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_0msokyo" associationDirection="None" sourceRef="bot-filing-subprocess-end-event" targetRef="TextAnnotation_0pzz1en" />
      <bpmn:textAnnotation id="TextAnnotation_1ut98wo">
        <bpmn:text>todo (currently stubbed doesn't work)</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_1egzv95" associationDirection="None" sourceRef="Flow_0mwbpl1" targetRef="TextAnnotation_1ut98wo" />
    </bpmn:subProcess>
    <bpmn:subProcess id="manual-filing" name="Manual Filing" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:extensionElements />
      <bpmn:incoming>manual-filing-start</bpmn:incoming>
      <bpmn:incoming>Flow_1gmsb71</bpmn:incoming>
      <bpmn:outgoing>Flow_164uuht</bpmn:outgoing>
      <bpmn:startEvent id="start-manual-filing">
        <bpmn:outgoing>start-sf-flow</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="start-sf-flow" sourceRef="start-manual-filing" targetRef="sf-manual-filing" />
      <bpmn:endEvent id="end-manual-filing">
        <bpmn:incoming>Flow_1guzdvu</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:callActivity id="sf-manual-filing" name="SF Manual Filing Queue" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce">
        <bpmn:extensionElements>
          <camunda:in variables="all" />
          <camunda:inputOutput>
            <camunda:inputParameter name="sfQueue">PRE_FILING</camunda:inputParameter>
            <camunda:outputParameter name="retry">${false}</camunda:outputParameter>
          </camunda:inputOutput>
          <camunda:out source="caseDisposition" target="caseDisposition" local="true" />
          <camunda:in businessKey="#{execution.processBusinessKey}" />
        </bpmn:extensionElements>
        <bpmn:incoming>start-sf-flow</bpmn:incoming>
        <bpmn:outgoing>Flow_1guzdvu</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:sequenceFlow id="Flow_1guzdvu" sourceRef="sf-manual-filing" targetRef="end-manual-filing" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_164uuht" sourceRef="manual-filing" targetRef="ha-check">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${annualReportsStatusListener}" event="take">
          <camunda:field name="processingStatus">
            <camunda:string>AnnualReportsStateFilingComplete</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ha-check">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${annualReportsStatusListener}" event="start">
          <camunda:field name="processingStatus">
            <camunda:string>AnnualReportsStateFilingComplete</camunda:string>
          </camunda:field>
        </camunda:executionListener>
        <camunda:executionListener delegateExpression="${annualReportsStatusListener}" event="start">
          <camunda:field name="processingStatus">
            <camunda:string>AnnualReportsDocumentsReceivedFromState</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_164uuht</bpmn:incoming>
      <bpmn:incoming>Flow_1kq3rbh</bpmn:incoming>
      <bpmn:outgoing>Flow_1a1gta1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1og2tlr" sourceRef="bot-filing-subprocess" targetRef="Gateway_0a6zf6f" />
    <bpmn:endEvent id="end" name="Status: Sent to Customer">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${annualReportsStatusListener}" event="start">
          <camunda:field name="processingStatus">
            <camunda:string>AnnualReportsShippingComplete</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>skip-print-flow</bpmn:incoming>
      <bpmn:incoming>Flow_0p8lmr1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1kja5q4" default="skip-print-flow">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${printingRequiredListener}" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_19i7g8j</bpmn:incoming>
      <bpmn:outgoing>Flow_1mlpzox</bpmn:outgoing>
      <bpmn:outgoing>skip-print-flow</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1mlpzox" sourceRef="Gateway_1kja5q4" targetRef="print-ship">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('isPrintingRequired') == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="skip-print-flow" name="digital delivery" sourceRef="Gateway_1kja5q4" targetRef="end" />
    <bpmn:subProcess id="print-ship" name="Temp Print &#38; Ship" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:incoming>Flow_1mlpzox</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8lmr1</bpmn:outgoing>
      <bpmn:sendTask id="print-launch" name="Create DSD Print Job" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${annualReportsFakeDelegate}">
        <bpmn:incoming>Flow_0ab2yr7</bpmn:incoming>
        <bpmn:incoming>Flow_1qlh8kw</bpmn:incoming>
        <bpmn:outgoing>Flow_1e1txrl</bpmn:outgoing>
      </bpmn:sendTask>
      <bpmn:receiveTask id="print-status" name="Receive DSD Print Status" camunda:asyncBefore="true" camunda:asyncAfter="true" messageRef="Message_3ibt0bj">
        <bpmn:incoming>Flow_1e1txrl</bpmn:incoming>
        <bpmn:outgoing>Flow_10z80fe</bpmn:outgoing>
      </bpmn:receiveTask>
      <bpmn:startEvent id="print-start">
        <bpmn:outgoing>Flow_0ab2yr7</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="print-end">
        <bpmn:incoming>Flow_1i56n7f</bpmn:incoming>
        <bpmn:incoming>Flow_0gsmrvs</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0ab2yr7" sourceRef="print-start" targetRef="print-launch" />
      <bpmn:sequenceFlow id="Flow_1e1txrl" sourceRef="print-launch" targetRef="print-status" />
      <bpmn:exclusiveGateway id="print-shipped-check" default="Flow_0vuiebt">
        <bpmn:incoming>Flow_10z80fe</bpmn:incoming>
        <bpmn:outgoing>Flow_1i56n7f</bpmn:outgoing>
        <bpmn:outgoing>Flow_0vuiebt</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_10z80fe" sourceRef="print-status" targetRef="print-shipped-check" />
      <bpmn:sequenceFlow id="Flow_1i56n7f" sourceRef="print-shipped-check" targetRef="print-end">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("printStatus") == "SHIPPED"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0vuiebt" sourceRef="print-shipped-check" targetRef="sf-print-exception" />
      <bpmn:callActivity id="sf-print-exception" name="Salesforce: P&#38;S Exception Queue" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce">
        <bpmn:extensionElements>
          <camunda:in businessKey="#{execution.processBusinessKey}" />
          <camunda:inputOutput>
            <camunda:inputParameter name="sfQueue">PRINT_EXCEPTION</camunda:inputParameter>
          </camunda:inputOutput>
          <camunda:in variables="all" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0vuiebt</bpmn:incoming>
        <bpmn:outgoing>Flow_1qlh8kw</bpmn:outgoing>
        <bpmn:outgoing>Flow_0gsmrvs</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:sequenceFlow id="Flow_1qlh8kw" sourceRef="sf-print-exception" targetRef="print-launch" />
      <bpmn:sequenceFlow id="Flow_0gsmrvs" sourceRef="sf-print-exception" targetRef="print-end" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0p8lmr1" sourceRef="print-ship" targetRef="end" />
    <bpmn:sequenceFlow id="Flow_19i7g8j" sourceRef="doc-gen" targetRef="Gateway_1kja5q4">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${annualReportsStatusListener}" event="take">
          <camunda:field name="processingStatus">
            <camunda:string>AnnualReportsDocumentsPrepared</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0a6zf6f" name="bot outcome" default="Flow_1gmsb71">
      <bpmn:incoming>Flow_1og2tlr</bpmn:incoming>
      <bpmn:outgoing>Flow_1kq3rbh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gmsb71</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1kq3rbh" name="success" sourceRef="Gateway_0a6zf6f" targetRef="ha-check">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('botOutcome') == 'success'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1gmsb71" name="failure" sourceRef="Gateway_0a6zf6f" targetRef="manual-filing" />
    <bpmn:callActivity id="doc-gen" name="dsd doc gen" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="docgen-dsd-process">
      <bpmn:extensionElements>
        <camunda:in source="processId" target="processId" />
        <camunda:in source="processingOrderId" target="processingOrderId" />
        <camunda:in source="customerId" target="customerId" />
        <camunda:in source="accountId" target="accountId" />
        <camunda:in source="orderId" target="orderId" />
        <camunda:in source="cp2OrderItemId" target="cp2OrderItemId" />
        <camunda:in source="jurisdiction" target="jurisdiction" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1a1gta1</bpmn:incoming>
      <bpmn:outgoing>Flow_19i7g8j</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1a1gta1" sourceRef="ha-check" targetRef="doc-gen" />
    <bpmn:textAnnotation id="TextAnnotation_1kb4h0j">
      <bpmn:text>Status: Entity Name Prelim</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0u2b1ra" associationDirection="None" sourceRef="ar-start-flow" targetRef="TextAnnotation_1kb4h0j" />
    <bpmn:textAnnotation id="TextAnnotation_16ju17q">
      <bpmn:text>note that some retries of failed bot jobs with system error can occur in here but they can be exhausted</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0n4zdc9" associationDirection="None" sourceRef="bot-filing-subprocess" targetRef="TextAnnotation_16ju17q" />
    <bpmn:textAnnotation id="TextAnnotation_1mvl2w7">
      <bpmn:text>status: sent to sos</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1tqik55" associationDirection="None" sourceRef="Flow_164uuht" targetRef="TextAnnotation_1mvl2w7" />
    <bpmn:textAnnotation id="TextAnnotation_0sr2orq">
      <bpmn:text>status: docs prepared</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1l6djgc" associationDirection="None" sourceRef="Flow_19i7g8j" targetRef="TextAnnotation_0sr2orq" />
    <bpmn:textAnnotation id="TextAnnotation_0der0v0">
      <bpmn:text>status: state filing complete
status: docs received from state</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_15pezfd" associationDirection="None" sourceRef="ha-check" targetRef="TextAnnotation_0der0v0" />
  </bpmn:process>
  <bpmn:message id="Message_3ibt0bj" name="DSD_PRINT_STATUS_UPDATE" />
  <bpmn:message id="Message_00h7rse" name="DSD_DOC_GEN_RESULT" />
  <bpmn:message id="Message_0er95n2" name="HA_DOCS_RECEIVED" />
  <bpmn:message id="Message_0jp6hqa" name="DSD-WEB-AUTOMATION-JOB-STATUS-UPDATE" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="annual-reports">
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="start">
        <dc:Bounds x="152" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0gewav7_di" bpmnElement="gateway-filing-method" isMarkerVisible="true">
        <dc:Bounds x="305" y="295" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="268" y="337" width="37" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16533rj_di" bpmnElement="end">
        <dc:Bounds x="1342" y="302" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1323" y="348" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kja5q4_di" bpmnElement="Gateway_1kja5q4" isMarkerVisible="true">
        <dc:Bounds x="1055" y="295" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wp0kug_di" bpmnElement="doc-gen">
        <dc:Bounds x="870" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ouwdh1_di" bpmnElement="bot-filing-subprocess">
        <dc:Bounds x="390" y="210" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1agxumv_di" bpmnElement="manual-filing">
        <dc:Bounds x="390" y="364" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1302jwr_di" bpmnElement="ha-check" isMarkerVisible="true">
        <dc:Bounds x="715" y="295" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="704" y="352" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0a6zf6f_di" bpmnElement="Gateway_0a6zf6f" isMarkerVisible="true">
        <dc:Bounds x="545" y="225" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="539" y="201" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0n4zdc9_di" bpmnElement="Association_0n4zdc9">
        <di:waypoint x="427" y="210" />
        <di:waypoint x="411" y="154" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_131a4zx_di" bpmnElement="print-ship">
        <dc:Bounds x="1170" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1l6djgc_di" bpmnElement="Association_1l6djgc">
        <di:waypoint x="1013" y="320" />
        <di:waypoint x="1013" y="251" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_15pezfd_di" bpmnElement="Association_15pezfd">
        <di:waypoint x="745" y="300" />
        <di:waypoint x="763" y="229" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1kb4h0j_di" bpmnElement="TextAnnotation_1kb4h0j">
        <dc:Bounds x="200" y="240" width="100" height="41" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1mvl2w7_di" bpmnElement="TextAnnotation_1mvl2w7">
        <dc:Bounds x="640" y="440" width="99.99274099883856" height="40.65040650406504" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0t94a7q_di" bpmnElement="ar-start-flow">
        <di:waypoint x="188" y="320" />
        <di:waypoint x="305" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14mv6un_di" bpmnElement="auto-filing-start">
        <di:waypoint x="330" y="295" />
        <di:waypoint x="330" y="250" />
        <di:waypoint x="390" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="336" y="228" width="16" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1irpo5x_di" bpmnElement="manual-filing-start">
        <di:waypoint x="330" y="345" />
        <di:waypoint x="330" y="404" />
        <di:waypoint x="390" y="404" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="327" y="413" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_164uuht_di" bpmnElement="Flow_164uuht">
        <di:waypoint x="490" y="404" />
        <di:waypoint x="660" y="404" />
        <di:waypoint x="660" y="320" />
        <di:waypoint x="715" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1og2tlr_di" bpmnElement="Flow_1og2tlr">
        <di:waypoint x="490" y="250" />
        <di:waypoint x="545" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mlpzox_di" bpmnElement="Flow_1mlpzox">
        <di:waypoint x="1105" y="320" />
        <di:waypoint x="1170" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u4c2i8_di" bpmnElement="skip-print-flow">
        <di:waypoint x="1080" y="295" />
        <di:waypoint x="1080" y="220" />
        <di:waypoint x="1360" y="220" />
        <di:waypoint x="1360" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1185" y="202" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8lmr1_di" bpmnElement="Flow_0p8lmr1">
        <di:waypoint x="1270" y="320" />
        <di:waypoint x="1342" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19i7g8j_di" bpmnElement="Flow_19i7g8j">
        <di:waypoint x="970" y="320" />
        <di:waypoint x="1055" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="941" y="286" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kq3rbh_di" bpmnElement="Flow_1kq3rbh">
        <di:waypoint x="595" y="250" />
        <di:waypoint x="660" y="250" />
        <di:waypoint x="660" y="320" />
        <di:waypoint x="715" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="624" y="228" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gmsb71_di" bpmnElement="Flow_1gmsb71">
        <di:waypoint x="570" y="275" />
        <di:waypoint x="570" y="384" />
        <di:waypoint x="490" y="384" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="575" y="323" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a1gta1_di" bpmnElement="Flow_1a1gta1">
        <di:waypoint x="765" y="320" />
        <di:waypoint x="870" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0u2b1ra_di" bpmnElement="Association_0u2b1ra">
        <di:waypoint x="230" y="320" />
        <di:waypoint x="242" y="281" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1tqik55_di" bpmnElement="Association_1tqik55">
        <di:waypoint x="644.5" y="404" />
        <di:waypoint x="677" y="440" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_16ju17q_di" bpmnElement="TextAnnotation_16ju17q">
        <dc:Bounds x="330" y="70" width="139.9924089068826" height="83.67071524966262" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0sr2orq_di" bpmnElement="TextAnnotation_0sr2orq">
        <dc:Bounds x="963" y="210" width="100" height="41" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0der0v0_di" bpmnElement="TextAnnotation_0der0v0">
        <dc:Bounds x="720" y="189" width="209.9991565452092" height="40.48582995951417" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmndi:BPMNDiagram id="BPMNDiagram_0om88hb">
    <bpmndi:BPMNPlane id="BPMNPlane_09r0dbe" bpmnElement="bot-filing-subprocess">
      <bpmndi:BPMNShape id="Event_03nj0ii_di" bpmnElement="bot-filing-subprocess-start-event">
        <dc:Bounds x="152" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_03e3ekm_di" bpmnElement="bot-filing-subprocess-end-event">
        <dc:Bounds x="752" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1f4ed0g_di" bpmnElement="queue-bot-job">
        <dc:Bounds x="320" y="130" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09sijpu_di" bpmnElement="await-bot-job">
        <dc:Bounds x="470" y="130" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15e2ih4_di" bpmnElement="gateway-retry-bot-job" isMarkerVisible="true">
        <dc:Bounds x="625" y="145" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="665" y="183" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bln1cq_di" bpmnElement="gateway-before-queue-bot-job" isMarkerVisible="true">
        <dc:Bounds x="225" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0pzz1en_di" bpmnElement="TextAnnotation_0pzz1en">
        <dc:Bounds x="800" y="60" width="100" height="70" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1ut98wo_di" bpmnElement="TextAnnotation_1ut98wo">
        <dc:Bounds x="360" y="310" width="100" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1iiqrvn_di" bpmnElement="Flow_1iiqrvn">
        <di:waypoint x="188" y="170" />
        <di:waypoint x="225" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xswi0o_di" bpmnElement="Flow_0xswi0o">
        <di:waypoint x="420" y="170" />
        <di:waypoint x="470" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1smfr7g_di" bpmnElement="Flow_1smfr7g">
        <di:waypoint x="570" y="170" />
        <di:waypoint x="625" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18sarqe_di" bpmnElement="Flow_18sarqe">
        <di:waypoint x="675" y="170" />
        <di:waypoint x="752" y="170" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="707" y="152" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pwl3ce_di" bpmnElement="Flow_1pwl3ce">
        <di:waypoint x="275" y="170" />
        <di:waypoint x="320" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mwbpl1_di" bpmnElement="Flow_0mwbpl1">
        <di:waypoint x="650" y="195" />
        <di:waypoint x="650" y="260" />
        <di:waypoint x="250" y="260" />
        <di:waypoint x="250" y="195" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="481" y="242" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0msokyo_di" bpmnElement="Association_0msokyo">
        <di:waypoint x="782" y="156" />
        <di:waypoint x="804" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1egzv95_di" bpmnElement="Association_1egzv95">
        <di:waypoint x="450" y="260" />
        <di:waypoint x="419" y="310" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmndi:BPMNDiagram id="BPMNDiagram_05qvkdw">
    <bpmndi:BPMNPlane id="BPMNPlane_0jddy6x" bpmnElement="manual-filing">
      <bpmndi:BPMNShape id="Event_1th8tmj_di" bpmnElement="start-manual-filing">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ytadvw_di" bpmnElement="end-manual-filing">
        <dc:Bounds x="402" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_15wudpa_di" bpmnElement="sf-manual-filing">
        <dc:Bounds x="240" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1b5oaf1_di" bpmnElement="start-sf-flow">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="240" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1guzdvu_di" bpmnElement="Flow_1guzdvu">
        <di:waypoint x="340" y="120" />
        <di:waypoint x="402" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmndi:BPMNDiagram id="BPMNDiagram_0mdyl5n">
    <bpmndi:BPMNPlane id="BPMNPlane_11vbd85" bpmnElement="print-ship">
      <bpmndi:BPMNShape id="BPMNShape_19cxo1w" bpmnElement="print-launch">
        <dc:Bounds x="260" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18rri4n" bpmnElement="print-status">
        <dc:Bounds x="440" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1p5siwr" bpmnElement="print-start">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_118lg8o" bpmnElement="print-end">
        <dc:Bounds x="752" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1gdsg6u_di" bpmnElement="print-shipped-check" isMarkerVisible="true">
        <dc:Bounds x="615" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1vmsy55_di" bpmnElement="sf-print-exception">
        <dc:Bounds x="590" y="210" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1k5dy0m" bpmnElement="Flow_0ab2yr7">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="260" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0adsqsd" bpmnElement="Flow_1e1txrl">
        <di:waypoint x="360" y="120" />
        <di:waypoint x="440" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10z80fe_di" bpmnElement="Flow_10z80fe">
        <di:waypoint x="540" y="120" />
        <di:waypoint x="615" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i56n7f_di" bpmnElement="Flow_1i56n7f">
        <di:waypoint x="665" y="120" />
        <di:waypoint x="752" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vuiebt_di" bpmnElement="Flow_0vuiebt">
        <di:waypoint x="640" y="145" />
        <di:waypoint x="640" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qlh8kw_di" bpmnElement="Flow_1qlh8kw">
        <di:waypoint x="590" y="250" />
        <di:waypoint x="310" y="250" />
        <di:waypoint x="310" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gsmrvs_di" bpmnElement="Flow_0gsmrvs">
        <di:waypoint x="690" y="250" />
        <di:waypoint x="770" y="250" />
        <di:waypoint x="770" y="138" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
