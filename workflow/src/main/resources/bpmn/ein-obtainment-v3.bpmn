<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0f34v8i" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.17.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:message id="Message_0y0ig5m" name="Message_RPA" />
  <bpmn:error id="Error_0tjp1nk" name="Error_NoRetries" errorCode="Error_NoRetries" camunda:errorMessage="Error Boundary Event" />
  <bpmn:message id="Message_1mnlpkj" name="Message_Route_Legacy" />
  <bpmn:error id="Error_0br6cmd" name="Error_NoRetries" errorCode="Error_NoRetries" camunda:errorMessage="Error Boundary Event" />
  <bpmn:process id="ein-obtainment-process-v3" name="ein-obtainment-process-v3" isExecutable="true">
    <bpmn:startEvent id="Event_0yrtavm">
      <bpmn:outgoing>Flow_0v3pt9f</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:callActivity id="rpa-bot-process" name="rpa-bot-process" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="rpa-bot-process">
      <bpmn:extensionElements>
        <camunda:in source="accountId" target="accountId" />
        <camunda:in source="orderId" target="orderId" />
        <camunda:in source="processingOrderId" target="processingOrderId" />
        <camunda:in source="processId" target="processId" />
        <camunda:in source="workOrderId" target="workOrderId" />
        <camunda:in source="entityName" target="entityName" />
        <camunda:in source="expediteSpeed" target="expediteSpeed" />
        <camunda:in source="customerId" target="customerId" />
        <camunda:out source="evidenceTransactionNumber" target="evidenceTransactionNumber" />
        <camunda:out source="validationError" target="validationError" />
        <camunda:out source="validationErrors" target="validationErrors" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in sourceExpression="${&#39;FEDERAL&#39;}" target="jurisdiction" />
        <camunda:in sourceExpression="${&#39;EIN_OBTAINMENT&#39;}" target="parentEventPhase" />
        <camunda:out source="status" target="status" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jiai5g</bpmn:incoming>
      <bpmn:incoming>Flow_1xe4ydm</bpmn:incoming>
      <bpmn:incoming>Flow_1o8fmfm</bpmn:incoming>
      <bpmn:outgoing>Flow_1oemjdh</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1m77m36" name="do we have a bot for this?" camunda:asyncBefore="true" camunda:asyncAfter="true" default="Flow_1jiai5g">
      <bpmn:incoming>Flow_15fmx1k</bpmn:incoming>
      <bpmn:outgoing>Flow_1jiai5g</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yhrrs0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1sf000n" default="Flow_1xiiwb6">
      <bpmn:incoming>Flow_1oemjdh</bpmn:incoming>
      <bpmn:outgoing>Flow_038ulbn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1xiiwb6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:callActivity id="Activity_12864yi" name="document-generation-process" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="document-generation-process">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:executionListener delegateExpression="${activityListener}" event="start" />
        <camunda:executionListener delegateExpression="${activityListener}" event="end" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in source="customerId" target="customerId" />
        <camunda:in source="accountId" target="accountId" />
        <camunda:in source="entityName" target="entityName" />
        <camunda:in source="jurisdiction" target="jurisdiction" />
        <camunda:in source="orderId" target="orderId" />
        <camunda:in source="processId" target="processId" />
        <camunda:in source="parentProcessId" target="parentProcessId" />
        <camunda:in source="processingOrderId" target="processingOrderId" />
        <camunda:in source="parentProcessingOrderId" target="parentProcessingOrderId" />
        <camunda:in source="workOrderId" target="workOrderId" />
        <camunda:in sourceExpression="${true}" target="setDocStatusToActive" />
        <camunda:in sourceExpression="null" target="validationError" />
        <camunda:in sourceExpression="null" target="validationErrors" />
        <camunda:in sourceExpression="SS4_PREPARED" target="requestedDocumentsCSV" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01w0y0z</bpmn:incoming>
      <bpmn:incoming>Flow_15qhnaj</bpmn:incoming>
      <bpmn:incoming>Flow_0cuopcu</bpmn:incoming>
      <bpmn:outgoing>Flow_0ydl00w</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="ein-salesforce-activity" name="salesforce-activity" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce-process">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in sourceExpression="VALIDATION_COMPLETE" target="eventType" />
        <camunda:in sourceExpression="EIN_FILING" target="salesforceEventPhase" />
        <camunda:in sourceExpression="${true}" target="validationError" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1a9kvm4</bpmn:incoming>
      <bpmn:outgoing>Flow_1hvwvkf</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:serviceTask id="validation-error-task" name="validation error check" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${validationErrorDelegate}">
      <bpmn:incoming>Flow_15etdon</bpmn:incoming>
      <bpmn:outgoing>Flow_1a9kvm4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ein-obtained-gateway">
      <bpmn:incoming>Flow_0kb48ih</bpmn:incoming>
      <bpmn:incoming>Flow_033zf84</bpmn:incoming>
      <bpmn:outgoing>Flow_1tjpkpq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:callActivity id="ein-document-generation-process" name="document-generation-process" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="document-generation-process">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:executionListener delegateExpression="${activityListener}" event="start" />
        <camunda:executionListener delegateExpression="${activityListener}" event="end" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in source="customerId" target="customerId" />
        <camunda:in source="accountId" target="accountId" />
        <camunda:in source="entityName" target="entityName" />
        <camunda:in source="jurisdiction" target="jurisdiction" />
        <camunda:in source="orderId" target="orderId" />
        <camunda:in source="processId" target="processId" />
        <camunda:in source="parentProcessId" target="parentProcessId" />
        <camunda:in source="processingOrderId" target="processingOrderId" />
        <camunda:in source="parentProcessingOrderId" target="parentProcessingOrderId" />
        <camunda:in source="workOrderId" target="workOrderId" />
        <camunda:inputOutput>
          <camunda:inputParameter name="setDocStatusToActive">${true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0xsfmjr</bpmn:incoming>
      <bpmn:outgoing>Flow_0xzlu0f</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_0b25qac" default="Flow_1l9nvud">
      <bpmn:incoming>Flow_0xzlu0f</bpmn:incoming>
      <bpmn:outgoing>Flow_0iypw8b</bpmn:outgoing>
      <bpmn:outgoing>Flow_1l9nvud</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:callActivity id="print" name="print-process" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="print-process">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in source="customerId" target="customerId" />
        <camunda:in source="entityName" target="entityName" />
        <camunda:in source="jurisdiction" target="jurisdiction" />
        <camunda:in source="orderId" target="orderId" />
        <camunda:in source="processId" target="processId" />
        <camunda:in source="processingOrderId" target="processingOrderId" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0iypw8b</bpmn:incoming>
      <bpmn:outgoing>Flow_04hs1pv</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:endEvent id="Event_0kwd9t4">
      <bpmn:incoming>Flow_04hs1pv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0yqchtf">
      <bpmn:incoming>Flow_1149l55</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="store_ein" name="store ein" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${saveEinDelegate}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_00e28wo</bpmn:incoming>
      <bpmn:outgoing>Flow_0kb48ih</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0v3pt9f" sourceRef="Event_0yrtavm" targetRef="Activity_13avu5t" />
    <bpmn:sequenceFlow id="Flow_1jiai5g" name="bot supports this order" sourceRef="Gateway_1m77m36" targetRef="rpa-bot-process" />
    <bpmn:sequenceFlow id="Flow_1oemjdh" sourceRef="rpa-bot-process" targetRef="Gateway_1sf000n" />
    <bpmn:sequenceFlow id="Flow_1yhrrs0" name="bot not supported for business type" sourceRef="Gateway_1m77m36" targetRef="prepare-bypass-message">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bypassRpaService.isBypassEinRpa(execution)}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_038ulbn" name="bot&#10;success" sourceRef="Gateway_1sf000n" targetRef="validate-rpa-ein-docs-task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('validationError') != true &amp;&amp; execution.getVariable('status') == 'Success'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1xiiwb6" name="bot&#10;failure" sourceRef="Gateway_1sf000n" targetRef="Activity_1cv37lm" />
    <bpmn:sequenceFlow id="Flow_0ydl00w" sourceRef="Activity_12864yi" targetRef="Gateway_0500rd1" />
    <bpmn:serviceTask id="customer-instructions-task" name="Send Customer Message" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${einCustomerInstructionsDelegate}">
      <bpmn:incoming>Flow_0earvpt</bpmn:incoming>
      <bpmn:incoming>Flow_1nuxn28</bpmn:incoming>
      <bpmn:outgoing>Flow_1c634sq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1a9kvm4" sourceRef="validation-error-task" targetRef="ein-salesforce-activity" />
    <bpmn:sequenceFlow id="Flow_0kb48ih" sourceRef="store_ein" targetRef="ein-obtained-gateway" />
    <bpmn:sequenceFlow id="Flow_0xzlu0f" sourceRef="ein-document-generation-process" targetRef="Gateway_0b25qac" />
    <bpmn:sequenceFlow id="Flow_0iypw8b" name="print &#38; ship" sourceRef="Gateway_0b25qac" targetRef="print">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('isAttachedOrder') == false &amp;&amp; printingBehaviourService.shouldPrint(execution) == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1l9nvud" name="no print &#38; ship" sourceRef="Gateway_0b25qac" targetRef="order-status-update-sent-to-customer" />
    <bpmn:sequenceFlow id="Flow_04hs1pv" sourceRef="print" targetRef="Event_0kwd9t4" />
    <bpmn:serviceTask id="prepare-bypass-message" name="prepare bypass error" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${einPrepareBypassErrorDelegate}">
      <bpmn:incoming>Flow_1yhrrs0</bpmn:incoming>
      <bpmn:outgoing>Flow_15etdon</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_15etdon" sourceRef="prepare-bypass-message" targetRef="validation-error-task" />
    <bpmn:serviceTask id="order-status-update-sent-to-customer" name="Update Status: Sent to Customer" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${orderStatusDelegate}">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle />
        <camunda:field name="processingStatus">
          <camunda:expression>Sent to Customer</camunda:expression>
        </camunda:field>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1l9nvud</bpmn:incoming>
      <bpmn:outgoing>Flow_1149l55</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1149l55" sourceRef="order-status-update-sent-to-customer" targetRef="Event_0yqchtf" />
    <bpmn:serviceTask id="validate-rpa-ein-docs-task" name="Validate RPA EIN and Docs" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${checkRpaEinSuccessDelegate}">
      <bpmn:incoming>Flow_038ulbn</bpmn:incoming>
      <bpmn:incoming>Flow_0fvi2bv</bpmn:incoming>
      <bpmn:outgoing>Flow_00e28wo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_00e28wo" sourceRef="validate-rpa-ein-docs-task" targetRef="store_ein" />
    <bpmn:serviceTask id="Activity_04au34l" name="Validate doc uploaded" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${checkEinDocUploadedDelegate}">
      <bpmn:incoming>Flow_11y2hu0</bpmn:incoming>
      <bpmn:outgoing>Flow_0r0w9hr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0r0w9hr" sourceRef="Activity_04au34l" targetRef="validate-ein-number-task" />
    <bpmn:serviceTask id="validate-ein-number-task" name="Validate EIN Number entered" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${checkEINNumberDelegate}">
      <bpmn:incoming>Flow_0r0w9hr</bpmn:incoming>
      <bpmn:outgoing>Flow_033zf84</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1qcuia0" name="SF case outcome">
      <bpmn:incoming>Flow_1hvwvkf</bpmn:incoming>
      <bpmn:outgoing>Flow_11y2hu0</bpmn:outgoing>
      <bpmn:outgoing>Flow_01w0y0z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hvwvkf" sourceRef="ein-salesforce-activity" targetRef="Gateway_1qcuia0" />
    <bpmn:sequenceFlow id="Flow_11y2hu0" name="manually filed successfully" sourceRef="Gateway_1qcuia0" targetRef="Activity_04au34l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${disposition == 'retry' || disposition == 'proceed'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:subProcess id="Activity_13avu5t" name="await related orders">
      <bpmn:incoming>Flow_0v3pt9f</bpmn:incoming>
      <bpmn:outgoing>Flow_03cm7m3</bpmn:outgoing>
      <bpmn:startEvent id="await_related_orders_start_event">
        <bpmn:outgoing>Flow_0o8uyti</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0qhcmj2" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_0wtnbgu</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:exclusiveGateway id="Gateway_1ptztgi" camunda:asyncBefore="true" camunda:asyncAfter="true" default="Flow_00kwiic">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_0lzkqc3</bpmn:incoming>
        <bpmn:outgoing>Flow_0wtnbgu</bpmn:outgoing>
        <bpmn:outgoing>Flow_00kwiic</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:callActivity id="salesforce-activity-await-related-order" name="salesforce await case" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce-process">
        <bpmn:extensionElements>
          <camunda:in businessKey="#{execution.processBusinessKey}" />
          <camunda:in variables="all" />
          <camunda:in sourceExpression="VALIDATION_COMPLETE" target="eventType" />
          <camunda:in sourceExpression="AWAITING_DEPENDENT_ORDER" target="salesforceEventPhase" />
          <camunda:in sourceExpression="${true}" target="validationError" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0rm2jj3</bpmn:incoming>
        <bpmn:outgoing>Flow_0hvp1z0</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:intermediateCatchEvent id="Event_1b3hsrd" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_07sattl</bpmn:incoming>
        <bpmn:incoming>Flow_1n2dljw</bpmn:incoming>
        <bpmn:outgoing>Flow_1b4mxng</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0bsfpoi">
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${execution.getVariable("conditionRecheckTimer") == null ? "PT1H" : execution.getVariable("conditionRecheckTimer")}</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      <bpmn:parallelGateway id="Gateway_010wpa1" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_0jfdi3e</bpmn:incoming>
        <bpmn:outgoing>Flow_0rm2jj3</bpmn:outgoing>
        <bpmn:outgoing>Flow_07sattl</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:exclusiveGateway id="Gateway_0lxokhw" camunda:asyncBefore="true" camunda:asyncAfter="true" default="Flow_02jt6dc">
        <bpmn:incoming>Flow_0nxldwm</bpmn:incoming>
        <bpmn:outgoing>Flow_1n2dljw</bpmn:outgoing>
        <bpmn:outgoing>Flow_02jt6dc</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:endEvent id="Event_1db64vk" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_02jt6dc</bpmn:incoming>
        <bpmn:terminateEventDefinition id="TerminateEventDefinition_0751eyo" />
      </bpmn:endEvent>
      <bpmn:endEvent id="Event_1kzxy38" camunda:asyncBefore="true" camunda:asyncAfter="true">
        <bpmn:incoming>Flow_0hvp1z0</bpmn:incoming>
        <bpmn:terminateEventDefinition id="TerminateEventDefinition_1bkwzv9" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0o8uyti" sourceRef="await_related_orders_start_event" targetRef="evaluate_related_orders_condition" />
      <bpmn:sequenceFlow id="Flow_0wtnbgu" name="no related orders or their status does not necessitate await" sourceRef="Gateway_1ptztgi" targetRef="Event_0qhcmj2">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('blocked') == false}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0lzkqc3" sourceRef="evaluate_related_orders_condition" targetRef="Gateway_1ptztgi" />
      <bpmn:sequenceFlow id="Flow_00kwiic" sourceRef="Gateway_1ptztgi" targetRef="order-status-update-waiting-formation" />
      <bpmn:sequenceFlow id="Flow_0rm2jj3" sourceRef="Gateway_010wpa1" targetRef="salesforce-activity-await-related-order" />
      <bpmn:sequenceFlow id="Flow_0hvp1z0" sourceRef="salesforce-activity-await-related-order" targetRef="Event_1kzxy38" />
      <bpmn:sequenceFlow id="Flow_07sattl" sourceRef="Gateway_010wpa1" targetRef="Event_1b3hsrd" />
      <bpmn:sequenceFlow id="Flow_1n2dljw" name="still blocked" sourceRef="Gateway_0lxokhw" targetRef="Event_1b3hsrd">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('blocked') == true}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1b4mxng" sourceRef="Event_1b3hsrd" targetRef="reevaluate_related_orders_condition" />
      <bpmn:sequenceFlow id="Flow_0nxldwm" sourceRef="reevaluate_related_orders_condition" targetRef="Gateway_0lxokhw" />
      <bpmn:sequenceFlow id="Flow_02jt6dc" name="no longer&#10;blocked" sourceRef="Gateway_0lxokhw" targetRef="Event_1db64vk" />
      <bpmn:serviceTask id="evaluate_related_orders_condition" name="evaluate is blocked condition" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${evaluateIsOrderBlockedDelegate}">
        <bpmn:incoming>Flow_0o8uyti</bpmn:incoming>
        <bpmn:outgoing>Flow_0lzkqc3</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="reevaluate_related_orders_condition" name="re-evaluate is blocked condition" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${evaluateIsOrderBlockedDelegate}">
        <bpmn:incoming>Flow_1b4mxng</bpmn:incoming>
        <bpmn:outgoing>Flow_0nxldwm</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="order-status-update-waiting-formation" name="Update Status: Awaiting Formation" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${orderStatusDelegate}">
        <bpmn:extensionElements>
          <camunda:failedJobRetryTimeCycle />
          <camunda:field name="processingStatus">
            <camunda:expression>Awaiting Formation</camunda:expression>
          </camunda:field>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_00kwiic</bpmn:incoming>
        <bpmn:outgoing>Flow_0jfdi3e</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0jfdi3e" sourceRef="order-status-update-waiting-formation" targetRef="Gateway_010wpa1" />
      <bpmn:textAnnotation id="TextAnnotation_0hakrs3">
        <bpmn:text>note that this also terminates the path below</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_0ku2e07" associationDirection="None" sourceRef="Event_1kzxy38" targetRef="TextAnnotation_0hakrs3" />
      <bpmn:textAnnotation id="TextAnnotation_17wndfe">
        <bpmn:text>note that this also terminates the path above</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_0optutv" associationDirection="None" sourceRef="Event_1db64vk" targetRef="TextAnnotation_17wndfe" />
      <bpmn:textAnnotation id="TextAnnotation_0orqk92">
        <bpmn:text>evaluates whether this order is blocked by others</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_113dpfu" associationDirection="None" sourceRef="evaluate_related_orders_condition" targetRef="TextAnnotation_0orqk92" />
      <bpmn:textAnnotation id="TextAnnotation_1cwlsec">
        <bpmn:text>gives human a chance to end the delay if needed</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_0x6kmrk" associationDirection="None" sourceRef="salesforce-activity-await-related-order" targetRef="TextAnnotation_1cwlsec" />
      <bpmn:textAnnotation id="TextAnnotation_0e9628f">
        <bpmn:text>wait before next check</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_1z0aljn" associationDirection="None" sourceRef="Event_1b3hsrd" targetRef="TextAnnotation_0e9628f" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_03cm7m3" sourceRef="Activity_13avu5t" targetRef="order-status-update-filing-in-progress" />
    <bpmn:serviceTask id="order-status-update-filing-in-progress" name="Update Status: Filing in Progress" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${orderStatusDelegate}">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle />
        <camunda:field name="processingStatus">
          <camunda:expression>Filing in Progress</camunda:expression>
        </camunda:field>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03cm7m3</bpmn:incoming>
      <bpmn:outgoing>Flow_15fmx1k</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_15fmx1k" sourceRef="order-status-update-filing-in-progress" targetRef="Gateway_1m77m36" />
    <bpmn:serviceTask id="Activity_1y1h6cc" name="obtained: notify external systems" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${notifyExternalSystemsOfCompletedEINFilingDelegate}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1tjpkpq</bpmn:incoming>
      <bpmn:outgoing>Flow_0xsfmjr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1tjpkpq" sourceRef="ein-obtained-gateway" targetRef="Activity_1y1h6cc" />
    <bpmn:sequenceFlow id="Flow_0xsfmjr" sourceRef="Activity_1y1h6cc" targetRef="ein-document-generation-process" />
    <bpmn:sequenceFlow id="Flow_1c634sq" sourceRef="customer-instructions-task" targetRef="Activity_1juy8v9" />
    <bpmn:serviceTask id="Activity_1juy8v9" name="Update Status: Sent to Customer" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${orderStatusDelegate}">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle />
        <camunda:field name="processingStatus">
          <camunda:expression>Sent to Customer</camunda:expression>
        </camunda:field>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1c634sq</bpmn:incoming>
      <bpmn:outgoing>Flow_12nsn6t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0o2ik4g">
      <bpmn:incoming>Flow_12nsn6t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_12nsn6t" sourceRef="Activity_1juy8v9" targetRef="Event_0o2ik4g" />
    <bpmn:task id="Activity_13zxj2h" name="Update myLZ task">
      <bpmn:incoming>Flow_08o3vw4</bpmn:incoming>
      <bpmn:outgoing>Flow_0earvpt</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0earvpt" sourceRef="Activity_13zxj2h" targetRef="customer-instructions-task" />
    <bpmn:sequenceFlow id="Flow_01w0y0z" name="processor attempted filing no success" sourceRef="Gateway_1qcuia0" targetRef="Activity_12864yi" />
    <bpmn:exclusiveGateway id="Gateway_0500rd1" name="is there a MyLZ task?">
      <bpmn:incoming>Flow_0ydl00w</bpmn:incoming>
      <bpmn:outgoing>Flow_1nuxn28</bpmn:outgoing>
      <bpmn:outgoing>Flow_08o3vw4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1nuxn28" name="no" sourceRef="Gateway_0500rd1" targetRef="customer-instructions-task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!hasMyLZTask}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_1bq7f0e" name="Create Camunda Incident" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${einSystemErrorDelegate}">
      <bpmn:incoming>Flow_1abbc0o</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08o3vw4" name="yes" sourceRef="Gateway_0500rd1" targetRef="Activity_13zxj2h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${hasMyLZTask}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_033zf84" sourceRef="validate-ein-number-task" targetRef="ein-obtained-gateway" />
    <bpmn:callActivity id="Activity_0f6997g" name="salesforce-activity" camunda:asyncBefore="true" camunda:asyncAfter="true" calledElement="salesforce-process">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in sourceExpression="VALIDATION_COMPLETE" target="eventType" />
        <camunda:in sourceExpression="EIN_FILING" target="salesforceEventPhase" />
        <camunda:in sourceExpression="${true}" target="validationError" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_09az7w8</bpmn:incoming>
      <bpmn:outgoing>Flow_1smmoru</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_07xmb1r" name="disposition">
      <bpmn:incoming>Flow_1smmoru</bpmn:incoming>
      <bpmn:outgoing>Flow_0fvi2bv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1xe4ydm</bpmn:outgoing>
      <bpmn:outgoing>Flow_0cuopcu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1smmoru" sourceRef="Activity_0f6997g" targetRef="Gateway_07xmb1r" />
    <bpmn:sequenceFlow id="Flow_0fvi2bv" sourceRef="Gateway_07xmb1r" targetRef="validate-rpa-ein-docs-task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${disposition == 'proceed'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1xe4ydm" name="disposition: retry&#10;bot&#10;" sourceRef="Gateway_07xmb1r" targetRef="rpa-bot-process">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${disposition == 'retry'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:businessRuleTask id="Activity_1cv37lm" name="inspect failure reason, decide how to proceed" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:resultVariable="action" camunda:decisionRef="ein-bot-failures">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${einUnrollDmnOutputVariables}" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xiiwb6</bpmn:incoming>
      <bpmn:outgoing>Flow_1g6tg2f</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="Flow_1g6tg2f" sourceRef="Activity_1cv37lm" targetRef="Gateway_0y4pu3c" />
    <bpmn:exclusiveGateway id="Gateway_0y4pu3c">
      <bpmn:incoming>Flow_1g6tg2f</bpmn:incoming>
      <bpmn:outgoing>Flow_1o8fmfm</bpmn:outgoing>
      <bpmn:outgoing>Flow_15qhnaj</bpmn:outgoing>
      <bpmn:outgoing>Flow_09az7w8</bpmn:outgoing>
      <bpmn:outgoing>Flow_1abbc0o</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o8fmfm" name="retry" sourceRef="Gateway_0y4pu3c" targetRef="rpa-bot-process">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('action') == 'RETRY')</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15qhnaj" sourceRef="Gateway_0y4pu3c" targetRef="Activity_12864yi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('action') == 'SS4')</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09az7w8" sourceRef="Gateway_0y4pu3c" targetRef="Activity_0f6997g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('action') == 'SALESFORCE_CASE')</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1abbc0o" sourceRef="Gateway_0y4pu3c" targetRef="Activity_1bq7f0e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('action') == 'CAMUNDA_INCIDENT')</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0cuopcu" name="ss4" sourceRef="Gateway_07xmb1r" targetRef="Activity_12864yi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${disposition == 'ss4'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:association id="Association_008whni" associationDirection="None" sourceRef="rpa-bot-process" targetRef="TextAnnotation_0lubtdm" />
    <bpmn:textAnnotation id="TextAnnotation_0lubtdm">
      <bpmn:text>1 - queues bot work
2 - awaits bot completion
3 - uploads relevant docs</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1ngjrnp">
      <bpmn:text>Docs checked:
1. Assigned IRS EIN
2. Form SS4 - IRS Complete</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1uhko1z" associationDirection="None" sourceRef="Activity_04au34l" targetRef="TextAnnotation_1ngjrnp" />
    <bpmn:textAnnotation id="TextAnnotation_05i97p5">
      <bpmn:text>manual filing</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0eylu7m" associationDirection="None" sourceRef="ein-salesforce-activity" targetRef="TextAnnotation_05i97p5" />
    <bpmn:textAnnotation id="TextAnnotation_1onbeih">
      <bpmn:text>note to engineer: resolve root cause then move token back to "rpa-bot-process"</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_05bhr2d" associationDirection="None" sourceRef="Activity_1bq7f0e" targetRef="TextAnnotation_1onbeih" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ein-obtainment-process-v3">
      <bpmndi:BPMNShape id="BPMNShape_0vhrdkq" bpmnElement="Event_0yrtavm">
        <dc:Bounds x="152" y="462" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1mlta2z" bpmnElement="rpa-bot-process">
        <dc:Bounds x="780" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1m77m36_di" bpmnElement="Gateway_1m77m36" isMarkerVisible="true">
        <dc:Bounds x="585" y="455" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="648" y="470" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0tuablm" bpmnElement="Gateway_1sf000n" isMarkerVisible="true">
        <dc:Bounds x="935" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0oosd0i" bpmnElement="Activity_12864yi">
        <dc:Bounds x="1620" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zu2h7k" bpmnElement="ein-salesforce-activity">
        <dc:Bounds x="1490" y="840" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0t6eboj" bpmnElement="validation-error-task">
        <dc:Bounds x="1340" y="840" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xkuubt" bpmnElement="ein-obtained-gateway" isMarkerVisible="true">
        <dc:Bounds x="2445" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0t9mlug" bpmnElement="ein-document-generation-process">
        <dc:Bounds x="2800" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c5gp5u" bpmnElement="Gateway_0b25qac" isMarkerVisible="true">
        <dc:Bounds x="2975" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d7g7mq" bpmnElement="print">
        <dc:Bounds x="3130" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_098nnvr" bpmnElement="Event_0kwd9t4">
        <dc:Bounds x="3342" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yqchtf_di" bpmnElement="Event_0yqchtf">
        <dc:Bounds x="2982" y="542" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_133v633" bpmnElement="store_ein">
        <dc:Bounds x="2000" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_customer_instructions" bpmnElement="customer-instructions-task">
        <dc:Bounds x="2000" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bdxxyy" bpmnElement="prepare-bypass-message">
        <dc:Bounds x="780" y="840" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ujqywn" bpmnElement="order-status-update-sent-to-customer">
        <dc:Bounds x="2950" y="383" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12pmai4" bpmnElement="validate-rpa-ein-docs-task">
        <dc:Bounds x="1830" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qw3023" bpmnElement="Activity_04au34l">
        <dc:Bounds x="2040" y="840" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1uzj7in" bpmnElement="validate-ein-number-task">
        <dc:Bounds x="2420" y="840" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qcuia0_di" bpmnElement="Gateway_1qcuia0" isMarkerVisible="true">
        <dc:Bounds x="1645" y="855" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1627" y="912" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13avu5t_di" bpmnElement="Activity_13avu5t">
        <dc:Bounds x="270" y="440" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1340jbh" bpmnElement="order-status-update-filing-in-progress">
        <dc:Bounds x="430" y="440" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1y1h6cc_di" bpmnElement="Activity_1y1h6cc">
        <dc:Bounds x="2620" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fgxvsu" bpmnElement="Activity_1juy8v9">
        <dc:Bounds x="2150" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0o2ik4g_di" bpmnElement="Event_0o2ik4g">
        <dc:Bounds x="2292" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13zxj2h_di" bpmnElement="Activity_13zxj2h">
        <dc:Bounds x="1870" y="570" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0500rd1_di" bpmnElement="Gateway_0500rd1" isMarkerVisible="true">
        <dc:Bounds x="1785" y="485" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1772" y="455" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bq7f0e_di" bpmnElement="Activity_1bq7f0e">
        <dc:Bounds x="1040" y="580" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06hazyv" bpmnElement="Activity_0f6997g">
        <dc:Bounds x="1200" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0d59nbh" bpmnElement="Gateway_07xmb1r" isMarkerVisible="true">
        <dc:Bounds x="1345" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1344" y="321" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1cv37lm_di" bpmnElement="Activity_1cv37lm">
        <dc:Bounds x="910" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0y4pu3c_di" bpmnElement="Gateway_0y4pu3c" isMarkerVisible="true">
        <dc:Bounds x="1065" y="465" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0lubtdm_di" bpmnElement="TextAnnotation_0lubtdm">
        <dc:Bounds x="840" y="160" width="170" height="59" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1ngjrnp_di" bpmnElement="TextAnnotation_1ngjrnp">
        <dc:Bounds x="2050" y="980" width="190" height="70" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_05i97p5_di" bpmnElement="TextAnnotation_05i97p5">
        <dc:Bounds x="1550" y="980" width="100" height="30" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1onbeih_di" bpmnElement="TextAnnotation_1onbeih">
        <dc:Bounds x="1170" y="680" width="170" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_16lgro2" bpmnElement="Flow_0v3pt9f">
        <di:waypoint x="188" y="480" />
        <di:waypoint x="270" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jiai5g_di" bpmnElement="Flow_1jiai5g">
        <di:waypoint x="610" y="455" />
        <di:waypoint x="610" y="290" />
        <di:waypoint x="780" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="539" y="256" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oemjdh_di" bpmnElement="Flow_1oemjdh">
        <di:waypoint x="880" y="290" />
        <di:waypoint x="935" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yhrrs0_di" bpmnElement="Flow_1yhrrs0">
        <di:waypoint x="610" y="505" />
        <di:waypoint x="610" y="880" />
        <di:waypoint x="780" y="880" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="497" y="604" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_038ulbn_di" bpmnElement="Flow_038ulbn">
        <di:waypoint x="985" y="290" />
        <di:waypoint x="1830" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1416" y="256" width="40" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xiiwb6_di" bpmnElement="Flow_1xiiwb6">
        <di:waypoint x="960" y="315" />
        <di:waypoint x="960" y="450" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="975" y="366" width="30" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ydl00w_di" bpmnElement="Flow_0ydl00w">
        <di:waypoint x="1720" y="510" />
        <di:waypoint x="1785" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a9kvm4_di" bpmnElement="Flow_1a9kvm4">
        <di:waypoint x="1440" y="880" />
        <di:waypoint x="1490" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kb48ih_di" bpmnElement="Flow_0kb48ih">
        <di:waypoint x="2100" y="290" />
        <di:waypoint x="2445" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1782lnm" bpmnElement="Flow_0xzlu0f">
        <di:waypoint x="2900" y="290" />
        <di:waypoint x="2975" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0e5rsr0" bpmnElement="Flow_0iypw8b">
        <di:waypoint x="3025" y="290" />
        <di:waypoint x="3130" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3050" y="272" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1c9ipj3" bpmnElement="Flow_1l9nvud">
        <di:waypoint x="3000" y="315" />
        <di:waypoint x="3000" y="383" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3005" y="339" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ss0e0d" bpmnElement="Flow_04hs1pv">
        <di:waypoint x="3230" y="290" />
        <di:waypoint x="3342" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15etdon_di" bpmnElement="Flow_15etdon">
        <di:waypoint x="880" y="880" />
        <di:waypoint x="1340" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1149l55_di" bpmnElement="Flow_1149l55">
        <di:waypoint x="3000" y="463" />
        <di:waypoint x="3000" y="542" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00e28wo_di" bpmnElement="Flow_00e28wo">
        <di:waypoint x="1930" y="290" />
        <di:waypoint x="2000" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r0w9hr_di" bpmnElement="Flow_0r0w9hr">
        <di:waypoint x="2140" y="880" />
        <di:waypoint x="2420" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hvwvkf_di" bpmnElement="Flow_1hvwvkf">
        <di:waypoint x="1590" y="880" />
        <di:waypoint x="1645" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11y2hu0_di" bpmnElement="Flow_11y2hu0">
        <di:waypoint x="1695" y="880" />
        <di:waypoint x="2040" y="880" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1835" y="905" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03cm7m3_di" bpmnElement="Flow_03cm7m3">
        <di:waypoint x="370" y="480" />
        <di:waypoint x="430" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15fmx1k_di" bpmnElement="Flow_15fmx1k">
        <di:waypoint x="530" y="480" />
        <di:waypoint x="585" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tjpkpq_di" bpmnElement="Flow_1tjpkpq">
        <di:waypoint x="2495" y="290" />
        <di:waypoint x="2620" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xsfmjr_di" bpmnElement="Flow_0xsfmjr">
        <di:waypoint x="2720" y="290" />
        <di:waypoint x="2800" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c634sq_di" bpmnElement="Flow_1c634sq">
        <di:waypoint x="2100" y="510" />
        <di:waypoint x="2150" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12nsn6t_di" bpmnElement="Flow_12nsn6t">
        <di:waypoint x="2250" y="510" />
        <di:waypoint x="2292" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0earvpt_di" bpmnElement="Flow_0earvpt">
        <di:waypoint x="1970" y="610" />
        <di:waypoint x="2050" y="610" />
        <di:waypoint x="2050" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01w0y0z_di" bpmnElement="Flow_01w0y0z">
        <di:waypoint x="1670" y="855" />
        <di:waypoint x="1670" y="550" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1682" y="680" width="75" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nuxn28_di" bpmnElement="Flow_1nuxn28">
        <di:waypoint x="1835" y="510" />
        <di:waypoint x="2000" y="510" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1913" y="492" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08o3vw4_di" bpmnElement="Flow_08o3vw4">
        <di:waypoint x="1810" y="535" />
        <di:waypoint x="1810" y="610" />
        <di:waypoint x="1870" y="610" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1816" y="573" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_033zf84_di" bpmnElement="Flow_033zf84">
        <di:waypoint x="2470" y="840" />
        <di:waypoint x="2470" y="315" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1smmoru_di" bpmnElement="Flow_1smmoru">
        <di:waypoint x="1300" y="370" />
        <di:waypoint x="1345" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fvi2bv_di" bpmnElement="Flow_0fvi2bv">
        <di:waypoint x="1395" y="370" />
        <di:waypoint x="1880" y="370" />
        <di:waypoint x="1880" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1343" y="430" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xe4ydm_di" bpmnElement="Flow_1xe4ydm">
        <di:waypoint x="1395" y="370" />
        <di:waypoint x="1730" y="370" />
        <di:waypoint x="1730" y="80" />
        <di:waypoint x="830" y="80" />
        <di:waypoint x="830" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1739" y="187" width="81" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g6tg2f_di" bpmnElement="Flow_1g6tg2f">
        <di:waypoint x="1010" y="490" />
        <di:waypoint x="1065" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o8fmfm_di" bpmnElement="Flow_1o8fmfm">
        <di:waypoint x="1090" y="465" />
        <di:waypoint x="1090" y="410" />
        <di:waypoint x="830" y="410" />
        <di:waypoint x="830" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1066" y="383" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15qhnaj_di" bpmnElement="Flow_15qhnaj">
        <di:waypoint x="1115" y="490" />
        <di:waypoint x="1578" y="490" />
        <di:waypoint x="1578" y="510" />
        <di:waypoint x="1620" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09az7w8_di" bpmnElement="Flow_09az7w8">
        <di:waypoint x="1105" y="480" />
        <di:waypoint x="1250" y="480" />
        <di:waypoint x="1250" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1abbc0o_di" bpmnElement="Flow_1abbc0o">
        <di:waypoint x="1090" y="515" />
        <di:waypoint x="1090" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cuopcu_di" bpmnElement="Flow_0cuopcu">
        <di:waypoint x="1370" y="395" />
        <di:waypoint x="1370" y="430" />
        <di:waypoint x="1670" y="430" />
        <di:waypoint x="1670" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1511" y="412" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_008whni_di" bpmnElement="Association_008whni">
        <di:waypoint x="875" y="252" />
        <di:waypoint x="913" y="219" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1uhko1z_di" bpmnElement="Association_1uhko1z">
        <di:waypoint x="2113" y="920" />
        <di:waypoint x="2145" y="975" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0eylu7m_di" bpmnElement="Association_0eylu7m">
        <di:waypoint x="1561" y="920" />
        <di:waypoint x="1592" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_05bhr2d_di" bpmnElement="Association_05bhr2d">
        <di:waypoint x="1140" y="649" />
        <di:waypoint x="1194" y="680" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmndi:BPMNDiagram id="BPMNDiagram_0fiqilp">
    <bpmndi:BPMNPlane id="BPMNPlane_186jy8d" bpmnElement="Activity_13avu5t">
      <bpmndi:BPMNShape id="BPMNShape_0t8yb0l" bpmnElement="await_related_orders_start_event">
        <dc:Bounds x="152" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1a91l07" bpmnElement="Event_0qhcmj2">
        <dc:Bounds x="402" y="454" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12nsij6" bpmnElement="Gateway_1ptztgi" isMarkerVisible="true">
        <dc:Bounds x="395" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1e9zyy8" bpmnElement="salesforce-activity-await-related-order">
        <dc:Bounds x="860" y="189" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1q56ocr" bpmnElement="Event_1b3hsrd">
        <dc:Bounds x="762" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vbgf1b" bpmnElement="Gateway_010wpa1">
        <dc:Bounds x="659" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hj7yl6" bpmnElement="Gateway_0lxokhw" isMarkerVisible="true">
        <dc:Bounds x="1009" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="626" y="195" width="56" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gic1it" bpmnElement="Event_1db64vk">
        <dc:Bounds x="1156" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jzqv5w" bpmnElement="Event_1kzxy38">
        <dc:Bounds x="1156" y="211" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1opk6jd_di" bpmnElement="evaluate_related_orders_condition">
        <dc:Bounds x="236" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1x25idp_di" bpmnElement="reevaluate_related_orders_condition">
        <dc:Bounds x="860" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_188jzsi" bpmnElement="order-status-update-waiting-formation">
        <dc:Bounds x="510" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0hakrs3_di" bpmnElement="TextAnnotation_0hakrs3">
        <dc:Bounds x="1200" y="130" width="100" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_17wndfe_di" bpmnElement="TextAnnotation_17wndfe">
        <dc:Bounds x="1200" y="432" width="100" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0orqk92_di" bpmnElement="TextAnnotation_0orqk92">
        <dc:Bounds x="320" y="130" width="100" height="70" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1cwlsec_di" bpmnElement="TextAnnotation_1cwlsec">
        <dc:Bounds x="960" y="80" width="100" height="70" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0e9628f_di" bpmnElement="TextAnnotation_0e9628f">
        <dc:Bounds x="650" y="410" width="100" height="40" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1jmublo" bpmnElement="Flow_0o8uyti">
        <di:waypoint x="188" y="300" />
        <di:waypoint x="236" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0jmfexd" bpmnElement="Flow_0wtnbgu">
        <di:waypoint x="420" y="325" />
        <di:waypoint x="420" y="454" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="327" y="373" width="85" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lzkqc3_di" bpmnElement="Flow_0lzkqc3">
        <di:waypoint x="336" y="300" />
        <di:waypoint x="395" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0qu6fha" bpmnElement="Flow_00kwiic">
        <di:waypoint x="445" y="300" />
        <di:waypoint x="510" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0r3nwmc" bpmnElement="Flow_0rm2jj3">
        <di:waypoint x="684" y="275" />
        <di:waypoint x="684" y="229" />
        <di:waypoint x="860" y="229" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01w1he2" bpmnElement="Flow_0hvp1z0">
        <di:waypoint x="960" y="229" />
        <di:waypoint x="1156" y="229" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0bi5soz" bpmnElement="Flow_07sattl">
        <di:waypoint x="684" y="325" />
        <di:waypoint x="684" y="370" />
        <di:waypoint x="762" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0jg9y8x" bpmnElement="Flow_1n2dljw">
        <di:waypoint x="1034" y="395" />
        <di:waypoint x="1034" y="460" />
        <di:waypoint x="780" y="460" />
        <di:waypoint x="780" y="388" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="878" y="476" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_18r09fo" bpmnElement="Flow_1b4mxng">
        <di:waypoint x="798" y="370" />
        <di:waypoint x="860" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nxldwm_di" bpmnElement="Flow_0nxldwm">
        <di:waypoint x="960" y="370" />
        <di:waypoint x="1009" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1buyeqn" bpmnElement="Flow_02jt6dc">
        <di:waypoint x="1059" y="370" />
        <di:waypoint x="1156" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1083" y="380" width="47" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jfdi3e_di" bpmnElement="Flow_0jfdi3e">
        <di:waypoint x="610" y="300" />
        <di:waypoint x="659" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0ku2e07_di" bpmnElement="Association_0ku2e07">
        <di:waypoint x="1186" y="216" />
        <di:waypoint x="1214" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0optutv_di" bpmnElement="Association_0optutv">
        <di:waypoint x="1187" y="383" />
        <di:waypoint x="1235" y="432" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_113dpfu_di" bpmnElement="Association_113dpfu">
        <di:waypoint x="308" y="260" />
        <di:waypoint x="340" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0x6kmrk_di" bpmnElement="Association_0x6kmrk">
        <di:waypoint x="940" y="189" />
        <di:waypoint x="969" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1z0aljn_di" bpmnElement="Association_1z0aljn">
        <di:waypoint x="765" y="380" />
        <di:waypoint x="722" y="410" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
