<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/2.0" id="Definitions_0gj8vmd" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="5.17.0">
  <decision id="Decision_0sa2opn" name="ein-bot-failures">
    <decisionTable id="DecisionTable_05kt1zk" hitPolicy="FIRST">
      <input id="Input_1" label="message" biodi:width="719">
        <inputExpression id="InputExpression_1" typeRef="string">
          <text>if( is defined(message)) then message else null</text>
        </inputExpression>
      </input>
      <output id="Output_1" label="action" name="action" typeRef="string" />
      <output id="OutputClause_16sr74m" name="delay" typeRef="boolean" />
      <rule id="DecisionRule_1328z6p">
        <inputEntry id="UnaryTests_0hr8g9v">
          <text>"We are unable to provide you with an EIN. - Reference Number 101 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1qjsieu">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_10vwxc5">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0tpn7ao">
        <inputEntry id="UnaryTests_1pezyj3">
          <text>"You Have Exceeded the Number of EINs You May Receive Per Day - Reference Number 114 - EIN Application may have been submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1y7zpy9">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0boojlw">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_19361lx">
        <inputEntry id="UnaryTests_0gop377">
          <text>"Order validation errors: Tax Contact Last Name is required.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1wgo4lu">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1ujezia">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_07njruf">
        <inputEntry id="UnaryTests_01lrnrv">
          <text>"You Have Exceeded the Number of EINs You May Receive Per Day - Reference Number 114 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_17n0jep">
          <text>"RETRY"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0695p6d">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1o6qfot">
        <inputEntry id="UnaryTests_1ctto32">
          <text>"Access Denied received from IRS after authenticating with SSN - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0x0i8bu">
          <text>"RETRY"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0y5u7q8">
          <text>false</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_054tk0m">
        <inputEntry id="UnaryTests_1s2wdi4">
          <text>"Technical Difficulties - Reference Number 109 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0henmdo">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1kphvdi">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1l57z1i">
        <inputEntry id="UnaryTests_0te8faa">
          <text>"We are unable to provide you with an EIN. - Reference Number 115 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0v80ago">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1ne7h21">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0yrxl9h">
        <inputEntry id="UnaryTests_097awfl">
          <text>"Order validation errors: Business Location Address line1 must not exceed 35 characters length.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_07zc1t9">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0idp7kp">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_186609y">
        <inputEntry id="UnaryTests_0sw13mq">
          <text>"An application error occurred. - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_063fgrm">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_18hj2ry">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0ugjm58">
        <inputEntry id="UnaryTests_1rlpn5q">
          <text>"We are unable to provide you with an EIN. - Reference Number 114 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0xs2xdz">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1lpy3e8">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1g08yp8">
        <inputEntry id="UnaryTests_1fx0ptu">
          <text>"Authenticate Error(s) has occurred: The information you have entered does not match IRS records. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_17xwku2">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_05g8qb0">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1aukqej">
        <inputEntry id="UnaryTests_0iavcz8">
          <text>"Technical Difficulties - Reference Number 110 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0n7w4ko">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1d7qt2h">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0hy8w9h">
        <inputEntry id="UnaryTests_1cd4ec8">
          <text>"We are unable to provide you with an EIN. - Reference Number 105 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0eye33y">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_063vhic">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0i0g7qx">
        <inputEntry id="UnaryTests_19aw7jw">
          <text>"Technical Difficulties - Reference Number 111 - EIN Application was not submitted. Order may not be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1vpq54c">
          <text>"SS4"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1ysx63f">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1h4tbyg">
        <inputEntry id="UnaryTests_1sxjfsk">
          <text>"The information you have entered does not match IRS records. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1j69pb3">
          <text>"ORCO_FOR_INFO"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0qlr6oj">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_14zsue4">
        <inputEntry id="UnaryTests_103u1nu">
          <text>"IRS detected an error in the Address information - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0t8hcir">
          <text>"ORCO_FOR_INFO"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0lqvr2e">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1sg6qzd">
        <inputEntry id="UnaryTests_14w7ive">
          <text>"The order was canceled - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_157uqkb">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0a21b30">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0u016ei">
        <inputEntry id="UnaryTests_16gs2ja">
          <text>"Physical Location Addresses cannot be found in the IRS database. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1yhxfyk">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0i6hm75">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1upil5f">
        <inputEntry id="UnaryTests_1dsmjmm">
          <text>"Order validation errors: Business Location Phone is required. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1dhwg8p">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0sr3hrv">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1ff6id3">
        <inputEntry id="UnaryTests_1ivag1w">
          <text>"The order was canceled - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1oy4wzs">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_07vrovn">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1919277">
        <inputEntry id="UnaryTests_0k7du00">
          <text>"Order validation errors: Tax Contact Last Name is required.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0d437j2">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0w44tvn">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_10w58ff">
        <inputEntry id="UnaryTests_1bpty7j">
          <text>"Error(s) has occurred: The total number of all employees must be at least 1. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0xx5ii6">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_10dhqod">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0r1pdso">
        <inputEntry id="UnaryTests_1cjn972">
          <text>"Order validation errors: Company Business Activity must not exceed 50 characters length.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1sk3ypk">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_19pluls">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1jhid8r">
        <inputEntry id="UnaryTests_0dvzeag">
          <text>"Order validation errors: Tax Contact Trade Name must not exceed 35 characters length. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1qk5wbu">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0o844ez">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0hzhgff">
        <inputEntry id="UnaryTests_0j3ro8f">
          <text>"Order validation errors: Company Purpose Description is required. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0qhm6fi">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0j5470l">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1rlexx2">
        <inputEntry id="UnaryTests_1ymozfj">
          <text>"Order validation errors: Company Formation Date is required. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0jt1mij">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1b0yukz">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1ld8syn">
        <inputEntry id="UnaryTests_1f9v01v">
          <text>"Error(s) has occurred: Pay date must not be before start date or greater than current date plus 1 year. - EIN Application was not submitted. Order can be retried.”</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0nza1hx">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0owumc0">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_175c9ig">
        <inputEntry id="UnaryTests_0glao0p">
          <text>"Order validation errors: Tax Contact Phone is required.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1i5ausk">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1uc6ytn">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0lxrlyf">
        <inputEntry id="UnaryTests_1rwl3rs">
          <text>"Order validation errors: Company Business Activity is required.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1hn22ic">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1ln24e6">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0nenil2">
        <inputEntry id="UnaryTests_0t5gts5">
          <text>"Order validation errors: Tax Contact SSN format is incorrect. Correct formats are: *********** or *********. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0tgkjcn">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_17imy6k">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0zbb44h">
        <inputEntry id="UnaryTests_1k62upv">
          <text>"Order validation errors: Business Location County Name is required.. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0a9j1c4">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0af4n6g">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1n55phm">
        <inputEntry id="UnaryTests_1cjbjpj">
          <text>"BusinessError-Details Error(s) has occurred: Enter letters and spaces. The only special characters allowed are - or &amp;. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_09714yv">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0bsvgga">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_05bijr0">
        <inputEntry id="UnaryTests_0narufq">
          <text>"BusinessError-Details Error(s) has occurred: Enter only letters and spaces. No special characters or numbers are allowed. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0hn9m7g">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1r6lch7">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0adlhxr">
        <inputEntry id="UnaryTests_1wlvrbh">
          <text>"BusinessError-Address Error(s) has occurred: Please enter the phone number. - EIN Application was not submitted. Order can be retried."</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1etkku2">
          <text>"SALESFORCE_CASE"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1mit1gp">
          <text></text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0hq8k17">
        <inputEntry id="UnaryTests_0a8n3uv">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0m0buju">
          <text>"CAMUNDA_INCIDENT"</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_1mujf91">
          <text></text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
  <dmndi:DMNDI>
    <dmndi:DMNDiagram>
      <dmndi:DMNShape dmnElementRef="Decision_0sa2opn">
        <dc:Bounds height="80" width="180" x="380" y="260" />
      </dmndi:DMNShape>
    </dmndi:DMNDiagram>
  </dmndi:DMNDI>
</definitions>
