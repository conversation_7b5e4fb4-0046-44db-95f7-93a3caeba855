package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.Company
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.order.GetCustomerOrdersResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderDto
import com.legalzoom.api.model.order.OrderGroupDto
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.order.ProductConfigurationDto
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.rpa.AddQueueItemRequest
import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.order.OrdersCustomerApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.revv.RevvEsignatureService
import com.legalzoom.api.revv.model.CreateDocumentResponse
import com.legalzoom.api.revv.model.DocumentFieldsResponse
import com.legalzoom.api.revv.model.DocumentPdfFieldResponse
import com.legalzoom.api.revv.model.TemplateResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.domain.Constants.APP_501C3_PROCESS
import com.legalzoom.fulfillment.domain.Constants.RPA_BOT_PROCESS
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.orco.entity.enumeration.OrcoStatus
import com.legalzoom.fulfillment.orco.entity.enumeration.OrcoType
import com.legalzoom.fulfillment.orco.entity.enumeration.ResolutionType
import com.legalzoom.fulfillment.orco.service.OrcoService
import com.legalzoom.fulfillment.orco.service.model.OrcoResponse
import com.legalzoom.fulfillment.orco.service.model.ReasonResponse
import com.legalzoom.fulfillment.orco.service.model.ResolutionResponse
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.data.OrderCancelMessage
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.ValidationResult
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.enumeration.BusinessType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.DocumentType.Form1023EZ
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.App501c3
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FieldUpdaterCoreService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.App501c3AwaitingFormation
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.App501c3FilingInProgress
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus.App501c3ReadyForDownload
import com.legalzoom.fulfillment.service.service.RevvService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.orderStatusNotifications.OrderStatusNotificationSender
import com.legalzoom.fulfillment.service.service.orderStatusNotifications.OrderStatusNotificationVariables
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataPayload
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.testing.Await
import com.legalzoom.fulfillment.testing.SpringBootProcessTest
import com.legalzoom.fulfillment.workflow.bpmn.helpers.RandomValueGenerators
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import com.legalzoom.fulfillment.workflow.dmn.JsonFileArgumentProvider
import com.legalzoom.fulfillment.workflow.service.AddQueuesItemService
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.revv.RevvDocumentGenerationService
import com.legalzoom.fulfillment.workflow.service.revv.RevvFieldsRequestBuilder
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.slf4j.LoggerFactory
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.http.MediaType
import org.springframework.kafka.test.context.EmbeddedKafka
import reactor.core.publisher.Mono
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID

@SpringBootProcessTest
@Deployment(resources = ["bpmn/501c3-filing.bpmn", "bpmn/rpa-bot.bpmn", "bpmn/salesforce.bpmn", "bpmn/doc-generation.bpmn"])
@EmbeddedKafka
class Application501C3WorkflowTest(
    private val processEngine: ProcessEngine,
    private val runtimeService: RuntimeService,
    private val objectMapper: ObjectMapper,
    private val salesforceHelper: SalesforceHelper,
    private val orderCancellationService: OrderCancellationService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @MockkBean
    private lateinit var addQueuesItemService: AddQueuesItemService

    @MockkBean
    private lateinit var salesforceApiService: SalesforceApiService

    @MockkBean
    private lateinit var s3Service: S3Service

    @MockkBean
    private lateinit var documentService: DocumentService

    @MockkBean
    private lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    private lateinit var orderStatusNotificationSender: OrderStatusNotificationSender

    @MockkBean
    private lateinit var orderStatusNotificationVariables: OrderStatusNotificationVariables

    @MockkBean
    lateinit var ordersApiService: OrdersApiService

    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var fieldUpdaterCoreService: FieldUpdaterCoreService

    @MockkBean
    private lateinit var revvEsignatureService: RevvEsignatureService

    @MockkBean
    private lateinit var revvService: RevvService

    @MockkBean
    lateinit var revvDocumentGenerationService: RevvDocumentGenerationService

    @MockkBean
    lateinit var requestBuilder: RevvFieldsRequestBuilder

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    lateinit var ordersCustomerApi: OrdersCustomerApi

    @MockkBean
    lateinit var orcoService: OrcoService

    @MockkBean
    private lateinit var fulfillmentEventService: FulfillmentEventService

    private var capturedAddQueueItemRequest = mutableListOf<AddQueueItemRequest>()

    private var capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
    private var capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
    private var capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()
    private var capturedFulfillmentEvents = mutableListOf<FulfillmentEvent>()

    private lateinit var testPaygovTrackingId: String
    private lateinit var testAgencyTrackingId: String
    private lateinit var testSubmissionDate1023EZ: String

    private fun mockFulfillmentEventService() {
        capturedFulfillmentEvents = mutableListOf()

        coEvery {
            fulfillmentEventService.send(capture(capturedFulfillmentEvents))
        } returns Unit
    }

    fun mockOrcoService() {
        every {
            orcoService.updateReason(any(), any(), any())
        } returns Unit

        every {
            orcoService.getOpenOrco(any())
        } returns null

        every {
            orcoService.search(any(), any(), any(), any())
        } returns
            listOf(
                OrcoResponse(
                    id = UUID.randomUUID(),
                    type = OrcoType.SELF_SERVE,
                    status = OrcoStatus.OPEN,
                    references = emptyList(),
                    createdBy = null,
                    createdDate = null,
                    modifiedBy = null,
                    modifiedDate = null,
                    reasons =
                        listOf(
                            simpleReasonResponse(),
                        ),
                ),
            )
    }

    private fun simpleReasonResponse() =
        ReasonResponse(
            id = UUID.randomUUID(),
            resolution = simpleResolutionResponse(),
            status = OrcoStatus.OPEN,
            categoryId = "test",
            note = "test",
            createdBy = null,
            createdDate = null,
            modifiedBy = null,
            modifiedDate = null,
        )

    private fun simpleResolutionResponse() =
        ResolutionResponse(
            type = ResolutionType.MANUAL,
            categoryId = UUID.randomUUID(),
            id = UUID.randomUUID(),
            createdBy = "NGX",
            modifiedBy = "NGX",
            createdDate = Instant.now(),
            modifiedDate = Instant.now(),
        )

    private fun mockFieldUpdaterCoreService() {
        every {
            fieldUpdaterCoreService.storeFieldAnswer(any(), any(), any(), any())
        } just runs
    }

    private fun mockAddQueueItem(variables: Variables) {
        capturedAddQueueItemRequest.clear()
        val response =
            objectMapper.readValue<QueueItemDto>(
                ClassPathResource("rpa_response.json", javaClass).file.readText()
                    .replace("\"orderId\": \"1234\"", "\"orderId\": \"${variables.orderId}\""),
            )
        every {
            addQueuesItemService.addQueuesItem(any(), any(), any(), capture(capturedAddQueueItemRequest), any())
        } returns response
    }

    private fun mockSalesforceApi() {
        capturedSalesForceRequests = mutableListOf()
        capturedLedgerNoteRequests = mutableListOf()
        capturedSalesForceUpdateCaseRequests = mutableListOf()

        every {
            salesforceApiService.createCase(capture(capturedSalesForceRequests))
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(capture(capturedLedgerNoteRequests))
        } returns AddLedgerNoteResponse("test", "test", emptyList())

        every {
            salesforceApiService.updateCase(capture(capturedSalesForceUpdateCaseRequests))
        } returns SalesforceCaseUpdateResponse("test", "test", "test")
    }

    private fun mockS3Service() {
        val testFile = ClassPathResource("test_file.png", javaClass).file
        every {
            s3Service.getDocument(any())
        } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "test_file.png",
            )
    }

    private fun mockDocumentService() {
        every {
            documentService.uploadDocument(any(), any(), any(), any(), any())
        } returns
            DocumentResponse().documentId("Test").documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")
        val documents = objectMapper.readValue<List<Document>>(ClassPathResource("sa501c3_documents.json", javaClass).file)
        every {
            documentService.findDocumentsBy(any(), any(), any(), accountId = any())
        } returns documents
    }

    private fun mockOrdersApiService(variables: Variables) {
        val orderResponse: GetOrderResponse =
            if (variables.isAttachedOrder!!) {
                // "processingOrderId": *********,
                objectMapper.readValue<GetOrderResponse>(
                    ClassPathResource("np_ein_501c3_order_response.json", javaClass).file.readText()
                        .replace(
                            // main order item
                            "\"processingOrderId\": *********",
                            "\"processingOrderId\": ${variables.parentProcessingOrderId}",
                        )
                        .replace(
                            // 501c3 order item
                            "\"processingOrderId\": *********",
                            "\"processingOrderId\": ${variables.processingOrderId}",
                        ),
                )
            } else {
                objectMapper.readValue<GetOrderResponse>(
                    ClassPathResource("standalone_501c3_order_response.json", javaClass).file.readText()
                        .replace("\"orderId\": 35186844", "\"orderId\": ${variables.orderId}")
                        .replace("\"customerId\": 17832777,", "\"customerId\": ${variables.customerId},")
                        .replace("\"processingOrderId\": 514830219,", "\"processingOrderId\": ${variables.processingOrderId},"),
                )
            }
        every {
            ordersApiService.getOrders(variables.orderId, any(), any(), any(), any(), any())
        } returns orderResponse
    }

    private fun mockFeatureToggleService() {
        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isDSDDocGenEnabled(any(), any(), any())
        } returns false
    }

    private fun mockProcessingOrdersApi(
        variables: Variables,
        blockedScenario: Boolean = false,
    ) {
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(), any(), any(), any(), any(), any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())

        if (variables.isAttachedOrder == true) {
            val processingOrderDto = ProcessingOrderDto()

            if (blockedScenario) {
                processingOrderDto.processingStatusId =
                    ProcessingOrderStatus.fromProcessIdAndDescription(
                        variables.parentProcessId!!,
                        "Sent To SOS",
                    )!!.processingStatusId
            } else {
                processingOrderDto.processingStatusId =
                    ProcessingOrderStatus.fromProcessIdAndDescription(
                        variables.parentProcessId!!,
                        "Sent to Customer",
                    )!!.processingStatusId
            }
            processingOrderDto.processId = variables.parentProcessId!!

            val getProcessingOrderResponse = GetProcessingOrderResponse()
            getProcessingOrderResponse.processingOrder = processingOrderDto

            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
            } returns Mono.just(getProcessingOrderResponse)
        } else {
            val processingOrderDto = ProcessingOrderDto()
            processingOrderDto.processingStatusId = ProcessingOrderStatus.App501c3NotYetStarted.processingStatusId
            processingOrderDto.processId = App501c3.processId
            val getProcessingOrderResponse = GetProcessingOrderResponse()
            getProcessingOrderResponse.processingOrder = processingOrderDto

            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(variables.processingOrderId!!, any(), any(), any())
            } returns Mono.just(getProcessingOrderResponse)
        }
    }

    private fun mockOrderStatusNotificationVariables() {
        val variables: Map<String, String> =
            mapOf(
                "firstName" to "John",
                "product" to "SomeProduct",
                "orderId" to "123",
                "paygovTrackingId" to "my paygovTrackingId",
                "agencyTrackingId" to "my agencyTrackingId",
                "submissionDate1023EZ" to "my submissionDate1023EZ",
            )

        every {
            orderStatusNotificationVariables.getVariables(
                any(),
                // You might need to provide specific instances of ActivityFeedVariables here
                any(),
            )
        } returns variables
    }

    private fun mockRevvCalls(
        templates: MutableList<RevvDocumentType> =
            mutableListOf(
                RevvDocumentType.FORM_501C3_FINAL_LETTER,
                RevvDocumentType.FORM_3500A,
            ),
    ) {
        var json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_get_all_templates.json").file
        val templateResponse = JsonFileArgumentProvider.objectMapper.readValue<TemplateResponse>(json)
        coEvery {
            revvEsignatureService.listAllTemplates(any())
        } returns templateResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_create_document.json").file
        val createDocumentResponse = JsonFileArgumentProvider.objectMapper.readValue<CreateDocumentResponse>(json)
        coEvery {
            revvEsignatureService.createDocumentUsingTemplate(any(), any())
        } returns createDocumentResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_save_document_fields.json").file
        val saveDocumentFiledsResponse = JsonFileArgumentProvider.objectMapper.readValue<DocumentFieldsResponse>(json)
        coEvery {
            revvEsignatureService.saveDocumentFileds(any(), any(), any())
        } returns saveDocumentFiledsResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_save_pdf_fields.json").file
        val savePdfFieldsResponse = JsonFileArgumentProvider.objectMapper.readValue<DocumentPdfFieldResponse>(json)
        coEvery {
            revvEsignatureService.savePDFFields(any(), any(), any())
        } returns savePdfFieldsResponse

        coEvery {
            revvEsignatureService.downloadPdfDocument(any(), any())
        } returns byteArrayOf()

        every {
            requestBuilder.getTemplates()
        } returns templates
        every {
            requestBuilder.getCustomerDocumentType()
        } returns
            mutableMapOf(
                RevvDocumentType.FORM_501C3_FINAL_LETTER to
                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                        App501c3.productName,
                        "Final Letter",
                    ),
                RevvDocumentType.FORM_3500A to
                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                        App501c3.productName,
                        "CA Form 3500A",
                    ),
            )
        every {
            requestBuilder.getMappingFields(any())
        } returns mutableListOf()
        every {
            revvDocumentGenerationService.uploadDocument(any(), any(), any(), any(), any(), any(), any(), any())
        } returns null
        every {
            revvDocumentGenerationService.getRevvRequestBuilder(any())
        } returns requestBuilder
        every {
            revvDocumentGenerationService.getRequestedDocumentTemplates(any())
        } returns templates

        every {
            revvService.sendforRevvDocAutomation(any())
        } returns "".toByteArray()
        every {
            revvService.getAllTemplates()
        } returns emptyList()
    }

    private fun mockQuestionnaireAnswerService(
        variables: Variables,
        ein: String? = null,
        businessType: String? = null,
    ) {
        val response = FakeAnswersPayload()
        response.ein = ein
        response.businessType = businessType ?: BusinessType.LLC.fieldType
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(
                processingOrderId = if (variables.isAttachedOrder!!) variables.parentProcessingOrderId!! else variables.processingOrderId!!,
                customerId = variables.customerId,
                answerSource = AnswerSource.AnswerBank,
            )
        } returns response
        every {
            questionnaireAnswerService.putPartialUpdate(any(), any())
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())

        val mockAnswer = mockk<FilingDataPayload>()
        every {
            mockAnswer.company
        } returns Company().apply { isProfessional = false }
        every {
            questionnaireAnswerService.getFilingData(any(), any(), any())
        } returns mockAnswer
    }

    private fun mockAnswerService(populated: Boolean = false) {
        if (populated) {
            every {
                questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
            } returns
                FakeAnswersPayload().apply {
                    paygovTrackingId = testPaygovTrackingId
                    agencyTrackingId = testAgencyTrackingId
                    submissionDate1023EZ = testSubmissionDate1023EZ
                }
        } else {
            every {
                questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
            } returns
                FakeAnswersPayload().apply {
                    paygovTrackingId = ""
                    agencyTrackingId = null
                    submissionDate1023EZ = ""
                }
        }
    }

    fun mockOrdersCustomerApi(
        variables: Variables,
        blockedScenario: Boolean = false,
    ) {
        if (blockedScenario) {
            val getCustomerOrdersResponse = GetCustomerOrdersResponse()
            getCustomerOrdersResponse.orderGroups = mutableListOf()
            val orderGroupDto = OrderGroupDto()
            orderGroupDto.accountId = variables.accountId
            val orderDto = OrderDto()
            orderDto.orderItems = mutableListOf()
            val orderItemDto = OrderItemDto()
            orderItemDto.processingOrder = com.legalzoom.api.model.order.ProcessingOrderDto()
            orderItemDto.orderId = 123
            orderItemDto.processingOrder!!.processId = ProductType.Amendment.processId
            orderItemDto.processingOrder!!.processingStatusId =
                ProcessingOrderStatus.AmendmentPreliminaryNameValidationComplete.processingStatusId
            orderItemDto.productConfiguration = ProductConfigurationDto()
            orderItemDto.productConfiguration!!.productTypeId = com.legalzoom.api.model.order.RelationshipType.NUMBER_2
            orderDto.orderItems!!.add(orderItemDto)
            orderGroupDto.orders = mutableListOf()
            orderGroupDto.orders!!.add(orderDto)
            getCustomerOrdersResponse.orderGroups!!.add(orderGroupDto)
            every {
                ordersCustomerApi.coreOrdersCustomerCustomerIdGet(any(), any(), any(), any(), any(), any(), any())
            } returns Mono.just(getCustomerOrdersResponse)
            return
        }
        every {
            ordersCustomerApi.coreOrdersCustomerCustomerIdGet(any(), any(), any(), any(), any(), any(), any())
        } returns Mono.just(GetCustomerOrdersResponse())
    }

    fun getIncident(processInstance: ProcessInstance) =
        runtimeService
            .createIncidentQuery()
            .processInstanceId(processInstance.id)
            .list().firstOrNull()

    private fun buildVariables(
        isAttached: Boolean = false,
        parentProcessIdVal: Int? = null,
    ): Variables {
        return variables {
            accountId = UUID.randomUUID()
            customerId = RandomValueGenerators.generateCustomerId()
            entityName = RandomValueGenerators.generateEntityName()
            expediteSpeed = "Standard"
            jurisdiction = "CA"
            orderId = RandomValueGenerators.generateOrderId()
            processId = App501c3.processId
            processingOrderId = RandomValueGenerators.generateProcessingOrderId()
            workOrderId = UUID.randomUUID()
            isAttachedOrder = isAttached
            if (isAttached) {
                parentProcessingOrderId = RandomValueGenerators.generateProcessingOrderId()
                parentProcessId = parentProcessIdVal ?: throw Exception("When IsAttached == true then parentProcessId must be provided")
            }
            conditionRecheckTimer = "PT1S" // waiting to fulfill - causes delay between rechecks to be minimal
        }
    }

    private fun start501c3ProcessInstance(variables: Variables = buildVariables()): ProcessInstance {
        val processInstance =
            runtimeService.startProcessInstanceByKey(
                APP_501C3_PROCESS,
                variables.processingOrderId.toString(),
                variables,
            )

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        return processInstance
    }

    private fun botSuccessVars(
        evidenceTransactionNumber: String? = null,
        message: String? = null,
    ) = variables {
        status = "Success"
        this.evidenceTransactionNumber = evidenceTransactionNumber
        this.message = message
        evidenceFilePath = "s3://rpa-storage-dev/Jobs_Data/501C3_FEDERAL/20240613115424/Proof_Of_Work/" +
            "PoW_20240613115424_20240613115722.pdf"
    }

    private fun botFailureVars(message: String? = null) =
        variables {
            status = "Failure"
            evidenceFilePath = "s3://rpa-storage-dev/Jobs_Data/501C3_FEDERAL/20240613115424/Proof_Of_Work/" +
                "PoW_20240613115424_20240613115722.pdf"
            this.message = message
        }

    private fun awaitThenSimulateRpaResponse(
        processInstance: ProcessInstance,
        variables: Variables,
    ) {
        var rpaProcessInstance: ProcessInstance? = null
        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted {
                rpaProcessInstance =
                    processEngine.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(RPA_BOT_PROCESS)
                        .superProcessInstanceId(processInstance.processInstanceId)
                        .active().singleResult()

                assertThat(rpaProcessInstance).isNotNull
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(rpaProcessInstance).isWaitingAt("rpa-task") }

        processEngine.runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceBusinessKey(processInstance.businessKey)
            .setVariables(variables)
            .correlate()
    }

    private fun assertDocUploaded(
        processId: Int,
        documentType: String,
    ) {
        var expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                // orderContext
                any(),
                expectedCustomerDocumentType,
                // resourceWithType
                any(),
                DocumentStatus.Active,
                availableForImmediateCustomerDownload = true,
            )
        }
    }

    private fun assertProcessingOrderStatus(
        processingOrderId: Int,
        status: Int,
    ) {
        verify(exactly = 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                status,
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    private fun assertEmailNotification(
        orderId: Int,
        processingOrderId: Int,
        capturedVariables: Map<String, String>,
    ) {
        verify(exactly = 1) {
            orderStatusNotificationSender.sendNotification(
                orderId.toString(),
                processingOrderId.toString(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
        assert(!capturedVariables["paygovTrackingId"].isNullOrEmpty())
        assert(!capturedVariables["submissionDate1023EZ"].isNullOrEmpty())
        assert(!capturedVariables["agencyTrackingId"].isNullOrEmpty())
    }

    fun setup(variables: Variables) {
        // the reason it's helpful to log the variables is that many of them are randomized.  So if you get failed tests
        // due to mocking issues it's confusing unless you know the variable values at play when the test started.
        val variablesLogString = StringBuilder()
        for ((key, value) in variables) {
            variablesLogString.append("Key: $key, Value: $value\n")
        }
        logger.info("Variables: \n$variablesLogString")

        AbstractAssertions.init(processEngine)
        clearAllMocks()
        testPaygovTrackingId = "abcdefgh"
        testAgencyTrackingId = "76742237663"
        testSubmissionDate1023EZ = LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a")) + " EDT"

        mockOrdersApiService(variables)
        mockAddQueueItem(variables)
        mockFeatureToggleService()
        mockSalesforceApi()
        mockS3Service()
        mockDocumentService()
        mockProcessingOrdersApi(variables, blockedScenario = false)
        mockOrdersCustomerApi(variables, blockedScenario = false)
        mockFieldUpdaterCoreService()
        mockRevvCalls()
        mockQuestionnaireAnswerService(variables)
        mockOrderStatusNotificationVariables()
        mockOrcoService()
        mockFulfillmentEventService()

        every {
            featureToggleService.isOrderStatusEmailNotificationsEnabled(any(), any(), any())
        } returns true
    }

    @ParameterizedTest
    @CsvSource(
        // standalone
        "false,,",
        // attached
        "true,20,",
    )
    fun testHappyPath(
        attached: Boolean,
        parentProcessIdVal: Int?,
    ) {
        val variables =
            buildVariables().apply {
                isAttachedOrder = attached
                parentProcessId = if (attached) parentProcessIdVal else null
                parentProcessingOrderId = if (attached) RandomValueGenerators.generateProcessingOrderId() else null
                orderStatusEmailNotificationsEnabled = true
            }
        setup(variables)

        val templateVariables = slot<Map<String, String>>()
        every {
            orderStatusNotificationSender.sendNotification(any(), any(), any(), any(), any(), any(), capture(templateVariables))
        } just runs

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(
            processInstance,
            botSuccessVars(
                evidenceTransactionNumber = testPaygovTrackingId,
                message = "$testAgencyTrackingId - $testSubmissionDate1023EZ",
            ),
        )

        mockProcessingOrdersApi(variables, blockedScenario = false)

        Await.await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        // verify expected docs uploaded
        assertDocUploaded(variables.processId!!, Form1023EZ.name)

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3ReadyForDownload.processingStatusId)
        assertEmailNotification(variables.orderId!!, variables.processingOrderId!!, templateVariables.captured)
    }

    @Test
    fun `RPA passed but did not obtain additional fields`() {
        var variables = buildVariables(isAttached = false)
        setup(variables)

        mockFieldUpdaterCoreService()

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botSuccessVars())

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("rpa-bot-process") }

        val incident =
            Await.await(Duration.of(1, ChronoUnit.MINUTES))
                .until({ getIncident(processInstance) }, { it != null })

        Assertions.assertThat(
            incident?.incidentMessage,
        ).contains("A paygovTrackingId number was not provided in evidenceTransactionNumber process variable")
    }

    @ParameterizedTest
    @CsvSource(
        // standalone
        "false,,",
        // attached
        "true,20,",
    )
    @Disabled
    fun `Failed to upload document after salesforce proceed`(
        attached: Boolean,
        parentProcessIdVal: Int?,
    ) {
        val variables =
            buildVariables().apply {
                isAttachedOrder = attached
                parentProcessId = if (attached) parentProcessIdVal else null
                parentProcessingOrderId = if (attached) RandomValueGenerators.generateProcessingOrderId() else null
            }
        setup(variables)
        mockAnswerService(populated = true)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botFailureVars("SystemError"))

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        every {
            documentService.findDocumentsBy(any(), any(), any(), accountId = any())
        } returns emptyList()

        capturedSalesForceRequests.clear()

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            variables =
                variables {
                    disposition = FulfillmentDisposition.Proceed.value
                },
        )

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "1023EZ document is missing.",
                            data = mapOf("Detail" to "1023EZ document is missing."),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        Assertions.assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.APP_501C3.toString())
        Assertions.assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        Assertions.assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        Assertions.assertThat(caseRequest.processId).isEqualTo(App501c3.processId.toString())

        mockDocumentService()

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            variables =
                variables {
                    disposition = FulfillmentDisposition.Proceed.value
                },
        )

        Await.await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3ReadyForDownload.processingStatusId)
    }

    @ParameterizedTest
    @CsvSource(
        // standalone
        "false,,",
        // attached
        "true,20,",
    )
    fun `Failed to enter 501c3 data after salesforce proceed`(
        attached: Boolean,
        parentProcessIdVal: Int?,
    ) {
        val variables =
            buildVariables().apply {
                isAttachedOrder = attached
                parentProcessId = if (attached) parentProcessIdVal else null
                parentProcessingOrderId = if (attached) RandomValueGenerators.generateProcessingOrderId() else null
            }
        setup(variables)
        mockAnswerService(populated = false)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botFailureVars("SystemError"))

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        capturedSalesForceRequests.clear()

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            variables =
                variables {
                    disposition = FulfillmentDisposition.Proceed.value
                },
        )

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "501c3 tracking IDs and/or submission date details were not added to Proofer.",
                            data =
                                mapOf(
                                    "Detail" to "Please enter and save the tracking IDs and/or submission date details, " +
                                        "then select \"Skip-I Have Manually Obtained 501c3\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        Assertions.assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.APP_501C3.toString())
        Assertions.assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        Assertions.assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        Assertions.assertThat(caseRequest.processId).isEqualTo(App501c3.processId.toString())

        mockAnswerService(populated = true)

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            variables =
                variables {
                    disposition = FulfillmentDisposition.Proceed.value
                },
        )

        Await.await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3ReadyForDownload.processingStatusId)
    }

    @ParameterizedTest
    @CsvSource(
        // standalone
        "false,,",
        // attached
        "true,20,",
    )
    fun `Failed to upload document and failed to enter 501c3 data after salesforce proceed`(
        attached: Boolean,
        parentProcessIdVal: Int?,
    ) {
        val variables =
            buildVariables().apply {
                isAttachedOrder = attached
                parentProcessId = if (attached) parentProcessIdVal else null
                parentProcessingOrderId = if (attached) RandomValueGenerators.generateProcessingOrderId() else null
            }
        setup(variables)
        mockAnswerService(populated = false)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botFailureVars("SystemError"))

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        every {
            documentService.findDocumentsBy(any(), any(), any(), accountId = any())
        } returns emptyList()

        capturedSalesForceRequests.clear()

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            variables =
                variables {
                    disposition = FulfillmentDisposition.Proceed.value
                },
        )

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "1023EZ document is missing.",
                            data = mapOf("Detail" to "1023EZ document is missing."),
                        ),
                        ValidationError(
                            "501c3 tracking IDs and/or submission date details were not added to Proofer.",
                            data =
                                mapOf(
                                    "Detail" to "Please enter and save the tracking IDs and/or submission date details, " +
                                        "then select \"Skip-I Have Manually Obtained 501c3\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        Assertions.assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.APP_501C3.toString())
        Assertions.assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        Assertions.assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        Assertions.assertThat(caseRequest.processId).isEqualTo(App501c3.processId.toString())

        mockDocumentService()
        mockAnswerService(populated = true)

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            variables =
                variables {
                    disposition = FulfillmentDisposition.Proceed.value
                },
        )

        Await.await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3ReadyForDownload.processingStatusId)
    }

    @ParameterizedTest
    @CsvSource(
        "true,20",
        "false,null",
    )
    fun `Blocked by related order and resolved by SF case`(
        isAttached: Boolean,
        parentProcessId: String?,
    ) {
        val variables = buildVariables(isAttached, if (parentProcessId == "null") null else parentProcessId!!.toInt())
        setup(variables)

        // setup mock such that when we check to see if we're blocked on another order, we find that we are blocked
        // by an amendment
        mockOrdersCustomerApi(variables, blockedScenario = true)
        mockProcessingOrdersApi(variables, blockedScenario = true)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-blocked") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3AwaitingFormation.processingStatusId)

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance = processInstance,
            activityId = "salesforce-activity-await-related-order",
            inSfProcess = true,
            caseDisposition = FulfillmentDisposition.Proceed,
        )

        val caseRequest = capturedSalesForceRequests.single()
        Assertions.assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.AWAITING_DEPENDENT_ORDER.toString())
        Assertions.assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        Assertions.assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        Assertions.assertThat(caseRequest.processId).isEqualTo(App501c3.processId.toString())

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(
            processInstance,
            botSuccessVars(
                evidenceTransactionNumber = testPaygovTrackingId,
                message = "$testAgencyTrackingId - $testSubmissionDate1023EZ",
            ),
        )

        mockProcessingOrdersApi(variables, blockedScenario = false)

        Await.await(Duration.of(30, ChronoUnit.SECONDS)).untilAsserted {
            assertThat(processInstance).hasPassed(
                "validate-docs-rpa-fields-task",
                "save-501c3-fields",
                "app501c3-document-generation-process",
                "populate-3500a-id",
                "order-status-update-sent-to-customer",
            )
        }

        Await.await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }
    }

    @Test
    fun blockedByRelatedOrderResolvedBySFCaseThenBotFails() {
        // this test was introduced when a bug was found in UAT.  The issue was that the blocked condition creates
        // a SF case and that included setting salesforceEventPhase variable to AWAITING_DEPENDENT_ORDER however
        // that variable is never cleared and propagates back up to app-501c3-filing-process so then when the bot fails
        // and we hit filing exception then salesforceEventPhase variable is still set to AWAITING_DEPENDENT_ORDER.
        // this test case was a means to reproduce and ensure expected behavior.

        val variables = buildVariables(isAttached = true, parentProcessIdVal = 20)
        setup(variables)

        // setup mock such that when we check to see if we're blocked on another order, we find that we are blocked
        // by an amendment
        mockOrdersCustomerApi(variables, blockedScenario = true)
        mockProcessingOrdersApi(variables, blockedScenario = true)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-blocked") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3AwaitingFormation.processingStatusId)

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance = processInstance,
            activityId = "salesforce-activity-await-related-order",
            inSfProcess = true,
            caseDisposition = FulfillmentDisposition.Proceed,
        )

        val caseRequest = capturedSalesForceRequests.single()
        Assertions.assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.AWAITING_DEPENDENT_ORDER.toString())
        Assertions.assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        Assertions.assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        Assertions.assertThat(caseRequest.processId).isEqualTo(App501c3.processId.toString())

        awaitThenSimulateRpaResponse(
            processInstance,
            botFailureVars(
                message = "bad things happened",
            ),
        )

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, activityId = "irrelevant", inSfProcess = true)

        val secondCaseRequest = capturedSalesForceRequests[1]
        Assertions.assertThat(secondCaseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.APP_501C3.toString())
        Assertions.assertThat(secondCaseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        Assertions.assertThat(secondCaseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
    }

    @Disabled
    @ParameterizedTest
    @CsvSource(
        "true,20",
        "false,null",
    )
    fun `Blocked by related order and resolved by reevaluation of criteria`(
        isAttached: Boolean,
        parentProcessId: String?,
    ) {
        val variables = buildVariables(isAttached, if (parentProcessId == "null") null else parentProcessId!!.toInt())
        setup(variables)

        // setup mock such that when we check to see if we're blocked on another order, we find that we are blocked
        // by an amendment
        mockOrdersCustomerApi(variables, blockedScenario = true)
        mockProcessingOrdersApi(variables, blockedScenario = true)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-blocked") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3AwaitingFormation.processingStatusId)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-blocked-reevaluate") }

        assertThat(processInstance).variables().containsAllEntriesOf(
            variables {
                conditionRecheckTimer = "PT1S"
            },
        )

        mockOrdersCustomerApi(variables, blockedScenario = false)
        mockProcessingOrdersApi(variables, blockedScenario = false)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(
            processInstance,
            botSuccessVars(
                evidenceTransactionNumber = testPaygovTrackingId,
                message = "$testAgencyTrackingId - $testSubmissionDate1023EZ",
            ),
        )

        mockProcessingOrdersApi(variables, blockedScenario = false)

        Await.await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }
    }

    @Test
    fun `Standalone 501c3 cancellation`() {
        val variables = buildVariables(isAttached = false)
        setup(variables)

        val processInstance = start501c3ProcessInstance(variables)

        Await.await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, App501c3FilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botFailureVars("SystemError"))

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "app501c3-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("app501c3-salesforce-activity")

        // Cancellation message
        orderCancellationService.cancelOrder(
            OrderCancelMessage(
                customerId = variables.customerId!!.toLong(),
                processingOrderId = variables.processingOrderId.toString(),
                processId = variables.processId!!.toLong(),
                state = variables.jurisdiction!!,
                workOrderId = null,
                orderId = variables.orderId!!.toLong(),
            ),
        )

        Await.await(Duration.of(30, ChronoUnit.SECONDS)).untilAsserted { assertThat(processInstance).isEnded }

        // 1. Check closeOpenOrcos
        verify(exactly = 1) {
            orcoService.updateReason(any(), any(), any())
        }

        // 2. Check ledger note
        verify(exactly = 1) {
            salesforceApiService.addLedgerNote(any())
        }

        // 3. check fulfillment event
        val einReceivedEvent = capturedFulfillmentEvents[1]
        Assertions.assertThat(einReceivedEvent.eventPhase).isEqualTo(EventPhase.APP_501C3)
        Assertions.assertThat(einReceivedEvent.eventType).isEqualTo(EventType.ORDER_CANCELLED)
    }
}
