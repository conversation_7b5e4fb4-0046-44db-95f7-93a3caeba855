package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.workflow.bpmn.helpers.RandomValueGenerators
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SpringBootProcessTest
import com.legalzoom.fulfillment.workflow.bpmn.helpers.variables
import com.legalzoom.fulfillment.workflow.service.BypassRpaService
import com.legalzoom.fulfillment.workflow.service.OrderStatusService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import org.awaitility.Awaitility.await
import org.camunda.bpm.engine.DecisionService
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.runtimeService
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.annotation.DirtiesContext
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.UUID

@SpringBootProcessTest
@Deployment(
    resources = [
        "bpmn/ein-obtainment-v3.bpmn",
        "bpmn/rpa-bot.bpmn",
        "bpmn/salesforce.bpmn",
        "bpmn/doc-generation.bpmn",
        "bpmn/print.bpmn",
        "dmn/einBotFailures.dmn",
    ],
)
@EmbeddedKafka
@DirtiesContext
class EINObtainmentProcessV3Tests(
    private val objectMapper: ObjectMapper,
    private val processEngine: ProcessEngine,
    private val salesforceHelper: SalesforceHelper,
    private val decisionService: DecisionService,
    private val runtimeService: RuntimeService,
) {
    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var bypassRpaService: BypassRpaService

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    private lateinit var orderStatusService: OrderStatusService

    private lateinit var variables: Variables

    @BeforeEach
    fun setup() {
        variables = buildVariables()
        setupMocks()
    }

    private fun buildVariables(): Variables {
        return variables {
            customerId = RandomValueGenerators.generateCustomerId()
            orderId = RandomValueGenerators.generateOrderId()
            processingOrderId = RandomValueGenerators.generateProcessingOrderId()
            processId = ProductType.LLC.processId
            workOrderId = RandomValueGenerators.generateWorkOrderId()
            entityName = "Test LLC"
            jurisdiction = "CA"
            accountId = RandomValueGenerators.generateAccountId()
            isAttachedOrder = false
        }
    }

    private fun setupMocks() {
        every { orderStatusService.updateProcessingOrderStatus(any(), any()) } returns Unit
        every { featureToggleService.isRpaEINBypassEnabled(any(), any(), any()) } returns false
        every { bypassRpaService.isBypassEinRpa(any()) } returns false
    }

    private fun startEinObtainmentV3ProcessInstance(variables: Variables) =
        runtimeService.startProcessInstanceByKey("ein-obtainment-process-v3", UUID.randomUUID().toString(), variables)

    @Test
    fun `should start process and reach bot decision gateway`() {
        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).isStarted
                assertThat(processInstance).hasPassed("order-status-update-filing-in-progress")
                assertThat(processInstance).isWaitingAt("Gateway_1m77m36")
            }

        verify {
            orderStatusService.updateProcessingOrderStatus(
                variables.processingOrderId!!,
                ProcessingOrderStatus.FilingInProgress.processingStatusId,
            )
        }
    }

    @Test
    fun `should bypass RPA when feature flag is enabled`() {
        every { bypassRpaService.isBypassEinRpa(any()) } returns true

        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).hasPassed("prepare-bypass-message")
                assertThat(processInstance).hasPassed("validation-error-task")
                assertThat(processInstance).isWaitingAt("ein-salesforce-activity")
            }

        assertThat(processInstance).variables().contains(
            java.util.Map.entry("validationError", true),
        )
    }

    @ParameterizedTest
    @CsvSource(
        "'We are unable to provide you with an EIN. - Reference Number 101 - EIN Application was not submitted. Order may not be retried.', SS4",
        "'You Have Exceeded the Number of EINs You May Receive Per Day - Reference Number 114 - EIN Application may have been submitted. Order may not be retried.', SS4",
        "'Physical Location Addresses cannot be found in the IRS database. - EIN Application was not submitted. Order can be retried.', SALESFORCE_CASE",
        "'Order validation errors: Business Location Phone is required. - EIN Application was not submitted. Order can be retried.', SALESFORCE_CASE",
        "'Some unknown error message', CAMUNDA_INCIDENT",
    )
    fun `DMN decision table should return correct action for bot failure messages`(
        message: String,
        expectedAction: String,
    ) {
        val dmnVariables = mapOf("message" to message)

        val result = decisionService.evaluateDecisionTableByKey("ein-bot-failures", dmnVariables)

        assertThat(result.singleResult).isNotNull
        val resultMap = result.singleResult as Map<String, Any>
        assertThat(resultMap["action"]).isEqualTo(expectedAction)
    }

    @Test
    fun `should handle bot failure with DMN decision - SS4 path`() {
        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        // Navigate to bot process
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("rpa-bot-process") }

        // Simulate bot failure with specific error message
        val botFailureVars =
            variables {
                status = "Failed"
                validationError = false
                message = "We are unable to provide you with an EIN. - Reference Number 101 - EIN Application was not submitted. Order may not be retried."
            }

        runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceId(processInstance.id)
            .setVariables(botFailureVars)
            .correlate()

        // Should go through DMN decision and choose SS4 path
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).hasPassed("Activity_1cv37lm") // DMN business rule task
                assertThat(processInstance).hasPassed("Gateway_0y4pu3c") // Decision gateway
                assertThat(processInstance).isWaitingAt("Activity_12864yi") // SS4 document generation
            }

        // Verify DMN output variables are set
        assertThat(processInstance).variables().contains(
            java.util.Map.entry("action", "SS4"),
        )
    }

    @Test
    fun `should handle bot failure with DMN decision - Salesforce case path`() {
        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        // Navigate to bot process
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("rpa-bot-process") }

        // Simulate bot failure with error that should create Salesforce case
        val botFailureVars =
            variables {
                status = "Failed"
                validationError = false
                message = "Physical Location Addresses cannot be found in the IRS database. - EIN Application was not submitted. Order can be retried."
            }

        runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceId(processInstance.id)
            .setVariables(botFailureVars)
            .correlate()

        // Should go through DMN decision and choose Salesforce case path
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).hasPassed("Activity_1cv37lm") // DMN business rule task
                assertThat(processInstance).hasPassed("Gateway_0y4pu3c") // Decision gateway
                assertThat(processInstance).isWaitingAt("Activity_0f6997g") // Salesforce activity
            }

        // Verify DMN output variables are set
        assertThat(processInstance).variables().contains(
            java.util.Map.entry("action", "SALESFORCE_CASE"),
        )
    }

    @Test
    fun `should handle bot failure with DMN decision - Retry path`() {
        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        // Navigate to bot process
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("rpa-bot-process") }

        // Simulate bot failure with error that should trigger retry
        val botFailureVars =
            variables {
                status = "Failed"
                validationError = false
                message = "Technical Difficulties - Reference Number 111 - EIN Application was not submitted. Order may not be retried."
            }

        runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceId(processInstance.id)
            .setVariables(botFailureVars)
            .correlate()

        // Should go through DMN decision and choose retry path
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).hasPassed("Activity_1cv37lm") // DMN business rule task
                assertThat(processInstance).hasPassed("Gateway_0y4pu3c") // Decision gateway
                // Should loop back to rpa-bot-process for retry
                assertThat(processInstance).isWaitingAt("rpa-bot-process")
            }

        // Verify DMN output variables are set
        assertThat(processInstance).variables().contains(
            java.util.Map.entry("action", "RETRY"),
        )
    }

    @Test
    fun `should handle bot failure with DMN decision - Camunda incident path`() {
        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        // Navigate to bot process
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("rpa-bot-process") }

        // Simulate bot failure with unknown error that should create incident
        val botFailureVars =
            variables {
                status = "Failed"
                validationError = false
                message = "Some completely unknown error that doesn't match any DMN rules"
            }

        runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceId(processInstance.id)
            .setVariables(botFailureVars)
            .correlate()

        // Should go through DMN decision and choose incident path
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).hasPassed("Activity_1cv37lm") // DMN business rule task
                assertThat(processInstance).hasPassed("Gateway_0y4pu3c") // Decision gateway
                assertThat(processInstance).hasPassed("Activity_1bq7f0e") // Create incident task
            }

        // Verify DMN output variables are set
        assertThat(processInstance).variables().contains(
            java.util.Map.entry("action", "CAMUNDA_INCIDENT"),
        )
    }

    @Test
    fun `should handle successful bot execution and complete process`() {
        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        // Navigate to bot process
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("rpa-bot-process") }

        // Simulate successful bot execution
        val botSuccessVars =
            variables {
                status = "Success"
                validationError = false
                evidenceTransactionNumber = "12-3456789"
            }

        runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceId(processInstance.id)
            .setVariables(botSuccessVars)
            .correlate()

        // Should proceed through success path
        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).hasPassed("validate-rpa-ein-docs-task")
                assertThat(processInstance).hasPassed("store_ein")
                assertThat(processInstance).hasPassed("ein-obtained-gateway")
                assertThat(processInstance).hasPassed("Activity_1y1h6cc") // notify external systems
                assertThat(processInstance).isWaitingAt("ein-document-generation-process")
            }
    }

    @Test
    fun `DMN should handle null message input gracefully`() {
        val dmnVariables = mapOf("message" to null)

        val result = decisionService.evaluateDecisionTableByKey("ein-bot-failures", dmnVariables)

        assertThat(result.singleResult).isNotNull
        val resultMap = result.singleResult as Map<String, Any>
        // Should default to CAMUNDA_INCIDENT for null/unknown messages
        assertThat(resultMap["action"]).isEqualTo("CAMUNDA_INCIDENT")
    }

    @Test
    fun `DMN should handle empty message input gracefully`() {
        val dmnVariables = mapOf("message" to "")

        val result = decisionService.evaluateDecisionTableByKey("ein-bot-failures", dmnVariables)

        assertThat(result.singleResult).isNotNull
        val resultMap = result.singleResult as Map<String, Any>
        // Should default to CAMUNDA_INCIDENT for empty/unknown messages
        assertThat(resultMap["action"]).isEqualTo("CAMUNDA_INCIDENT")
    }

    @Test
    fun `should test DMN decision table with multiple outputs`() {
        val dmnVariables =
            mapOf(
                "message" to "We are unable to provide you with an EIN. - Reference Number 101 - EIN Application was not submitted. Order may not be retried.",
            )

        val result = decisionService.evaluateDecisionTableByKey("ein-bot-failures", dmnVariables)

        assertThat(result.singleResult).isNotNull
        val resultMap = result.singleResult as Map<String, Any>
        assertThat(resultMap["action"]).isEqualTo("SS4")
        // The delay column should be empty/null for this rule
        assertThat(resultMap["delay"]).isNull()
    }

    @Test
    fun `should handle await related orders subprocess`() {
        variables.isAttachedOrder = true
        variables.parentProcessId = 2
        variables.parentProcessingOrderId = RandomValueGenerators.generateProcessingOrderId()

        val processInstance = startEinObtainmentV3ProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                assertThat(processInstance).isStarted
                assertThat(processInstance).hasPassed("Activity_13avu5t") // await related orders subprocess
                assertThat(processInstance).hasPassed("order-status-update-filing-in-progress")
            }
    }

    private fun simulateRpaMessage(
        processInstance: org.camunda.bpm.engine.runtime.ProcessInstance,
        variables: Variables,
    ) {
        runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceId(processInstance.id)
            .setVariables(variables)
            .correlate()
    }
}
