package com.legalzoom.fulfillment.workflow.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.SpykBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@SpringBootTest
@ExtendWith(MockKExtension::class)
class ManualWorkflowConversionOrderDataFetchServiceTest(
    val manualWorkflowConversionOrderDataFetchService: ManualWorkflowConversionOrderDataFetchService,
) {
    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    @MockK
    private lateinit var execution: DelegateExecution

    @SpykBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @BeforeEach
    fun setUp() {
        clearAllMocks()
    }

    @ParameterizedTest
    @CsvSource(
        // processingOrderId, convertTo, expectedConvertToValue, mock answers
        // convert to LLC
        "*********, INC, LLC, conversions_to_llc_answers.json",
        // convert to INC
        "571617738, LLC, INC, conversions_to_inc_answers.json",
    )
    fun testFetchData(
        processingOrderId: Int,
        existingEntityType: String,
        expectedAnswerValue: String,
        answersFileName: String,
    ) {
        val processId = ProductType.Conversion.processId
        every { execution.input } returns
            variables {
                this.processId = processId
                customerId = "********"
                this.processingOrderId = processingOrderId
            }
        val answersApiJSON = ClassPathResource(answersFileName, javaClass).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJSON)

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            GetAnswersComposite(
                processId,
                answersApiResponse,
            )

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        manualWorkflowConversionOrderDataFetchService.fetchData(execution, execution.input.processingOrderId!!, execution.input.customerId)

        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        }

        assertThat(variables.first()).containsAllEntriesOf(
            variables {
                entityType = existingEntityType
                entityName = "Manual Workflow Test"
                convertEntityTo = expectedAnswerValue
            },
        )
    }

    @Test
    fun testFetchDataEmptyResponse() {
        val processId = ProductType.Conversion.processId
        val processingOrderId = *********
        every { execution.input } returns
            variables {
                this.processId = processId
                customerId = "********"
                this.processingOrderId = processingOrderId
            }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            GetAnswersComposite(
                processId,
                GetQuestionnaireAnswerResponse(),
            )

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        manualWorkflowConversionOrderDataFetchService.fetchData(execution, execution.input.processingOrderId!!, execution.input.customerId)

        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        }

        assertThat(variables.first()).containsAllEntriesOf(
            variables {
                entityType = null
                entityName = null
                convertEntityTo = null
            },
        )
    }
}
