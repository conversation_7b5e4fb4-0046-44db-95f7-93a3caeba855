package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.businessentities.BusinessEntitiesApi
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.model.answer.Company
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.businessentities.EntityDetailDto
import com.legalzoom.api.model.businessentities.GetEntityByEntityIdResponse
import com.legalzoom.api.model.businessentities.UpdateEntityResponse
import com.legalzoom.api.model.dsd.printship.PrintShipJobResponseDto
import com.legalzoom.api.model.order.GetCustomerOrdersResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderDto
import com.legalzoom.api.model.order.OrderGroupDto
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.order.ProductConfigurationDto
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.model.rpa.AddQueueItemRequest
import com.legalzoom.api.model.rpa.QueueItemDto
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.order.OrdersCustomerApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.api.revv.RevvEsignatureService
import com.legalzoom.api.revv.model.CreateDocumentResponse
import com.legalzoom.api.revv.model.DocumentFieldsResponse
import com.legalzoom.api.revv.model.DocumentPdfFieldResponse
import com.legalzoom.api.revv.model.TemplateResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants
import com.legalzoom.fulfillment.domain.Constants.EIN_SS4_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.Constants.PRINT_PROCESS
import com.legalzoom.fulfillment.domain.Constants.RPA_BOT_PROCESS
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.orco.entity.enumeration.OrcoStatus.OPEN
import com.legalzoom.fulfillment.orco.entity.enumeration.OrcoType.SELF_SERVE
import com.legalzoom.fulfillment.orco.entity.enumeration.ResolutionType
import com.legalzoom.fulfillment.orco.service.OrcoService
import com.legalzoom.fulfillment.orco.service.model.OrcoRequest
import com.legalzoom.fulfillment.orco.service.model.OrcoResponse
import com.legalzoom.fulfillment.orco.service.model.ReasonRequest
import com.legalzoom.fulfillment.orco.service.model.ReasonResponse
import com.legalzoom.fulfillment.orco.service.model.ResolutionResponse
import com.legalzoom.fulfillment.orco.service.model.toEntity
import com.legalzoom.fulfillment.orco.service.model.toResponse
import com.legalzoom.fulfillment.printandship.entity.VendorCode
import com.legalzoom.fulfillment.printandship.repository.VendorCodeRepository
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintConfig
import com.legalzoom.fulfillment.printandshipapi.enumeration.RequestType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType.Companion.findCustomerDocTypeFromProductName
import com.legalzoom.fulfillment.service.data.FulfillmentEvent
import com.legalzoom.fulfillment.service.data.OrderCancelMessage
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.ValidationResult
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType.EIN_FINAL_LETTER
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType.SS4_PREPARED
import com.legalzoom.fulfillment.service.enumeration.BusinessType.LLC
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.DocumentType
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition.Proceed
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.DBA
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.RevvService
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedServiceImpl
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.orderStatusNotifications.OrderStatusNotificationsService
import com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.EIN
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataPayload
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.GetAnswersPayload
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.testing.Await.await
import com.legalzoom.fulfillment.testing.SpringBootProcessTest
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.bpmn.helpers.RandomValueGenerators
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import com.legalzoom.fulfillment.workflow.delegate.helper.LedgerNoteHelperService
import com.legalzoom.fulfillment.workflow.dmn.JsonFileArgumentProvider
import com.legalzoom.fulfillment.workflow.service.AddQueuesItemService
import com.legalzoom.fulfillment.workflow.service.EinAnswerService
import com.legalzoom.fulfillment.workflow.service.OrderCancellationService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.REF_101_PREFIX
import com.legalzoom.fulfillment.workflow.service.SS4OrcoService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.revv.RevvDocumentGenerationService
import com.legalzoom.fulfillment.workflow.service.revv.RevvFieldsRequestBuilder
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.awaitility.kotlin.untilNotNull
import org.camunda.bpm.engine.HistoryService
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.history.HistoricProcessInstance
import org.camunda.bpm.engine.history.HistoricVariableInstance
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.assertThat
import org.camunda.bpm.engine.test.assertions.cmmn.CmmnAwareTests
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.slf4j.LoggerFactory
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.http.MediaType
import org.springframework.kafka.test.context.EmbeddedKafka
import reactor.core.publisher.Mono
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@SpringBootProcessTest
@Deployment(
    resources = ["bpmn/ein-obtainment.bpmn", "bpmn/rpa-bot.bpmn", "bpmn/salesforce.bpmn", "bpmn/doc-generation.bpmn", "bpmn/print.bpmn"],
)
@EmbeddedKafka
class EinObtainmentProcessTest(
    private val processEngine: ProcessEngine,
    private val runtimeService: RuntimeService,
    private val objectMapper: ObjectMapper,
    private val historyService: HistoryService,
    private val salesforceHelper: SalesforceHelper,
    private val orderCancellationService: OrderCancellationService,
) {
    private lateinit var ein: String
    private val logger = LoggerFactory.getLogger(javaClass)

    fun setup(variables: Variables) {
        // the reason it's helpful to log the variables is that many of them are randomized.  So if you get failed tests
        // due to mocking issues it's confusing unless you know the variable values at play when the test started.
        val variablesLogString = StringBuilder()
        for ((key, value) in variables) {
            variablesLogString.append("Key: $key, Value: $value\n")
        }
        logger.info("Variable values: \n" + variablesLogString)

        AbstractAssertions.init(processEngine)
        clearAllMocks()
        ein = RandomValueGenerators.generateEin()
        mockOrdersApiService(variables)
        mockAddQueueItem(variables)
        mockQuestionnaireAnswerService(variables)
        mockFeatureToggleService()
        mockSalesforceApi()
        mockS3Service()
        mockDocumentService()
        mockProcessingOrdersApi(variables)
        mockActivityFeedService()
        mockOrderStatusNotificationService()
        mockBusinessEntitiesApi(
            if (variables.isAttachedOrder == true) variables.parentProcessingOrderId!! else variables.processingOrderId!!,
        )
        mockAnswersApi()
        mockGetOrderContact()
        mockRevvCalls()
        mockPrintShipServices()
        mockOrcoService()
        mockSs4OrcoService()
        mockEinAnswerService(ein)
        mockLedgerNoteHelperService()
        mockOrdersCustomerApi(variables)
        mockFulfillmentEventService()
    }

    private fun logVariables(processInstance: ProcessInstance) {
        // val variables: VariableMap = runtimeService.getVariablesTyped(processInstance.processInstanceId)
        val historicVariables: List<HistoricVariableInstance> =
            historyService
                .createHistoricVariableInstanceQuery()
                .processInstanceId(processInstance.processInstanceId)
                .list()

        val variableValues =
            historicVariables.joinToString(separator = "\n") { variable ->
                "${variable.name} -> ${variable.value}"
            }
        logger.info("Variable values: \n" + variableValues)
    }

    @MockkBean
    lateinit var orcoService: OrcoService

    fun mockOrcoService() {
        every {
            orcoService.save(any())
        } answers {
            val arg = it.invocation.args[0] as OrcoRequest
            arg.toEntity().toResponse()
        }
        every {
            orcoService.updateReason(any(), any(), any())
        } returns Unit
        every {
            orcoService.calculateOrco(any())
        } answers { (it.invocation.args.first() as OrcoRequest).toEntity().toResponse() }

        every {
            orcoService.getOpenOrco(any())
        } returns null

        every {
            orcoService.search(any(), any(), any(), any())
        } returns
            listOf(
                OrcoResponse(
                    id = UUID.randomUUID(),
                    type = SELF_SERVE,
                    status = OPEN,
                    references = emptyList(),
                    createdBy = null,
                    createdDate = null,
                    modifiedBy = null,
                    modifiedDate = null,
                    reasons =
                        listOf(
                            simpleReasonResponse(),
                        ),
                ),
            )
    }

    private fun simpleReasonResponse() =
        ReasonResponse(
            id = UUID.randomUUID(),
            resolution = simpleResolutionResponse(),
            status = OPEN,
            categoryId = "test",
            note = "test",
            createdBy = null,
            createdDate = null,
            modifiedBy = null,
            modifiedDate = null,
        )

    private fun simpleResolutionResponse() =
        ResolutionResponse(
            type = ResolutionType.MANUAL,
            categoryId = UUID.randomUUID(),
            id = UUID.randomUUID(),
            createdBy = "NGX",
            modifiedBy = "NGX",
            createdDate = Instant.now(),
            modifiedDate = Instant.now(),
        )

    @MockkBean
    lateinit var ordersCustomerApi: OrdersCustomerApi

    fun mockOrdersCustomerApi(
        variables: Variables,
        blockedScenario: Boolean = false,
    ) {
        if (blockedScenario) {
            val getCustomerOrdersResponse = GetCustomerOrdersResponse()
            getCustomerOrdersResponse.orderGroups = mutableListOf()
            val orderGroupDto = OrderGroupDto()
            orderGroupDto.accountId = variables.accountId
            val orderDto = OrderDto()
            orderDto.orderItems = mutableListOf()
            val orderItemDto = OrderItemDto()
            orderItemDto.processingOrder = com.legalzoom.api.model.order.ProcessingOrderDto()
            orderItemDto.orderId = 123
            orderItemDto.processingOrder!!.processId = ProductType.Amendment.processId
            orderItemDto.processingOrder!!.processingStatusId =
                ProcessingOrderStatus.AmendmentPreliminaryNameValidationComplete.processingStatusId
            orderItemDto.productConfiguration = ProductConfigurationDto()
            orderItemDto.productConfiguration!!.productTypeId = com.legalzoom.api.model.order.RelationshipType.NUMBER_2
            orderDto.orderItems!!.add(orderItemDto)
            orderGroupDto.orders = mutableListOf()
            orderGroupDto.orders!!.add(orderDto)
            getCustomerOrdersResponse.orderGroups!!.add(orderGroupDto)
            every {
                ordersCustomerApi.coreOrdersCustomerCustomerIdGet(any(), any(), any(), any(), any(), any(), any())
            } returns Mono.just(getCustomerOrdersResponse)
            return
        }
        every {
            ordersCustomerApi.coreOrdersCustomerCustomerIdGet(any(), any(), any(), any(), any(), any(), any())
        } returns Mono.just(GetCustomerOrdersResponse())
    }

    @MockkBean
    lateinit var ss4OrcoService: SS4OrcoService

    fun mockSs4OrcoService(requiresSs4PreparedDoc: Boolean = false) {
        every {
            ss4OrcoService.requiresSS4PreparedDocGen(any())
        } returns requiresSs4PreparedDoc
    }

    @MockkBean
    lateinit var ordersApiService: OrdersApiService

    private fun mockOrdersApiService(variables: Variables) {
        val orderResponse =
            if (variables.isAttachedOrder!!) {
                // "processingOrderId": 511205226,
                objectMapper.readValue<GetOrderResponse>(
                    ClassPathResource("ca_llc_order_response.json", javaClass).file.readText()
                        .replace(
                            "\"processingOrderId\": 511205226",
                            "\"processingOrderId\": ${variables.parentProcessingOrderId}",
                            // main order item
                        )
                        .replace(
                            "\"processingOrderId\": 511205234",
                            "\"processingOrderId\": ${variables.processingOrderId}",
                            // ein order item
                        ),
                )
            } else {
                objectMapper.readValue<GetOrderResponse>(
                    ClassPathResource("standalone_ein_order_response.json", javaClass).file.readText()
                        .replace("\"orderId\": 82510222", "\"orderId\": ${variables.orderId}")
                        .replace("\"customerId\": 56303594,", "\"customerId\": ${variables.customerId},")
                        .replace("\"processingOrderId\": 574934667,", "\"processingOrderId\": ${variables.processingOrderId},"),
                )
            }
        every {
            ordersApiService.getOrders(variables.orderId, any(), any(), any(), any(), any())
        } returns orderResponse
    }

    @MockkBean
    private lateinit var addQueuesItemService: AddQueuesItemService
    private var capturedAddQueueItemRequest = mutableListOf<AddQueueItemRequest>()

    private fun mockAddQueueItem(variables: Variables) {
        capturedAddQueueItemRequest.clear()
        val response =
            objectMapper.readValue<QueueItemDto>(
                ClassPathResource("rpa_response.json", javaClass).file.readText()
                    .replace("\"orderId\": \"1234\"", "\"orderId\": \"${variables.orderId}\""),
            )
        every {
            addQueuesItemService.addQueuesItem(any(), any(), any(), capture(capturedAddQueueItemRequest), any())
        } returns response
    }

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    private fun mockQuestionnaireAnswerService(
        variables: Variables,
        ein: String? = null,
        businessType: String? = null,
    ) {
        val response = FakeAnswersPayload()
        response.ein = ein
        response.businessType = businessType ?: LLC.fieldType
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(
                processingOrderId = if (variables.isAttachedOrder!!) variables.parentProcessingOrderId!! else variables.processingOrderId!!,
                customerId = variables.customerId,
                answerSource = AnswerSource.AnswerBank,
            )
        } returns response
        every {
            questionnaireAnswerService.putPartialUpdate(any(), any())
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())

        val mockAnswer = mockk<FilingDataPayload>()
        every {
            mockAnswer.company
        } returns Company().apply { isProfessional = false }
        every {
            questionnaireAnswerService.getFilingData(any(), any(), any())
        } returns mockAnswer
    }

    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var orcoFeatureToggleService: ORCOFeatureToggleService

    private fun mockFeatureToggleService(
        isSsorcoEnabled: Boolean = true,
        isSS4SSOrcoEnabled: Boolean = false,
    ) {
        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isRpaEINBypassEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false
        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns true
        every {
            featureToggleService.isRevvSS4DocGenEnabled(any(), any(), any())
        } returns true
        every {
            orcoFeatureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns isSsorcoEnabled
        every {
            orcoFeatureToggleService.isSS4SSOrcoEnabled(any(), any(), any())
        } returns isSS4SSOrcoEnabled
        every {
            orcoFeatureToggleService.isManualOrcoEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.isSSORCORepeatNotificationsEnabled(any(), any(), any())
        } returns false
        every {
            featureToggleService.einBEUpdateEnabled(any(), any(), LLC.processId)
        } returns true
        every {
            featureToggleService.einBEUpdateEnabled(any(), any(), ProductType.EIN.processId)
        } returns true
        every {
            featureToggleService.einBEUpdateEnabled(any(), any(), DBA.processId)
        } returns false

        every { featureToggleService.isPrintNameplateEnabled(any(), any(), any()) } returns true

        every {
            featureToggleService.isDSDDocGenEnabled(any(), any(), any())
        } returns false
    }

    @MockkBean
    private lateinit var salesforceApiService: SalesforceApiService
    private var capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
    private var capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
    private var capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

    private fun mockSalesforceApi() {
        capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
        capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
        capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

        every {
            salesforceApiService.createCase(capture(capturedSalesForceRequests))
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(capture(capturedLedgerNoteRequests))
        } returns AddLedgerNoteResponse("test", "test", emptyList())

        every {
            salesforceApiService.updateCase(capture(capturedSalesForceUpdateCaseRequests))
        } returns SalesforceCaseUpdateResponse("test", "test", "test")
    }

    @MockkBean
    private lateinit var s3Service: S3Service

    private fun mockS3Service() {
        val testFile = ClassPathResource("test_file.png", javaClass).file
        every {
            s3Service.getDocument(any())
        } returns
            ResourceWithType(
                FileSystemResource(testFile),
                MediaType.IMAGE_PNG,
                "test_file.png",
            )
    }

    @MockkBean
    private lateinit var documentService: DocumentService

    private fun mockDocumentService() {
        every {
            documentService.uploadDocument(any(), any(), any(), any(), any())
        } returns
            DocumentResponse().documentId("Test").documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")
        val documents = objectMapper.readValue<List<Document>>(ClassPathResource("saein_documents.json", javaClass).file)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns documents
    }

    @MockkBean
    private lateinit var processingOrdersApi: ProcessingOrdersApi

    private fun mockProcessingOrdersApi(
        variables: Variables,
        blockedScenario: Boolean = false,
    ) {
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(), any(), any(), any(), any(), any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())

        if (variables.isAttachedOrder == true) {
            val processingOrderDto = ProcessingOrderDto()

            if (blockedScenario) {
                processingOrderDto.processingStatusId =
                    ProcessingOrderStatus.fromProcessIdAndDescription(
                        variables.parentProcessId!!,
                        "Sent To SOS",
                    )!!.processingStatusId
            } else {
                processingOrderDto.processingStatusId =
                    ProcessingOrderStatus.fromProcessIdAndDescription(
                        variables.parentProcessId!!,
                        "Sent to Customer",
                    )!!.processingStatusId
            }
            processingOrderDto.processId = variables.parentProcessId!!

            val getProcessingOrderResponse = GetProcessingOrderResponse()
            getProcessingOrderResponse.processingOrder = processingOrderDto

            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
            } returns Mono.just(getProcessingOrderResponse)
        } else {
            val processingOrderDto = ProcessingOrderDto()
            processingOrderDto.processingStatusId = ProcessingOrderStatus.EinFilingComplete.processingStatusId
            processingOrderDto.processId = ProductType.EIN.processId
            val getProcessingOrderResponse = GetProcessingOrderResponse()
            getProcessingOrderResponse.processingOrder = processingOrderDto

            every {
                processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(variables.processingOrderId!!, any(), any(), any())
            } returns Mono.just(getProcessingOrderResponse)
        }
    }

    @MockkBean
    private lateinit var activityFeedServiceImpl: ActivityFeedServiceImpl

    private fun mockActivityFeedService() {
        every {
            activityFeedServiceImpl.sendEvent(any<ProcessingOrderStatus>(), any())
        } returns null
    }

    @MockkBean
    private lateinit var orderStatusNotificationsService: OrderStatusNotificationsService

    private fun mockOrderStatusNotificationService() {
        every {
            orderStatusNotificationsService.processOrderStatusChange(any(), any())
        } returns Unit
    }

    @MockkBean
    private lateinit var businessEntitiesApi: BusinessEntitiesApi

    private fun mockBusinessEntitiesApi(processingOrderId: Int) {
        every {
            businessEntitiesApi.businessEntitiesEntityIdPut(any(), any(), any(), any(), any(), any())
        } returns Mono.just(UpdateEntityResponse())

        every {
            businessEntitiesApi.businessEntitiesProcessingOrdersProcessingOrderIdGet(processingOrderId, any(), any(), any())
        } returns Mono.just(GetEntityByEntityIdResponse().entity(EntityDetailDto().entityId(123)))
    }

    @MockkBean
    private lateinit var answersApi: AnswerApi

    private fun mockAnswersApi() {
        val response =
            GetQuestionnaireAnswerResponse().apply {
                questionnaireFieldGroupAnswers = QuestionnaireAnswerDto()
            }

        every {
            answersApi.answersUserOrderIdSourceGet(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)
    }

    @MockkBean
    private lateinit var ordersContactsApiService: OrdersContactsApiService

    private fun mockGetOrderContact() {
        val json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/order_contacts_response.json").file
        val response = JsonFileArgumentProvider.objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            ordersContactsApiService.getOrdersContacts(any(), any())
        } returns response
    }

    @MockkBean
    private lateinit var revvEsignatureService: RevvEsignatureService

    @MockkBean
    private lateinit var revvService: RevvService

    @MockkBean
    lateinit var revvDocumentGenerationService: RevvDocumentGenerationService

    @MockkBean
    lateinit var requestBuilder: RevvFieldsRequestBuilder

    private fun mockRevvCalls(templates: MutableList<RevvDocumentType> = mutableListOf(EIN_FINAL_LETTER)) {
        var json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_get_all_templates.json").file
        val templateResponse = JsonFileArgumentProvider.objectMapper.readValue<TemplateResponse>(json)
        coEvery {
            revvEsignatureService.listAllTemplates(any())
        } returns templateResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_create_document.json").file
        val createDocumentResponse = JsonFileArgumentProvider.objectMapper.readValue<CreateDocumentResponse>(json)
        coEvery {
            revvEsignatureService.createDocumentUsingTemplate(any(), any())
        } returns createDocumentResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_save_document_fields.json").file
        val saveDocumentFiledsResponse = JsonFileArgumentProvider.objectMapper.readValue<DocumentFieldsResponse>(json)
        coEvery {
            revvEsignatureService.saveDocumentFileds(any(), any(), any())
        } returns saveDocumentFiledsResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_save_pdf_fields.json").file
        val savePdfFieldsResponse = JsonFileArgumentProvider.objectMapper.readValue<DocumentPdfFieldResponse>(json)
        coEvery {
            revvEsignatureService.savePDFFields(any(), any(), any())
        } returns savePdfFieldsResponse

        coEvery {
            revvEsignatureService.downloadPdfDocument(any(), any())
        } returns byteArrayOf()

        every {
            requestBuilder.getTemplates()
        } returns templates
        every {
            requestBuilder.getCustomerDocumentType()
        } returns
            mutableMapOf(
                EIN_FINAL_LETTER to findCustomerDocTypeFromProductName(ProductType.EIN.productName, "Final Letter"),
                SS4_PREPARED to findCustomerDocTypeFromProductName(ProductType.EIN.productName, "SS4 Prepared"),
            )
        every {
            requestBuilder.getMappingFields(any())
        } returns mutableListOf()
        every {
            revvDocumentGenerationService.uploadDocument(any(), any(), any(), any(), any(), any(), any(), any())
        } returns null
        every {
            revvDocumentGenerationService.getRevvRequestBuilder(any())
        } returns requestBuilder
        every {
            revvDocumentGenerationService.getRequestedDocumentTemplates(any())
        } returns templates

        every {
            revvService.sendforRevvDocAutomation(any())
        } returns "".toByteArray()
        every {
            revvService.getAllTemplates()
        } returns emptyList()
    }

    @MockkBean
    private lateinit var completedOrderDetailService: CompletedOrderDetailService

    fun mockCompletedOrderDetailService() {
        val json = ClassPathResource("completedOrderDetailResponse.json", javaClass).file
        val response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)

        every {
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(any(), any())
        } returns response
    }

    @MockkBean
    private lateinit var documentListService: DocumentListService

    fun mockDocumentListService() {
        every {
            documentListService.getDefaultDocumentListWithConfig(any(), any(), any(), any(), any())
        } returns
            listOf(
                PrintDocumentInfo(
                    documentId = "1234",
                    documentName = "Articles Filed.pdf",
                    printConfig = PrintConfig.LZDOC1,
                    documentType = "Articles Filed",
                ),
            )
    }

    @MockkBean
    private lateinit var orderContactsService: OrderContactsService

    fun mockOrderContactsService() {
        val json = ClassPathResource("orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns response.contacts!!
    }

    @MockkBean
    private lateinit var printShipJobsApi: PrintShipJobsApi

    fun mockPrintAndShipApi() {
        every {
            printShipJobsApi.createJobAsync(any())
        } returns
            Mono.just(
                PrintShipJobResponseDto().also { response ->
                    response.id = UniqueId.nextUUIDString()
                },
            )
    }

    @MockkBean
    private lateinit var vendorCodeRepository: VendorCodeRepository

    fun mockVendorCodeRepository() {
        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.PRINT },
            )
        } answers {
            listOf(
                VendorCode(
                    "viatech",
                    "PLZ-LLCPOD",
                    "LLC Printing",
                    RequestType.PRINT,
                    AnswersEntityType.LLC.processId,
                ),
            )
        }

        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.KIT },
            )
        } returns
            listOf(
                VendorCode("viatech", "KLZ-LCTT-CA", "LLC Founders Kit", RequestType.KIT, AnswersEntityType.LLC.processId),
            )
    }

    @MockkBean
    private lateinit var productsApi: ProductsApi

    private fun mockProductsApi() {
        val json = ClassPathResource("productsApiPostOptionResponse.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                match { it == 7848 },
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)

        every {
            productsApi.coreProductsProductIdPostOptionGet(
                // not attached
                match { it == 958 },
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response.apply { postOption!!.isKitIncluded = false })
    }

    fun mockPrintShipServices() {
        mockCompletedOrderDetailService()
        mockDocumentListService()
        mockOrderContactsService()
        mockPrintAndShipApi()
        mockVendorCodeRepository()
        mockkStatic(LocalDateTime::class)
        every {
            LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        } returns LocalDateTime.parse("2024-01-01T00:00:00")
        mockProductsApi()
    }

    @MockkBean
    private lateinit var einAnswerService: EinAnswerService

    private fun mockEinAnswerService(ein: String?) {
        every {
            einAnswerService.getEinFieldNullable(any())
        } returns ein
        if (ein != null) {
            every {
                einAnswerService.getEinField(any())
            } returns ein
        }
    }

    @MockkBean
    private lateinit var ledgerNoteHelperService: LedgerNoteHelperService

    private fun mockLedgerNoteHelperService() {
        every {
            ledgerNoteHelperService.createLedger(any(), any(), any())
        } returns Unit
    }

    @MockkBean
    private lateinit var fulfillmentEventService: FulfillmentEventService
    private var capturedFulfillmentEvents = mutableListOf<FulfillmentEvent>()

    private fun mockFulfillmentEventService() {
        capturedFulfillmentEvents = mutableListOf()

        every {
            fulfillmentEventService.send(capture(capturedFulfillmentEvents))
        } returns Unit
    }

    protected fun verifyNewPrintAndShip(
        parentProcessInstance: ProcessInstance,
        variables: Variables,
    ) {
        val printAndShipProcess =
            await(Duration.of(30, ChronoUnit.SECONDS))
                .until({ awaitAndGetChildProcessInstance(parentProcessInstance, PRINT_PROCESS) }, { it != null })

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { CmmnAwareTests.assertThat(printAndShipProcess).isWaitingAt("waiting-print-response") }

        processEngine.runtimeService
            .createMessageCorrelation(com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_STATUS_MESSAGE)
            .processInstanceId(printAndShipProcess?.id)
            .setVariables(
                mapOf(
                    "printStatus" to "SHIPPED",
                    "evidenceTransactionNumber" to "1234567890",
                ),
            )
            .correlate()

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { CmmnAwareTests.assertThat(printAndShipProcess).isEnded }

        assertAll(
            "Assert print and ship in/out mappings",
            {
                CmmnAwareTests.assertThat(printAndShipProcess).variables().containsAllEntriesOf(
                    variables {
                        customerId = variables.customerId
                        jurisdiction = variables.jurisdiction
                        orderId = variables.orderId
                        processId = variables.processId
                        processingOrderId = variables.processingOrderId
                        evidenceTransactionNumber = "1234567890"
                        printStatus = "SHIPPED"
                    },
                )
            },
        )
    }

    fun awaitAndGetChildProcessInstance(
        parentProcessInstance: ProcessInstance,
        processDefinitionKey: String,
    ): ProcessInstance? {
        var childProcessInstance: ProcessInstance? = null
        await(Duration.of(1, ChronoUnit.MINUTES))
            .untilAsserted {
                childProcessInstance =
                    processEngine.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(processDefinitionKey)
                        .superProcessInstanceId(parentProcessInstance.processInstanceId).list().singleOrNull()

                assertThat(childProcessInstance).isNotNull
            }

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(childProcessInstance).isStarted }

        return childProcessInstance
    }

    fun getIncident(processInstance: ProcessInstance) =
        runtimeService
            .createIncidentQuery()
            .processInstanceId(processInstance.id)
            .list().firstOrNull()

    private fun buildVariables(
        isAttached: Boolean = false,
        parentProcessIdVal: Int? = null,
    ): Variables {
        return variables {
            accountId = UUID.randomUUID()
            customerId = RandomValueGenerators.generateCustomerId()
            entityName = RandomValueGenerators.generateEntityName()
            expediteSpeed = "Standard"
            jurisdiction = "CA"
            orderId = RandomValueGenerators.generateOrderId()
            processId = ProductType.EIN.processId
            processingOrderId = RandomValueGenerators.generateProcessingOrderId()
            workOrderId = UUID.randomUUID()
            isAttachedOrder = isAttached
            conditionRecheckTimer = "PT1S" // causes delay between rechecks to be minimal

            if (isAttached) {
                parentProcessingOrderId = RandomValueGenerators.generateProcessingOrderId()
                parentProcessId =
                    if (parentProcessIdVal == null) {
                        throw Exception(
                            "When IsAttached == true then parentProcessId must be provided",
                        )
                    } else {
                        parentProcessIdVal
                    }
            }
        }
    }

    private fun startEinObtainmentProcessInstance(variables: Variables = buildVariables()): ProcessInstance {
        val processInstance =
            runtimeService.startProcessInstanceByKey(
                Constants.EIN_OBTAINMENT_PROCESS,
                variables.processingOrderId.toString(),
                variables,
            )

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        return processInstance
    }

    private fun getHistoricRpaProcess(
        parentProcessInstance: ProcessInstance,
        processDefinitionKey: String,
    ): HistoricProcessInstance? {
        return historyService.createHistoricProcessInstanceQuery()
            .processDefinitionKey(processDefinitionKey)
            .superProcessInstanceId(parentProcessInstance.processInstanceId)
            .list().singleOrNull()
    }

    private fun botSuccessVars(
        processInstance: ProcessInstance,
        evidenceTransactionNumber: String? = null,
    ) = variables {
        status = "Success"
        this.evidenceTransactionNumber = evidenceTransactionNumber
        documentPaths =
            mutableListOf(
                "s3://rpa-storage-prod/Jobs_Data/EIN_FEDERAL/d581f243-0800-11ef-977f-226c103580c3/" +
                    "Proof_Of_Work/PoW_d581f243-0800-11ef-977f-226c103580c3.pdf",
            )
        evidenceFilePath = "s3://rpa-storage-prod/Jobs_Data/EIN_FEDERAL/d581f243-0800-11ef-977f-226c103580c3/Proof_Of_Work/" +
            "EIN1-${processInstance.businessKey}.pdf"
    }

    private fun botFailureVars(
        processInstance: ProcessInstance,
        message: String? = null,
    ) = variables {
        status = "Failure"
        evidenceFilePath = "s3://rpa-storage-prod/Jobs_Data/EIN_FEDERAL/d581f243-0800-11ef-977f-226c103580c3/Proof_Of_Work/" +
            "EIN1-${processInstance.businessKey}.pdf"
        this.message = message
    }

    private fun awaitThenSimulateRpaResponse(
        processInstance: ProcessInstance,
        variables: Variables,
    ) {
        val rpaProcessInstance =
            await(Duration.of(1, ChronoUnit.MINUTES))
                .untilNotNull {
                    processEngine.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(RPA_BOT_PROCESS)
                        .superProcessInstanceId(processInstance.id)
                        .active().list().singleOrNull()
                }

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(rpaProcessInstance).isWaitingAt("rpa-task") }

        processEngine.runtimeService.createMessageCorrelation("Message_RPA")
            .processInstanceBusinessKey(processInstance.businessKey)
            .setVariables(variables)
            .correlate()
    }

    private fun assertDocUploaded(
        processId: Int,
        documentType: String,
    ) {
        var expectedCustomerDocumentType =
            findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                // orderContext
                any(),
                expectedCustomerDocumentType,
                // resourceWithType
                any(),
                DocumentStatus.Active,
                expectedCustomerDocumentType.alwaysActive ?: false,
            )
        }
    }

    private fun assertBusinessEntityUpdated(ein: String) {
        // verify that business entities was updated.  note that this call doesn't directly take processing order id
        // so I can't directly verify that there's no mixup of child versus parent processing order id.  However there's
        // a GET call that does take processingOrderId and where that's mocked in this file it's mocked only
        // for the expected processing order id thus it's an indirect verification.
        verify(exactly = 1) {
            businessEntitiesApi.businessEntitiesEntityIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                match {
                    it.request!!.ein == ein
                },
            )
        }
    }

    private fun assertQuestionnaireAnswerServicePut(
        fieldName: String,
        fieldValue: String,
        processingOrderId: Int,
    ) {
        verify(exactly = 1) {
            questionnaireAnswerService.putPartialUpdate(
                any(),
                match {
                    it.questionnaireFieldGroupAnswers.fieldAnswers.first().fieldName == fieldName &&
                        it.questionnaireFieldGroupAnswers.fieldAnswers.first().fieldValue == fieldValue
                    it.questionnaireFieldGroupAnswers.processingOrderId == processingOrderId
                },
            )
        }
    }

    private fun assertProcessingOrderStatus(
        processingOrderId: Int,
        status: Int,
    ) {
        verify(exactly = 1) {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                processingOrderId,
                status,
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        "false,,'BusinessTypeLLC'",
        "true,2,",
        "true,18,",
    )
    fun testHappyPath(
        attached: Boolean,
        parentProcessIdVal: Int?,
        businessTypeVal: String?,
    ) {
        val variables =
            buildVariables().apply {
                isAttachedOrder = attached
                parentProcessId = if (attached) parentProcessIdVal else null
                parentProcessingOrderId = if (attached) RandomValueGenerators.generateProcessingOrderId() else null
            }
        setup(variables)
        if (businessTypeVal != null) mockQuestionnaireAnswerService(variables, ein = null, businessType = businessTypeVal)
        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        mockEinAnswerService(this.ein)

        awaitAndGetChildProcessInstance(processInstance, "rpa-bot-process")

        val processingOrderDto = ProcessingOrderDto()
        processingOrderDto.processingStatusId = ProcessingOrderStatus.EinFilingStarted.processingStatusId
        processingOrderDto.processId = ProductType.EIN.processId
        val getProcessingOrderResponse = GetProcessingOrderResponse()
        getProcessingOrderResponse.processingOrder = processingOrderDto

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(variables.processingOrderId!!, any(), any(), any())
        } returns Mono.just(getProcessingOrderResponse)

        awaitThenSimulateRpaResponse(processInstance, botSuccessVars(processInstance, ein))

        if (!attached) verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        if (attached) assertNull(getHistoricRpaProcess(processInstance, PRINT_PROCESS))

        assertDocUploaded(variables.processId!!, DocumentType.EINProofOfWork.name)
        assertDocUploaded(variables.processId!!, DocumentType.IRSAssignedEIN.name)

        assertQuestionnaireAnswerServicePut(
            fieldName = EIN.FieldName,
            fieldValue = ein,
            processingOrderId = if (attached) variables.parentProcessingOrderId!! else variables.processingOrderId!!,
        )

        if (attached && parentProcessIdVal != DBA.processId) {
            assertBusinessEntityUpdated(ein)
        } else {
            verify(exactly = 0) {
                businessEntitiesApi.businessEntitiesEntityIdPut(any(), any(), any(), any(), any(), any())
            }
        }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingComplete.processingStatusId)

        verify(exactly = 1) {
            activityFeedServiceImpl.sendEvent(ProcessingOrderStatus.EinFilingComplete, any())
        }

        val fieldUpdatedEvent = capturedFulfillmentEvents.first()
        assertThat(fieldUpdatedEvent.eventPhase).isEqualTo(EventPhase.EIN_FILING)
        assertThat(fieldUpdatedEvent.eventType).isEqualTo(EventType.FIELD_UPDATED)
        @Suppress("unchecked_cast")
        val data = fieldUpdatedEvent.data as? Map<String, *>
        assertThat(data).containsEntry("processId", 49)
        assertThat(
            data,
        ).containsEntry(
            "processingOrderId",
            if (attached) variables.parentProcessingOrderId.toString() else variables.processingOrderId.toString(),
        )
        assertThat(data).containsEntry("customerId", variables.customerId)
        assertThat(data).containsEntry("fieldName", "EIN")

        val einReceivedEvent = capturedFulfillmentEvents[1]
        assertThat(einReceivedEvent.eventPhase).isEqualTo(EventPhase.EIN_FILING)
        assertThat(einReceivedEvent.eventType).isEqualTo(EventType.EIN_RECEIVED)
        assertThat(
            einReceivedEvent.processingOrderId,
        ).isEqualTo(if (attached)variables.parentProcessingOrderId.toString() else variables.processingOrderId.toString())
        val einReceivedData = einReceivedEvent.data as ValidationResult
        assertThat(einReceivedData.evidenceTransactionNumber).isEqualTo(ein)
        if (attached) {
            assertThat(einReceivedData.childProcessingOrderId).isEqualTo(variables.processingOrderId.toString())
        }
    }

    @Test
    fun testSS4OrcoPath() {
        val variables =
            buildVariables().apply {
                isAttachedOrder = true
                parentProcessId = 2
                parentProcessingOrderId = RandomValueGenerators.generateProcessingOrderId()
            }
        setup(variables)
        mockFeatureToggleService(isSsorcoEnabled = true, isSS4SSOrcoEnabled = true)
        mockSs4OrcoService(requiresSs4PreparedDoc = true)
        mockRevvCalls(templates = mutableListOf(SS4_PREPARED))
        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botFailureVars(processInstance, REF_101_PREFIX))

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("ein-salesforce-activity") }

        mockRevvCalls()

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, activityId = "ein-salesforce-activity", inSfProcess = true)
        mockEinAnswerService(this.ein)
        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, variables = variables { disposition = Proceed.value })

        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }
        verify(exactly = 1) {
            orcoService.save(
                match {
                    assertThat(it.reasons).contains(ReasonRequest(EIN_SS4_ORCO_CAT_ID, REF_101_PREFIX, OPEN))
                    true
                },
            )
        }

        verify(exactly = 1) {
            revvDocumentGenerationService.getRequestedDocumentTemplates(mutableListOf(SS4_PREPARED.name))
        }

        verify(exactly = 1) {
            revvDocumentGenerationService.uploadDocument(
                any(),
                any(),
                any(),
                findCustomerDocTypeFromProductName(ProductType.EIN.productName, "SS4 Prepared"),
                any(),
                any(),
                any(),
                true,
            )
        }
    }

    @Test
    fun testSS4OrcoPath_irsIssueVariation() {
        val variables =
            buildVariables().apply {
                isAttachedOrder = true
                parentProcessId = 2
                parentProcessingOrderId = RandomValueGenerators.generateProcessingOrderId()
            }
        setup(variables)
        mockFeatureToggleService(isSsorcoEnabled = true, isSS4SSOrcoEnabled = true)
        mockSs4OrcoService(requiresSs4PreparedDoc = true)
        mockRevvCalls(templates = mutableListOf(SS4_PREPARED))
        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botFailureVars(processInstance, REF_101_PREFIX))

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("ein-salesforce-activity") }

        mockRevvCalls()

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            activityId = "ein-salesforce-activity",
            caseDisposition = FulfillmentDisposition.EFaxedToIRS,
        )
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            activityId = "ein-salesforce-activity-awaiting-irs",
            inSfProcess = true,
            caseDisposition = FulfillmentDisposition.IRSIssue,
        )
        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, activityId = "ein-salesforce-activity", inSfProcess = true)

        assertEquals(3, capturedSalesForceRequests.count())
        val caseRequest = capturedSalesForceRequests.last()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assertThat(caseRequest.processId).isEqualTo("49")
    }

    @Test
    @Disabled
    fun einOrderItemIsCancelled() {
        val variables =
            buildVariables().apply {
                isAttachedOrder = true
                parentProcessId = 2
                parentProcessingOrderId = RandomValueGenerators.generateProcessingOrderId()
            }
        setup(variables)
        mockOrdersApiService(variables)
        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertNull(getHistoricRpaProcess(processInstance, RPA_BOT_PROCESS))
    }

    @ParameterizedTest
    @CsvSource(
        "'BusinessTypeLLC', 2",
        "'BusinessTypeNP', 20",
        "'BusinessTypeCCORP', 1",
        "'BusinessTypeSCORP', 1",
        "'BusinessTypeDBAPartner', 18",
        "'BusinessTypeDBASoleProp', 18",
        "'BusinessTypeNP', 20",
    )
    fun testBotBypassing(
        businessType: String,
        processId: Int,
    ) {
        val variables = buildVariables(isAttached = false)
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = null, businessType = businessType)
        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, null, processId)
        } returns true

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("ein-salesforce-activity")
        val caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assertThat(caseRequest.processId).isEqualTo("49")

        assertNull(getHistoricRpaProcess(processInstance, RPA_BOT_PROCESS))
    }

    @ParameterizedTest
    @CsvSource(
        "true,2",
        "false,null",
    )
    fun `Bot not supported flow then manual file`(
        isAttached: Boolean,
        parentProcessId: String?,
    ) {
        val variables = buildVariables(isAttached = isAttached, parentProcessIdVal = parentProcessId?.toIntOrNull())
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = null, businessType = "BusinessTypeLLC")

        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, null, ProductType.LLC.processId)
        } returns true

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)

        // simulate that user entered ein in proofer
        mockQuestionnaireAnswerService(variables, ein = this.ein, businessType = "BusinessTypeLLC")
        mockEinAnswerService(this.ein)

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "Manual EIN obtainment required",
                            data =
                                mapOf(
                                    "Detail" to "This order requires manual EIN obtainment. Please manually obtain the EIN and" +
                                        " then select \"Skip-I Have Manually Obtained EIN\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        assertThat(caseRequest.processId).isEqualTo(ProductType.EIN.processId.toString())

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, "ein-salesforce-activity", inSfProcess = true)

        if (!isAttached) {
            verifyNewPrintAndShip(processInstance, variables)
        }
        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        if (isAttached) assertBusinessEntityUpdated(ein)

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingComplete.processingStatusId)
    }

    // variation of bot-not-supported-then-manual-file path
    @Test
    fun `Bot not supported flow then manual file with no EIN number entered`() {
        val variables = buildVariables(isAttached = false)
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = null, businessType = "BusinessTypeLLC")
        mockEinAnswerService(null)

        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, null, ProductType.LLC.processId)
        } returns true

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("ein-salesforce-activity")

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "Manual EIN obtainment required",
                            data =
                                mapOf(
                                    "Detail" to "This order requires manual EIN obtainment. Please manually obtain the EIN and " +
                                        "then select \"Skip-I Have Manually Obtained EIN\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        assertThat(caseRequest.processId).isEqualTo(ProductType.EIN.processId.toString())

        capturedSalesForceRequests.clear()

        mockLedgerNoteHelperService()

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, "ein-salesforce-activity", inSfProcess = true)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("validate-ein-number-task") }

        assertThat(processInstance).variables().contains(
            java.util.Map.entry("validationError", true),
        )

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("ein-salesforce-activity")

        validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "EIN number is missing.",
                            data =
                                mapOf(
                                    "Detail" to "Please enter and save the EIN number, then select \"Skip-I Have Manually" +
                                        " Obtained EIN\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        assertThat(caseRequest.processId).isEqualTo(ProductType.EIN.processId.toString())

        mockEinAnswerService(this.ein)

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, "ein-salesforce-activity", inSfProcess = true)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("ein-obtained-gateway") }

        assertThat(processInstance).variables().contains(
            java.util.Map.entry("validationError", false),
        )
        verifyNewPrintAndShip(processInstance, variables)
        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingComplete.processingStatusId)
    }

    // variation of bot-not-supported-then-manual-file path
    @Test
    fun `Bot not supported flow then manual file with no document uploaded`() {
        val variables = buildVariables(isAttached = false)
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = "NO", businessType = "BusinessTypeLLC")

        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, null, ProductType.LLC.processId)
        } returns true

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("ein-salesforce-activity")

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "Manual EIN obtainment required",
                            data =
                                mapOf(
                                    "Detail" to "This order requires manual EIN obtainment. Please manually obtain the EIN and " +
                                        "then select \"Skip-I Have Manually Obtained EIN\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        assertThat(caseRequest.processId).isEqualTo(ProductType.EIN.processId.toString())

        capturedSalesForceRequests.clear()

        mockEinAnswerService(this.ein)
        mockLedgerNoteHelperService()

        val documents = objectMapper.readValue<List<Document>>(ClassPathResource("saein_documents.json", javaClass).file)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns
            documents.filter {
                it.documentType != DocumentType.IRSAssignedEIN.displayName &&
                    it.documentType != DocumentType.FormSS4Completed.displayName
            }

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, "ein-salesforce-activity", inSfProcess = true)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("validate-ein-number-task") }

        assertThat(processInstance).variables().contains(
            java.util.Map.entry("validationError", true),
        )

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("ein-salesforce-activity")

        validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "IRS documents are missing.",
                            data =
                                mapOf(
                                    "Detail" to "Please upload documents, then select \"Skip-I Have Manually Obtained EIN" +
                                        "\" to proceed",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        assertThat(caseRequest.processId).isEqualTo(ProductType.EIN.processId.toString())

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns documents

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, "ein-salesforce-activity", inSfProcess = true)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("ein-obtained-gateway") }

        assertThat(processInstance).variables().contains(
            java.util.Map.entry("validationError", false),
        )
        verifyNewPrintAndShip(processInstance, variables)
        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingComplete.processingStatusId)
    }

    @Test
    fun `Bot not supported flow then manual file with EIN number entered and document uploaded`() {
        val variables = buildVariables(isAttached = false)
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = null, businessType = "BusinessTypeLLC")

        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, null, ProductType.LLC.processId)
        } returns true

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)

        // simulate that while on the SF case the user enteres the EIN
        mockQuestionnaireAnswerService(variables, ein = null, businessType = "BusinessTypeLLC")
        mockEinAnswerService(this.ein)

        var validationResult =
            ValidationResult(
                passed = false,
                errors =
                    mutableListOf(
                        ValidationError(
                            "Manual EIN obtainment required",
                            data =
                                mapOf(
                                    "Detail" to "This order requires manual EIN obtainment. Please manually obtain the EIN and " +
                                        "then select \"Skip-I Have Manually Obtained EIN\"",
                                ),
                        ),
                    ),
                isSelfServeOrco = false,
            )

        var caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo("EIN_FILING")
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assert(((caseRequest.exceptions.first().optionalData as Map<*, *>)["validationResult"]!! as ValidationResult) == validationResult)
        assertThat(caseRequest.processId).isEqualTo(ProductType.EIN.processId.toString())

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, "ein-salesforce-activity", inSfProcess = true)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("ein-obtained-gateway") }

        assertThat(processInstance).variables().contains(
            java.util.Map.entry("validationError", false),
        )
        verifyNewPrintAndShip(processInstance, variables)
        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingComplete.processingStatusId)
    }

    @Test
    fun `RPA passed but did not obtain EIN number`() {
        var variables = buildVariables(isAttached = false)
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = "NO", businessType = "BusinessTypeLLC")

        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, null, ProductType.LLC.processId)
        } returns false

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinFilingInProgress.processingStatusId)

        awaitThenSimulateRpaResponse(processInstance, botSuccessVars(processInstance, null))

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("rpa-bot-process") }

        val incident =
            await(Duration.of(1, ChronoUnit.MINUTES))
                .until({ getIncident(processInstance) }, { it != null })

        assertThat(incident?.incidentMessage).contains("An EIN number was not provided in evidenceTransactionNumber process variable")
    }

    @ParameterizedTest
    @CsvSource(
        "true,2",
        "false,null",
    )
    fun `Blocked by related order and resolved by SF case`(
        isAttached: Boolean,
        parentProcessId: String?,
    ) {
        val variables = buildVariables(isAttached, if (parentProcessId == "null") null else parentProcessId!!.toInt())
        setup(variables)

        mockOrdersCustomerApi(variables, blockedScenario = true)
        mockProcessingOrdersApi(variables, blockedScenario = true)

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-waiting-formation") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinAwaitingFormation.processingStatusId)

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance = processInstance,
            activityId = "salesforce-activity-await-related-order",
            inSfProcess = true,
            caseDisposition = Proceed,
        )

        val caseRequest = capturedSalesForceRequests.single()
        assertThat(caseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.AWAITING_DEPENDENT_ORDER.toString())
        assertThat(caseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(caseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        assertThat(caseRequest.processId).isEqualTo("49")

        awaitThenSimulateRpaResponse(processInstance, botSuccessVars(processInstance, ein))
        if (!isAttached) verifyNewPrintAndShip(processInstance, variables)
        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }
    }

    @Test
    fun `Blocked by related order and resolved by SF case then rpa failure`() {
        val variables = buildVariables(isAttached = true, parentProcessIdVal = 2)
        setup(variables)

        mockOrdersCustomerApi(variables, blockedScenario = true)
        mockProcessingOrdersApi(variables, blockedScenario = true)

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-waiting-formation") }

        assertProcessingOrderStatus(variables.processingOrderId!!, ProcessingOrderStatus.EinAwaitingFormation.processingStatusId)

        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance = processInstance,
            activityId = "salesforce-activity-await-related-order",
            inSfProcess = true,
            caseDisposition = Proceed,
        )

        awaitThenSimulateRpaResponse(processInstance, botFailureVars(processInstance, message = "something bad"))

        salesforceHelper.awaitAndResolveSalesforceCase(processInstance, caseDisposition = FulfillmentDisposition.Retry)

        val secondCaseRequest = capturedSalesForceRequests[1]
        assertThat(secondCaseRequest.exceptions.first().eventPhase).isEqualTo(EventPhase.EIN_FILING.toString())
        assertThat(secondCaseRequest.exceptions.first().type).isEqualTo(SalesforceExceptionType.Exception)
        assertThat(secondCaseRequest.exceptions.first().eventType).isEqualTo(EventType.VALIDATION_COMPLETE.toString())

        awaitThenSimulateRpaResponse(processInstance, botSuccessVars(processInstance, evidenceTransactionNumber = "123"))

        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }
    }

    @Test
    @Disabled
    fun `Blocked by related order and resolved by change in condition evaluation`() {
        val variables =
            buildVariables().apply {
                isAttachedOrder = false
                parentProcessId = null
                parentProcessingOrderId = null
            }
        setup(variables)

        mockOrdersCustomerApi(variables, blockedScenario = true)

        val processInstance = startEinObtainmentProcessInstance(variables)

        salesforceHelper.awaitSalesforceCaseForActivity(
            processInstance = processInstance,
            activityId = "salesforce-activity-await-related-order",
            inSfProcess = true,
        )

        mockOrdersCustomerApi(variables, blockedScenario = false)

        awaitThenSimulateRpaResponse(processInstance, botSuccessVars(processInstance, ein))
        verifyNewPrintAndShip(processInstance, variables)
        await(Duration.of(1, ChronoUnit.MINUTES)).untilAsserted { assertThat(processInstance).isEnded }
    }

    @Test
    fun `Standalone EIN cancellation`() {
        val variables = buildVariables(isAttached = false)
        setup(variables)

        mockQuestionnaireAnswerService(variables, ein = null, businessType = "BusinessTypeLLC")
        mockEinAnswerService(null)
        mockLedgerNoteHelperService()

        every {
            featureToggleService.isRpaEINBypassEnabled(variables.customerId, any(), any())
        } returns true

        val processInstance = startEinObtainmentProcessInstance(variables)

        await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("order-status-update-filing-in-progress") }

        salesforceHelper.awaitSalesforceCaseForActivity(processInstance, "ein-salesforce-activity", inSfProcess = true)
        assertThat(processInstance).isWaitingAt("ein-salesforce-activity")

        // Cancellation
        orderCancellationService.cancelOrder(
            OrderCancelMessage(
                customerId = variables.customerId!!.toLong(),
                processingOrderId = variables.processingOrderId.toString(),
                processId = variables.processId!!.toLong(),
                state = variables.jurisdiction!!,
                workOrderId = null,
                orderId = variables.orderId!!.toLong(),
            ),
        )

        await(Duration.of(30, ChronoUnit.SECONDS)).untilAsserted { assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            orcoService.updateReason(any(), any(), any())
        }

        verify(exactly = 1) {
            salesforceApiService.addLedgerNote(any())
        }

        val einReceivedEvent = capturedFulfillmentEvents[1]
        assertThat(einReceivedEvent.eventPhase).isEqualTo(EventPhase.EIN_FILING)
        assertThat(einReceivedEvent.eventType).isEqualTo(EventType.ORDER_CANCELLED)
    }
}

class FakeAnswersPayload : GetAnswersPayload {
    override var ein: String? = null
    override var effectiveDate: String? = null
    override var entityName: String? = null
    override var stateEntityNumber: String? = null
    override var businessCounty: String? = null
    override var registeredAgentCounty: String? = null
    override var manualFilingEIN: String? = null
    override var einUnavailableReason: String? = null
    override var entityType: String? = null
    override var jurisdiction: String? = null
    override var convertEntityTo: String? = null
    override var schemaName: String? = null
    override var nameChange: String? = null
    override var otherChange: String? = null
    override var businessType: String? = null
    override var paygovTrackingId: String? = null
    override var agencyTrackingId: String? = null
    override var submissionDate1023EZ: String? = null
    override val registrantType: String? = null
}
