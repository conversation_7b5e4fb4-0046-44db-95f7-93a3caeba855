package com.legalzoom.fulfillment.workflow.service

import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants.EIN_SS4_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.Constants.SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.Constants.SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.AUTHENTICATION_ERROR_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.EIN_APPLICATION_ERROR_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.FORCE_ADDRESS_ERROR
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.FORCE_NAME_ERROR
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.FORCE_REF_ERROR
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.INVALID_ADDRESS_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.INVALID_BUSINESS_LOCATION_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.INVALID_NAME_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.REF_101_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.REF_105_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.REF_109_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.REF_110_PREFIX
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService.Companion.REF_114_PREFIX
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource

@ExtendWith(MockKExtension::class)
class RpaSosSsorcoServiceTest {
    @MockK
    private lateinit var featureToggleService: ORCOFeatureToggleService

    @InjectMockKs
    private lateinit var rpaSosSsorcoService: RpaSosSsorcoService

    private val variables =
        variables {
            processId = 2
        }

    companion object {
        @JvmStatic
        fun createValidationErrorArguments() =
            listOf(
                // LLC name
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 2
                    },
                    true,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 2
                    },
                    false,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 2
                    },
                    true,
                    false,
                    true,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 2
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                // INC name
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 1
                    },
                    false,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    false,
                    true,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_NAME_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                // LLC address
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 2
                    },
                    true,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 2
                    },
                    false,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 2
                    },
                    true,
                    false,
                    true,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 2
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                // INC address
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 1
                    },
                    false,
                    true,
                    true,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    false,
                    true,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_ADDRESS_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                // SS4
                arguments(
                    REF_101_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        REF_101_PREFIX,
                        data = mapOf("Detail" to REF_101_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    REF_105_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        REF_105_PREFIX,
                        data = mapOf("Detail" to REF_105_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    REF_109_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        REF_109_PREFIX,
                        data = mapOf("Detail" to REF_109_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    REF_110_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        REF_110_PREFIX,
                        data = mapOf("Detail" to REF_110_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    REF_114_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        REF_114_PREFIX,
                        data = mapOf("Detail" to REF_114_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    INVALID_BUSINESS_LOCATION_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        INVALID_BUSINESS_LOCATION_PREFIX,
                        data = mapOf("Detail" to INVALID_BUSINESS_LOCATION_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    EIN_APPLICATION_ERROR_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        EIN_APPLICATION_ERROR_PREFIX,
                        data = mapOf("Detail" to EIN_APPLICATION_ERROR_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    AUTHENTICATION_ERROR_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        AUTHENTICATION_ERROR_PREFIX,
                        data = mapOf("Detail" to AUTHENTICATION_ERROR_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    REF_101_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        REF_101_PREFIX,
                        data = mapOf("Detail" to REF_101_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    REF_105_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        REF_105_PREFIX,
                        data = mapOf("Detail" to REF_105_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    REF_109_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        REF_109_PREFIX,
                        data = mapOf("Detail" to REF_109_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    REF_110_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        REF_110_PREFIX,
                        data = mapOf("Detail" to REF_110_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    REF_114_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        REF_114_PREFIX,
                        data = mapOf("Detail" to REF_114_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    INVALID_BUSINESS_LOCATION_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        INVALID_BUSINESS_LOCATION_PREFIX,
                        data = mapOf("Detail" to INVALID_BUSINESS_LOCATION_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    EIN_APPLICATION_ERROR_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        EIN_APPLICATION_ERROR_PREFIX,
                        data = mapOf("Detail" to EIN_APPLICATION_ERROR_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                arguments(
                    AUTHENTICATION_ERROR_PREFIX,
                    variables {
                        processId = 1
                    },
                    true,
                    true,
                    false,
                    false,
                    ValidationError(
                        AUTHENTICATION_ERROR_PREFIX,
                        data = mapOf("Detail" to AUTHENTICATION_ERROR_PREFIX),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
                // Other
                arguments(
                    "foo",
                    variables {
                        processId = 2
                    },
                    true,
                    true,
                    true,
                    false,
                    ValidationError(
                        "foo",
                        data = mapOf("Detail" to "foo"),
                        isSelfServe = false,
                        orcoReasonCategoryId = null,
                    ),
                ),
            )

        @JvmStatic
        fun checkCheatCodesArguments() =
            listOf(
                // LLC name
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    true,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    false,
                    true,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    false,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                // INC name
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    true,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    false,
                    true,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    false,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    false,
                    true,
                    ValidationError(
                        INVALID_NAME_PREFIX,
                        data = mapOf("Detail" to INVALID_NAME_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    ),
                ),
                // LLC address
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    true,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    false,
                    true,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    false,
                    true,
                    true,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    false,
                    true,
                    null,
                ),
                // INC address
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    true,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    false,
                    true,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    false,
                    true,
                    true,
                    ValidationError(
                        INVALID_ADDRESS_PREFIX,
                        data = mapOf("Detail" to INVALID_ADDRESS_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_ADDRESS_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    false,
                    true,
                    null,
                ),
                // SS4
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_REF_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    true,
                    ValidationError(
                        REF_101_PREFIX,
                        data = mapOf("Detail" to REF_101_PREFIX),
                        isSelfServe = true,
                        orcoReasonCategoryId = EIN_SS4_ORCO_CAT_ID,
                    ),
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_REF_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    false,
                    true,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_REF_ERROR
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    false,
                    null,
                ),
                // Already applied cheat
                arguments(
                    variables {
                        processId = 2
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                        alreadyAppliedSsorcoCheat = true
                    },
                    true,
                    true,
                    true,
                    true,
                    null,
                ),
                arguments(
                    variables {
                        processId = 1
                        entityName = FORCE_NAME_ERROR
                        alreadyAppliedSsorcoCheat = false
                        alreadyAppliedSsorcoCheat = true
                    },
                    true,
                    true,
                    true,
                    true,
                    null,
                ),
                // others
                arguments(
                    variables {
                        processId = 2
                        entityName = "foo"
                        alreadyAppliedSsorcoCheat = false
                    },
                    true,
                    true,
                    true,
                    true,
                    null,
                ),
            )
    }

    @BeforeEach
    fun setup() {
        every {
            featureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isSsSosNameOrcoEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isSsSosAddressOrcoEnabled(any(), any(), any())
        } returns true
    }

    @ParameterizedTest
    @MethodSource("createValidationErrorArguments")
    fun testCreateValidationError(
        errorMessage: String,
        variables: Variables,
        isSsOrcoEnabled: Boolean,
        isSsSosNameOrcoEnabled: Boolean,
        isSsSosAddressOrcoEnabled: Boolean,
        isSS4SSOrcoEnabled: Boolean,
        expectedError: ValidationError?,
    ) {
        every {
            featureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns isSsOrcoEnabled

        every {
            featureToggleService.isSsSosNameOrcoEnabled(any(), any(), any())
        } returns isSsSosNameOrcoEnabled

        every {
            featureToggleService.isSsSosAddressOrcoEnabled(any(), any(), any())
        } returns isSsSosAddressOrcoEnabled

        every {
            featureToggleService.isSS4SSOrcoEnabled(any(), any(), any())
        } returns isSS4SSOrcoEnabled

        val validationError = rpaSosSsorcoService.createValidationError(errorMessage, variables)
        assertThat(validationError).isEqualTo(expectedError)
    }

    @ParameterizedTest
    @MethodSource("checkCheatCodesArguments")
    fun testCheckCheatCodes(
        inVars: Variables,
        isSsOrcoEnabled: Boolean,
        isSsSosNameOrcoEnabled: Boolean,
        isSsSosAddressOrcoEnabled: Boolean,
        isSS4SSOrcoEnabled: Boolean,
        expectedError: ValidationError?,
    ) {
        every {
            featureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns isSsOrcoEnabled

        every {
            featureToggleService.isSsSosNameOrcoEnabled(any(), any(), any())
        } returns isSsSosNameOrcoEnabled

        every {
            featureToggleService.isSsSosAddressOrcoEnabled(any(), any(), any())
        } returns isSsSosAddressOrcoEnabled

        every {
            featureToggleService.isSS4SSOrcoEnabled(any(), any(), any())
        } returns isSS4SSOrcoEnabled

        rpaSosSsorcoService.checkCheatCodes(inVars)

        if (expectedError != null) {
            assertThat(inVars.validationErrors).containsOnly(expectedError)
        } else {
            assertThat(inVars.validationErrors).isNull()
        }
    }

    @Test
    fun testGetExistingErrorMessage() {
        val messageError = INVALID_NAME_PREFIX
        val validationError = rpaSosSsorcoService.createValidationError(messageError, variables)
        assertNotNull(validationError.orcoReasonCategoryId)
    }

    @Test
    fun testGetNonExistingErrorMessage() {
        val errorMessage = "Message does not exist."
        val validationError = rpaSosSsorcoService.createValidationError(errorMessage, variables)
        val expectedError =
            ValidationError(
                errorMessage,
                mapOf("Detail" to errorMessage),
            )
        assertEquals(validationError, expectedError)
    }
}
