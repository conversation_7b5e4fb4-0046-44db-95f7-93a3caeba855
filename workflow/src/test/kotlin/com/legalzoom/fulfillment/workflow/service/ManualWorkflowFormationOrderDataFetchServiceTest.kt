package com.legalzoom.fulfillment.workflow.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.extensions.findOrderItemByProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.FilingDataComposite
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.SpykBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@SpringBootTest
@ExtendWith(MockKExtension::class)
class ManualWorkflowFormationOrderDataFetchServiceTest(
    val manualWorkflowFormationOrderDataFetchService: ManualWorkflowFormationOrderDataFetchService,
) {
    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    @MockK
    private lateinit var execution: DelegateExecution

    @SpykBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @SpykBean
    private lateinit var ordersApiService: OrdersApiService

    @SpykBean
    private lateinit var orderItemsApiService: OrdersOrderItemsApiService

    @BeforeEach
    fun setUp() {
        clearAllMocks()
    }

    @ParameterizedTest
    @CsvSource(
        // processId, customerId, processingOrderId, orderId, mock answers, mock order response, expectedEntityName
        "24, 34174311, 572673893, 81492413, llp_answers_api_questionnaire_field_group_answers.json, llp_order_response.json, " +
            "LegalZoom Unit testing LLP, null",
        "23, 43688164, 556170442, 26083625, lp_answers_api_questionnaire_field_group_answers.json, lp_order_response.json, " +
            "Clark Jackson LP, null",
        "18, 55316745, 573542352, 81892887, dba_gp_answers_api_questionnaire_field_group_answers.json, dba_gp_order_response.json, " +
            "Weeb Works, Partnership",
        "18, 7532196, 573646649, 81935928, dba_inc_answers_api_questionnaire_field_group_answers.json, dba_inc_order_response.json, " +
            "O2 Pilates, Inc",
    )
    fun testFetchData(
        processId: Int,
        customerId: String,
        processingOrderId: Int,
        orderId: Int,
        answersFileName: String,
        orderResponseFileName: String,
        expectedEntityName: String,
        registrantType: String?,
    ) {
        every { execution.input } returns
            variables {
                this.processId = processId
                this.processingOrderId = processingOrderId
            }
        val answersApiJSON = ClassPathResource(answersFileName, javaClass).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJSON)

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            GetAnswersComposite(
                processId,
                answersApiResponse,
            )

        every {
            questionnaireAnswerService.getFilingData(any(), any(), any(), any())
        } returns
            FilingDataComposite(
                processId,
                answerSource = AnswerSource.AnswerBank,
                answerApiData = null,
                modelOneAnswer = null,
            )

        every {
            orderItemsApiService.getOrdersOrderItems(processingOrderId, any(), any(), any())
        } returns GetOrderItemResponse().orderId(orderId)

        val orderResponseApiJSON = ClassPathResource(orderResponseFileName, javaClass).file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(orderResponseApiJSON)
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse
        val expectedRegistrantType = if (registrantType == "null") null else registrantType

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        manualWorkflowFormationOrderDataFetchService.fetchData(
            execution,
            execution.input.processingOrderId!!,
            customerId,
        )

        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        }

        assertThat(variables.first()).containsAllEntriesOf(
            variables {
                entityName = expectedEntityName
                einFilingEnabled = orderResponse.order?.findOrderItemByProductType(ProductType.EIN.processId) != null
                this.registrantType = expectedRegistrantType
            },
        )
    }
}
