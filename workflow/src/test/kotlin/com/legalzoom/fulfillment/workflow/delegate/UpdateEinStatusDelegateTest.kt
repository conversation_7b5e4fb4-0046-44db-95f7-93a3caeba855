package com.legalzoom.fulfillment.workflow.delegate

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.order.OrderItemDto
import com.legalzoom.api.model.order.ProcessingOrderDto
import com.legalzoom.api.model.order.ProductConfigurationDto
import com.legalzoom.api.model.order.RelationshipType
import com.legalzoom.fulfillment.service.data.activityFeed.ActivityFeedVariables
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.activityFeed.ActivityFeedServiceImpl
import com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.EIN
import com.legalzoom.fulfillment.service.service.questionnaire.Constants.FieldType.ManualFilingEIN
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.slot
import io.mockk.verify
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@ExtendWith(MockKExtension::class)
class UpdateEinStatusDelegateTest {
    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    @MockK
    private lateinit var ordersApiService: OrdersApiService

    @MockK
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockK
    private lateinit var processingOrderService: ProcessingOrderService

    @MockK
    private lateinit var activityFeedServiceImpl: ActivityFeedServiceImpl

    @MockK
    private lateinit var execution: DelegateExecution

    @InjectMockKs
    private lateinit var delegate: UpdateEinStatusDelegate

    companion object {
        const val TEST_CUSTOMER_ID = "1"
        const val TEST_ORDER_ID = 2345
        const val TEST_PROCESSING_ORDER_ID = 511205226
        const val TEST_CHILD_PROCESSING_ID = 564997818
        const val TEST_EIN = "33-3333333"
    }

    private val testProcessId = ProductType.LLC.processId

    @BeforeEach
    fun setUp() {
        clearAllMocks()

        every {
            processingOrderService.updateProcessingOrderStatus(any(), any(), any(), any())
        } returns Unit

        every {
            activityFeedServiceImpl.sendEvent(any(), any<ActivityFeedVariables>())
        } returns null

        mockOrderResponse()
    }

    private fun mockOrderResponse() {
        val orderResponseJson =
            ClassPathResource("com/legalzoom/fulfillment/workflow/integration/GetOrderByIdResponse.json").file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(orderResponseJson)

        val productComponentDto = ProductConfigurationDto()
        productComponentDto.productTypeId(RelationshipType.NUMBER_9)
        productComponentDto.productConfigurationId(5793)
        productComponentDto.shouldDisplayOnBill(false)

        val processingOrderDto = ProcessingOrderDto()
        processingOrderDto.processId(ProductType.EIN.processId)
        processingOrderDto.processingStatusId(634)
        processingOrderDto.processingOrderId(564997818)
        val orderItem = OrderItemDto()
        orderItem.orderId(34796367)
        orderItem.orderItemId(69461055)
        orderItem.parentOrderItemId(69461054)
        orderItem.stateId(0)
        orderItem.basePrice(0.0)
        orderItem.extendedPrice(0.0)
        orderItem.quantity(1)
        orderItem.productConfiguration(productComponentDto)
        orderItem.isCancelled(true)
        orderItem.productName("EIN Obtainment")
        orderItem.isShipped(false)
        orderItem.shipMethodId(null)
        orderItem.lineNumber(2)
        orderItem.createdBy("16904728")
        orderItem.lastModifiedBy("AWSStepFunction")
        orderItem.processingOrder(processingOrderDto)
        orderItem.isCancelled(false)
        orderResponse.order?.orderItems?.add(orderItem)

        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse
    }

    @Test
    fun testUpdateEinStatusDelegateManualEINFilingSkipsEinObtainment() {
        every { execution.input } returns
            variables {
                customerId = TEST_CUSTOMER_ID
                orderId = TEST_ORDER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
            }
        val json =
            ClassPathResource(
                "com/legalzoom/fulfillment/workflow/service/answers_api_questionnaire_field_group_answers.json",
            ).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(json)

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any { it.fieldName == ManualFilingEIN.FieldName } == false) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297949
            fieldAnswerDto.fieldName = ManualFilingEIN.FieldName
            fieldAnswerDto.fieldValue = "Yes"
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                testProcessId,
                answersApiResponse,
            )

        val variables = slot<Variables>()
        justRun { execution.variables = capture(variables) }

        delegate.execute(execution)

        verify {
            processingOrderService.updateProcessingOrderStatus(
                TEST_CHILD_PROCESSING_ID,
                TEST_CUSTOMER_ID,
                ProcessingOrderStatus.EinPreliminaryProblem,
                any(),
            )
        }
    }

    @Test
    fun testUpdateEinStatusDelegateManualEINFilingWithEIN() {
        every { execution.input } returns
            variables {
                customerId = TEST_CUSTOMER_ID
                orderId = TEST_ORDER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
            }
        val json =
            ClassPathResource(
                "com/legalzoom/fulfillment/workflow/service/answers_api_questionnaire_field_group_answers.json",
            ).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(json)

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any { it.fieldName == ManualFilingEIN.FieldName } == false) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297949
            fieldAnswerDto.fieldName = ManualFilingEIN.FieldName
            fieldAnswerDto.fieldValue = "Yes"
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any { it.fieldName == EIN.FieldName } == false) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297942
            fieldAnswerDto.fieldName = EIN.FieldName
            fieldAnswerDto.fieldValue = ""
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                testProcessId,
                answersApiResponse,
            )

        val variables = slot<Variables>()
        justRun { execution.variables = capture(variables) }

        delegate.execute(execution)

        verify {
            processingOrderService.updateProcessingOrderStatus(
                TEST_CHILD_PROCESSING_ID,
                TEST_CUSTOMER_ID,
                ProcessingOrderStatus.EinPreliminaryProblem,
                any(),
            )
        }
    }

    @Test
    fun testUpdateEinStatusDelegateManualEINwithEINObtained() {
        every { execution.input } returns
            variables {
                customerId = TEST_CUSTOMER_ID
                orderId = TEST_ORDER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
            }
        val json =
            ClassPathResource(
                "com/legalzoom/fulfillment/workflow/service/answers_api_questionnaire_field_group_answers.json",
            ).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(json)

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any { it.fieldName == ManualFilingEIN.FieldName } == false) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297949
            fieldAnswerDto.fieldName = ManualFilingEIN.FieldName
            fieldAnswerDto.fieldValue = "Yes"
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        if (answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.any { it.fieldName == EIN.FieldName } == false) {
            val fieldAnswerDto = FieldAnswerDto()
            fieldAnswerDto.fieldId = 297942
            fieldAnswerDto.fieldName = EIN.FieldName
            fieldAnswerDto.fieldValue = TEST_EIN
            answersApiResponse.questionnaireFieldGroupAnswers?.fieldAnswers?.add(fieldAnswerDto)
        }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                testProcessId,
                answersApiResponse,
            )

        val variables = slot<Variables>()
        justRun { execution.variables = capture(variables) }

        delegate.execute(execution)

        verify(exactly = 1) {
            processingOrderService.updateProcessingOrderStatus(
                TEST_CHILD_PROCESSING_ID,
                TEST_CUSTOMER_ID,
                ProcessingOrderStatus.EinFilingComplete,
                any(),
            )
        }
    }
}
