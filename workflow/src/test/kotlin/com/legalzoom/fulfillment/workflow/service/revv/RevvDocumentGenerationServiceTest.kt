package com.legalzoom.fulfillment.workflow.service.revv

import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.businessentities.BusinessEntitiesApi
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.fulfillment.common.enumeration.RelationshipType
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvDocgenRequest
import com.legalzoom.fulfillment.service.data.revv.RevvDocument
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentField
import com.legalzoom.fulfillment.service.data.revv.RevvDocumentType
import com.legalzoom.fulfillment.service.data.revv.RevvMappingFields
import com.legalzoom.fulfillment.service.data.revv.RevvTemplateType
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.workflow.dmn.JsonFileArgumentProvider
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.core.io.ClassPathResource
import reactor.core.publisher.Mono
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import java.util.stream.Stream

@ExtendWith(MockKExtension::class)
class RevvDocumentGenerationServiceTest : RevvMappingFieldsTestBase() {
    // This test will cover RevvDocumentGenerationService, RevvFieldsRequestBuilder and product specific request builder

    @MockK
    private lateinit var execution: DelegateExecution

    @MockK
    private lateinit var ordersContactsApiService: OrdersContactsApiService

    @MockK
    private lateinit var documentService: DocumentService

    @MockK
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockK
    private lateinit var answerApi: AnswerApi

    @MockK
    private lateinit var ordersApiService: OrdersApiService

    @MockK
    private lateinit var businessEntitiesApi: BusinessEntitiesApi

    @MockK
    private lateinit var featureToggleService: FeatureToggleService

    @InjectMockKs
    private lateinit var service: RevvDocumentGenerationService

    companion object {
        const val TEST_ENTITY_NAME = "Test Entity Name LLC"
        const val TEST_CUSTOMER_ID = "12345"
        const val TEST_ORDER_ID = 34796367
        const val TEST_PROCESSING_ORDER_ID = 511205227

        @JvmStatic
        fun revvDocGenEnabledProducts(): Stream<Arguments> =
            Stream.of(
                Arguments.of(
                    ProductType.CertificateofGoodStanding.processId,
                    ProductType.CertificateofGoodStanding.productName,
                ),
                Arguments.of(ProductType.LLP.processId, ProductType.LLP.productName),
                Arguments.of(ProductType.LP.processId, ProductType.LP.productName),
                Arguments.of(ProductType.Amendment.processId, ProductType.Amendment.productName),
                Arguments.of(ProductType.Conversion.processId, ProductType.Conversion.productName),
            )

        @JvmStatic
        fun addressValues(): Stream<Arguments> =
            Stream.of(
                Arguments.of("101 N Brand Blvd", null, "Glendale", "California", "91203", "101 N Brand Blvd\nGlendale, California, 91203"),
                Arguments.of(
                    "101 N Brand Blvd",
                    "test",
                    "Glendale",
                    "California",
                    "91203",
                    "101 N Brand Blvd\ntest\nGlendale, California, 91203",
                ),
            )
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
    }

    @Test
    fun testUploadDocument() {
        mockDocumentService()
        val revvDocgenRequest =
            RevvDocgenRequest(
                TEST_PROCESSING_ORDER_ID.toString(),
                "correlationId",
                TEST_CUSTOMER_ID,
                mutableListOf(
                    RevvDocumentField(
                        RevvMappingFields.DATE.value,
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")).toString(),
                    ),
                ),
                RevvDocument(RevvDocumentType.INITIAL_REPORTS_FINAL_LETTER.revvTemplateName, RevvTemplateType.DOCX),
                "CA",
                TEST_ORDER_ID.toString(),
            )

        service.uploadDocument(
            byteArrayOf(),
            revvDocgenRequest,
            "test_File_name",
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.InitialReports.productName,
                "Final Letter",
            ),
            UUID.randomUUID(),
            TEST_ENTITY_NAME,
            ProductType.InitialReports.processId,
            false,
        )
        verify(exactly = 1) { documentService.uploadDocument(any(), any(), any(), any()) }
    }

    private fun mockDocumentService() {
        val uploadResponse =
            DocumentResponse()
                .documentId("Test")
                .documentStatus(DocumentResponse.DocumentStatusEnum.ACTIVE)
                .documentVersion("1")

        every {
            documentService.uploadDocument(any(), any(), any(), any())
        } returns uploadResponse
    }

    @ParameterizedTest(name = "testGetMappingFields {1}")
    @MethodSource("revvDocGenEnabledProducts")
    fun testGetMappingFields(
        testProcessId: Int,
        processName: String,
    ) {
        val response = getMappingFieldsTestSetup(testProcessId)
        assertCommonFinalLetterFields(response)
        assertFormationStateAndProductName(response, testProcessId)
    }

    @Test
    fun testGetMappingFieldsInitialReports() {
        val response = getMappingFieldsTestSetup(ProductType.InitialReports.processId)
        assertCommonFinalLetterFields(response)
        assertFormationStateAndProductName(response, ProductType.InitialReports.processId)
        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.REGISTRATION_TYPE.value, "initial registration")),
        )
    }

    @Test
    fun testGetMappingFieldsAnnualReports() {
        val response = getMappingFieldsTestSetup(ProductType.AnnualReports.processId)
        assertCommonFinalLetterFields(response)
        assertFormationStateAndProductName(response, ProductType.AnnualReports.processId)
        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.REGISTRATION_TYPE.value, "periodic report")),
        )
    }

    @Test
    fun testGetMappingFieldsCorporateDissolution() {
        val response = getMappingFieldsTestSetup(ProductType.CorporateDissolution.processId)
        assertCommonFinalLetterFields(response)
    }

    @Test
    fun testGetMappingFieldsForeignQualification() {
        val response = getMappingFieldsTestSetup(ProductType.ForeignQualification.processId)

        assertCommonFinalLetterFields(response)

        assertFormationStateAndProductName(response, ProductType.ForeignQualification.processId)

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.LZ_IS_RA.value, "registered agent")),
        )
    }

    @Test
    fun testGetMappingFieldsEIN() {
        val getQuestionnaireAnswerResponse =
            GetQuestionnaireAnswerResponse().apply {
                questionnaireFieldGroupAnswers =
                    QuestionnaireAnswerDto().apply {
                        revision = 0
                        subRevision = 8
                        userOrderId = 511405966
                        fieldAnswers =
                            mutableListOf(
                                FieldAnswerDto().apply {
                                    fieldName = "EIN_Number_hidden"
                                    fieldValue = "11-1111111"
                                },
                            )
                    }
            }

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                ProductType.EIN.processId,
                getQuestionnaireAnswerResponse,
            )

        val response = getMappingFieldsTestSetup(ProductType.EIN.processId, mutableListOf(RevvDocumentType.EIN_FINAL_LETTER))

        assertCommonFinalLetterFields(response)

        Assertions.assertTrue(
            response.contains(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.fromProcessId(ProductType.EIN.processId).productName,
                ),
            ),
        )

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.EIN_NUMBER.value, "11-1111111")),
        )
    }

    @Test
    fun testGetMappingFieldsDBA() {
        val response = getMappingFieldsTestSetup(ProductType.DBA.processId)

        assertCommonFinalLetterFields(response)

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.FORMATION_STATE.value, "California")),
        )

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.COUNTY.value, "test_county")),
        )

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.EIN_NUMBER.value, "11-1111111")),
        )
    }

    @Test
    fun testGetMappingFieldsLP() {
        val response = getMappingFieldsTestSetup(ProductType.LP.processId)

        assertCommonFinalLetterFields(response)

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.EIN_NUMBER.value, "11-1111111")),
        )

        Assertions.assertTrue(
            response.contains(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.fromProcessId(ProductType.LP.processId).productName,
                ),
            ),
        )
    }

    @Test
    fun testGetMappingFieldsLLP() {
        val response = getMappingFieldsTestSetup(ProductType.LLP.processId)

        assertCommonFinalLetterFields(response)

        Assertions.assertTrue(
            response.contains(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.fromProcessId(ProductType.LLP.processId).productName,
                ),
            ),
        )
        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.EIN_NUMBER.value, "11-1111111")),
        )
    }

    @ParameterizedTest(name = "testGetMappingFieldsOperatingAgreements {1}")
    @MethodSource("revvDocGenEnabledProducts")
    fun testGetMappingFieldsOperatingAgreements(
        testProcessId: Int,
        processName: String,
    ) {
        val response = getMappingFieldsTestSetup(testProcessId)

        assertCommonFinalLetterFields(response)

        Assertions.assertTrue(
            response.contains(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.fromProcessId(testProcessId).productName,
                ),
            ),
        )
    }

    @ParameterizedTest()
    @MethodSource("addressValues")
    fun testAddressInfo(
        addressLine1: String?,
        addressLine2: String?,
        city: String?,
        state: String?,
        zip: String?,
        result: String,
    ) {
        setupVariables(ProductType.AnnualReports.processId)
        val builder = service.getRevvRequestBuilder(execution.input)
        mockGetOrderContact(
            ordersContactsApiService,
            addressLine1,
            addressLine2,
            city,
            state,
            zip,
        )

        val response = builder?.getFinalLetterDocumentFields(execution.input)

        Assertions.assertTrue(
            response!!.contains(
                RevvDocumentField(
                    RevvMappingFields.SHIPPING_CONTACT_ADDRESS.value,
                    result,
                ),
            ),
        )
    }

    @Test
    fun testGetMappingFieldsBylawsAndResolution() {
        val response = getMappingFieldsTestSetup(ProductType.ByLawsAndResolutions.processId)

        assertCommonFinalLetterFields(response)

        Assertions.assertTrue(
            response.contains(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.fromProcessId(ProductType.ByLawsAndResolutions.processId).productName,
                ),
            ),
        )
    }

    @Test
    fun testGetMappingFieldsCertifiedCopies() {
        val response = getMappingFieldsTestSetup(ProductType.CertifiedCopies.processId)

        assertCommonFinalLetterFields(response)

        assertFormationStateAndProductName(response, ProductType.CertifiedCopies.processId)

        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.DOCUMENT_OBTAINED.value, "articles")),
        )
    }

    @Test
    fun testIRGetDataFromParentProcessingOrder() {
        mockGetOrderContact(ordersContactsApiService)

        // Get blank field Answer data for initial report processing order
        val getQuestionnaireAnswerResponse =
            GetQuestionnaireAnswerResponse().apply {
                questionnaireFieldGroupAnswers =
                    QuestionnaireAnswerDto().apply {
                        revision = 0
                        subRevision = 8
                        userOrderId = TEST_PROCESSING_ORDER_ID
                        fieldAnswers = null
                    }
            }
        every {
            answerApi.answersUserOrderIdSourceGet(any(), any(), any(), any(), any(), any(), any(), any())
        } returns Mono.just(getQuestionnaireAnswerResponse)

        val answersApiJSON =
            ClassPathResource(
                "com/legalzoom/fulfillment/workflow/bpmn/llc_questionnaire_answer_response_for_ss4_mapping.json",
            ).file
        val answersApiResponse = JsonFileArgumentProvider.objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJSON)

        val orderResponseApiJSON = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/ca_llc_order_response.json").file
        val orderResponse = JsonFileArgumentProvider.objectMapper.readValue<GetOrderResponse>(orderResponseApiJSON)
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns
            GetAnswersComposite(
                orderResponse.order?.orderItems?.firstOrNull {
                    it.productConfiguration?.productTypeId?.value == RelationshipType.PACKAGE.Id
                }!!.processingOrder!!.processId!!,
                answersApiResponse,
            )

        val builder = setupRevvRequestBuilder(ProductType.InitialReports.processId, 511205226, ProductType.LLC.processId)
        val response = builder?.getMappingFields()!!

        assertCommonFinalLetterFields(response)
        assertFormationStateAndProductName(response, ProductType.InitialReports.processId)
        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.REGISTRATION_TYPE.value, "initial registration")),
        )
        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(
                orderResponse.order?.orderItems?.firstOrNull {
                    it.productConfiguration?.productTypeId?.value == RelationshipType.PACKAGE.Id
                }!!.processingOrder!!.processingOrderId!!,
                any(),
                any(),
            )
        }
    }

    private fun assertFormationStateAndProductName(
        response: MutableList<RevvDocumentField>,
        testProcessId: Int,
    ) {
        Assertions.assertTrue(
            response.contains(RevvDocumentField(RevvMappingFields.FORMATION_STATE.value, "California")),
        )
        Assertions.assertTrue(
            response.contains(
                RevvDocumentField(
                    RevvMappingFields.PRODUCT_NAME.value,
                    ProductType.fromProcessId(testProcessId).productName,
                ),
            ),
        )
    }

    private fun getMappingFieldsTestSetup(
        testProcessId: Int,
        templates: MutableList<RevvDocumentType>? = null,
    ): MutableList<RevvDocumentField> {
        mockGetOrderContact(ordersContactsApiService)
        mockQuestionnaireAnswer(
            questionnaireAnswerService,
            getQuestionnaireAnswerResponse(TEST_PROCESSING_ORDER_ID),
        )
        mockAnswerApi(
            answerApi,
            getQuestionnaireAnswerResponse(TEST_PROCESSING_ORDER_ID),
        )
        mockOrdersApi(ordersApiService)

        val builder = setupRevvRequestBuilder(testProcessId)
        val response = builder?.getMappingFields(templates)!!
        return response
    }

    private fun setupRevvRequestBuilder(
        testProcessId: Int,
        parentProcessingOrderId: Int? = TEST_PROCESSING_ORDER_ID,
        parentProcessId: Int? = null,
    ): RevvFieldsRequestBuilder? {
        setupVariables(testProcessId, parentProcessId, parentProcessingOrderId)
        val builder = service.getRevvRequestBuilder(execution.input)
        return builder
    }

    private fun setupVariables(
        testProcessId: Int,
        parentProcessId: Int? = TEST_PROCESSING_ORDER_ID,
        parentProcessingOrderId: Int? = null,
    ) {
        every {
            execution.input
        } returns
            variables {
                jurisdiction = "DE"
                entityName = TEST_ENTITY_NAME
                processId = testProcessId
                orderId = TEST_ORDER_ID
                customerId = TEST_CUSTOMER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
                this.parentProcessId = parentProcessId ?: testProcessId
                this.parentProcessingOrderId = parentProcessingOrderId
            }
    }
}
