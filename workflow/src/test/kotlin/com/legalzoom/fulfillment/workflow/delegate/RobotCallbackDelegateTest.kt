package com.legalzoom.fulfillment.workflow.delegate

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.rpa.ODataValueOfIEnumerableOfQueueItemDto
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.rpa.QueueItemsApi
import com.legalzoom.fulfillment.domain.Constants.ALTM_FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.APP_501C3_PROCESS
import com.legalzoom.fulfillment.domain.Constants.FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID
import com.legalzoom.fulfillment.domain.Constants.SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.OrderContext
import com.legalzoom.fulfillment.service.data.ValidationError
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus.Inactive
import com.legalzoom.fulfillment.service.enumeration.DocumentType.App501c3FilingException
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ArticlesFiled
import com.legalzoom.fulfillment.service.enumeration.DocumentType.Form1023EZ
import com.legalzoom.fulfillment.service.enumeration.DocumentType.ProofOfWork
import com.legalzoom.fulfillment.service.enumeration.DocumentType.SoSFilingException
import com.legalzoom.fulfillment.service.enumeration.DocumentType.SupportingDocs
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.enumeration.ProductType.ALTM
import com.legalzoom.fulfillment.service.enumeration.ProductType.App501c3
import com.legalzoom.fulfillment.service.enumeration.ProductType.INC
import com.legalzoom.fulfillment.service.enumeration.ProductType.LLC
import com.legalzoom.fulfillment.service.exception.DocumentRetrievalException
import com.legalzoom.fulfillment.service.exception.StoragePlatformException
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.workflow.service.CourierFilingService
import com.legalzoom.fulfillment.workflow.service.InstantFilingService
import com.legalzoom.fulfillment.workflow.service.RpaRetryService
import com.legalzoom.fulfillment.workflow.service.RpaSosSsorcoService
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.SpyK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.slot
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.RepositoryService
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.camunda.bpm.engine.repository.ProcessDefinition
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.MediaType.IMAGE_PNG
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder.json
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.net.URI
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID

@ExtendWith(MockKExtension::class)
class RobotCallbackDelegateTest {
    private val objectMapper = json().build<ObjectMapper>()

    @SpyK
    private var clock = Clock.fixed(Instant.now(), ZoneId.from(UTC))

    @MockK
    private lateinit var execution: DelegateExecution

    @MockK
    private lateinit var process: ProcessDefinition

    @MockK
    private lateinit var documentService: DocumentService

    @MockK
    private lateinit var s3Service: S3Service

    @MockK
    private lateinit var s3Client: S3Client

    @MockK
    private lateinit var salesforceApiService: SalesforceApiService

    @MockK
    private lateinit var featureToggleService: FeatureToggleService

    @MockK
    private lateinit var instantFilingService: InstantFilingService

    @MockK
    private lateinit var courierFilingService: CourierFilingService

    @MockK
    private lateinit var repositoryService: RepositoryService

    @MockK
    private lateinit var rpaSosSsorcoService: RpaSosSsorcoService

    @MockK
    private lateinit var rpaRetryService: RpaRetryService

    @InjectMockKs
    private lateinit var delegate: RobotCallbackDelegate

    @MockK
    private lateinit var queueItemsApi: QueueItemsApi

    companion object {
        private const val S3_URL_PARAM =
            "s3://uipath-rpa-storage-dev/Jobs_Data/LLC_CA/bdfe584d-59e8-11ec-940c-9eb06bf2cd7b/Proof_Of_Work/" +
                "PoW_testFile.png"

        @JvmStatic
        fun parameters() =
            listOf(
                arguments(
                    "Success condition",
                    "Success", S3_URL_PARAM, true, true, false, false, false, LLC.processId,
                ),
                arguments(
                    "Business error with exception screenshot",
                    "BusinessError", S3_URL_PARAM, true, true, false, true, false, LLC.processId,
                ),
                arguments(
                    "SystemError with exception screenshot",
                    "SystemError", S3_URL_PARAM, true, true, false, true, false, LLC.processId,
                ),
                arguments(
                    "Business error without exception screenshot",
                    "BusinessError", "", true, true, false, true, false, LLC.processId,
                ),
                arguments(
                    "evidenceFilePath is empty",
                    "Success", "", true, true, false, true, false, LLC.processId,
                ),
                arguments(
                    "S3 read fails",
                    "Success", S3_URL_PARAM, false, true, false, true, false, LLC.processId,
                ),
                arguments(
                    "document upload failed. Exception is thrown",
                    "Success", S3_URL_PARAM, true, false, false, false, true, LLC.processId,
                ),
                arguments(
                    "Exception is thrown within document upload",
                    "Success", S3_URL_PARAM, true, true, true, false, true, LLC.processId,
                ),
                arguments(
                    "Success condition",
                    "Success", S3_URL_PARAM, true, true, false, false, false, INC.processId,
                ),
                arguments(
                    "Business error with exception screenshot",
                    "BusinessError", S3_URL_PARAM, true, true, false, true, false, INC.processId,
                ),
                arguments(
                    "SystemError with exception screenshot",
                    "SystemError", S3_URL_PARAM, true, true, false, true, false, INC.processId,
                ),
                arguments(
                    "Business error without exception screenshot",
                    "BusinessError", "", true, true, false, true, false, INC.processId,
                ),
                arguments(
                    "evidenceFilePath is empty",
                    "Success", "", true, true, false, true, false, INC.processId,
                ),
                arguments(
                    "S3 read fails",
                    "Success", S3_URL_PARAM, false, true, false, true, false, INC.processId,
                ),
                arguments(
                    "document upload failed. Exception is thrown",
                    "Success", S3_URL_PARAM, true, false, false, false, true, INC.processId,
                ),
                arguments(
                    "Exception is thrown within document upload",
                    "Success", S3_URL_PARAM, true, true, true, false, true, INC.processId,
                ),
            )

        @JvmStatic
        fun validationErrorParameters() =
            listOf(
                arguments(
                    RpaSosSsorcoService.INVALID_NAME_PREFIX,
                    SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    LLC.processId,
                ),
                arguments(
                    RpaSosSsorcoService.INVALID_ADDRESS_PREFIX,
                    SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    LLC.processId,
                ),
                arguments(
                    RpaSosSsorcoService.INVALID_NAME_PREFIX,
                    SOS_ENTITY_NAME_UNAVAILABLE_ORCO_CAT_ID,
                    INC.processId,
                ),
                arguments(
                    RpaSosSsorcoService.INVALID_ADDRESS_PREFIX,
                    SOS_INVALID_BUSINESS_ADDRESS_ORCO_CAT_ID,
                    INC.processId,
                ),
            )

        @JvmStatic
        fun products() =
            listOf(
                arguments(LLC),
                arguments(INC),
            )

        @JvmStatic
        fun app501c3() =
            listOf(
                arguments(
                    "Success condition",
                    "Success",
                    "s3://rpa-storage-dev/Jobs_Data/501C3_FEDERAL/20240613115424/Proof_Of_Work/PoW_20240613115424_20240613115722.pdf",
                    true,
                    true,
                    false,
                    false,
                    false,
                    true,
                ),
                arguments(
                    "SystemError with exception screenshot",
                    "SystemError",
                    "s3://rpa-storage-dev/Jobs_Data/501C3_FEDERAL/20240613115424/Proof_Of_Work/PoW_20240613115424_20240613115722.png",
                    true,
                    true,
                    false,
                    true,
                    false,
                    false,
                ),
            )

        const val TEST_CUSTOMER_ID = "********"
        const val TEST_PROCESSING_ORDER_ID = *********
        const val TEST_ORDER_ID = *********
        const val SP_DOCUMENT_ID = "00154c503df1476f873bd054a52a311f_"
        const val DOCUMENT_ID = "00154c503df1476f873bd054a52a311f_C6E51891-5B48-C635-8709-7DC175600000"
        const val TEST_JURISDICTION = "CA"
        const val TEST_ENTITY_NAME = "Random-Name ProductType"
        val testAccountId = UUID.randomUUID()
    }

    private val docUpdateResponse =
        DocumentResponse().documentId(SP_DOCUMENT_ID)
            .documentStatus(DocumentResponse.DocumentStatusEnum.ACTIVE).documentVersion("1")
    private val testFile = ClassPathResource("com/legalzoom/fulfillment/workflow/integration/testFile.png").file
    private val resourceWithType = ResourceWithType(FileSystemResource(testFile), IMAGE_PNG, "testFile.png")

    private val expectedResource =
        ClassPathResource("com/legalzoom/fulfillment/workflow/integration/documentBoth.json").file
    private val expectedResponse = objectMapper.readValue<List<Document>>(expectedResource)

    @BeforeAll
    fun setup() {
        mockServices()
    }

    private fun mockVariables(
        statusFromRpa: String,
        s3Url: String,
        isInstantOrSemiInstant: Boolean,
        testProcessId: Int,
        testMessage: String = "Success",
    ) {
        if (isInstantOrSemiInstant) {
            every { execution.input } returns
                variables {
                    status = statusFromRpa
                    evidenceFilePath = s3Url
                    processId = testProcessId
                    customerId = TEST_CUSTOMER_ID
                    processingOrderId = TEST_PROCESSING_ORDER_ID
                    orderId = TEST_ORDER_ID
                    message = testMessage
                    jurisdiction = TEST_JURISDICTION
                    entityName = TEST_ENTITY_NAME
                    documentPaths = if (statusFromRpa == "Success") mutableListOf(s3Url) else null
                    accountId = testAccountId
                }
        } else {
            every { execution.input } returns
                variables {
                    status = statusFromRpa
                    evidenceFilePath = s3Url
                    processId = testProcessId
                    customerId = TEST_CUSTOMER_ID
                    processingOrderId = TEST_PROCESSING_ORDER_ID
                    orderId = TEST_ORDER_ID
                    message = testMessage
                    jurisdiction = TEST_JURISDICTION
                    entityName = TEST_ENTITY_NAME
                    accountId = testAccountId
                }
        }
    }

    private fun mockS3Service(
        s3ReadSucceeds: Boolean,
        s3Url: String,
    ) {
        if (s3ReadSucceeds) {
            every { s3Service.getDocument(URI(s3Url)) } returns resourceWithType
        } else {
            every { s3Service.getDocument(URI(s3Url)) } throws DocumentRetrievalException()
        }
    }

    private fun mockS3Client() {
        val responseInputSteam =
            ResponseInputStream(
                GetObjectResponse.builder()
                    .contentType(MediaType.APPLICATION_PDF_VALUE)
                    .build(),
                "TEST DATA".byteInputStream(),
            )

        every {
            s3Client.getObject(any<GetObjectRequest>())
        } returns responseInputSteam
    }

    private fun mockGetQueueItem() {
        every {
            queueItemsApi.queueItemsGet(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns
            Mono.just(
                ODataValueOfIEnumerableOfQueueItemDto().apply {
                    value = emptyList()
                },
            )
    }

    private fun mockServices() {
        every {
            repositoryService.getProcessDefinition(any())
        } returns process
        every { process.key } returns FILING_PROCESS
        every { execution.processDefinitionId } returns FILING_PROCESS
        every { courierFilingService.isCourierFilingState(any()) } returns false
        every {
            salesforceApiService.addLedgerNote(any())
        } returns AddLedgerNoteResponse("test", "test", emptyList())
        every { rpaSosSsorcoService.createValidationError(any(), any()) } returns ValidationError("")
        every { rpaRetryService.getMaxRetries() } returns 6
        mockGetQueueItem()
    }

    private fun mockDocumentService(
        exceptionThrown: Boolean,
        uploadSucceeds: Boolean,
        testProcessId: Int,
        expectedAvailableForImmediateDownload: Boolean,
    ) {
        val orderContext =
            OrderContext(
                processingOrderId = TEST_PROCESSING_ORDER_ID.toLong(),
                customerId = TEST_CUSTOMER_ID.toLong(),
                orderId = TEST_ORDER_ID.toLong(),
                jurisdiction = TEST_JURISDICTION,
                processId = testProcessId,
                entityName = TEST_ENTITY_NAME,
                isFromWorkflow = true,
                accountId = testAccountId.toString(),
            )
        if (!exceptionThrown) { // Not ideal but need to add a conditional case for when this method throws an exception

            every {
                documentService.uploadDocument(orderContext, any(), any(), any(), expectedAvailableForImmediateDownload)
            } returns if (!uploadSucceeds) null else docUpdateResponse
            every {
                documentService.findDocumentsBy(
                    TEST_PROCESSING_ORDER_ID.toLong(),
                    TEST_CUSTOMER_ID.toLong(),
                    any(),
                    any(),
                    any(),
                    any(),
                    accountId = any(),
                )
            } returns expectedResponse
            every {
                documentService.updateDocument(
                    DOCUMENT_ID,
                    Inactive,
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            } returns docUpdateResponse
        } else {
            every {
                documentService.uploadDocument(
                    orderContext,
                    any(),
                    any(),
                    any(),
                )
            } throws StoragePlatformException()
        }
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("parameters")
    fun testRobotCallbackDelegate(
        testCaseName: String,
        statusFromRpa: String,
        s3Url: String,
        s3ReadSucceeds: Boolean,
        uploadSucceeds: Boolean,
        exceptionThrown: Boolean,
        assertValidationError: Boolean,
        shouldAssertThrows: Boolean,
        testProcessId: Int,
    ) {
        mockVariables(statusFromRpa, s3Url, isInstantOrSemiInstant = false, testProcessId)
        mockS3Service(s3ReadSucceeds, s3Url)
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns false
        every { instantFilingService.isCourierFilingState(any()) } returns false
        mockDocumentService(exceptionThrown, uploadSucceeds, testProcessId, false)

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }
        if (shouldAssertThrows) {
            assertThrows<Exception> {
                delegate.execute(execution)
            }
        } else {
            delegate.execute(execution)
            // if any validationError is found
            val testValidationError = variables.any { it.validationError == true }
            if (testCaseName == "Success condition") {
                assertThat(variables.mapNotNull { it.documentType }).isEqualTo(
                    listOf(
                        CustomerDocumentType.findCustomerDocTypeFromProductName(
                            ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                            ProofOfWork.name,
                        ).documentType,
                    ),
                )
            }
            if (testCaseName in
                setOf(
                    "Business error with exception screenshot",
                    "SystemError with exception screenshot",
                )
            ) {
                assertThat(variables.mapNotNull { it.documentType }).isEqualTo(
                    listOf(
                        CustomerDocumentType.findCustomerDocTypeFromProductName(
                            ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                            SoSFilingException.name,
                        ).documentType,
                    ),
                )
            }
            assertThat(testValidationError).isEqualTo(assertValidationError)
        }
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("parameters")
    fun testRobotCallbackDelegateInstantFiling(
        testCaseName: String,
        statusFromRpa: String,
        s3Url: String,
        s3ReadSucceeds: Boolean,
        uploadSucceeds: Boolean,
        exceptionThrown: Boolean,
        assertValidationError: Boolean,
        shouldAssertThrows: Boolean,
        testProcessId: Int,
    ) {
        mockVariables(statusFromRpa, s3Url, isInstantOrSemiInstant = true, testProcessId)
        mockS3Service(s3ReadSucceeds, s3Url)
        every { instantFilingService.isInstantFilingState(any()) } returns true
        every { instantFilingService.isSemiInstantFilingState(any()) } returns false

        mockDocumentService(exceptionThrown, uploadSucceeds, testProcessId, false)

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }
        if (shouldAssertThrows) {
            assertThrows<Exception> { delegate.execute(execution) }
        } else {
            delegate.execute(execution)
            // if any validationError is found
            val testValidationError = variables.any { it.validationError == true }
            if (testCaseName == "Success condition") {
                assertThat(
                    variables.any {
                        it.documentResults ==
                            mutableMapOf(
                                Pair(
                                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                                        ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                                        ArticlesFiled.name,
                                    ).documentType,
                                    docUpdateResponse,
                                ),
                                Pair(
                                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                                        ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                                        ProofOfWork.name,
                                    ).documentType,
                                    docUpdateResponse,
                                ),
                            )
                    },
                )
                // Articles Uploaded First, Proof of Work uploaded second
            }
            if (testCaseName in
                setOf(
                    "Business error with exception screenshot",
                    "SystemError with exception screenshot",
                )
            ) {
                assertThat(
                    variables.any {
                        it.documentResults ==
                            mutableMapOf(
                                Pair(
                                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                                        ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                                        SoSFilingException.name,
                                    ).documentType,
                                    docUpdateResponse,
                                ),
                            )
                    },
                )
            }
            assertThat(testValidationError).isEqualTo(assertValidationError)
        }
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("parameters")
    fun testRobotCallbackDelegateSemiInstantFiling(
        testCaseName: String,
        statusFromRpa: String,
        s3Url: String,
        s3ReadSucceeds: Boolean,
        uploadSucceeds: Boolean,
        exceptionThrown: Boolean,
        assertValidationError: Boolean,
        shouldAssertThrows: Boolean,
        testProcessId: Int,
    ) {
        mockVariables(statusFromRpa, s3Url, isInstantOrSemiInstant = true, testProcessId)
        mockS3Service(s3ReadSucceeds, s3Url)
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns true

        mockDocumentService(exceptionThrown, uploadSucceeds, testProcessId, false)

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }
        if (shouldAssertThrows) {
            assertThrows<Exception> { delegate.execute(execution) }
        } else {
            delegate.execute(execution)
            // if any validationError is found
            val testValidationError = variables.any { it.validationError == true }
            if (testCaseName == "Success condition") {
                assertThat(
                    variables.any {
                        it.documentResults ==
                            mutableMapOf(
                                Pair(
                                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                                        ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                                        ArticlesFiled.name,
                                    ).documentType,
                                    docUpdateResponse,
                                ),
                                Pair(
                                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                                        ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                                        ProofOfWork.name,
                                    ).documentType,
                                    docUpdateResponse,
                                ),
                            )
                    },
                )
                // Articles Uploaded First, Proof of Work uploaded second
            }
            if (testCaseName in
                setOf(
                    "Business error with exception screenshot",
                    "SystemError with exception screenshot",
                )
            ) {
                assertThat(
                    variables.any {
                        it.documentResults ==
                            mutableMapOf(
                                Pair(
                                    CustomerDocumentType.findCustomerDocTypeFromProductName(
                                        ProductType.fromProcessIdNullable(testProcessId)!!.productName,
                                        SoSFilingException.name,
                                    ).documentType,
                                    docUpdateResponse,
                                ),
                            )
                    },
                )
            }
            assertThat(testValidationError).isEqualTo(assertValidationError)
        }
    }

    @ParameterizedTest()
    @MethodSource("products")
    fun testRobotCallbackDelegateSemiInstantFilingTN(productType: ProductType) {
        mockVariables("Success", S3_URL_PARAM, isInstantOrSemiInstant = true, productType.processId)
        mockS3Service(true, S3_URL_PARAM)
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns true
        every { instantFilingService.isCourierFilingState(any()) } returns true

        mockDocumentService(exceptionThrown = false, uploadSucceeds = true, productType.processId, false)

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        delegate.execute(execution)
        assertThat(
            variables.any {
                it.documentResults ==
                    mutableMapOf(
                        Pair(
                            CustomerDocumentType.findCustomerDocTypeFromProductName(
                                ProductType.fromProcessIdNullable(LLC.processId)!!.productName,
                                SupportingDocs.displayName,
                            ).documentType,
                            docUpdateResponse,
                        ),
                    )
            },
        )
    }

    @Test
    fun testEmptyEvidenceFilePathDoesNotThrowException() {
        val inVariables =
            variables {
                status = "Success"
                evidenceFilePath = ""
                processId = LLC.processId
                customerId = TEST_CUSTOMER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
                orderId = TEST_ORDER_ID
                jurisdiction = TEST_JURISDICTION
                entityName = TEST_ENTITY_NAME
                documentPaths = mutableListOf(S3_URL_PARAM)
                accountId = testAccountId
            }

        every { execution.input } returns inVariables
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns true

        mockS3Client()
        mockDocumentService(exceptionThrown = false, uploadSucceeds = true, inVariables.processId!!, false)

        val outVariables = mutableListOf<Variables>()

        justRun { execution.variables = capture(outVariables) }

        val instance =
            RobotCallbackDelegate(
                clock,
                documentService,
                S3Service(s3Client),
                salesforceApiService,
                instantFilingService,
                courierFilingService,
                repositoryService,
                featureToggleService,
                rpaSosSsorcoService,
                rpaRetryService,
                queueItemsApi,
            )

        instance.execute(execution)
        // Implied assertion that no exception is thrown
    }

    @ParameterizedTest
    @MethodSource("validationErrorParameters")
    fun testRpaValidationErrors(
        errorMessage: String,
        category: String,
        testProcessId: Int,
    ) {
        mockVariables("BusinessError", S3_URL_PARAM, isInstantOrSemiInstant = true, testProcessId, errorMessage)
        val variables = mutableListOf<Variables>()
        val assertValidationError = ValidationError(errorMessage, isSelfServe = true, orcoReasonCategoryId = category)

        justRun { execution.variables = capture(variables) }
        execution.input.processId = testProcessId
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns true
        every { rpaSosSsorcoService.createValidationError(errorMessage, any()) } returns assertValidationError

        mockS3Service(true, S3_URL_PARAM)
        every {
            documentService.uploadDocument(any(), any(), any(), any())
        } returns docUpdateResponse

        delegate.execute(execution)
        val testValidationError = execution.input.validationErrors?.get(0)
        assertThat(testValidationError).isEqualTo(assertValidationError)
    }

    @Test
    fun testLedgerNoteFailureWasSuccessful() {
        val inVariables =
            variables {
                status = "Success"
                evidenceFilePath =
                    "s3://uipath-rpa-storage-dev/Jobs_Data/DOC_RETRIEVAL_PA/0495689f-bc3d-11ec-a7ca-4e7a83e6458a/" +
                    "Proof_Of_Work/0495689f-bc3d-11ec-a7ca-4e7a83e6458a.pdf"
                processId = LLC.processId
                customerId = TEST_CUSTOMER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
                orderId = TEST_ORDER_ID
                jurisdiction = TEST_JURISDICTION
                entityName = TEST_ENTITY_NAME
                documentPaths = mutableListOf(S3_URL_PARAM)
            }

        every { execution.input } returns inVariables
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns true

        mockS3Client()
        mockDocumentService(exceptionThrown = false, uploadSucceeds = true, inVariables.processId!!, false)

        val outVariables = mutableListOf<Variables>()

        justRun { execution.variables = capture(outVariables) }

        RobotCallbackDelegate(
            clock,
            documentService,
            S3Service(s3Client),
            salesforceApiService,
            instantFilingService,
            courierFilingService,
            repositoryService,
            featureToggleService,
            rpaSosSsorcoService,
            rpaRetryService,
            queueItemsApi,
        )

        every {
            salesforceApiService.addLedgerNote(any())
        } returns AddLedgerNoteResponse("test", "test", emptyList())
    }

    @Test
    fun testLedgerNoteFailureDoesNotThrowException() {
        val inVariables =
            variables {
                status = "Success"
                evidenceFilePath =
                    "s3://uipath-rpa-storage-dev/Jobs_Data/DOC_RETRIEVAL_PA/0495689f-bc3d-11ec-a7ca-4e7a83e6458a/" +
                    "Proof_Of_Work/0495689f-bc3d-11ec-a7ca-4e7a83e6458a.pdf"
                processId = INC.processId
                customerId = TEST_CUSTOMER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
                orderId = TEST_ORDER_ID
                jurisdiction = TEST_JURISDICTION
                entityName = TEST_ENTITY_NAME
                documentPaths = mutableListOf(S3_URL_PARAM)
                accountId = testAccountId
            }

        every { execution.input } returns inVariables
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns true

        mockS3Client()
        mockDocumentService(exceptionThrown = false, uploadSucceeds = true, inVariables.processId!!, false)

        val outVariables = mutableListOf<Variables>()

        justRun { execution.variables = capture(outVariables) }

        val instance =
            RobotCallbackDelegate(
                clock,
                documentService,
                S3Service(s3Client),
                salesforceApiService,
                instantFilingService,
                courierFilingService,
                repositoryService,
                featureToggleService,
                rpaSosSsorcoService,
                rpaRetryService,
                queueItemsApi,
            )

        every {
            salesforceApiService.addLedgerNote(any())
        } throws
            WebClientResponseException(
                HttpStatus.SERVICE_UNAVAILABLE.value(),
                HttpStatus.SERVICE_UNAVAILABLE.reasonPhrase,
                null,
                null,
                null,
            )

        instance.execute(execution)
        // Implied assertion that no exception is thrown when ledger note fails
    }

    @Test
    fun testInstantFilingStateUpdatesMissingEffectiveDate() {
        val inVariables =
            variables {
                status = "Success"
                evidenceFilePath =
                    "s3://uipath-rpa-storage-dev/Jobs_Data/DOC_RETRIEVAL_PA/0495689f-bc3d-11ec-a7ca-4e7a83e6458a/" +
                    "Proof_Of_Work/0495689f-bc3d-11ec-a7ca-4e7a83e6458a.pdf"
                processId = ALTM.processId
                customerId = TEST_CUSTOMER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
                orderId = TEST_ORDER_ID
                jurisdiction = TEST_JURISDICTION
                entityName = TEST_ENTITY_NAME
                documentPaths = mutableListOf(S3_URL_PARAM)
                effectiveDate = null
                accountId = testAccountId
            }

        val outVarSlot = slot<Variables>()

        every { execution.input } returns inVariables
        justRun { execution.variables = capture(outVarSlot) }
        every { instantFilingService.isInstantFilingState(any()) } returns true
        every { instantFilingService.isSemiInstantFilingState(any()) } returns false
        every { repositoryService.getProcessDefinition(any()).key } returns ALTM_FILING_PROCESS

        mockS3Client()
        mockDocumentService(exceptionThrown = false, uploadSucceeds = true, inVariables.processId!!, false)

        val instance =
            RobotCallbackDelegate(
                clock,
                documentService,
                S3Service(s3Client),
                salesforceApiService,
                instantFilingService,
                courierFilingService,
                repositoryService,
                featureToggleService,
                rpaSosSsorcoService,
                rpaRetryService,
                queueItemsApi,
            )

        instance.execute(execution)
        assertThat(outVarSlot.captured).containsAllEntriesOf(
            variables {
                effectiveDate = LocalDate.now(clock).toString()
            },
        )
    }

    @Test
    fun testNonInstantFilingStateDoesNotUpdateMissingEffectiveDate() {
        val inVariables =
            variables {
                status = "Success"
                evidenceFilePath =
                    "s3://uipath-rpa-storage-dev/Jobs_Data/DOC_RETRIEVAL_PA/0495689f-bc3d-11ec-a7ca-4e7a83e6458a/" +
                    "Proof_Of_Work/0495689f-bc3d-11ec-a7ca-4e7a83e6458a.pdf"
                processId = ALTM.processId
                customerId = TEST_CUSTOMER_ID
                processingOrderId = TEST_PROCESSING_ORDER_ID
                orderId = TEST_ORDER_ID
                jurisdiction = TEST_JURISDICTION
                entityName = TEST_ENTITY_NAME
                documentPaths = mutableListOf(S3_URL_PARAM)
                effectiveDate = null
            }

        val outVarSlot = slot<Variables>()

        every { execution.input } returns inVariables
        justRun { execution.variables = capture(outVarSlot) }
        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns false
        every { instantFilingService.isCourierFilingState(any()) } returns false
        every { repositoryService.getProcessDefinition(any()).key } returns ALTM_FILING_PROCESS

        mockS3Client()
        mockDocumentService(exceptionThrown = false, uploadSucceeds = true, inVariables.processId!!, false)

        val instance =
            RobotCallbackDelegate(
                clock,
                documentService,
                S3Service(s3Client),
                salesforceApiService,
                instantFilingService,
                courierFilingService,
                repositoryService,
                featureToggleService,
                rpaSosSsorcoService,
                rpaRetryService,
                queueItemsApi,
            )

        instance.execute(execution)
        assertThat(outVarSlot.captured).containsAllEntriesOf(variables { effectiveDate = null })
    }

    @ParameterizedTest()
    @MethodSource("app501c3")
    fun `501c3 filing`(
        testCaseName: String,
        statusFromRpa: String,
        s3Url: String,
        s3ReadSucceeds: Boolean,
        uploadSucceeds: Boolean,
        exceptionThrown: Boolean,
        assertValidationError: Boolean,
        shouldAssertThrows: Boolean,
        expectedAvailableForImmediateDownload: Boolean,
    ) {
        mockVariables(statusFromRpa, s3Url, isInstantOrSemiInstant = false, App501c3.processId)
        mockS3Service(s3ReadSucceeds, s3Url)
        mockDocumentService(exceptionThrown, uploadSucceeds, testProcessId = App501c3.processId, expectedAvailableForImmediateDownload)

        every { instantFilingService.isInstantFilingState(any()) } returns false
        every { instantFilingService.isSemiInstantFilingState(any()) } returns false
        every { instantFilingService.isCourierFilingState(any()) } returns false

        every {
            repositoryService.getProcessDefinition(any())
        } returns process
        every { process.key } returns APP_501C3_PROCESS
        every { execution.processDefinitionId } returns APP_501C3_PROCESS

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        if (shouldAssertThrows) {
            assertThrows<Exception> {
                delegate.execute(execution)
            }
        } else {
            delegate.execute(execution)
            val testValidationError = variables.any { it.validationError == true }

            if (testCaseName == "Success condition") {
                assertThat(variables.mapNotNull { it.documentType }).isEqualTo(
                    listOf(
                        CustomerDocumentType.findCustomerDocTypeFromProductName(
                            ProductType.fromProcessIdNullable(App501c3.processId)!!.productName,
                            Form1023EZ.name,
                        ).documentType,
                    ),
                )
            } else {
                assertThat(variables.mapNotNull { it.documentType }).isEqualTo(
                    listOf(
                        CustomerDocumentType.findCustomerDocTypeFromProductName(
                            ProductType.fromProcessIdNullable(App501c3.processId)!!.productName,
                            App501c3FilingException.name,
                        ).documentType,
                    ),
                )
            }
            assertThat(testValidationError).isEqualTo(assertValidationError)
        }
    }
}
