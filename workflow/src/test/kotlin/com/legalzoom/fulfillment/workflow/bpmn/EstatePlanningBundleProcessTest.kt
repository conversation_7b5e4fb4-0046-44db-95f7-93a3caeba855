package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.dds.DocumentRequestApi
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.dsd.printship.PrintShipJobResponseDto
import com.legalzoom.api.model.filenetprint.PrintResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailResponse
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.api.revv.RevvEsignatureService
import com.legalzoom.api.revv.model.CreateDocumentResponse
import com.legalzoom.api.revv.model.DocumentFieldsResponse
import com.legalzoom.api.revv.model.TemplateResponse
import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants.DOCUMENT_GENERATION_PROCESS
import com.legalzoom.fulfillment.domain.Constants.EP_BUNDLE_PROCESS
import com.legalzoom.fulfillment.domain.Constants.PRINT_PROCESS
import com.legalzoom.fulfillment.printandship.entity.VendorCode
import com.legalzoom.fulfillment.printandship.repository.VendorCodeRepository
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_STATUS_MESSAGE
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintConfig
import com.legalzoom.fulfillment.printandshipapi.enumeration.RequestType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.LLC
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.PrintService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.document.DocumentStorageFolderNameProvider
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.helper.documents.StoragePlatformClient
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.testing.Await.await
import com.legalzoom.fulfillment.testing.SpringBootProcessTest
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import com.legalzoom.fulfillment.workflow.dmn.JsonFileArgumentProvider
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.SlackMessageService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import com.slack.api.Slack
import com.slack.api.webhook.Payload
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockkStatic
import io.mockk.verify
import org.awaitility.kotlin.untilNotNull
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.TaskService
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions.init
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.task
import org.camunda.bpm.engine.test.assertions.cmmn.CmmnAwareTests.assertThat
import org.camunda.bpm.engine.test.assertions.cmmn.CmmnAwareTests.complete
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.toEntity
import reactor.core.publisher.Mono
import java.io.File
import java.net.URI
import java.time.Clock
import java.time.Duration
import java.time.temporal.ChronoUnit.MINUTES
import java.time.temporal.ChronoUnit.SECONDS
import kotlin.random.Random

@SpringBootProcessTest
@EmbeddedKafka
@Deployment(resources = ["bpmn/ep-bundle.bpmn", "bpmn/document-generation.bpmn", "bpmn/print.bpmn", "bpmn/salesforce.bpmn"])
class EstatePlanningBundleProcessTest(
    protected val clock: Clock,
    protected val processEngine: ProcessEngine,
    protected val taskService: TaskService,
    private val salesforceHelper: SalesforceHelper,
) {
    protected val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    val getActiveInstanceOf = { processDefinitionKey: String, parentProcessInstance: ProcessInstance ->
        processEngine.runtimeService
            .createProcessInstanceQuery()
            .processDefinitionKey(processDefinitionKey)
            .superProcessInstanceId(parentProcessInstance.id)
            .active()
            .singleResult()
    }

    @MockkBean
    protected lateinit var documentRequestApi: DocumentRequestApi

    @MockkBean
    protected lateinit var documentService: DocumentService

    @MockkBean
    protected lateinit var storagePlatformClient: StoragePlatformClient

    @MockkBean(relaxed = true)
    protected lateinit var salesforceApiService: SalesforceApiService

    @MockkBean
    protected lateinit var s3Service: S3Service

    @MockkBean
    protected lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    protected lateinit var slack: Slack

    @MockkBean
    protected lateinit var featureToggleService: FeatureToggleService

    @MockK
    private lateinit var documentStorageFolderNameProvider: DocumentStorageFolderNameProvider

    @MockkBean
    protected lateinit var ssorcoFeatureToggleService: ORCOFeatureToggleService

    @MockkBean
    protected lateinit var customerApi: CustomerApi

    @MockkBean
    protected lateinit var notificationEventService: NotificationEventService

    @MockkBean
    protected lateinit var printService: PrintService

    @MockkBean
    protected lateinit var ordersApiService: OrdersApiService

    @MockkBean
    protected lateinit var processingOrderService: ProcessingOrderService

    @MockkBean
    private lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    protected lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean(relaxed = true)
    @Qualifier("webClient")
    protected lateinit var webClient: WebClient

    @MockkBean
    protected lateinit var ordersOrderItemsApiService: OrdersOrderItemsApiService

    @MockkBean
    protected lateinit var ordersContactsApiService: OrdersContactsApiService

    @MockkBean
    protected lateinit var answerApi: AnswerApi

    @MockkBean
    protected lateinit var revvEsignatureService: RevvEsignatureService

    @MockkBean
    protected lateinit var completedOrderDetailService: CompletedOrderDetailService

    @MockkBean
    protected lateinit var documentListService: DocumentListService

    @MockkBean
    protected lateinit var orderContactsService: OrderContactsService

    @MockkBean
    protected lateinit var vendorCodeRepository: VendorCodeRepository

    @MockkBean
    protected lateinit var slackMessageService: SlackMessageService

    @MockkBean
    protected lateinit var productsApi: ProductsApi

    protected fun mockProductsApi() {
        val json = ClassPathResource("productsApiPostOptionResponse.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                null,
                null,
                null,
            )
        } returns Mono.just(response)
    }

    protected fun mockVendorCodeRepository() {
        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.PRINT },
            )
        } answers {
            listOf(VendorCode("viatech", "PLZ-LLCPOD", "LLC Printing", RequestType.PRINT, LLC.processId))
        }

        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.KIT },
            )
        } returns
            listOf(
                VendorCode("viatech", "KLZ-LCTT-CA", "LLC Founders Kit", RequestType.KIT, LLC.processId),
            )
    }

    @MockkBean
    private lateinit var printShipJobsApi: PrintShipJobsApi

    fun mockPrintAndShipApi() {
        every {
            printShipJobsApi.createJobAsync(any())
        } returns
            Mono.just(
                PrintShipJobResponseDto().also { response ->
                    response.id = UniqueId.nextUUIDString()
                },
            )
    }

    protected fun mockOrderContactsService() {
        val json = ClassPathResource("orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns response.contacts!!
    }

    protected fun mockDocumentListService() {
        every {
            documentListService.getDefaultDocumentListWithConfig(any(), any(), any(), any())
        } returns
            listOf(
                PrintDocumentInfo(
                    documentId = "1234",
                    documentName = "Articles Filed.pdf",
                    printConfig = PrintConfig.LZDOC1,
                    documentType = "Articles Filed",
                ),
            )
    }

    protected fun mockCompletedOrderDetailService() {
        val json = ClassPathResource("completedOrderDetailResponse.json", javaClass).file
        var response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)

        every {
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(any(), any())
        } returns response
    }

    fun startEPBundleProcess(variables: Variables): Pair<String, ProcessInstance> {
        val businessKey = variables.processingOrderId.toString()

        val processInstance =
            processEngine.runtimeService.startProcessInstanceByKey(EP_BUNDLE_PROCESS, businessKey, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        return Pair(businessKey, processInstance)
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
        init(processEngine)
        mockCustomerDataFetch()
        mockSnsServiceData()
        mockDocumentRequestApi()
        mockS3Service()
        mockDocumentService()
        mockSalesforceApi()
        mockProcessingOrdersApi()
        mockSlack()
        mockFeatureToggleService()
        mockPrintService()
        mockProcessingOrdersService()
        mockWebClient()
        mockOrderOrderItemsApi()
        mockSlackMessageCalls()
        mockCompletedOrderDetailService()
        mockDocumentListService()
        mockOrderContactsService()
        mockPrintAndShipApi()
        mockVendorCodeRepository()
        mockkStatic(LocalDateTime::class)
        every {
            LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        } returns LocalDateTime.parse("2024-01-01T00:00:00")
        mockProductsApi()
    }

    protected fun mockSlackMessageCalls() {
        every {
            slackMessageService.sendSlackMessage(any(), any())
        } returns Unit
    }

    protected fun mockWebClient() {
        val testFile = ClassPathResource("com/legalzoom/fulfillment/workflow/integration/testFile.png").file
        val resource: Resource = FileSystemResource(testFile)
        val responseEntity = ResponseEntity(resource, HttpStatus.OK)
        every {
            webClient.get().uri(any<URI>()).retrieve().toEntity<Resource>().block()
        } returns responseEntity
    }

    protected fun mockCustomerDataFetch() {
        val customerByIdResponseJson =
            ClassPathResource("com/legalzoom/fulfillment/workflow/integration/customer_api_customer_by_customerId_response.json").file
        val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        } returns Mono.just(customerByIdResponse)
    }

    protected fun mockSnsServiceData() {
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns "d413681a-9810-5dc7-b485-349134df00ad"
    }

    protected fun mockDocumentRequestApi() {
        val response =
            DocumentsGenerationResponse().apply {
                isAllDocRequestSubmitted = true
            }

        every {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(any(), any(), any(), any())
        } returns Mono.just(response)
    }

    protected fun mockS3Service() {
        every {
            s3Service.getDocument(any())
        } returns
            ResourceWithType(
                FileSystemResource(File("PreFiling_Articles.pdf")),
                MediaType.APPLICATION_PDF,
                "PreFiling_Articles.pdf",
            )
    }

    private var capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter =
        mutableListOf<CustomerDocumentType>()

    protected fun mockDocumentService() {
        capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter.clear()
        val updateDocumentMetaDataResponse =
            DocumentResponse().documentId("Test")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")

        every {
            storagePlatformClient.updateDocumentMetaData(any(), any(), any())
        } returns updateDocumentMetaDataResponse

        every {
            documentService.uploadDocument(
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            DocumentResponse().documentId("Test").documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")

        val testFile = ClassPathResource("com/legalzoom/fulfillment/workflow/integration/documentBoth.json").file
        val documents = objectMapper.readValue<List<Document>>(testFile)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns documents
        every {
            documentService.findDocumentsBy(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns documents

        every {
            documentService.updateDocument(any(), DocumentStatus.Active, any(), any(), any(), any(), any())
        } returns
            DocumentResponse().documentId("00154c503df1476f873bd054a52a311f")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE")).documentVersion("1")
    }

    protected var capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
    protected var capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
    protected var capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

    protected fun mockSalesforceApi() {
        capturedSalesForceRequests = mutableListOf()
        capturedLedgerNoteRequests = mutableListOf()
        capturedSalesForceUpdateCaseRequests = mutableListOf()

        every {
            salesforceApiService.updateCase(capture(capturedSalesForceUpdateCaseRequests))
        } returns SalesforceCaseUpdateResponse("test", "test", "test")

        every {
            salesforceApiService.createCase(capture(capturedSalesForceRequests))
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(capture(capturedLedgerNoteRequests))
        } returns AddLedgerNoteResponse("test", "test", emptyList())
    }

    protected fun mockProcessingOrdersApi() {
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())
        val getResponse = GetProcessingOrderResponse()
        val processingOrder = ProcessingOrderDto().processingOrderId(567893).processingStatusId(75).processId(2)
        getResponse.processingOrder(processingOrder)
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(getResponse)
    }

    protected fun mockSlack() {
        every {
            slack.send(any(), any<Payload>())
        } returns null
    }

    protected fun mockFeatureToggleService() {
        every {
            featureToggleService.isCustomHandlerEnabled(any(), any(), any())
        } returns false

        every {
            ssorcoFeatureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false

        every {
            featureToggleService.isManualNotificationEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isDSDDocGenEnabled(any(), any(), any())
        } returns false
    }

    protected fun mockPrintService() {
        every {
            printService.printOrder(any())
        } returns PrintResponse().requestId(Random.nextInt().toString())
    }

    protected fun mockOrdersApi(
        mockOrderResponseFileName: String,
        processingOrderId: Int,
    ) {
        val getOrderResponseFile = ClassPathResource(mockOrderResponseFileName, javaClass).file
        val getOrderResponse = objectMapper.readValue<GetOrderResponse>(getOrderResponseFile)
        getOrderResponse.order!!.orderItems!!.forEach {
            if (it.processingOrder != null) {
                it.processingOrder!!.processingOrderId = processingOrderId
            }
        }

        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns getOrderResponse
    }

    protected fun mockOrderOrderItemsApi() {
        val getOrderItemResponse = GetOrderItemResponse().orderId(1234)

        every {
            ordersOrderItemsApiService.getOrdersOrderItems(
                any(),
                any(),
                any(),
                any(),
            )
        } returns getOrderItemResponse
    }

    var capturedUpdateProcessingOrderStatus = mutableListOf<ProcessingOrderStatus>()
    var capturedUpdateProcessingOrderId = mutableListOf<Int>()

    protected fun mockProcessingOrdersService() {
        capturedUpdateProcessingOrderStatus = mutableListOf() // reset between tests
        capturedUpdateProcessingOrderId = mutableListOf()

        every {
            processingOrderService.updateProcessingOrderStatus(
                capture(capturedUpdateProcessingOrderId),
                any(),
                capture(capturedUpdateProcessingOrderStatus),
                any(),
            )
        } returns Unit
    }

    private fun mockCompletedOrderDetailApi() {
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(UpdateCompletedOrderDetailResponse().entityName("test"))

        val completedOrderDetailResponseJson =
            ClassPathResource("com/legalzoom/fulfillment/workflow/integration/GetCompletedOrderDetailByProcessingOrderId.json").file
        val completedOrderDetailResponse =
            objectMapper.readValue<GetCompleteOrderDetailResponse>(completedOrderDetailResponseJson)

        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(completedOrderDetailResponse)
    }

    protected fun verifyProcessingStatusChanges(
        expectedProcessingStatusList: List<String>,
        processId: Int,
        variables: Variables,
    ) {
        expectedProcessingStatusList.forEach { processingStatus ->
            ProcessingOrderStatus.fromProcessIdAndStatus(processId, processingStatus)
                ?.let { verifyProcessingOrderStatusChange(it, variables.customerId!!) }
        }
    }

    protected fun mockGetOrderContact() {
        val json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/order_contacts_response.json").file
        val response = JsonFileArgumentProvider.objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            ordersContactsApiService.getOrdersContacts(any(), any())
        } returns response
    }

    protected fun mockGetAnswer() {
        val getQuestionnaireAnswerResponse =
            GetQuestionnaireAnswerResponse().apply {
                questionnaireFieldGroupAnswers =
                    QuestionnaireAnswerDto().apply {
                        revision = 0
                        subRevision = 8
                        userOrderId = 1234
                        fieldAnswers =
                            mutableListOf(
                                FieldAnswerDto().apply {
                                    fieldName = "State_of_formation"
                                    fieldValue = "California"
                                },
                            )
                    }
            }

        every {
            answerApi.answersUserOrderIdSourceGet(any(), any(), any(), any(), any(), any(), any(), any())
        } returns Mono.just(getQuestionnaireAnswerResponse)
    }

    protected fun mockRevService() {
        var json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_get_all_templates.json").file
        val templateResponse = JsonFileArgumentProvider.objectMapper.readValue<TemplateResponse>(json)
        coEvery {
            revvEsignatureService.listAllTemplates(any())
        } returns templateResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_create_document.json").file
        val createDocumentResponse = JsonFileArgumentProvider.objectMapper.readValue<CreateDocumentResponse>(json)
        coEvery {
            revvEsignatureService.createDocumentUsingTemplate(any(), any())
        } returns createDocumentResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_save_document_fields.json").file
        val saveDocumentFiledsResponse = JsonFileArgumentProvider.objectMapper.readValue<DocumentFieldsResponse>(json)
        coEvery {
            revvEsignatureService.saveDocumentFileds(any(), any(), any())
        } returns saveDocumentFiledsResponse

        coEvery {
            revvEsignatureService.downloadPdfDocument(any(), any())
        } returns byteArrayOf()
    }

    protected fun expireSalesforceWaitTimer(processInstance: ProcessInstance) {
        val job =
            await(Duration.of(1, MINUTES))
                .untilNotNull {
                    AbstractAssertions.processEngine().managementService.createJobQuery()
                        .processInstanceId(processInstance.processInstanceId)
                        .timers()
                        .singleResult()
                }

        AbstractAssertions.processEngine().managementService.executeJob(job.id)
    }

    fun getProcessInstance(
        parentProcessInstance: ProcessInstance,
        processDefinitionKey: String,
    ): ProcessInstance? {
        var childProcessInstance: ProcessInstance? = null
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                childProcessInstance =
                    processEngine.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(processDefinitionKey)
                        .superProcessInstanceId(parentProcessInstance.processInstanceId).active().singleResult()

                assertThat(childProcessInstance).isNotNull
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(childProcessInstance).isStarted }

        return childProcessInstance
    }

    protected fun verifyNewPrintAndShip(
        parentProcessInstance: ProcessInstance,
        variables: Variables,
    ) {
        val printAndShipProcess =
            await(Duration.of(30, SECONDS))
                .until({ getProcessInstance(parentProcessInstance, PRINT_PROCESS) }, { it != null })

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(printAndShipProcess).isWaitingAt("waiting-print-response") }

        processEngine.runtimeService
            .createMessageCorrelation(PRINT_STATUS_MESSAGE)
            .processInstanceId(printAndShipProcess?.id)
            .setVariables(
                mapOf(
                    "printStatus" to "SHIPPED",
                    "evidenceTransactionNumber" to "1234567890",
                ),
            )
            .correlate()

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(printAndShipProcess).isEnded }

        assertAll(
            "Assert print and ship in/out mappings",
            {
                assertThat(printAndShipProcess).variables().containsAllEntriesOf(
                    variables {
                        customerId = variables.customerId
                        jurisdiction = variables.jurisdiction
                        orderId = variables.orderId
                        processId = variables.processId
                        processingOrderId = variables.processingOrderId
                        evidenceTransactionNumber = "1234567890"
                        printStatus = "SHIPPED"
                    },
                )
            },
        )
    }

    protected fun verifyProcessingOrderStatusChange(
        processingOrderStatus: ProcessingOrderStatus,
        customerId: String,
    ) {
        verify {
            processingOrderService.updateProcessingOrderStatus(
                any(),
                customerId,
                processingOrderStatus,
                any(),
            )
        }
    }

    protected fun buildVariables(testProcessId: Int) =
        variables {
            customerId = Random.nextInt().toString()
            orderId = Random.nextInt()
            processingOrderId = Random.nextInt()
            processId = testProcessId
            // causes the SF timers to be skipped.  previously tried moving clock forward but there are still 5 second
            // intervals between jobs such that that approach wasn't as fast as this
            // Needed for HOLD_AREA flow
            sfDelayTimer = "PT0S"
        }

    protected fun completeSalesforceTaskWithDisposition(
        processInstance: ProcessInstance,
        taskName: String,
        dispositionPassed: FulfillmentDisposition,
    ) {
        complete(
            await(Duration.of(60, SECONDS))
                .untilNotNull {
                    task(
                        taskName,
                        salesforceHelper.getSalesforceProcessInstance(processInstance),
                    )
                },
            variables {
                disposition = dispositionPassed.value
            },
        )
    }

    private fun mockBuildDocumentPath() {
        every {
            documentStorageFolderNameProvider.buildDocumentPath(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns ""

        every {
            documentStorageFolderNameProvider.getEPProductsDocumentPathForRename(
                any(),
                any(),
                any(),
            )
        } returns "Wills and trusts/CustomerName"

        justRun {
            documentService.renameFolder(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    protected fun mockAnswerApi(
        processId: Int,
        mockAnswerFileName: String,
    ) {
        val answersResponseFile = ClassPathResource(mockAnswerFileName, javaClass).file
        val response = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersResponseFile)

        val getAnswersComposite = GetAnswersComposite(processId, response)
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns getAnswersComposite
    }

    protected fun mockAnswerVersionApi(mockAnswerFileName: String) {
        val answerVersionResponseFile = ClassPathResource(mockAnswerFileName, javaClass).file
        val response = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answerVersionResponseFile)

        every {
            questionnaireAnswerService.getAnswerVersionForUserOrderId(any(), any(), any())
        } returns response
    }

    companion object {
        @JvmStatic
        fun epBundleProcesses() =
            listOf(
                Arguments.arguments(
                    "EP Bundle Living Trust Test",
                    ProductType.LivingTrust.processId,
                    "living_trust_order_response.json",
                    "living_trust_answers.json",
                    listOf(
                        ProcessingOrderStatus.LivingTrustReadyForDownload,
                        ProcessingOrderStatus.LivingTrustSentToShipping,
                        ProcessingOrderStatus.LivingTrustOrderPrintedAndShipped,
                    ),
                ),
            )
    }

    @ParameterizedTest
    @MethodSource("epBundleProcesses")
    fun testEPBundleProcess(
        testCaseName: String,
        processId: Int,
        mockOrderResponseFileName: String,
        mockAnswersResponseFileName: String,
        expectedProcessingStatusList: List<ProcessingOrderStatus>,
    ) {
        val variables = buildVariables(processId)

        mockOrdersApi(mockOrderResponseFileName, variables.processingOrderId!!)
        mockAnswerApi(processId, mockOrderResponseFileName)
        mockAnswerVersionApi(mockAnswersResponseFileName)
        mockBuildDocumentPath()

        val (businessKey, processInstance) = startEPBundleProcess(variables)

        // Confirm if process instance started
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("ep-bundle-fetch-order-data") }

        // Confirm doc gen process started
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("ep-bundle-doc-gen-process") }

        val docGenSubProcessInstance =
            await(Duration.of(30, SECONDS))
                .until({ getActiveInstanceOf(DOCUMENT_GENERATION_PROCESS, processInstance) }, { it != null })

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(docGenSubProcessInstance).isWaitingAt("documents-generated-task") }

        // doc generated successfully
        processEngine.runtimeService.correlateMessage(
            "Message_DOCGEN",
            businessKey,
            variables {
                status = "success"
                this.processId = variables.processId
                documentPaths =
                    mutableListOf(
                        "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_Bill_of_Transfer.pdf",
                        "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_Certification_of_Trust.pdf",
                        "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_Cover_Letter.pdf",
                        "s3://dds-document-storage-dev/181121/181121_${variables.processingOrderId}_Declaration_of_Trust.pdf",
                    )
            },
        )

        // Confirm doc gen subprocess ended
        await(Duration.of(30, SECONDS))
            .untilAsserted { docGenSubProcessInstance!!.isEnded }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-document-status") }

        // verify docs were uploaded
        verify {
            documentService.uploadDocument(
                any(),
                any(),
                any(),
                DocumentStatus.Active,
                any(),
            )
        }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-document-status") }

        // verify docs were set to active
        verify {
            documentService.updateDocument(any(), DocumentStatus.Active, any(), any(), any(), any(), any())
        }

        // Confirm if process instance started
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }
    }

    fun verifyProcessingStatusChanges(
        expectedProcessingStatusList: List<ProcessingOrderStatus>,
        variables: Variables,
    ) {
        expectedProcessingStatusList.forEach { processingStatus ->
            verify {
                processingOrderService.updateProcessingOrderStatus(
                    any(),
                    variables.customerId.toString(),
                    processingStatus,
                    any(),
                )
            }
        }
    }
}
