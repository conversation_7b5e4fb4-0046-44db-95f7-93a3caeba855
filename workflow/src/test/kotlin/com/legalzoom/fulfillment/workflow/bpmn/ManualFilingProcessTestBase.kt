package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.answer.AnswerApi
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.dds.DocumentRequestApi
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.model.answer.FieldAnswerDto
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.answer.QuestionnaireAnswerDto
import com.legalzoom.api.model.answer.SaveQuestionnaireAnswerResponse
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.dsd.printship.PrintShipJobResponseDto
import com.legalzoom.api.model.filenetprint.PrintResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.GetProcessingOrderResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.processingorder.PutProcessingOrderResponse
import com.legalzoom.api.model.processingorder.UpdateCompletedOrderDetailResponse
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.processingorder.ProcessingOrdersApi
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.api.revv.RevvEsignatureService
import com.legalzoom.api.revv.model.CreateDocumentResponse
import com.legalzoom.api.revv.model.DocumentFieldsResponse
import com.legalzoom.api.revv.model.TemplateResponse
import com.legalzoom.fulfillment.common.service.ORCOFeatureToggleService
import com.legalzoom.fulfillment.domain.Constants.HOLD_AREA_PROCESS
import com.legalzoom.fulfillment.domain.Constants.MANUAL_FILING_PROCESS
import com.legalzoom.fulfillment.domain.Constants.PRINT_AND_SHIP_PROCESS
import com.legalzoom.fulfillment.domain.Constants.PRINT_PROCESS
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.EIN_FILING
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.MANUAL_POST_FILING
import com.legalzoom.fulfillment.domain.enumeration.EventPhase.PUBLICATION
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.printandship.entity.VendorCode
import com.legalzoom.fulfillment.printandship.repository.VendorCodeRepository
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_STATUS_MESSAGE
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintConfig
import com.legalzoom.fulfillment.printandshipapi.enumeration.RequestType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.LLC
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.AlchemyDocumentType
import com.legalzoom.fulfillment.service.data.AlchemyMessage
import com.legalzoom.fulfillment.service.data.CustomerDocumentType
import com.legalzoom.fulfillment.service.data.documents.Document
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.DocumentStatus
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition.Proceed
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.AlchemyMessageHandlerService
import com.legalzoom.fulfillment.service.service.DocumentAutomationService
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FieldUpdaterService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersContactsApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.PrintService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.helper.documents.S3Service
import com.legalzoom.fulfillment.service.service.helper.documents.StoragePlatformClient
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.service.service.questionnaire.partialUpdates.SaveAnswerComposite
import com.legalzoom.fulfillment.testing.Await.await
import com.legalzoom.fulfillment.testing.SpringBootProcessTest
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import com.legalzoom.fulfillment.workflow.dmn.JsonFileArgumentProvider
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.SlackMessageService
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import com.slack.api.Slack
import com.slack.api.webhook.Payload
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.awaitility.kotlin.untilNotNull
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.TaskService
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions.init
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.task
import org.camunda.bpm.engine.test.assertions.cmmn.CmmnAwareTests.assertThat
import org.camunda.bpm.engine.test.assertions.cmmn.CmmnAwareTests.complete
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.assertAll
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.toEntity
import reactor.core.publisher.Mono
import java.io.File
import java.net.URI
import java.time.Clock
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit.MINUTES
import java.time.temporal.ChronoUnit.SECONDS
import java.util.UUID
import javax.ws.rs.WebApplicationException
import kotlin.random.Random

@SpringBootProcessTest
@EmbeddedKafka
@Deployment(
    resources = [
        "bpmn/manual-filing.bpmn",
        "bpmn/document-generation.bpmn",
        "bpmn/print-and-ship.bpmn",
        "bpmn/print.bpmn",
        "bpmn/hold-area.bpmn",
        "bpmn/salesforce.bpmn",
        "dmn/manualWorkflow/*.dmn",
    ],
)
@Disabled
class ManualFilingProcessTestBase(
    protected val clock: Clock,
    protected val processEngine: ProcessEngine,
    protected val alchemyMessageHandlerService: AlchemyMessageHandlerService,
    protected val taskService: TaskService,
    private val salesforceHelper: SalesforceHelper,
) {
    protected val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    val getActiveInstanceOf = { processDefinitionKey: String, parentProcessInstance: ProcessInstance ->
        processEngine.runtimeService
            .createProcessInstanceQuery()
            .processDefinitionKey(processDefinitionKey)
            .superProcessInstanceId(parentProcessInstance.id)
            .active()
            .singleResult()
    }

    @MockkBean
    protected lateinit var documentRequestApi: DocumentRequestApi

    @MockkBean
    protected lateinit var documentService: DocumentService

    @MockkBean
    protected lateinit var storagePlatformClient: StoragePlatformClient

    @MockkBean(relaxed = true)
    protected lateinit var salesforceApiService: SalesforceApiService

    @MockkBean
    protected lateinit var s3Service: S3Service

    @MockkBean
    protected lateinit var processingOrdersApi: ProcessingOrdersApi

    @MockkBean
    protected lateinit var slack: Slack

    @MockkBean
    protected lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    protected lateinit var ssorcoFeatureToggleService: ORCOFeatureToggleService

    @MockkBean
    protected lateinit var customerApi: CustomerApi

    @MockkBean
    protected lateinit var notificationEventService: NotificationEventService

    @MockkBean
    protected lateinit var printService: PrintService

    @MockkBean
    protected lateinit var ordersApiService: OrdersApiService

    @MockkBean
    protected lateinit var processingOrderService: ProcessingOrderService

    @MockkBean
    private lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    protected lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean(relaxed = true)
    @Qualifier("webClient")
    protected lateinit var webClient: WebClient

    @MockkBean
    protected lateinit var ordersOrderItemsApiService: OrdersOrderItemsApiService

    @MockkBean
    protected lateinit var ordersContactsApiService: OrdersContactsApiService

    @MockkBean
    protected lateinit var answerApi: AnswerApi

    @MockkBean
    protected lateinit var revvEsignatureService: RevvEsignatureService

    @MockkBean
    protected lateinit var completedOrderDetailService: CompletedOrderDetailService

    @MockkBean
    protected lateinit var documentListService: DocumentListService

    @MockkBean
    protected lateinit var orderContactsService: OrderContactsService

    @MockkBean
    protected lateinit var vendorCodeRepository: VendorCodeRepository

    @MockkBean
    protected lateinit var slackMessageService: SlackMessageService

    @MockkBean
    protected lateinit var fieldUpdaterService: FieldUpdaterService

    @MockkBean
    protected lateinit var productsApi: ProductsApi

    @MockkBean
    protected lateinit var documentAutomationService: DocumentAutomationService

    protected fun mockPrintShipServices() {
        mockCompletedOrderDetailService()
        mockDocumentListService()
        mockOrderContactsService()
        mockPrintAndShipApi()
        mockVendorCodeRepository()
        mockkStatic(LocalDateTime::class)
        every {
            LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        } returns LocalDateTime.parse("2024-01-01T00:00:00")
        mockProductsApi()
    }

    protected fun mockProductsApi() {
        val json = ClassPathResource("productsApiPostOptionResponse.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)
    }

    protected fun mockVendorCodeRepository() {
        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.PRINT },
            )
        } answers {
            listOf(VendorCode("viatech", "PLZ-LLCPOD", "LLC Printing", RequestType.PRINT, LLC.processId))
        }

        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                any(),
                any(),
                match { it == RequestType.KIT },
            )
        } returns
            listOf(
                VendorCode("viatech", "KLZ-LCTT-CA", "LLC Founders Kit", RequestType.KIT, LLC.processId),
            )
    }

    @MockkBean
    private lateinit var printShipJobsApi: PrintShipJobsApi

    fun mockPrintAndShipApi() {
        every {
            printShipJobsApi.createJobAsync(any())
        } returns
            Mono.just(
                PrintShipJobResponseDto().also { response ->
                    response.id = UniqueId.nextUUIDString()
                },
            )
    }

    protected fun mockOrderContactsService() {
        val json = ClassPathResource("orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(any(), any())
        } returns response.contacts!!
    }

    protected fun mockDocumentListService() {
        every {
            documentListService.getDefaultDocumentListWithConfig(any(), any(), any(), any())
        } returns
            listOf(
                PrintDocumentInfo(
                    documentId = "1234",
                    documentName = "Articles Filed.pdf",
                    printConfig = PrintConfig.LZDOC1,
                    documentType = "Articles Filed",
                ),
            )
    }

    protected fun mockCompletedOrderDetailService() {
        val json = ClassPathResource("completedOrderDetailResponse.json", javaClass).file
        var response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)

        every {
            completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(any(), any())
        } returns response
    }

    // End Print & Ship mocks

    fun startManualFilingProcess(variables: Variables): Pair<String, ProcessInstance> {
        val businessKey = variables.processingOrderId.toString()

        val processInstance =
            processEngine.runtimeService.startProcessInstanceByKey(MANUAL_FILING_PROCESS, businessKey, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        return Pair(businessKey, processInstance)
    }

    @BeforeEach
    fun setup() {
        clearAllMocks()
        init(processEngine)
        mockCustomerDataFetch()
        mockSnsServiceData()
        mockAnswerApi()
        mockDocumentRequestApi()
        mockS3Service()
        mockDocumentService()
        mockSalesforceApi()
        mockProcessingOrdersApi()
        mockSlack()
        mockFeatureToggleService()
//        mockPrintService()
        mockProcessingOrdersService()
        mockOrdersApi()
        mockWebClient()
        mockOrderOrderItemsApi()
        mockSlackMessageCalls()
        mockFieldUpdaterService()
        mockDocumentAutomationService()
        mockPrintShipServices()
    }

    protected fun mockDocumentAutomationService() {
        every {
            documentAutomationService.packSuiteCase(any())
        } returns Unit
    }

    protected fun mockSlackMessageCalls() {
        every {
            slackMessageService.sendSlackMessage(any(), any())
        } returns Unit
    }

    protected fun mockWebClient() {
        val testFile = ClassPathResource("com/legalzoom/fulfillment/workflow/integration/testFile.png").file
        val resource: Resource = FileSystemResource(testFile)
        val responseEntity = ResponseEntity(resource, HttpStatus.OK)
        every {
            webClient.get().uri(any<URI>()).retrieve().toEntity<Resource>().block()
        } returns responseEntity
    }

    protected fun mockCustomerDataFetch() {
        val customerByIdResponseJson =
            ClassPathResource("com/legalzoom/fulfillment/workflow/integration/customer_api_customer_by_customerId_response.json").file
        val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        } returns Mono.just(customerByIdResponse)
    }

    protected fun mockSnsServiceData() {
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns "d413681a-9810-5dc7-b485-349134df00ad"
    }

    protected fun mockAnswerApi(
        entityType: AnswersEntityType? = null,
        processId: Int = LLC.processId,
    ) {
        val response =
            GetQuestionnaireAnswerResponse().apply {
                questionnaireFieldGroupAnswers =
                    QuestionnaireAnswerDto().apply {
                        fieldAnswers =
                            listOf(
                                FieldAnswerDto().fieldName("Type_of_entity").fieldValue((entityType ?: LLC).labels[0]),
                                FieldAnswerDto().fieldName("Company_name").fieldValue("Test Entity Name"),
                                FieldAnswerDto().fieldName("LLP_Name").fieldValue("Test Entity Name"),
                                FieldAnswerDto().fieldName("LP_Name").fieldValue("Test Entity Name"),
                                FieldAnswerDto().fieldName("Converted_entity_type").fieldValue("limited liability company (llc)"),
                                FieldAnswerDto().fieldName("entity_type").fieldValue("Limited Liability Company"),
                                FieldAnswerDto().fieldName("Trade_name").fieldValue("Test Entity Name"),
                            )
                    }
            }

        val getAnswersComposite = GetAnswersComposite(processId, response)
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), any())
        } returns getAnswersComposite

        every {
            questionnaireAnswerService.putPartialUpdate(any(), any())
        } returns SaveAnswerComposite(SaveQuestionnaireAnswerResponse())
    }

    protected fun mockDocumentRequestApi() {
        val response =
            DocumentsGenerationResponse().apply {
                isAllDocRequestSubmitted = true
            }

        every {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(any(), any(), any(), any())
        } returns Mono.just(response)
    }

    protected fun mockS3Service() {
        every {
            s3Service.getDocument(any())
        } returns
            ResourceWithType(
                FileSystemResource(File("PreFiling_Articles.pdf")),
                MediaType.APPLICATION_PDF,
                "PreFiling_Articles.pdf",
            )
    }

    private var capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter =
        mutableListOf<CustomerDocumentType>()

    protected fun mockDocumentService() {
        capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter.clear()
        val updateDocumentMetaDataResponse =
            DocumentResponse().documentId("Test")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")

        every {
            storagePlatformClient.updateDocumentMetaData(any(), any(), any())
        } returns updateDocumentMetaDataResponse

        every {
            documentService.uploadDocument(
                any(),
                capture(capturedDocumentServiceUploadDocumentCustomerDocumentTypeParameter),
                any(),
                any(),
            )
        } returns
            DocumentResponse().documentId("Test").documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE"))
                .documentVersion("1")

        val testFile = ClassPathResource("com/legalzoom/fulfillment/workflow/integration/documentBoth.json").file
        val documents = objectMapper.readValue<List<Document>>(testFile)
        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns documents
        every {
            documentService.findDocumentsBy(
                any(),
                null,
                null,
                orderId = any(),
            )
        } returns documents

        every {
            documentService.updateDocument(any(), DocumentStatus.Active, any(), any(), any(), any(), any())
        } returns
            DocumentResponse().documentId("00154c503df1476f873bd054a52a311f")
                .documentStatus(DocumentResponse.DocumentStatusEnum.valueOf("ACTIVE")).documentVersion("1")
    }

    protected var capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
    protected var capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
    protected var capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

    protected fun mockSalesforceApi() {
        capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
        capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
        capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

        every {
            salesforceApiService.updateCase(capture(capturedSalesForceUpdateCaseRequests))
        } returns SalesforceCaseUpdateResponse("test", "test", "test")

        every {
            salesforceApiService.createCase(capture(capturedSalesForceRequests))
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(capture(capturedLedgerNoteRequests))
        } returns AddLedgerNoteResponse("test", "test", emptyList())
    }

    protected fun mockProcessingOrdersApi() {
        every {
            processingOrderService.getProcessingOrder(any())
        } returns ProcessingOrderDto()

        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdProcessingStatusProcessingStatusIdPut(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(PutProcessingOrderResponse())
        val getResponse = GetProcessingOrderResponse()
        val processingOrder = ProcessingOrderDto().processingOrderId(567893).processingStatusId(75).processId(2)
        getResponse.processingOrder(processingOrder)
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(any(), any(), any(), any())
        } returns Mono.just(getResponse)

        var processingOrderResponse = GetProcessingOrderResponse()
        processingOrderResponse.processingOrder = processingOrder
        every {
            processingOrdersApi.coreProcessingOrdersProcessingOrderIdGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(processingOrderResponse)
    }

    protected fun mockSlack() {
        every {
            slack.send(any(), any<Payload>())
        } returns null
    }

    protected fun mockFeatureToggleService() {
        every {
            featureToggleService.isCustomHandlerEnabled(any(), any(), any())
        } returns false

        every {
            ssorcoFeatureToggleService.isSsOrcoEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false

        every {
            featureToggleService.isManualNotificationEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isRpaEINBypassEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isPrintNameplateEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isDSDDocGenEnabled(any(), any(), any())
        } returns false
    }

    protected fun mockPrintService() {
        every {
            printService.printOrder(any())
        } returns PrintResponse().requestId(Random.nextInt().toString())
    }

    protected fun mockOrdersApi(
        processingOrderId: Int = 511205226,
        getOrderResponseFileName: String = "ca_llc_order_without_soi_response.json",
    ) {
        val json = ClassPathResource(getOrderResponseFileName, javaClass).file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(json)
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse.also { it.order!!.orderItems!!.first().processingOrder!!.processingOrderId = processingOrderId }
    }

    protected fun mockOrderOrderItemsApi() {
        val getOrderItemResponse = GetOrderItemResponse().orderId(1234)

        every {
            ordersOrderItemsApiService.getOrdersOrderItems(
                any(),
                any(),
                any(),
                any(),
            )
        } returns getOrderItemResponse
    }

    var capturedUpdateProcessingOrderStatus = mutableListOf<ProcessingOrderStatus>()
    var capturedUpdateProcessingOrderId = mutableListOf<Int>()

    protected fun mockProcessingOrdersService() {
        capturedUpdateProcessingOrderStatus = mutableListOf() // reset between tests
        capturedUpdateProcessingOrderId = mutableListOf()

        every {
            processingOrderService.updateProcessingOrderStatus(
                capture(capturedUpdateProcessingOrderId),
                any(),
                capture(capturedUpdateProcessingOrderStatus),
                any(),
            )
        } returns Unit

        every {
            processingOrderService.getProcessingOrderStatusId(
                any(),
                any(),
            )
        } returns ProcessingOrderStatus.ByLawsAndResolutionsNotYetStarted
    }

    protected fun mockCompletedOrderDetailApi() {
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderPut(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(UpdateCompletedOrderDetailResponse().entityName("test"))

        val completedOrderDetailResponseJson =
            ClassPathResource("com/legalzoom/fulfillment/workflow/integration/GetCompletedOrderDetailByProcessingOrderId.json").file
        val completedOrderDetailResponse =
            objectMapper.readValue<GetCompleteOrderDetailResponse>(completedOrderDetailResponseJson)

        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(completedOrderDetailResponse)
    }

    protected fun verifyProcessingStatusChanges(
        expectedProcessingStatusList: List<String>,
        processId: Int,
        variables: Variables,
    ) {
        expectedProcessingStatusList.forEach { processingStatus ->
            ProcessingOrderStatus.fromProcessIdAndStatus(processId, processingStatus)
                ?.let { verifyProcessingOrderStatusChange(it, variables.customerId!!) }
        }
    }

    protected fun mockGetOrderContact() {
        val json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/order_contacts_response.json").file
        val response = JsonFileArgumentProvider.objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            ordersContactsApiService.getOrdersContacts(any(), any())
        } returns response
    }

    protected fun mockGetAnswer() {
        val getQuestionnaireAnswerResponse =
            GetQuestionnaireAnswerResponse().apply {
                questionnaireFieldGroupAnswers =
                    QuestionnaireAnswerDto().apply {
                        revision = 0
                        subRevision = 8
                        userOrderId = 1234
                        fieldAnswers =
                            mutableListOf(
                                FieldAnswerDto().apply {
                                    fieldName = "State_of_formation"
                                    fieldValue = "California"
                                },
                            )
                    }
            }

        every {
            answerApi.answersUserOrderIdSourceGet(any(), any(), any(), any(), any(), any(), any(), any())
        } returns Mono.just(getQuestionnaireAnswerResponse)
    }

    protected fun mockRevService() {
        var json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_get_all_templates.json").file
        val templateResponse = JsonFileArgumentProvider.objectMapper.readValue<TemplateResponse>(json)
        coEvery {
            revvEsignatureService.listAllTemplates(any())
        } returns templateResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_create_document.json").file
        val createDocumentResponse = JsonFileArgumentProvider.objectMapper.readValue<CreateDocumentResponse>(json)
        coEvery {
            revvEsignatureService.createDocumentUsingTemplate(any(), any())
        } returns createDocumentResponse

        json = ClassPathResource("com/legalzoom/fulfillment/workflow/bpmn/revv_save_document_fields.json").file
        val saveDocumentFiledsResponse = JsonFileArgumentProvider.objectMapper.readValue<DocumentFieldsResponse>(json)
        coEvery {
            revvEsignatureService.saveDocumentFileds(any(), any(), any())
        } returns saveDocumentFiledsResponse

        coEvery {
            revvEsignatureService.downloadPdfDocument(any(), any())
        } returns byteArrayOf()
    }

    protected fun mockFieldUpdaterService() {
        every {
            fieldUpdaterService.saveFieldValue(any(), any(), any(), any(), any())
        } returns Unit
    }

    protected fun expireSalesforceWaitTimer(processInstance: ProcessInstance) {
        val job =
            await(Duration.of(1, MINUTES))
                .untilNotNull {
                    AbstractAssertions.processEngine().managementService.createJobQuery()
                        .processInstanceId(processInstance.processInstanceId)
                        .timers()
                        .singleResult()
                }

        AbstractAssertions.processEngine().managementService.executeJob(job.id)
    }

    protected fun getPastHoldArea(
        holdAreaProcessInstance: ProcessInstance,
        businessKey: String,
        variables: Variables,
        documentTypeValue: AlchemyDocumentType,
    ) {
        simulateAlchemyMessage(variables, businessKey, holdAreaProcessInstance, documentTypeValue)

        await(Duration.of(1, MINUTES))
            .untilAsserted { Assertions.assertTrue { capturedSalesForceUpdateCaseRequests.count() > 0 } }
    }

    fun simulateAlchemyMessage(
        variables: Variables,
        businessKey: String,
        processInstance: ProcessInstance,
        documentTypeValue: AlchemyDocumentType,
    ) {
        assertThat(taskService).isNotNull

        val evidenceFilePathFakeValue =
            "https://document-ingestion-processed-bucket-dev.s3.us-west-2.amazonaws.com/" +
                "tmr_test_1_14e891f7-8a40-417e-b9a6-6546e64b8352.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256" +
                "=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIA5XCWCT3VJRW5QJPP%2F20230726%2Fus-west-2%2Fs3%2Faws4_request&" +
                "X-Amz-Date=20230726T110623Z&X-Amz-Expires=172800&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEKv%2F%2F%2F%2" +
                "F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMiJHMEUCIHflqu4d%2B5YLYVLpBB1yGuSpoX6D%2BX0TFtjnjtoDg9h6AiEA3TljQwb" +
                "qSRBpiOSMEwjJazucuhBDPUUxcMYywLOyKLUqsQMIRBAEGgw5NDI5MjU4NDgyOTgiDFYjddzcgsg2HHjIlSqOA6f6OQAXInPhYfvg" +
                "MJh830%2FlKqGrBU%2BA0THbzeMYGNwlVQdNwV6GW%2Bm8vIg2t1KYVD%2BfirsLTUl%2BkKXBP9M7f3kSXwl2fyMwrk6yC0yN2tg" +
                "qDE9HuU6rObpuhflcDjjm1%2FYbZ7vcP0LmSfDbzyisAcYEmOBBfFPZEbnXznRgWjgjTtVMHcOYyhepFsg4H%2Bv%2FVo9O7wtGmW" +
                "iAnvumkrj4dp1zEraYr9EjhOMHOQeTz69wzOlzp8jmaabpQmlnfpCwBjxbnGXKCTuvgvgJYsCojkY0wtumrPtgN4w3lg%2Fq1ldKy" +
                "x7cm2ZIAdYppn3FhJBvk7qI%2BmPowRTr6jePNgJsDU1R4p2u20DWhjPo3dMrrpYMcd%2FqMkiGym17WotAsQbnjn%2Fxqs%2BXh%" +
                "2BZFGFVqwviBr%2FfmQHPi0ET%2BPPf3briBxqyQxLoNoOBJWhqPVmYpTHLE3MzGfxSMitlWBlswJkZ7HEJYHJRDRiy6JIU0zHDBO" +
                "Fxx8lCrNKRKoinIKVYqKL4eKnLrlbqi9otVq%2FL5gb%2FdMK38g6YGOp0BHnrw7zt7mcTvMTiSFw8xkAktEXnl%2B7sn8hmCCMWP" +
                "eSfxUKMRtr3Ll%2BsgDnUlGmp%2F6%2Fm77wobTj1hgm3pdbpkWWs8kRwxjM4wKpzXPlLK8N%2FGu%2FmWl%2BsguyxLfDh0LmdBq" +
                "0Z6aCf0RFqJqDx4HTMXjyRhtYZqxyA8TMF7GoMj8%2BO3qbOqwws6Wf4xSm6wtnjcFq0SMLxCTBMl1MbslA%3D%3D&X-Amz-Signa" +
                "ture=f091e3d0413cefc0d263a7130177f3b02da61154cb857663c1f54360ca1e7695&X-Amz-SignedHeaders=host&x-id=GetObject"

        val alchemyMessage =
            buildAlchemyMessage(variables, businessKey, evidenceFilePathFakeValue, documentTypeValue, "12345")

        alchemyMessageHandlerService.onAlchemyMessage(alchemyMessage)
    }

    protected fun buildAlchemyMessage(
        variables: Variables,
        businessKey: String,
        evidenceFilePathFakeValue: String,
        documentTypeValue: AlchemyDocumentType,
        stateIssuedEntityId: String?,
    ) = AlchemyMessage(
        processingOrderId = businessKey.toLong(),
        correlationId = UUID.randomUUID().toString(),
        disposition = null,
        evidenceFilePath = evidenceFilePathFakeValue,
        documentType = documentTypeValue.displayName,
        effectiveDate =
            if (documentTypeValue == AlchemyDocumentType.ARTICLES_FILED) {
                OffsetDateTime.of(
                    LocalDate.now(clock),
                    LocalTime.NOON,
                    ZoneOffset.UTC,
                ).toString()
            } else {
                ""
            },
        evidenceTransactionNumber = stateIssuedEntityId,
        jobId = UUID.randomUUID().toString(),
    )

    fun getProcessInstance(
        parentProcessInstance: ProcessInstance,
        processDefinitionKey: String,
    ): ProcessInstance? {
        var childProcessInstance: ProcessInstance? = null
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                childProcessInstance =
                    processEngine.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(processDefinitionKey)
                        .superProcessInstanceId(parentProcessInstance.processInstanceId).active().singleResult()

                assertThat(childProcessInstance).isNotNull
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(childProcessInstance).isStarted }

        return childProcessInstance
    }

    protected fun verifyNewPrintAndShip(
        parentProcessInstance: ProcessInstance,
        variables: Variables,
    ) {
        val printAndShipProcess =
            await(Duration.of(30, SECONDS))
                .until({ getProcessInstance(parentProcessInstance, PRINT_PROCESS) }, { it != null })

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(printAndShipProcess).isWaitingAt("waiting-print-response") }

        processEngine.runtimeService
            .createMessageCorrelation(PRINT_STATUS_MESSAGE)
            .processInstanceId(printAndShipProcess?.id)
            .setVariables(
                mapOf(
                    "printStatus" to "SHIPPED",
                    "evidenceTransactionNumber" to "1234567890",
                ),
            )
            .correlate()

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(printAndShipProcess).isEnded }

        assertAll(
            "Assert print and ship in/out mappings",
            {
                assertThat(printAndShipProcess).variables().containsAllEntriesOf(
                    variables {
                        customerId = variables.customerId
                        jurisdiction = variables.jurisdiction
                        orderId = variables.orderId
                        processId = variables.processId
                        processingOrderId = variables.processingOrderId
                        evidenceTransactionNumber = "1234567890"
                        printStatus = "SHIPPED"
                    },
                )
            },
        )
    }

    protected fun verifyPrintAndShip(
        parentProcessInstance: ProcessInstance,
        variables: Variables,
        throwPrintApiException: Boolean = false,
    ) {
        if (throwPrintApiException) {
            every {
                printService.printOrder(any())
            } throws WebApplicationException("TEST EXCEPTION")
        }

        val printAndShipProcess =
            await(Duration.of(30, SECONDS))
                .until({ getActiveInstanceOf(PRINT_AND_SHIP_PROCESS, parentProcessInstance) }, { it != null })

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(printAndShipProcess).isWaitingAt("receive-print-response") }

        processEngine.runtimeService
            .createMessageCorrelation("Message_Print_Response")
            .processInstanceId(printAndShipProcess?.id)
            .setVariable("printStatus", "Success")
            .correlate()

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(printAndShipProcess).isEnded }

        assertAll(
            "Assert print and ship in/out mappings",
            {
                assertThat(printAndShipProcess).variables().containsAllEntriesOf(
                    variables {
                        customerId = variables.customerId
                        jurisdiction = variables.jurisdiction
                        orderId = variables.orderId
                        processId = variables.processId
                    },
                )
            },
        )
    }

    protected fun verifyProcessingOrderStatusChange(
        processingOrderStatus: ProcessingOrderStatus,
        customerId: String,
    ) {
        verify {
            processingOrderService.updateProcessingOrderStatus(
                any(),
                customerId,
                processingOrderStatus,
                any(),
            )
        }
    }

    protected fun buildVariables(
        testProcessId: Int = ProductType.AnnualReports.processId,
        testJurisdiction: String = "VA",
    ): Variables {
        val vars =
            variables {
                customerId = Random.nextInt().toString()
                orderId = Random.nextInt()
                processingOrderId = Random.nextInt()
                processId = testProcessId
                jurisdiction = testJurisdiction
                // causes the SF timers to be skipped.  previously tried moving clock forward but there are still 5 second
                // intervals between jobs such that that approach wasn't as fast as this
                // Needed for HOLD_AREA flow
                sfDelayTimer = "PT0S"
            }

        mockOrdersApi(vars.processingOrderId!!)
        return vars
    }

    protected fun testInstantFilingProcess(
        testName: String,
        processId: Int,
        stateAbbr: String,
        expectedProcessingStatusList: List<String>,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // complete pre-filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual filing is complete
        expireSalesforceWaitTimer(processInstance)

        // complete manual filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any { exception ->
                                exception.eventPhase == MANUAL_POST_FILING.name &&
                                    exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process", "hold-area") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testDelayFilingProcessWithHoldArea(
        testName: String,
        processId: Int,
        stateAbbr: String,
        documentType: String,
        expectedProcessingStatusList: List<String>,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // complete pre-filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        val holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // QC Review case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any {
                                    exception ->
                                exception.eventPhase == EventType.MANUAL_POST_FILING.name && exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "hold-area",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testDelayFilingProcessWithHoldAreaRejectionFlow(
        testName: String,
        processId: Int,
        stateAbbr: String,
        alchemyDocumentType: AlchemyDocumentType,
        documentType: String,
        eventType: EventType,
        expectedProcessingStatusList: List<String>,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                humanTaskCreationEnabled = true
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)
        salesforceHelper.getSalesforceProcessInstance(processInstance)

        // complete pre-filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        var holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // SOS rejected saying ARTICLES_NAME_REJECTION or ARTICLES_MISFILED
        getPastHoldArea(processInstance, businessKey, variables, alchemyDocumentType)

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedRejectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedRejectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }
        // this should create new rejection cases which when resolved, should create hold area case
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                assertThat(
                    salesforceHelper.getSalesforceProcessInstance(
                        holdAreaProcessInstance!!,
                    ),
                ).isWaitingAt("salesforce-task")
            }

        // verify processingOrderStatus is updated to State rejected filing
        val expectedRejectionProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "State Rejected Filing")!!
        verifyProcessingOrderStatusChange(expectedRejectionProcessingOrderStatus, variables.customerId!!)

        var salesforceProcessInstance =
            holdAreaProcessInstance?.let {
                salesforceHelper.getSalesforceProcessInstance(it)
            }

        // move the reject case with proceed disposition and EventType as Name rejection

        complete(
            await(Duration.of(60, SECONDS))
                .untilNotNull { task("salesforce-task", salesforceProcessInstance) },
            variables {
                disposition = Proceed.value
                this.eventType = eventType.toString()
            },
        )

        // let's verify if order is in hold area here after rejection case was closed
        holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // let's upload correct articles this time
        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "hold-area",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testInstantFilingProcessWithNameCheck(
        testName: String,
        processId: Int,
        stateAbbr: String,
        expectedProcessingStatusList: List<String>,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        // name check case
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce name check case is complete
        expireSalesforceWaitTimer(processInstance)

        // complete pre-filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // manual pre filing case
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // complete case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual filing is complete
        expireSalesforceWaitTimer(processInstance)

        // complete final review case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any { exception ->
                                exception.eventPhase == MANUAL_POST_FILING.name &&
                                    exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process", "hold-area") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testDelayFilingProcessWithHoldAreaAndNameCheck(
        testName: String,
        processId: Int,
        stateAbbr: String,
        documentType: String,
        expectedProcessingStatusList: List<String>,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        // name check case
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve name check case
        expireSalesforceWaitTimer(processInstance)

        // complete name check case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // pre filing case
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // complete pre-filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        val holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // QC Review case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any {
                                    exception ->
                                exception.eventPhase == EventType.MANUAL_POST_FILING.name && exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "hold-area",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testDelayFilingProcessWithHoldAreaRejectionFlowAndNameCheck(
        testName: String,
        processId: Int,
        stateAbbr: String,
        alchemyDocumentType: AlchemyDocumentType,
        documentType: String,
        eventType: EventType,
        expectedProcessingStatusList: List<String>,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        // name check case
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve name check case
        expireSalesforceWaitTimer(processInstance)

        // complete name check case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // pre filing case
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // complete pre-filing case
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        var holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // SOS rejected saying ARTICLES_NAME_REJECTION or ARTICLES_MISFILED
        getPastHoldArea(processInstance, businessKey, variables, alchemyDocumentType)

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedRejectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedRejectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }
        // this should create new rejection cases which when resolved, should create hold area case
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                assertThat(
                    salesforceHelper.getSalesforceProcessInstance(
                        holdAreaProcessInstance!!,
                    ),
                ).isWaitingAt("salesforce-task")
            }

        // verify processingOrderStatus is updated to State rejected filing
        val expectedRejectionProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "State Rejected Filing")!!
        verifyProcessingOrderStatusChange(expectedRejectionProcessingOrderStatus, variables.customerId!!)

        val salesforceProcessInstance =
            holdAreaProcessInstance?.let {
                salesforceHelper.getSalesforceProcessInstance(
                    it,
                )
            }

        // move the reject case with proceed disposition and EventType as Name rejection
        complete(
            await(Duration.of(60, SECONDS))
                .untilNotNull { task("salesforce-task", salesforceProcessInstance) },
            variables {
                disposition = Proceed.value
                this.eventType = eventType.toString()
            },
        )

        // let's verify if order is in hold area here after rejection case was closed
        holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // let's upload correct articles this time
        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "hold-area",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testInstantFilingProcessWithNameCheckAndEIN(
        testName: String,
        processId: Int,
        stateAbbr: String,
        expectedProcessingStatusList: List<String>,
        einFiling: Boolean,
    ) {
        mockCompletedOrderDetailApi()
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
                einFilingEnabled = einFiling
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual name check validation is complete
        expireSalesforceWaitTimer(processInstance)

        // manual name check validation is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual filing
        expireSalesforceWaitTimer(processInstance)

        // manual filing is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        if (einFiling) {
            // verify EIN process was started
            await(Duration.of(30, SECONDS))
                .untilAsserted { assertThat(processInstance).hasPassed("start-attached-ein-obtainment-process") }
        }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // assuming POST Filing QC is created here
        expireSalesforceWaitTimer(processInstance)

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any { exception ->
                                exception.eventPhase == EventType.MANUAL_POST_FILING.name &&
                                    exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        if (processId == ProductType.DBA.processId) {
            assertAll(
                testName,
                {
                    assertThat(processInstance).hasPassed(
                        "order-data-fetch",
                        "step-transition-check",
                        "update-order-status",
                        "exit",
                    )
                },
                {
                    assertThat(processInstance).hasNotPassed(
                        "generate-docs-sub-process",
                        "hold-area",
                        "print-and-ship",
                    )
                },
                {
                    assertThat(processInstance).variables().containsAllEntriesOf(
                        variables {
                            manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                            action = "EXIT_WORKFLOW_SKIP_PRINT"
                        },
                    )
                },
            )
        } else {
            verifyNewPrintAndShip(processInstance, variables)

            await(Duration.of(30, SECONDS))
                .untilAsserted { assertThat(processInstance).isEnded }

            verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

            assertAll(
                testName,
                {
                    assertThat(processInstance).hasPassed(
                        "order-data-fetch",
                        "step-transition-check",
                        "update-order-status",
                        "print-and-ship",
                        "exit",
                    )
                },
                { assertThat(processInstance).hasNotPassed("generate-docs-sub-process", "hold-area") },
                {
                    assertThat(processInstance).variables().containsAllEntriesOf(
                        variables {
                            manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                            action = "EXIT_WORKFLOW"
                        },
                    )
                },
            )
        }
    }

    private fun getEinProcessInstance(processInstance: ProcessInstance): ProcessInstance? {
        var einProcessInstance: ProcessInstance? = null
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                einProcessInstance =
                    processEngine.runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(EIN_FILING.workflowName)
                        .superProcessInstanceId(processInstance.processInstanceId).active().singleResult()

                assertThat(einProcessInstance).isNotNull
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(einProcessInstance).isStarted }

        return einProcessInstance
    }

    protected fun testDelayFilingProcessWithHoldAreaNameCheckAndEIN(
        testName: String,
        processId: Int,
        stateAbbr: String,
        documentType: String,
        expectedProcessingStatusList: List<String>,
        einFiling: Boolean,
    ) {
        mockCompletedOrderDetailApi()
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
                einFilingEnabled = einFiling
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual name check validation is complete
        expireSalesforceWaitTimer(processInstance)

        // manual name check validation is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual filing case
        expireSalesforceWaitTimer(processInstance)

        // manual filing case is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        val holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        if (einFiling) {
            // verify EIN process was started
            await(Duration.of(30, SECONDS))
                .untilAsserted { assertThat(processInstance).hasPassed("start-attached-ein-obtainment-process") }
        }

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any {
                                    exception ->
                                exception.eventPhase == EventType.MANUAL_POST_FILING.name && exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        if (processId == ProductType.DBA.processId) {
            assertAll(
                "Limited Liability Partnership(LLP) Manual Delayed(Online) Filing Test",
                {
                    assertThat(processInstance).hasPassed(
                        "order-data-fetch",
                        "step-transition-check",
                        "update-order-status",
                        "hold-area",
                        "exit",
                    )
                },
                {
                    assertThat(processInstance).hasNotPassed(
                        "generate-docs-sub-process",
                        "print-and-ship",
                    )
                },
                {
                    assertThat(processInstance).variables().containsAllEntriesOf(
                        variables {
                            manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                            action = "EXIT_WORKFLOW_SKIP_PRINT"
                        },
                    )
                },
            )
        } else {
            verifyNewPrintAndShip(processInstance, variables)

            await(Duration.of(30, SECONDS))
                .untilAsserted { assertThat(processInstance).isEnded }

            verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

            assertAll(
                "Limited Liability Partnership(LLP) Manual Delayed(Online) Filing Test",
                {
                    assertThat(processInstance).hasPassed(
                        "order-data-fetch",
                        "step-transition-check",
                        "update-order-status",
                        "hold-area",
                        "print-and-ship",
                        "exit",
                    )
                },
                { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
                {
                    assertThat(processInstance).variables().containsAllEntriesOf(
                        variables {
                            manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                            action = "EXIT_WORKFLOW"
                        },
                    )
                },
            )
        }
    }

    protected fun testDelayFilingProcessWithHoldAreaRejectionFlowWithNameCheckAndEIN(
        testName: String,
        processId: Int,
        stateAbbr: String,
        alchemyDocumentType: AlchemyDocumentType,
        documentType: String,
        eventType: EventType,
        expectedProcessingStatusList: List<String>,
        einFiling: Boolean,
    ) {
        mockCompletedOrderDetailApi()
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                humanTaskCreationEnabled = true
                einFilingEnabled = einFiling
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual name check validation is complete
        expireSalesforceWaitTimer(processInstance)

        // manual name check validation is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual filing case
        expireSalesforceWaitTimer(processInstance)

        // manual filing case is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        var holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // SOS rejected saying ARTICLES_NAME_REJECTION or ARTICLES_MISFILED
        getPastHoldArea(processInstance, businessKey, variables, alchemyDocumentType)

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedRejectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedRejectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }
        // this should create new rejection cases which when resolved, should create hold area case
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                assertThat(
                    salesforceHelper.getSalesforceProcessInstance(
                        holdAreaProcessInstance!!,
                    )!!,
                )
                    .isWaitingAt("salesforce-task")
            }

        // verify processingOrderStatus is updated to State rejected filing
        val expectedRejectionProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "State Rejected Filing")!!
        verifyProcessingOrderStatusChange(expectedRejectionProcessingOrderStatus, variables.customerId!!)

        // move the reject case with proceed disposition and EventType as Name rejection
        complete(
            await(Duration.of(60, SECONDS))
                .untilNotNull {
                    task(
                        "salesforce-task",
                        salesforceHelper.getSalesforceProcessInstance(
                            holdAreaProcessInstance!!,
                        )!!,
                    )
                },
            variables {
                disposition = Proceed.value
                this.eventType = eventType.toString()
            },
        )

        // let's verify if order is in hold area here after rejection case was closed
        holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // let's upload correct articles this time
        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        if (einFiling) {
            // verify EIN process was started
            await(Duration.of(30, SECONDS))
                .untilAsserted { assertThat(processInstance).hasPassed("start-attached-ein-obtainment-process") }
        }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual EIN filing is complete
        expireSalesforceWaitTimer(processInstance)

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        if (processId == ProductType.DBA.processId) {
            assertAll(
                testName,
                {
                    assertThat(processInstance).hasPassed(
                        "order-data-fetch",
                        "step-transition-check",
                        "update-order-status",
                        "hold-area",
                        "exit",
                    )
                },
                {
                    assertThat(processInstance).hasNotPassed(
                        "generate-docs-sub-process",
                        "print-and-ship",
                    )
                },
                {
                    assertThat(processInstance).variables().containsAllEntriesOf(
                        variables {
                            manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                            action = "EXIT_WORKFLOW_SKIP_PRINT"
                        },
                    )
                },
            )
        } else {
            verifyNewPrintAndShip(processInstance, variables)

            await(Duration.of(30, SECONDS))
                .untilAsserted { assertThat(processInstance).isEnded }

            verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

            assertAll(
                testName,
                {
                    assertThat(processInstance).hasPassed(
                        "order-data-fetch",
                        "step-transition-check",
                        "update-order-status",
                        "hold-area",
                        "print-and-ship",
                        "exit",
                    )
                },
                { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
                {
                    assertThat(processInstance).variables().containsAllEntriesOf(
                        variables {
                            manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                            action = "EXIT_WORKFLOW"
                        },
                    )
                },
            )
        }
    }

    protected fun testDelayFilingProcessWithHoldAreaWithOrWithoutNameCheck(
        testName: String,
        processId: Int,
        stateAbbr: String,
        documentType: String,
        expectedProcessingStatusList: List<String>,
        nameCheck: Boolean,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
                needNameCheck = nameCheck
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // resolve salesforce case assuming manual pre-filing validation is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        val holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        // QC Review case
        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any {
                                    exception ->
                                exception.eventPhase == EventType.MANUAL_POST_FILING.name && exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "hold-area",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }

    protected fun testDelayFilingProcessWithPublications(
        testName: String,
        processId: Int,
        stateAbbr: String,
        documentType: String,
        expectedProcessingStatusList: List<String>,
        county: String,
    ) {
        mockCompletedOrderDetailApi()
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                manualDocGenEnabled = false
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual name check validation is complete
        expireSalesforceWaitTimer(processInstance)

        // manual name check validation is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual filing case
        expireSalesforceWaitTimer(processInstance)

        // manual filing case is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming publication case
        expireSalesforceWaitTimer(processInstance)

        // manual publication is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // prove that after filing is done, we are creating a PUBLICATION CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any {
                                    exception ->
                                exception.eventPhase == PUBLICATION.name && exception.type == SalesforceExceptionType.Exception
                            }
                    },
                )
            }

        // prove that after filing is done, we are creating a POST FILING QC REVIEW CASE
        await(Duration.of(30, SECONDS))
            .untilAsserted {
                assert(
                    capturedSalesForceRequests.any {
                        it.processingNumber == businessKey &&
                            it.exceptions.any {
                                    exception ->
                                exception.eventPhase == EventType.MANUAL_POST_FILING.name && exception.type == SalesforceExceptionType.QC
                            }
                    },
                )
            }
    }

    protected fun testDelayFilingProcessWithHoldAreaRejectionFlowWithOrWithoutNameCheck(
        testName: String,
        processId: Int,
        stateAbbr: String,
        alchemyDocumentType: AlchemyDocumentType,
        documentType: String,
        eventType: EventType,
        expectedProcessingStatusList: List<String>,
        nameCheck: Boolean,
    ) {
        mockAnswerApi(AnswersEntityType.fromProcessIdNullable(processId), processId)

        val variables =
            buildVariables(processId, stateAbbr).apply {
                entityType = LLC.name
                humanTaskCreationEnabled = true
                needNameCheck = nameCheck
            }

        val (businessKey, processInstance) = startManualFilingProcess(variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isStarted }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // resolve salesforce case assuming prefiling validation is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        // let's verify if order is in hold area here
        var holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // SOS rejected saying ARTICLES_NAME_REJECTION or ARTICLES_MISFILED
        getPastHoldArea(processInstance, businessKey, variables, alchemyDocumentType)

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedRejectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedRejectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }
        // this should create new rejection cases which when resolved, should create hold area case
        await(Duration.of(1, MINUTES))
            .untilAsserted {
                assertThat(
                    salesforceHelper.getSalesforceProcessInstance(
                        holdAreaProcessInstance!!,
                    ),
                ).isWaitingAt("salesforce-task")
            }

        // verify processingOrderStatus is updated to State rejected filing
        val expectedRejectionProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "State Rejected Filing")!!
        verifyProcessingOrderStatusChange(expectedRejectionProcessingOrderStatus, variables.customerId!!)

        val salesforceProcessInstance =
            holdAreaProcessInstance?.let {
                salesforceHelper.getSalesforceProcessInstance(
                    it,
                )
            }

        // move the reject case with proceed disposition and EventType as Name rejection

        complete(
            await(Duration.of(60, SECONDS))
                .untilNotNull { task("salesforce-task", salesforceProcessInstance) },
            variables {
                disposition = Proceed.value
                this.eventType = eventType.toString()
            },
        )

        // let's verify if order is in hold area here after rejection case was closed
        holdAreaProcessInstance = getProcessInstance(processInstance, HOLD_AREA_PROCESS)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isWaitingAt("salesforce-task-shadowcase") }

        // let's upload correct articles this time
        getPastHoldArea(processInstance, businessKey, variables, AlchemyDocumentType.ARTICLES_FILED)

        // proves the order has moved further in the flow
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("salesforce-task-shadowcase") }

        // and now let's verify if upload docs delegate was called and document was uploaded
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("upload-documents") }

        val expectedCustomerDocumentType =
            CustomerDocumentType.findCustomerDocTypeFromProductName(
                ProductType.fromProcessId(processId).productName,
                documentType,
            )
        verify(exactly = 1) {
            documentService.uploadDocument(
                any(),
                expectedCustomerDocumentType,
                any(),
                DocumentStatus.Inactive,
            )
        }

        // and now let's verify if upload docs delegate & save effectiveDate delegate was called.
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).hasPassed("save-effective-date") }

        // verify processingOrderStatus is updated to
        val expectedProcessingOrderStatus =
            ProcessingOrderStatus.fromProcessIdAndDescription(processId, "Documents Received From SOS")!!
        verifyProcessingOrderStatusChange(expectedProcessingOrderStatus, variables.customerId!!)

        // verify hold area process instance is complete
        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(holdAreaProcessInstance).isEnded }

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isWaitingAt("sf-wait-timer") }

        // resolve salesforce case assuming manual pre-filing validation is complete
        expireSalesforceWaitTimer(processInstance)

        // resolve salesforce case assuming final review is complete
        salesforceHelper.awaitAndResolveSalesforceCase(
            processInstance,
            "salesforce-task",
            true,
            Proceed.value,
        )

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).hasPassed("update-doc-status") }

        verifyNewPrintAndShip(processInstance, variables)

        await(Duration.of(30, SECONDS))
            .untilAsserted { assertThat(processInstance).isEnded }

        verifyProcessingStatusChanges(expectedProcessingStatusList, processId, variables)

        assertAll(
            testName,
            {
                assertThat(processInstance).hasPassed(
                    "order-data-fetch",
                    "step-transition-check",
                    "update-order-status",
                    "hold-area",
                    "print-and-ship",
                    "exit",
                )
            },
            { assertThat(processInstance).hasNotPassed("generate-docs-sub-process") },
            {
                assertThat(processInstance).variables().containsAllEntriesOf(
                    variables {
                        manualWorkflowStep = "MANUAL_PRINT_AND_SHIP"
                        action = "EXIT_WORKFLOW"
                    },
                )
            },
        )
    }
}
