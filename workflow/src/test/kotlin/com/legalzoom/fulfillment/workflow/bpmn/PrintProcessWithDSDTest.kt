package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.customer.CustomerApi
import com.legalzoom.api.dds.DocumentRequestApi
import com.legalzoom.api.dsd.printship.PrintShipJobsApi
import com.legalzoom.api.dsd.printship.ViatechStatusCallbackApi
import com.legalzoom.api.model.customer.CustomerDetailResponse
import com.legalzoom.api.model.dds.DocumentsGenerationResponse
import com.legalzoom.api.model.dsd.printship.PrintShipJobDocumentInfoDto
import com.legalzoom.api.model.dsd.printship.PrintShipJobKitRequestDto
import com.legalzoom.api.model.dsd.printship.PrintShipJobReferenceIdsDto
import com.legalzoom.api.model.dsd.printship.PrintShipJobRequestDto
import com.legalzoom.api.model.dsd.printship.PrintShipJobResponseDto
import com.legalzoom.api.model.dsd.printship.ViatechStatusUpdateError
import com.legalzoom.api.model.dsd.printship.ViatechStatusUpdateResponseDto
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.api.model.ordercontacts.GetOrderContactsResponse
import com.legalzoom.api.model.processingorder.GetCompleteOrderDetailResponse
import com.legalzoom.api.model.processingorder.ProcessingOrderDto
import com.legalzoom.api.model.product.GetPostOptionResponse
import com.legalzoom.api.model.storageplatform.DocumentResponse
import com.legalzoom.api.processingorder.CompletedOrderDetailApi
import com.legalzoom.api.product.ProductsApi
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.domain.enumeration.EventPhase
import com.legalzoom.fulfillment.domain.enumeration.EventType
import com.legalzoom.fulfillment.domain.enumeration.State
import com.legalzoom.fulfillment.printandship.data.toDSDShippingAddressDto
import com.legalzoom.fulfillment.printandship.entity.ShippingInformation
import com.legalzoom.fulfillment.printandship.entity.VendorCode
import com.legalzoom.fulfillment.printandship.repository.VendorCodeRepository
import com.legalzoom.fulfillment.printandship.service.CompletedOrderDetailService
import com.legalzoom.fulfillment.printandship.service.DocumentListService
import com.legalzoom.fulfillment.printandshipapi.Constants.PRINT_STATUS_MESSAGE
import com.legalzoom.fulfillment.printandshipapi.data.PrintDocumentInfo
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintConfig
import com.legalzoom.fulfillment.printandshipapi.enumeration.PrintOrderStatuses
import com.legalzoom.fulfillment.printandshipapi.enumeration.RequestType
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteRequest
import com.legalzoom.fulfillment.salesforce.model.AddLedgerNoteResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateRequest
import com.legalzoom.fulfillment.salesforce.model.SalesforceCaseUpdateResponse
import com.legalzoom.fulfillment.salesforce.model.SalesforceExceptionType
import com.legalzoom.fulfillment.service.data.documents.ResourceWithType
import com.legalzoom.fulfillment.service.enumeration.Cp1CustomerShipMethod
import com.legalzoom.fulfillment.service.enumeration.FulfillmentDisposition
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.DocumentService
import com.legalzoom.fulfillment.service.service.EntityNameService
import com.legalzoom.fulfillment.service.service.FeatureToggleService
import com.legalzoom.fulfillment.service.service.FulfillmentEventService
import com.legalzoom.fulfillment.service.service.LegacyEventHandlerService
import com.legalzoom.fulfillment.service.service.OrderContactsService
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.ProcessingOrderService
import com.legalzoom.fulfillment.service.service.ProcessingOrderStatus
import com.legalzoom.fulfillment.service.service.WorkOrderService
import com.legalzoom.fulfillment.service.service.helper.documents.NotificationEventService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.filingData.GetAnswersPayload
import com.legalzoom.fulfillment.testing.Await
import com.legalzoom.fulfillment.testing.SpringBootProcessTest
import com.legalzoom.fulfillment.testing.UniqueId
import com.legalzoom.fulfillment.testing.configuration.FixedClockConfiguration
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import com.legalzoom.fulfillment.workflow.delegate.UploadDocumentsDocGenDelegate
import com.legalzoom.fulfillment.workflow.delegate.ValidatePrintResponseDelegate
import com.legalzoom.fulfillment.workflow.service.SalesforceApiService
import com.legalzoom.fulfillment.workflow.service.SlackMessageService
import com.legalzoom.fulfillment.workflow.variable.startProcessInstanceByKey
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.MockkBean
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.AssertionsForClassTypes.assertThat
import org.awaitility.kotlin.untilNotNull
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.impl.util.ClockUtil
import org.camunda.bpm.engine.runtime.ProcessInstance
import org.camunda.bpm.engine.test.Deployment
import org.camunda.bpm.engine.test.assertions.bpmn.AbstractAssertions
import org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests
import org.camunda.bpm.engine.test.assertions.cmmn.CmmnAwareTests
import org.joda.time.DateTimeZone
import org.joda.time.LocalDateTime
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.kafka.test.context.EmbeddedKafka
import reactor.core.publisher.Mono
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.UUID

@SpringBootProcessTest
@Deployment(resources = ["bpmn/print.bpmn", "bpmn/document-generation.bpmn", "bpmn/salesforce.bpmn"])
@EmbeddedKafka
@Import(FixedClockConfiguration::class)
class PrintProcessWithDSDTest(
    private val processEngine: ProcessEngine,
    private val runtimeService: RuntimeService,
    private val objectMapper: ObjectMapper,
    private val salesforceHelper: SalesforceHelper,
) {
    @MockkBean
    private lateinit var completedOrderDetailService: CompletedOrderDetailService

    @MockkBean
    private lateinit var documentListService: DocumentListService

    @MockkBean
    private lateinit var orderContactsService: OrderContactsService

    @MockkBean
    private lateinit var documentRequestApi: DocumentRequestApi

    @MockkBean
    private lateinit var salesforceApiService: SalesforceApiService

    @MockkBean
    private lateinit var documentService: DocumentService

    @MockkBean
    private lateinit var vendorCodeRepository: VendorCodeRepository

    @MockkBean
    private lateinit var slackMessageService: SlackMessageService

    @MockkBean
    private lateinit var ordersApiService: OrdersApiService

    @MockkBean
    private lateinit var processingOrderService: ProcessingOrderService

    @MockkBean
    private lateinit var featureToggleService: FeatureToggleService

    @MockkBean
    private lateinit var uploadDocumentsDocGenDelegate: UploadDocumentsDocGenDelegate

    @MockkBean
    private lateinit var notificationEventService: NotificationEventService

    @MockkBean
    private lateinit var completedOrderDetailApi: CompletedOrderDetailApi

    @MockkBean
    private lateinit var workOrderService: WorkOrderService

    @MockkBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @MockkBean
    private lateinit var entityNameService: EntityNameService

    @MockkBean
    private lateinit var productsApi: ProductsApi

    @MockkBean
    private lateinit var fulfillmentEventService: FulfillmentEventService

    @MockkBean
    private lateinit var printShipJobsApi: PrintShipJobsApi

    @MockkBean
    private lateinit var viatechStatusCallbackApi: ViatechStatusCallbackApi

    @MockkBean
    private lateinit var customerApi: CustomerApi

    @MockkBean
    private lateinit var legacyEventHandlerService: LegacyEventHandlerService

    companion object {
        const val DSD_JOB_ID = "**********"
        const val PROCESSING_ORDER_ID = *********
        const val CUSTOMER_ID = "1120875"
        val ACCOUNT_ID = UniqueId.nextUUID()
        const val REQUEST_ID = "print-request-0001"
        const val ORDER_ID = 3456789
        val PROCESS_ID = ProductType.LLC.processId
        const val ENTITY_NAME = "Test Company LLC"
        private val JURISDICTION = State.fromId(1)!!.abbreviation
        val testShippingInfo =
            ShippingInformation(
                name = "Landen Testing",
                firm = "Test Company LLC",
                addressLine1 = "1542 11th St",
                addressLine2 = "",
                city = "West Linn",
                state = "OR",
                zipCode = "97068",
                country = "USA",
                contactNumber = "**********",
                specialInstructions = "",
            )
        val date = LocalDateTime.parse("2024-01-01T12:00:00") // Monday, no maintenance delay
        private val testDocumentInfoListInitial =
            listOf(
                PrintDocumentInfo("123-abc", null, null, null),
                PrintDocumentInfo("456-def", null, null, null),
                PrintDocumentInfo("789-ghi", null, null, null),
            )

        private val testDocumentInfoListFinal =
            listOf(
                PrintDocumentInfo(
                    "123-abc",
                    "abc.pdf",
                    PrintConfig.LZDOC1,
                    "Articles Filed",
                ),
                PrintDocumentInfo(
                    "456-def",
                    "Welcome Packet.pdf",
                    PrintConfig.LZDOC2,
                    "Articles Filed",
                ),
                PrintDocumentInfo(
                    "789-ghi",
                    "Articles Filed.pdf",
                    PrintConfig.LZDOC3,
                    "Articles Filed",
                ),
            )

        const val TEST_SHIP_METHOD = "FEDEX 2ND DAY"

        const val TRACKING_ID = "SOMETRACKINGID12345789"

        private val testLLCKit =
            PrintShipJobKitRequestDto().also {
                it.entityName = ENTITY_NAME
                it.formationDate = "05/26/2023"
                it.formationState = JURISDICTION
                it.vendorCode = "KLZ-LCTT-CA"
            }

        private fun generateExpectedDSDcreateJobRequest(kit: PrintShipJobKitRequestDto? = testLLCKit): PrintShipJobRequestDto {
            return PrintShipJobRequestDto().also {
                it.kit = kit
                it.documentInfo =
                    testDocumentInfoListFinal.map { doc ->
                        PrintShipJobDocumentInfoDto().also { d ->
                            d.documentId = doc.documentId
                            d.printConfig = doc.printConfig!!.printConfigCode
                            d.copies = 1
                        }
                    }
                it.referenceIds =
                    PrintShipJobReferenceIdsDto().also { ref ->
                        ref.processingOrderId = PROCESSING_ORDER_ID.toString()
                        ref.cp1OrderId = ORDER_ID.toString()
                    }
                it.shipMethod = TEST_SHIP_METHOD
                it.accountId = ACCOUNT_ID.toString()
                it.viatechVendorCode = "PLZ-LLCPOD"
                it.shippingAddress = testShippingInfo.toDto().toDSDShippingAddressDto()
            }
        }
    }

    private fun movePrintHoldTimerForward(processInstance: ProcessInstance) {
        val holdTimerJob =
            AbstractAssertions.processEngine().managementService.createJobQuery()
                .processInstanceId(processInstance.processInstanceId)
                .timers()
                .singleResult()
        AbstractAssertions.processEngine().managementService.executeJob(holdTimerJob.id)
    }

    private var capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
    private var capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
    private var capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

    private fun mockSalesforceApi() {
        capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
        capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
        capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()

        every {
            salesforceApiService.updateCase(capture(capturedSalesForceUpdateCaseRequests))
        } returns SalesforceCaseUpdateResponse("test", "test", "test")

        every {
            salesforceApiService.createCase(capture(capturedSalesForceRequests))
        } returns SalesforceCaseResponse("test", "test")

        every {
            salesforceApiService.addLedgerNote(capture(capturedLedgerNoteRequests))
        } returns AddLedgerNoteResponse("test", "test", emptyList())
    }

    var capturedUpdateProcessingOrderStatus = mutableListOf<ProcessingOrderStatus>()
    var capturedUpdateProcessingOrderId = mutableListOf<Int>()

    private fun mockProcessingOrderService() {
        capturedUpdateProcessingOrderStatus = mutableListOf<ProcessingOrderStatus>() // reset between tests
        capturedUpdateProcessingOrderId = mutableListOf<Int>()

        every {
            processingOrderService.getProcessingOrder(any())
        } returns ProcessingOrderDto()

        every {
            processingOrderService.updateProcessingOrderStatus(
                capture(capturedUpdateProcessingOrderId),
                any(),
                capture(capturedUpdateProcessingOrderStatus),
                any(),
            )
        } returns Unit

        every {
            processingOrderService.getProcessingOrderStatusId(any(), any())
        } returns
            ProcessingOrderStatus.fromProcessIdAndDescription(
                ProductType.LLC.processId,
                "Sent to Customer",
            )!!
    }

    private fun mockCompletedOrderDetailService(
        shipMethodFail: Boolean = false,
        printingRequired: Boolean = true,
    ) {
        val json = ClassPathResource("print-and-ship/completedOrderDetailResponse.json", javaClass).file
        var response = objectMapper.readValue<GetCompleteOrderDetailResponse>(json)
        if (!printingRequired) {
            response =
                response.apply {
                    this.completedOrderDetail!!.shipMethodId = Cp1CustomerShipMethod.DOWNLOAD.Id
                    this.completedOrderDetail!!.shipMethod = com.legalzoom.api.model.processingorder.ShipMethod.DOWNLOAD
                }
        }

        if (shipMethodFail) {
            every {
                completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(PROCESSING_ORDER_ID, CUSTOMER_ID)
            } throws Exception("This is a test!")
        } else {
            every {
                completedOrderDetailService.getCompletedOrderDetailByProcessingOrderId(PROCESSING_ORDER_ID, CUSTOMER_ID)
            } returns response
        }

        // Used for OrderStatusService
        every {
            completedOrderDetailApi.coreProcessingOrdersProcessingOrderIdCompletedOrderDetailGet(
                any(),
                any(),
                match { it == CUSTOMER_ID },
                any(),
            )
        } returns Mono.just(response)
    }

    private fun mockDocumentListService() {
        val defaultReturn =
            testDocumentInfoListFinal.map {
                PrintDocumentInfo(it.documentId, it.documentName, it.printConfig, "Articles Filed")
            }

        every {
            documentListService.getDefaultDocumentListWithConfig(
                PROCESS_ID,
                "viatech",
                PROCESSING_ORDER_ID,
                CUSTOMER_ID,
                ACCOUNT_ID.toString(),
            )
        } returns defaultReturn

        val printDocumentList = slot<List<PrintDocumentInfo>>()
        every {
            documentListService.getPrintDocumentInfoByIds(
                capture(printDocumentList),
                PROCESS_ID,
                "viatech",
                PROCESSING_ORDER_ID,
                CUSTOMER_ID,
            )
        } answers {
            printDocumentList.captured.forEach {
                val finalEntry = testDocumentInfoListFinal.first { d -> d.documentId == it.documentId }
                it.documentName = finalEntry.documentName
                it.printConfig = finalEntry.printConfig
            }
            printDocumentList.captured // Return the modified list, the same obj, to mock the behavior in documentListService
        }
    }

    private fun mockOrderContactsApiService() {
        val json = ClassPathResource("print-and-ship/orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns response.contacts!!
    }

    private fun mockOrdersApiService(
        customerId: String?,
        xLZAuthorize: Boolean?,
    ) {
        val json = ClassPathResource("print-and-ship/ordersApiResponse.json", javaClass).file

        val response = objectMapper.readValue<GetOrderResponse>(json)

        every {
            ordersApiService.getOrders(ORDER_ID, null, null, "1.0", customerId, xLZAuthorize)
        } returns response
    }

    private fun mockDocumentService() {
        every {
            documentService.downloadDocument(
                match { it in (testDocumentInfoListFinal.map { d -> d.documentId }) },
                any(),
                any(),
                any(),
            )
        } returns mockk<ResourceWithType>()

        every {
            documentService.findDocumentsBy(any(), any(), any(), any(), any(), any())
        } returns emptyList()

        every { // Docgen use only
            documentService.findDocumentsBy(
                any(),
                any(),
                customerDocumentType = any(),
                documentStatus = any(),
                currentVersionOnly = any(),
            )
        } returns emptyList()

        val uploadResponse =
            DocumentResponse()
                .documentId("Test")
                .documentStatus(DocumentResponse.DocumentStatusEnum.ACTIVE)
                .documentVersion("1")

        every {
            documentService.uploadDocument(any(), any(), any(), any())
        } returns uploadResponse
    }

    private fun mockVendorCodeRepository() {
        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                "viatech",
                ProductType.LLC.processId,
                RequestType.PRINT,
            )
        } returns
            listOf(
                VendorCode(
                    "viatech",
                    "PLZ-LLCPOD",
                    "LLC Printing",
                    RequestType.PRINT,
                    PROCESS_ID,
                ),
            )

        every {
            vendorCodeRepository.findByLocationCodeAndProcessIdAndRequestType(
                "viatech",
                ProductType.LLC.processId,
                RequestType.KIT,
            )
        } returns
            listOf(
                VendorCode(
                    "viatech",
                    "KLZ-LCTT-CA",
                    "LLC Founders Kit",
                    RequestType.KIT,
                    PROCESS_ID,
                ),
            )
    }

    private fun mockSlackMessageService() {
        every {
            slackMessageService.sendSlackMessage(any(), any())
        } answers { }
    }

    private fun mockWorkOrderService() {
        every {
            workOrderService.updateWorkOrderStringStatus(any(), any())
        } returns Unit
        every {
            workOrderService.updateWorkOrderEnumStatus(any(), any())
        } returns Unit
    }

    private fun mockPrintShipJobsApi() {
        every {
            printShipJobsApi.createJobAsync(any())
        } returns
            Mono.just(
                PrintShipJobResponseDto().also { jobResponse ->
                    jobResponse.id = DSD_JOB_ID
                },
            )
    }

    private fun mockViatechStatusCallbackApi(
        successCount: Int = 1,
        failureCount: Int = 0,
    ) {
        val fakeFailedStatus =
            ViatechStatusUpdateError().also {
                it.requestId = "fakeRequestId"
                it.reason = "fakeReason"
            }

        every {
            viatechStatusCallbackApi.saveBulkViatechStatus(any())
        } returns
            Mono.just(
                ViatechStatusUpdateResponseDto().also {
                    it.successCount = successCount
                    it.failedCount = failureCount
                    it.failedStatuses = List(failureCount) { fakeFailedStatus }
                },
            )
    }

    private fun mockCustomerService() {
        val customerByIdResponseJson =
            ClassPathResource("com/legalzoom/fulfillment/workflow/integration/customer_api_customer_by_customerId_response.json").file
        val customerByIdResponse = objectMapper.readValue<CustomerDetailResponse>(customerByIdResponseJson)

        every {
            customerApi.customersCustomerIdGet(any(), any(), any(), any())
        } returns Mono.just(customerByIdResponse)
    }

    private fun correlatePrintResponse(
        processInstance: ProcessInstance,
        businessKey: String,
        status: PrintOrderStatuses,
    ) {
        Await.await(Duration.of(30, ChronoUnit.SECONDS)).untilAsserted {
            BpmnAwareTests.assertThat(processInstance).isWaitingFor(PRINT_STATUS_MESSAGE)
        }
        runtimeService.createMessageCorrelation(PRINT_STATUS_MESSAGE)
            .processInstanceBusinessKey(businessKey)
            .setVariables(
                variables {
                    printStatus = status.name
                    evidenceTransactionNumber = TRACKING_ID
                    printErrorMessage =
                        if (status == PrintOrderStatuses.SHIPPED) {
                            null
                        } else {
                            "sample error reason"
                        }
                },
            ).correlate()

        Await.await(Duration.of(10, ChronoUnit.SECONDS)).untilAsserted {
            BpmnAwareTests.assertThat(processInstance).hasPassed("validate-print-response", "check-print-response")
        }

//        if (status == PrintOrderStatuses.SHIPPED) {
//            Await.await(Duration.of(10, ChronoUnit.SECONDS))
//                .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassed("stc-status-update") }
//        }
    }

    private fun expireTimer(processInstance: ProcessInstance) {
        val job =
            processEngine.managementService.createJobQuery().processInstanceId(processInstance.processInstanceId)
                .timers()
                .singleResult()
        processEngine.managementService.executeJob(job.id)
    }

    @BeforeEach
    fun setupAll() {
        capturedSalesForceRequests = mutableListOf<SalesforceCaseRequest>()
        capturedLedgerNoteRequests = mutableListOf<AddLedgerNoteRequest>()
        capturedSalesForceUpdateCaseRequests = mutableListOf<SalesforceCaseUpdateRequest>()
        clearAllMocks()
        AbstractAssertions.init(processEngine)
        mockSlackMessageService()
        mockGenerateDocuments()
        mockProcessingOrderService()
        mockFeatureToggleService()
        mockDocumentService()
        mockDocumentListService()
        mockSalesforceApi()
        mockVendorCodeRepository()
        mockDocumentService()
        mockNotificationEventService()
        mockOrderContactsApiService()
        mockWorkOrderService()
        mockQuestionnaireAnswerService()
        mockkStatic(LocalDateTime::class)
        every {
            LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        } returns date
        every {
            fulfillmentEventService.send(any())
        } returns Unit

        mockEntityNameService() // in docgen
        mockProductsApi()
        mockPrintShipJobsApi()
        mockViatechStatusCallbackApi()
        mockCustomerService()
        mockLegacyEventHandlerService()
    }

    private fun mockLegacyEventHandlerService() {
        every {
            legacyEventHandlerService.postToEventHandler(
                any(),
                any(),
            )
        } just Runs
    }

    private fun mockProductsApi() {
        val json = ClassPathResource("productsApiPostOptionResponse.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)
    }

    fun tearDown() {
        val processInstancesAfterTest = runtimeService.createProcessInstanceQuery().active().list()
        for (instance in processInstancesAfterTest) {
            runtimeService.deleteProcessInstance(instance.processInstanceId, "Test cleanup")
        }
    }

    private fun mockQuestionnaireAnswerService() {
        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            mockk<GetAnswersPayload>().also {
                every { it.entityName } returns ENTITY_NAME
            }
    }

    private fun mockEntityNameService() {
        every {
            entityNameService.getEntityName(any(), any(), any())
        } returns ENTITY_NAME

        every {
            entityNameService.getEntityNameByParentProcessingOrderId(any(), any())
        } returns ENTITY_NAME
    }

    private fun mockFeatureToggleService() {
        every {
            featureToggleService.isRevvDocGenEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false

        every {
            featureToggleService.isAccelerateOrcoNotificationsEnabled(any(), any(), any())
        } returns false

        every {
            featureToggleService.isPrintNameplateEnabled(any(), any(), any())
        } returns true

        every {
            featureToggleService.isDSDDocGenEnabled(any(), any(), any())
        } returns false
    }

    private fun mockNotificationEventService() {
        every {
            notificationEventService.publishToTopic(any(), any(), any())
        } returns UUID.randomUUID().toString()
    }

    private fun mockGenerateDocuments() {
        every {
            documentRequestApi.documentDeliveryDocumentRequestGeneratePost(
                any(),
                any(),
                any(),
                any(),
            )
        }.answers {
            val response = DocumentsGenerationResponse()
            response.isAllDocRequestSubmitted = true
            Mono.just(response)
        }
    }

    private fun setup(
        shipMethodFail: Boolean = false,
        printingRequired: Boolean = true,
    ) {
        mockCompletedOrderDetailService(
            shipMethodFail = shipMethodFail,
            printingRequired = printingRequired,
        )
        mockOrdersApiService(CUSTOMER_ID, true)

        ClockUtil.setCurrentTime(Date.from(Instant.parse("2024-01-01T12:00:00Z")))
//         returns LocalDateTime.parse("2024-01-01T12:00:00") // Monday, no maintenance delay
    }

    fun movePrintMaintenanceTimerForward(processInstance: ProcessInstance) {
        val job =
            AbstractAssertions.processEngine().managementService.createJobQuery()
                .processInstanceId(processInstance.processInstanceId)
                .timers()
                .singleResult()
        AbstractAssertions.processEngine().managementService.executeJob(job.id)
    }

    @Test
    fun `process succeeds process from start`() {
        setup()
        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                shippingInformation = testShippingInfo.toDto()
                shipMethod = TEST_SHIP_METHOD
                processId = 2
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.SHIPPED)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest())
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString() &&
                        it.shippingAddress.addressLine1 == testShippingInfo.addressLine1
                },
            )
        }

        verify(exactly = 1) {
            processingOrderService.updateProcessingOrderStatus(
                PROCESSING_ORDER_ID,
                any(),
                ProcessingOrderStatus.fromProcessIdAndDescription(ProductType.LLC.processId, "Sent to Customer")!!,
                any(),
            )
        }

        assert(capturedLedgerNoteRequests.last().description == "Order has been shipped with the trackingNumber: $TRACKING_ID")
    }

    @Test
    fun `process succeeds without any order info`() {
        setup()
        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.SHIPPED)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest())
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString() &&
                        it.shippingAddress.addressLine1 == testShippingInfo.addressLine1
                },
            )
        }

        verify(exactly = 1) {
            processingOrderService.updateProcessingOrderStatus(
                PROCESSING_ORDER_ID,
                any(),
                ProcessingOrderStatus.fromProcessIdAndDescription(ProductType.LLC.processId, "Sent to Customer")!!,
                any(),
            )
        }

        assert(capturedLedgerNoteRequests.last().description == "Order has been shipped with the trackingNumber: $TRACKING_ID")
    }

    @Test
    fun `should route order to salesforce case on timeout waiting for response`() {
        setup()

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("waiting-print-response") }
        expireTimer(processInstance)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        completeSFTask(processInstance, FulfillmentDisposition.Proceed)
        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest())
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString()
                },
            )
        }

        Assertions.assertEquals(1, capturedSalesForceRequests.count())
        val caseRequest = capturedSalesForceRequests.first()
        org.assertj.core.api.Assertions.assertThat(caseRequest.exceptions.first().eventPhase)
            .isEqualTo(EventPhase.PRINT_SHIP.toString())
        org.assertj.core.api.Assertions.assertThat(caseRequest.exceptions.first().type)
            .isEqualTo(SalesforceExceptionType.Exception)
        org.assertj.core.api.Assertions.assertThat(caseRequest.exceptions.first().eventType)
            .isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        org.assertj.core.api.Assertions.assertThat(caseRequest.processId)
            .isEqualTo(ProductType.LLC.processId.toString())
        assertThat(
            ((caseRequest.exceptions.first().optionalData as Map<*, *>)["printErrorMessage"]!! as String).contains("sample error reason"),
        )

        assert(capturedLedgerNoteRequests.last().description == ValidatePrintResponseDelegate.PRINT_CALLBACK_TIMEOUT_MESSAGE)
    }

    @Test
    fun `should route order to salesforce case on error response from print callback`() {
        setup()

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.ERROR)

        Await.await(
            Duration.of(
                30,
                ChronoUnit.SECONDS,
            ),
        ).untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        completeSFTask(processInstance, FulfillmentDisposition.Proceed)
        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest())
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString() &&
                        it.shippingAddress.addressLine1 == testShippingInfo.addressLine1
                },
            )
        }

        Assertions.assertEquals(1, capturedSalesForceRequests.count())
        val caseRequest = capturedSalesForceRequests.first()
        org.assertj.core.api.Assertions.assertThat(caseRequest.exceptions.first().eventPhase)
            .isEqualTo(EventPhase.PRINT_SHIP.toString())
        org.assertj.core.api.Assertions.assertThat(caseRequest.exceptions.first().type)
            .isEqualTo(SalesforceExceptionType.Exception)
        org.assertj.core.api.Assertions.assertThat(caseRequest.exceptions.first().eventType)
            .isEqualTo(EventType.VALIDATION_COMPLETE.toString())
        org.assertj.core.api.Assertions.assertThat(caseRequest.processId)
            .isEqualTo(ProductType.LLC.processId.toString())
        assert(
            ((caseRequest.exceptions.first().optionalData as Map<*, *>)["printErrorMessage"]!! as String)
                .contains("sample error reason"),
        )

        assert(capturedLedgerNoteRequests.any { it.description.contains("sample error reason") })
    }

    @Test
    fun `process should exit out on exception`() {
        mockDocumentListService()
        mockSalesforceApi()
        mockCompletedOrderDetailService(shipMethodFail = true)
        mockVendorCodeRepository()

        val businessKey = UUID.randomUUID().toString()
        assertThrows<Exception> {
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }
        }
    }

    @Test
    fun `salesforce case doc regen spawns subprocess`() {
        setup()

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.ERROR)

        Await.await(
            Duration.of(
                30,
                ChronoUnit.SECONDS,
            ),
        ).untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        // Enter doc regen
        clearMocks(ordersApiService)
        mockOrdersApiService(null, null)
        completeSFTask(processInstance, FulfillmentDisposition.DocRegen)

        var docGenProcessInstance: ProcessInstance? = null
        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                docGenProcessInstance =
                    runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey(EventPhase.DOC_GENERATION.workflowName)
                        .superProcessInstanceId(processInstance.processInstanceId).active().singleResult()

                BpmnAwareTests.assertThat(docGenProcessInstance).isNotNull
            }

        BpmnAwareTests.assertThat(docGenProcessInstance).variables().containsAllEntriesOf(
            variables {
                "customerId" to CUSTOMER_ID
                "entityName" to testShippingInfo.firm
                "jurisdiction" to testShippingInfo.state
                "orderId" to ORDER_ID
                "processId" to PROCESS_ID
                "processingOrderId" to PROCESSING_ORDER_ID
            },
        )
        val messageVars =
            variables {
                status = "success"
                documentPaths =
                    mutableListOf("s3://dds-document-storage-dev/181121/181121_${businessKey}_PreFiling_Articles.pdf")
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(docGenProcessInstance).isWaitingAt("documents-generated-task") }

        processEngine.runtimeService.correlateMessage(
            "Message_DOCGEN",
            businessKey,
            messageVars,
        )

        every { // Skip actual docgen
            uploadDocumentsDocGenDelegate.execute(any())
        } returns Unit
        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(docGenProcessInstance).isEnded }
        clearMocks(ordersApiService)
        setup()
        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }
        completeSFTask(processInstance, FulfillmentDisposition.Proceed)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }
    }

    @Test
    fun `should skip process if shipMethod is not physical`() {
        setup(printingRequired = false)

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasNotPassed("sync-print-docs") }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }
    }

    @Test
    fun `should not send kit if not in postOption or orderItems`() {
        setup()
        val json = ClassPathResource("productsApiPostOptionResponseNoKit.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)

        val orderItemJson = ClassPathResource("print-and-ship/ordersApiResponseNoKit.json", javaClass).file
        val orderItemResponse = objectMapper.readValue<GetOrderResponse>(orderItemJson)
        every {
            ordersApiService.getOrders(ORDER_ID, null, null, "1.0", CUSTOMER_ID, true)
        } returns orderItemResponse

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.SHIPPED)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest(kit = null))
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString() &&
                        it.shippingAddress.addressLine1 == testShippingInfo.addressLine1 && it.kit == null
                },
            )
        }
    }

    @Test
    fun `should send a kit in request if not postOption config but in added orderItems`() {
        setup()
        val json = ClassPathResource("productsApiPostOptionResponseNoKit.json", javaClass).file
        val response = objectMapper.readValue<GetPostOptionResponse>(json)
        every {
            productsApi.coreProductsProductIdPostOptionGet(
                any(),
                any(),
                any(),
                any(),
            )
        } returns Mono.just(response)

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.SHIPPED)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest())
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString() &&
                        it.shippingAddress.addressLine1 == testShippingInfo.addressLine1
                },
            )
        }
    }

    @Test
    fun `throws exception during auto maintenance for reprint`() {
        setup()

        mockkStatic(LocalDateTime::class)
        every {
            LocalDateTime.now(DateTimeZone.forID("America/Los_Angeles"))
        } returns LocalDateTime.parse("2024-01-19T21:01:00")

        val businessKey = UUID.randomUUID().toString()
        assertThrows<Exception> {
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                action = "REPRINT"
                accountId = ACCOUNT_ID
            }
        }
    }

    @Test
    fun `throws PrintMaintenanceException when reprint and LD flag on`() {
        setup()

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns true

        val businessKey = UUID.randomUUID().toString()
        assertThrows<Exception> {
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                action = "REPRINT"
                accountId = ACCOUNT_ID
            }
        }
    }

    @Test
    fun `should wait until LD flag is off to print`() {
        setup()

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns true

        ClockUtil.setCurrentTime(Date.from(Instant.parse("2024-01-01T12:00:00Z")))

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        every {
            featureToggleService.shouldPrintShipHoldOrders(any(), any(), any())
        } returns false
        ClockUtil.setCurrentTime(Date.from(Instant.parse("2024-01-21T21:01:00Z")))

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("waiting-print-response") }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.SHIPPED)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }

        verify(exactly = 1) {
            printShipJobsApi.createJobAsync(
                match {
                    assertThat(it).usingRecursiveComparison().isEqualTo(generateExpectedDSDcreateJobRequest())
                    it.referenceIds.processingOrderId == PROCESSING_ORDER_ID.toString() &&
                        it.shippingAddress.addressLine1 == testShippingInfo.addressLine1
                },
            )
        }

        verify(exactly = 1) {
            processingOrderService.updateProcessingOrderStatus(
                PROCESSING_ORDER_ID,
                any(),
                ProcessingOrderStatus.fromProcessIdAndDescription(ProductType.LLC.processId, "Sent to Customer")!!,
                any(),
            )
        }

        assert(capturedLedgerNoteRequests.last().description == "Order has been shipped with the trackingNumber: $TRACKING_ID")
    }

    @Test
    fun `SHIPPED correlation in SF case completes process`() {
        setup()

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("waiting-print-response") }
        expireTimer(processInstance)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.SHIPPED)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }
    }

    @Test
    fun `ERROR correlation in SF case recreates user task`() {
        setup()

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("waiting-print-response") }
        expireTimer(processInstance)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassed("validate-print-response") }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilNotNull {
                BpmnAwareTests.task(
                    "salesforce-task",
                    salesforceHelper.getSalesforceProcessInstance(processInstance),
                )
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        correlatePrintResponse(processInstance, businessKey, PrintOrderStatuses.ERROR)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassedInOrder("late-print-response", "validate-print-response") }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        completeSFTask(processInstance, FulfillmentDisposition.Proceed)

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isEnded }
    }

    @Test
    fun `actionable pre-print problem orders route to SF case`() {
        setup()

        every {
            documentListService.getDefaultDocumentListWithConfig(
                PROCESS_ID,
                "viatech",
                PROCESSING_ORDER_ID,
                CUSTOMER_ID,
                ACCOUNT_ID.toString(),
            )
        } returns emptyList()

        val businessKey = UUID.randomUUID().toString()
        val processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassed("problem-order-event", "problem-order-exit-check") }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilNotNull {
                BpmnAwareTests.task(
                    "salesforce-task",
                    salesforceHelper.getSalesforceProcessInstance(processInstance),
                )
            }

        BpmnAwareTests.assertThat(processInstance).variables().containsEntry("validationError", true)

        mockDocumentListService()
        val json = ClassPathResource("print-and-ship/orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns
            response.contacts!!.onEach {
                it.addressLine1 = null
            }
        completeSFTask(processInstance, FulfillmentDisposition.Retry)

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassed("problem-order-event", "problem-order-exit-check") }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).isWaitingAt("print-salesforce-activity") }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilNotNull {
                BpmnAwareTests.task(
                    "salesforce-task",
                    salesforceHelper.getSalesforceProcessInstance(processInstance),
                )
            }
        BpmnAwareTests.assertThat(processInstance).variables().containsEntry("validationError", true)

        completeSFTask(processInstance, FulfillmentDisposition.Proceed)
        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted {
                BpmnAwareTests.assertThat(processInstance).isEnded
            }
    }

    @Test
    fun `pre-print problem orders exits on REPRINT`() {
        setup()

        // 1. No documents uploaded
        every {
            documentListService.getDefaultDocumentListWithConfig(
                PROCESS_ID,
                "viatech",
                PROCESSING_ORDER_ID,
                CUSTOMER_ID,
                ACCOUNT_ID.toString(),
            )
        } returns emptyList()

        val businessKey = UUID.randomUUID().toString()
        var processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                action = "REPRINT"
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassed("problem-order-event", "problem-order-exit-check") }
        BpmnAwareTests.assertThat(processInstance).isEnded

        // 2. Missing shipping info
        mockDocumentListService()
        val json = ClassPathResource("print-and-ship/orderContactsResponse.json", javaClass).file
        val response = objectMapper.readValue<GetOrderContactsResponse>(json)
        every {
            orderContactsService.getOrderContactsByOrderId(ORDER_ID, CUSTOMER_ID)
        } returns
            response.contacts!!.onEach {
                it.addressLine1 = null
            }
        processInstance =
            runtimeService.startProcessInstanceByKey(EventPhase.PRINT.workflowName, businessKey) {
                processingOrderId = PROCESSING_ORDER_ID
                printRequestId = REQUEST_ID
                processId = ProductType.LLC.processId
                orderId = ORDER_ID
                customerId = CUSTOMER_ID
                workOrderId = UUID.randomUUID()
                action = "REPRINT"
                accountId = ACCOUNT_ID
            }

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted { BpmnAwareTests.assertThat(processInstance).hasPassed("problem-order-event", "problem-order-exit-check") }

        verify(exactly = 2) {
            fulfillmentEventService.send(any())
        }
        BpmnAwareTests.assertThat(processInstance).isEnded
    }

    private fun completeSFTask(
        processInstance: ProcessInstance,
        disposition: FulfillmentDisposition,
    ) {
        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilNotNull {
                salesforceHelper.getSalesforceProcessInstance(processInstance)
            }

        Await.await(Duration.of(30, ChronoUnit.SECONDS))
            .untilAsserted {
                CmmnAwareTests.assertThat(salesforceHelper.getSalesforceProcessInstance(processInstance))
                    .isWaitingAt("salesforce-task")
            }

        CmmnAwareTests.complete(
            Await.await(Duration.of(30, ChronoUnit.SECONDS))
                .untilNotNull {
                    BpmnAwareTests.task(
                        "salesforce-task",
                        salesforceHelper.getSalesforceProcessInstance(processInstance),
                    )
                },
            variables {
                this.disposition = disposition.value
            },
        )

        Await.await(Duration.of(10, ChronoUnit.SECONDS))
            .untilAsserted {
                BpmnAwareTests.assertThat(processInstance).hasPassed("print-salesforce-activity")
            }
    }
}
