package com.legalzoom.fulfillment.workflow.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.legalzoom.api.model.answer.GetQuestionnaireAnswerResponse
import com.legalzoom.api.model.order.GetOrderItemResponse
import com.legalzoom.api.model.order.GetOrderResponse
import com.legalzoom.fulfillment.answersapi.model.AnswerSource
import com.legalzoom.fulfillment.common.extensions.packageOrderItem
import com.legalzoom.fulfillment.salesforce.model.AnswersEntityType.LLC
import com.legalzoom.fulfillment.service.enumeration.ProductType
import com.legalzoom.fulfillment.service.service.OrdersApiService
import com.legalzoom.fulfillment.service.service.OrdersOrderItemsApiService
import com.legalzoom.fulfillment.service.service.questionnaire.QuestionnaireAnswerService
import com.legalzoom.fulfillment.service.service.questionnaire.answersByUserOrderId.GetAnswersComposite
import com.legalzoom.fulfillment.workflow.variable.Variables
import com.legalzoom.fulfillment.workflow.variable.input
import com.legalzoom.fulfillment.workflow.variable.variables
import com.ninjasquad.springmockk.SpykBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.delegate.DelegateExecution
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.core.io.ClassPathResource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@SpringBootTest
@ExtendWith(MockKExtension::class)
class ManualWorkflowDataFetchServiceTest(
    val manualWorkflowDataFetchService: ManualWorkflowOrderDataFetchService,
) {
    private val objectMapper = Jackson2ObjectMapperBuilder.json().build<ObjectMapper>()

    @MockK
    private lateinit var execution: DelegateExecution

    @SpykBean
    private lateinit var questionnaireAnswerService: QuestionnaireAnswerService

    @SpykBean
    private lateinit var ordersApiService: OrdersApiService

    @SpykBean
    private lateinit var orderItemsApiService: OrdersOrderItemsApiService

    val testProcessId = 81

    @BeforeEach
    fun setUp() {
        clearAllMocks()
    }

    @Test
    fun testFetchData() {
        every { execution.input } returns
            variables {
                processId = testProcessId
                customerId = "********"
                processingOrderId = *********
            }

        val orderResponseApiJSON = ClassPathResource("co_ar_order_response.json", javaClass).file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(orderResponseApiJSON)
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse

        every {
            orderItemsApiService.getOrdersOrderItems(any(), any(), any(), any())
        } returns GetOrderItemResponse().orderId(orderResponse.order!!.orderId)

        val answersApiJSON = ClassPathResource("answers_api_questionnaire_field_group_answers.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJSON)

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            GetAnswersComposite(
                testProcessId,
                answersApiResponse,
            )

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        manualWorkflowDataFetchService.fetchData(execution, execution.input.processingOrderId, execution.input.customerId)

        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        }

        assertThat(variables.first()).containsAllEntriesOf(
            variables {
                entityType = LLC.name
                entityName = "Manual Workflow Test"
            },
        )
    }

    @Test
    fun testFetchDataEmptyResponse() {
        every { execution.input } returns
            variables {
                processId = 81
                customerId = "********"
                processingOrderId = *********
            }

        val orderResponseApiJSON = ClassPathResource("co_ar_order_response.json", javaClass).file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(orderResponseApiJSON)
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse

        every {
            orderItemsApiService.getOrdersOrderItems(any(), any(), any(), any())
        } returns GetOrderItemResponse().orderId(orderResponse.order!!.orderId)

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            GetAnswersComposite(
                testProcessId,
                GetQuestionnaireAnswerResponse(),
            )

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        manualWorkflowDataFetchService.fetchData(execution, execution.input.processingOrderId, execution.input.customerId)

        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        }

        assertThat(variables.first()).containsAllEntriesOf(
            variables {
                entityType = null
                entityName = null
            },
        )
    }

    @Test
    fun testFetchDataForChildOrder() {
        val processId = ProductType.AnnualReports.processId
        val processingOrderId = ********
        every { execution.input } returns
            variables {
                this.processId = processId
                customerId = "********"
                this.processingOrderId = processingOrderId
            }

        every {
            orderItemsApiService.getOrdersOrderItems(processingOrderId, any(), any(), any())
        } returns GetOrderItemResponse().orderId(********)

        val orderResponseApiJSON = ClassPathResource("ca_llc_order_response.json", javaClass).file
        val orderResponse = objectMapper.readValue<GetOrderResponse>(orderResponseApiJSON)
        every {
            ordersApiService.getOrders(any(), any(), any(), any(), any(), any())
        } returns orderResponse

        val answersApiJSON = ClassPathResource("llc_questionnaire_answer_response_for_ss4_mapping.json", javaClass).file
        val answersApiResponse = objectMapper.readValue<GetQuestionnaireAnswerResponse>(answersApiJSON)

        every {
            questionnaireAnswerService.getAnswersByUserOrderId(any(), any(), AnswerSource.AnswerBank)
        } returns
            GetAnswersComposite(
                orderResponse.order?.packageOrderItem()?.processingOrder?.processId!!,
                answersApiResponse,
            )

        val variables = mutableListOf<Variables>()
        justRun { execution.variables = capture(variables) }

        manualWorkflowDataFetchService.fetchData(
            execution,
            execution.input.processingOrderId,
            execution.input.customerId,
        )

        verify(exactly = 1) {
            questionnaireAnswerService.getAnswersByUserOrderId(
                orderResponse.order?.packageOrderItem()!!.processingOrder!!.processingOrderId!!,
                any(),
                AnswerSource.AnswerBank,
            )
        }

        assertThat(variables.first()).containsAllEntriesOf(
            variables {
                entityName = "Test Company Name LLC"
            },
        )
    }
}
