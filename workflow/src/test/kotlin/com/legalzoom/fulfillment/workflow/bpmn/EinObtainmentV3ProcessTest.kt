package com.legalzoom.fulfillment.workflow.bpmn

import com.fasterxml.jackson.databind.ObjectMapper
import com.legalzoom.fulfillment.testing.SpringBootProcessTest
import com.legalzoom.fulfillment.workflow.bpmn.helpers.SalesforceHelper
import org.assertj.core.api.Assertions.assertThat
import org.camunda.bpm.engine.DecisionService
import org.camunda.bpm.engine.ProcessEngine
import org.camunda.bpm.engine.RuntimeService
import org.camunda.bpm.engine.test.Deployment
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.kafka.test.context.EmbeddedKafka

@SpringBootProcessTest
@Deployment(
    resources = [
        "bpmn/ein-obtainment-v3.bpmn",
        "bpmn/rpa-bot.bpmn",
        "bpmn/salesforce.bpmn",
        "bpmn/doc-generation.bpmn",
        "bpmn/print.bpmn",
        "dmn/einBotFailures.dmn",
    ],
)
@EmbeddedKafka
class EinObtainmentV3ProcessTest(
    private val objectMapper: ObjectMapper,
    private val processEngine: ProcessEngine,
    private val salesforceHelper: SalesforceHelper,
    private val decisionService: DecisionService,
    private val runtimeService: RuntimeService,
) {
    @ParameterizedTest
    @CsvSource(
        value = [
            "'We are unable to provide you with an EIN. - Reference Number 101 - " +
                "EIN Application was not submitted. Order may not be retried.', SS4",
            "'You Have Exceeded the Number of EINs You May Receive Per Day - Reference Number 114 - " +
                "EIN Application may have been submitted. Order may not be retried.', SALESFORCE_CASE",
            "'Physical Location Addresses cannot be found in the IRS database. - " +
                "EIN Application was not submitted. Order can be retried.', SALESFORCE_CASE",
            "'Order validation errors: Business Location Phone is required. - " +
                "EIN Application was not submitted. Order can be retried.', SALESFORCE_CASE",
            "'Some unknown error message', CAMUNDA_INCIDENT",
        ],
    )
    fun `DMN decision table should return correct action for bot failure messages`(
        message: String,
        expectedAction: String,
    ) {
        val dmnVariables = mapOf("message" to message)

        val result = decisionService.evaluateDecisionTableByKey("Decision_0sa2opn", dmnVariables)

        assertThat(result.singleResult).isNotNull()
        val resultMap = result.singleResult as Map<String, Any>
        assertThat(resultMap["action"]).isEqualTo(expectedAction)
    }

    @Test
    fun `DMN should handle null message input gracefully`() {
        val dmnVariables = mapOf("message" to null)

        val result = decisionService.evaluateDecisionTableByKey("Decision_0sa2opn", dmnVariables)

        assertThat(result.singleResult).isNotNull()
        val resultMap = result.singleResult as Map<String, Any>
        // Should default to CAMUNDA_INCIDENT for null/unknown messages
        assertThat(resultMap["action"]).isEqualTo("CAMUNDA_INCIDENT")
    }

    @Test
    fun `DMN should handle empty message input gracefully`() {
        val dmnVariables = mapOf("message" to "")

        val result = decisionService.evaluateDecisionTableByKey("Decision_0sa2opn", dmnVariables)

        assertThat(result.singleResult).isNotNull()
        val resultMap = result.singleResult as Map<String, Any>
        // Should default to CAMUNDA_INCIDENT for empty/unknown messages
        assertThat(resultMap["action"]).isEqualTo("CAMUNDA_INCIDENT")
    }

    @Test
    fun `DMN should test decision table with multiple outputs`() {
        val dmnVariables =
            mapOf(
                "message" to "We are unable to provide you with an EIN. - Reference Number 101 - " +
                    "EIN Application was not submitted. Order may not be retried.",
            )

        val result = decisionService.evaluateDecisionTableByKey("Decision_0sa2opn", dmnVariables)

        assertThat(result.singleResult).isNotNull()
        val resultMap = result.singleResult as Map<String, Any>
        assertThat(resultMap["action"]).isEqualTo("SS4")
        assertThat(resultMap["delay"]).isNull()
    }

    @Test
    fun `DMN should handle retry scenarios with delay`() {
        val dmnVariables =
            mapOf(
                "message" to "You Have Exceeded the Number of EINs You May Receive Per Day - " +
                    "Reference Number 114 - EIN Application was not submitted. Order may not be retried.",
            )

        val result = decisionService.evaluateDecisionTableByKey("Decision_0sa2opn", dmnVariables)

        assertThat(result.singleResult).isNotNull()
        val resultMap = result.singleResult as Map<String, Any>
        assertThat(resultMap["action"]).isEqualTo("RETRY")
        if (resultMap.containsKey("delay")) {
            assertThat(resultMap["delay"]).isNotNull()
        }
    }
}
